package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

type ControllerMetricsRegistry struct {
	appSyncTotal  *prometheus.CounterVec
	toolExecCount *prometheus.GaugeVec
}

func NewControllerMetricsRegistry() *ControllerMetricsRegistry {
	return &ControllerMetricsRegistry{
		appSyncTotal: promauto.NewCounterVec(
			prometheus.CounterOpts{
				Name: "argocd_app_sync_total",
				Help: "Count of app syncs",
			},
			[]string{"phase"},
		),
		toolExecCount: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "platform_controller_tool_exec_count",
			Help: "Count of in-progress ytt/kustomize execs",
		}, []string{"cmd"}),
	}
}

func (m *ControllerMetricsRegistry) IncAppSyncCount(phase string) {
	m.appSyncTotal.WithLabelValues(phase).Inc()
}

func (m *ControllerMetricsRegistry) IncExecCount(cmd string) {
	m.toolExecCount.WithLabelValues(cmd).Inc()
}

func (m *ControllerMetricsRegistry) DecExecCount(cmd string) {
	m.toolExecCount.WithLabelValues(cmd).Dec()
}
