package metrics

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/go-logr/logr"
	"github.com/volatiletech/sqlboiler/v4/queries"

	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/models/util/status"
)

const (
	countAllInstancesQuery = `
select 
	(select count(id) from argo_cd_instance where argo_cd_instance.shard = $1) + 
	(select count(id) from kargo_instance   where kargo_instance.shard = $1);
`

	countAllSelectedInstancesQuery = `
select 
	(select count(id) from argo_cd_instance where argo_cd_instance.shard = $1 and argo_cd_instance.id IN ('%s')) + 
	(select count(id) from kargo_instance   where kargo_instance.shard = $1   and kargo_instance.id   IN ('%s'));
`

	argoInstancesStatusQuery = `
select argo_cd_instance.id, 
	   argo_cd_instance.name, 
       argo_cd_instance.generation, 
       argo_cd_instance.status_observed_generation,
       argo_cd_instance_config.version, 
       coalesce((organization.org_status ->> 'trial')::bool, false) as trial, 
       argo_cd_instance.status_conditions, 
       argo_cd_instance.status_health, 
       argo_cd_instance.status_recent_processed_event_info 
from argo_cd_instance 
    inner join argo_cd_instance_config on
        argo_cd_instance.id = argo_cd_instance_config.instance_id 
    inner join organization on 
        argo_cd_instance.organization_owner = organization.id 
where argo_cd_instance.shard = $1;
`

	argoClustersStatusQuery = `
select argo_cd_cluster.id, 
       argo_cd_cluster.name, 
       argo_cd_cluster.instance_id, 
       argo_cd_cluster.generation,
       argo_cd_cluster.status_observed_generation, 
       argo_cd_cluster.status_conditions,  
       argo_cd_cluster.status_agent_state, 
       argo_cd_cluster.spec,
       coalesce((organization.org_status ->> 'trial')::bool, false) as trial
from argo_cd_cluster 
    inner join argo_cd_instance on 
        argo_cd_instance.id = argo_cd_cluster.instance_id 
    inner join organization on 
        argo_cd_instance.organization_owner = organization.id 
where argo_cd_instance.shard = $1;
`

	kargoInstancesStatusQuery = `
select kargo_instance.id, 
	   kargo_instance.name, 
       kargo_instance.generation, 
       kargo_instance.status_observed_generation,
       kargo_instance_config.version,
       coalesce((organization.org_status ->> 'trial')::bool, false) as trial, 
       kargo_instance.status_conditions, 
       kargo_instance.status_health, 
       kargo_instance.status_recent_processed_event_info 
from kargo_instance 
    inner join kargo_instance_config on
        kargo_instance.id = kargo_instance_config.instance_id 
    inner join organization on 
        kargo_instance.organization_owner = organization.id 
where kargo_instance.shard = $1;
`

	kargoAgentsStatusQuery = `
select kargo_agent.id, 
	   kargo_agent.name, 
       kargo_agent.instance_id, 
       kargo_agent.generation,
       kargo_agent.status_observed_generation, 
       kargo_agent.status_conditions,  
       kargo_agent.status_agent_state, 
       kargo_agent.spec,
       coalesce((organization.org_status ->> 'trial')::bool, false) as trial
from kargo_agent
    inner join kargo_instance on 
        kargo_instance.id = kargo_agent.instance_id 
    inner join organization on 
        kargo_instance.organization_owner = organization.id 
where kargo_instance.shard = $1;
`
)

type StatusRefresher struct {
	deadlinesConfig config.DeadlinesConfig
	portalDBClient  *sql.DB
	shard           string
	log             *logr.Logger
	ctx             context.Context
}

func NewStatusRefresher(deadlinesConfig config.DeadlinesConfig, portalDBClient *sql.DB, log *logr.Logger, shard string, ctx context.Context) *StatusRefresher {
	return &StatusRefresher{
		deadlinesConfig: deadlinesConfig,
		portalDBClient:  portalDBClient,
		shard:           shard,
		log:             log,
		ctx:             ctx,
	}
}

// InstancesExist - determines if some of the Instances specified exist.
// Returns false if no Argo or Kargo Instance with IDs specified exist.
// Returns true  if some instances exist or fails to execute the query.
func (r *StatusRefresher) InstancesExist(instanceIDs ...string) bool {
	query := countAllInstancesQuery

	if len(instanceIDs) > 0 {
		concatenatedIDs := strings.Join(instanceIDs, "' , '")
		query = fmt.Sprintf(countAllSelectedInstancesQuery, concatenatedIDs, concatenatedIDs)
	}

	var instancesCounter int
	if err := r.portalDBClient.QueryRowContext(r.ctx, query, r.shard).Scan(&instancesCounter); err != nil {
		r.log.Error(err, "Failed to read query results", "query", query)
		return true
	}

	return instancesCounter > 0
}

func (r *StatusRefresher) RefreshArgoInstances(instanceIDs ...string) (map[InstanceStatus][]string, error) {
	instancesStatuses := map[InstanceStatus][]string{}
	query := argoInstancesStatusQuery

	if len(instanceIDs) > 0 {
		query = fmt.Sprintf("%s AND argo_cd_instance.id IN ('%s');",
			strings.TrimSuffix(strings.TrimSpace(query), ";"),
			strings.Join(instanceIDs, "' , '"))
	}

	rows, err := r.portalDBClient.QueryContext(r.ctx, query, r.shard)
	if err != nil {
		return nil, err
	}

	defer func() { _ = rows.Close() }()

	for {
		// lazy load results instead of loading all at once
		instanceData := AggregateArgoInstanceData{}
		if err := queries.Bind(rows, &instanceData); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				break
			}
			return nil, err
		}

		instanceId := instanceData.Instance.ID
		instanceStatus, err := instanceData.Instance.GetStatus()
		if err != nil {
			return nil, err
		}
		reconciliationStatus, err := instanceData.Instance.GetReconciliationStatus(r.deadlinesConfig.InstanceProgressingDeadline)
		if err != nil {
			return nil, err
		}
		recentEventsInfo, err := instanceData.Instance.GetRecentProcessedEventInfo()
		if err != nil {
			return nil, err
		}

		if instanceStatus.Health.Code == status.HealthStatusCodeDegraded {
			r.log.Info("Argo Instance Health has Degraded",
				"instanceId", instanceId,
				"instanceName", instanceData.Instance.Name,
				"message", instanceStatus.Health.Message)
		}

		if reconciliationStatus.Code == status.ReconciliationStatusCodeFailed {
			r.log.Info("Argo Instance Reconciliation has Failed",
				"instanceId", instanceId,
				"instanceName", instanceData.Instance.Name,
				"message", reconciliationStatus.Message)
		}

		eventsSynced := recentEventsInfo.Error == "" && time.Since(recentEventsInfo.LastTransitionTime) < r.deadlinesConfig.EventsSyncDeadline
		instanceStatusKey := InstanceStatus{
			HealthStatusCode:         instanceStatus.Health.Code,
			ReconciliationStatusCode: reconciliationStatus.Code,
			trial:                    instanceData.Trial,
			eventsSynced:             eventsSynced,
			version:                  instanceData.Version,
		}

		instancesStatuses[instanceStatusKey] = append(instancesStatuses[instanceStatusKey], instanceId)
	}

	return instancesStatuses, nil
}

func (r *StatusRefresher) RefreshArgoClusters(instanceIDs ...string) (map[ClusterStatus][]string, error) {
	clustersStatuses := map[ClusterStatus][]string{}
	query := argoClustersStatusQuery

	if len(instanceIDs) > 0 {
		query = fmt.Sprintf("%s AND argo_cd_instance.id IN ('%s');",
			strings.TrimSuffix(strings.TrimSpace(query), ";"),
			strings.Join(instanceIDs, "' , '"))
	}

	rows, err := r.portalDBClient.QueryContext(r.ctx, query, r.shard)
	if err != nil {
		return nil, err
	}

	defer func() { _ = rows.Close() }()

	for {
		// lazy load results instead of loading all at once
		clusterData := AggregateArgoClusterData{}
		if err := queries.Bind(rows, &clusterData); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				break
			}
			return nil, err
		}

		clusterId := clusterData.Cluster.ID
		clusterSpec, err := clusterData.Cluster.GetSpec()
		if err != nil {
			return nil, err
		}

		if clusterSpec.MaintenanceMode {
			continue
		}

		clusterStatus, err := clusterData.Cluster.GetStatus()
		if err != nil {
			return nil, err
		}
		reconciliation, err := clusterData.Cluster.GetReconciliationStatus(r.deadlinesConfig.ClusterProgressingDeadline, true)
		if err != nil {
			return nil, err
		}

		if reconciliation.Code == status.ReconciliationStatusCodeFailed {
			r.log.Info("Argo Cluster Reconciliation has Failed",
				"clusterId", clusterId,
				"clusterName", clusterData.Cluster.Name,
				"instanceId", clusterData.Cluster.InstanceID,
				"message", reconciliation.Message)
		}

		agentState := clusterStatus.GetAgentState()
		if agentState == nil || agentState.Status == nil {
			clusterStatusKey := ClusterStatus{
				ReconciliationStatusCode: reconciliation.Code,
				trial:                    clusterData.Trial,
				connected:                false,
				healthy:                  false,
				degraded:                 false,
			}
			clustersStatuses[clusterStatusKey] = append(clustersStatuses[clusterStatusKey], clusterId)
			continue
		}

		// True if any agent's resource is Degraded with ControlPlane set to True
		for resourceName, healthStatus := range agentState.Status.Degraded {
			if healthStatus.ControlPlane {
				r.log.Info("Argo Cluster ControlPlane resource has Degraded",
					"clusterId", clusterId,
					"clusterName", clusterData.Cluster.Name,
					"instanceId", clusterData.Cluster.InstanceID,
					"resourceName", resourceName,
					"message", healthStatus.Message)
				if !agentState.Status.ControlPlane {
					r.log.Error(fmt.Errorf("Status.ControlPlane isn't set for Degraded ControlPlane Argo Cluster"), "",
						"clusterId", clusterId,
						"clusterName", clusterData.Cluster.Name,
						"instanceId", clusterData.Cluster.InstanceID,
					)
				}
			}
		}

		// ignore the case where only kubevision is degraded since it might be an issue from client side, e.g., connection issue
		// https://github.com/akuityio/akuity-platform/issues/6359#issuecomment-**********
		isOnlyKubeVisionDegraded := false
		if agentState.Status != nil && len(agentState.Status.Degraded) == 1 && agentState.Status.Degraded["kubevision"] != nil {
			isOnlyKubeVisionDegraded = true
		}

		clusterStatusKey := ClusterStatus{
			ReconciliationStatusCode: reconciliation.Code,
			trial:                    clusterData.Trial,
			connected:                true,
			healthy:                  agentState.Status.PriorityStatus == common.TenantPhaseHealthy || isOnlyKubeVisionDegraded,
			degraded:                 agentState.Status.ControlPlane && agentState.Status.PriorityStatus == common.TenantPhaseDegraded && !isOnlyKubeVisionDegraded,
		}

		clustersStatuses[clusterStatusKey] = append(clustersStatuses[clusterStatusKey], clusterId)
	}

	return clustersStatuses, nil
}

func (r *StatusRefresher) RefreshKargoInstances(instanceIDs ...string) (map[InstanceStatus][]string, error) {
	instancesStatuses := map[InstanceStatus][]string{}
	query := kargoInstancesStatusQuery

	if len(instanceIDs) > 0 {
		query = fmt.Sprintf("%s AND kargo_instance.id IN ('%s');",
			strings.TrimSuffix(strings.TrimSpace(query), ";"),
			strings.Join(instanceIDs, "' , '"))
	}

	rows, err := r.portalDBClient.QueryContext(r.ctx, query, r.shard)
	if err != nil {
		return nil, err
	}

	defer func() { _ = rows.Close() }()

	for {
		// lazy load results instead of loading all at once
		instanceData := AggregateKargoInstanceData{}
		if err := queries.Bind(rows, &instanceData); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				break
			}
			return nil, err
		}

		instanceId := instanceData.Instance.ID
		instanceStatus, err := instanceData.Instance.GetStatus()
		if err != nil {
			return nil, err
		}
		reconciliationStatus, err := instanceData.Instance.GetReconciliationStatus(r.deadlinesConfig.InstanceProgressingDeadline)
		if err != nil {
			return nil, err
		}
		recentEventsInfo, err := instanceData.Instance.GetRecentProcessedEventInfo()
		if err != nil {
			return nil, err
		}

		if instanceStatus.Health.Code == status.HealthStatusCodeDegraded {
			r.log.Info("Kargo Instance Health has Degraded",
				"instanceId", instanceId,
				"instanceName", instanceData.Instance.Name,
				"message", instanceStatus.Health.Message)
		}

		if reconciliationStatus.Code == status.ReconciliationStatusCodeFailed {
			r.log.Info("Kargo Instance Reconciliation has Failed",
				"instanceId", instanceId,
				"instanceName", instanceData.Instance.Name,
				"message", reconciliationStatus.Message)
		}

		eventsSynced := recentEventsInfo.Error == "" && time.Since(recentEventsInfo.LastTransitionTime) < r.deadlinesConfig.EventsSyncDeadline
		instanceStatusKey := InstanceStatus{
			HealthStatusCode:         instanceStatus.Health.Code,
			ReconciliationStatusCode: reconciliationStatus.Code,
			trial:                    instanceData.Trial,
			eventsSynced:             eventsSynced,
			version:                  instanceData.Version,
		}

		instancesStatuses[instanceStatusKey] = append(instancesStatuses[instanceStatusKey], instanceId)
	}

	return instancesStatuses, nil
}

func (r *StatusRefresher) RefreshKargoAgents(instanceIDs ...string) (map[AgentStatus][]string, error) {
	agentsStatuses := map[AgentStatus][]string{}
	query := kargoAgentsStatusQuery

	if len(instanceIDs) > 0 {
		query = fmt.Sprintf("%s AND kargo_instance.id IN ('%s');",
			strings.TrimSuffix(strings.TrimSpace(query), ";"),
			strings.Join(instanceIDs, "' , '"))
	}

	rows, err := r.portalDBClient.QueryContext(r.ctx, query, r.shard)
	if err != nil {
		return nil, err
	}

	defer func() { _ = rows.Close() }()

	for {
		// lazy load results instead of loading all at once
		agentData := AggregateKargoAgentData{}
		if err := queries.Bind(rows, &agentData); err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				break
			}
			return nil, err
		}

		agentId := agentData.Agent.ID

		agentStatus, err := agentData.Agent.GetStatus()
		if err != nil {
			return nil, err
		}
		reconciliation, err := agentData.Agent.GetReconciliationStatus(r.deadlinesConfig.AgentProgressingDeadline, true)
		if err != nil {
			return nil, err
		}

		if reconciliation.Code == status.ReconciliationStatusCodeFailed {
			r.log.Info("Kargo Agent Reconciliation has Failed",
				"agentId", agentId,
				"agentName", agentData.Agent.Name,
				"instanceId", agentData.Agent.InstanceID,
				"message", reconciliation.Message)
		}

		agentState := agentStatus.GetKargoAgentState()
		if agentState == nil || agentState.Status == nil {
			agentStatusKey := AgentStatus{
				ReconciliationStatusCode: reconciliation.Code,
				trial:                    agentData.Trial,
				connected:                false,
				healthy:                  false,
				degraded:                 false,
			}
			agentsStatuses[agentStatusKey] = append(agentsStatuses[agentStatusKey], agentId)
			continue
		}

		// True if any agent's resource is Degraded with ControlPlane set to True
		for resourceName, healthStatus := range agentState.Status.Degraded {
			if healthStatus.ControlPlane {
				r.log.Info("Kargo Agent ControlPlane resource has Degraded",
					"agentId", agentId,
					"agentName", agentData.Agent.Name,
					"instanceId", agentData.Agent.InstanceID,
					"resourceName", resourceName,
					"message", healthStatus.Message)
				if !agentState.Status.ControlPlane {
					r.log.Error(fmt.Errorf("Status.ControlPlane isn't set for Degraded ControlPlane Kargo Agent"), "",
						"agentId", agentId,
						"agentName", agentData.Agent.Name,
						"instanceId", agentData.Agent.InstanceID,
					)
				}
			}
		}

		agentStatusKey := AgentStatus{
			ReconciliationStatusCode: reconciliation.Code,
			trial:                    agentData.Trial,
			connected:                true,
			healthy:                  agentState.Status.PriorityStatus == common.TenantPhaseHealthy,
			degraded:                 agentState.Status.ControlPlane && agentState.Status.PriorityStatus == common.TenantPhaseDegraded,
		}

		agentsStatuses[agentStatusKey] = append(agentsStatuses[agentStatusKey], agentId)
	}

	return agentsStatuses, nil
}
