package integration

import (
	"context"
	"database/sql"

	"github.com/go-logr/logr"
	"k8s.io/client-go/rest"

	"github.com/akuityio/akuity-platform/controllers/shared/tenant"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/ai"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

type timelineEventsReconciler struct {
	repoSet           client.RepoSet
	tenantStateClient tenant.StateClient
	db                *sql.DB
	cfg               config.AIConfig
	hostRestConfig    *rest.Config
	logger            logr.Logger
	featureSvc        features.Service
}

func NewTimelineEventsReconciler(featureSvc features.Service, repoSet client.RepoSet, tenantStateClient tenant.StateClient, db *sql.DB, cfg config.AIConfig, hostRestConfig *rest.Config, logger logr.Logger) *timelineEventsReconciler {
	return &timelineEventsReconciler{
		repoSet:           repoSet,
		tenantStateClient: tenantStateClient,
		db:                db,
		cfg:               cfg,
		hostRestConfig:    hostRestConfig,
		logger:            logger,
		featureSvc:        featureSvc,
	}
}

func (r *timelineEventsReconciler) ItemToID(item *models.ArgoCDInstance) string {
	return item.ID
}

func (r *timelineEventsReconciler) IDColumn() string {
	return models.ArgoCDInstanceTableColumns.ID
}

func (r *timelineEventsReconciler) LogValuesFromID(id string) []interface{} {
	return []interface{}{"instance_id", id}
}

func (r *timelineEventsReconciler) LogValuesFromItem(item *models.ArgoCDInstance) []interface{} {
	return []interface{}{"instance_id", item.ID}
}

func (r *timelineEventsReconciler) Reconcile(ctx context.Context, instance *models.ArgoCDInstance) error {
	featureStatuses := r.featureSvc.GetFeatureStatuses(ctx, &instance.OrganizationOwner)
	svc, err := ai.NewService(r.db, r.repoSet, featureStatuses, instance.OrganizationOwner, r.cfg, r.hostRestConfig, r.logger)
	if err != nil {
		return err
	}

	return svc.AutoCreateIncidents(ctx, instance, r.tenantStateClient)
}
