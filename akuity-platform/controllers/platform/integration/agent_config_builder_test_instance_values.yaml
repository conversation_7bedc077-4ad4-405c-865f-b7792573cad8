kustomization:
  apiVersion: kustomize.config.k8s.io/v1beta1
  kind: Kustomization
  patches:
  - patch: "apiVersion: apps/v1\nkind: Deployment        \nmetadata:\n  name: argocd-server\nspec:\n  template:\n    spec:\n      nodeSelector:\n        kubernetes.io/arch: amd64\n      affinity:\n        podAntiAffinity:\n          preferredDuringSchedulingIgnoredDuringExecution:\n            - podAffinityTerm:\n                labelSelector:\n                  matchLabels:\n                    app.kubernetes.io/name: argocd-server\n                    akuity.io/argo-cd-instance-id: REPLACE_ME\n                topologyKey: karpenter.sh/capacity-type\n              weight: 100"
    target:
      group: apps
      kind: Deployment
      name: argocd-server
  - patch: "apiVersion: apps/v1\nkind: Deployment        \nmetadata:\n  name: agent-server\nspec:\n  template:\n    spec:\n      affinity:\n        podAntiAffinity:\n          preferredDuringSchedulingIgnoredDuringExecution:\n            - podAffinityTerm:\n                labelSelector:\n                  matchLabels:\n                    app.kubernetes.io/name: agent-server\n                    akuity.io/argo-cd-instance-id: REPLACE_ME\n                topologyKey: karpenter.sh/capacity-type\n              weight: 100"
    target:
      group: apps
      kind: Deployment
      name: agent-server
  - patch: "apiVersion: apps/v1\nkind: Deployment        \nmetadata:\n  name: argocd-application-controller\nspec:\n  template:\n    spec:\n      affinity:\n        podAntiAffinity:\n          preferredDuringSchedulingIgnoredDuringExecution:\n            - podAffinityTerm:\n                labelSelector:\n                  matchLabels:\n                    app.kubernetes.io/name: argocd-application-controller\n                    akuity.io/argo-cd-instance-id: REPLACE_ME\n                topologyKey: karpenter.sh/capacity-type\n              weight: 100"
    target:
      group: apps
      kind: Deployment
      name: argocd-application-controller
  - patch: "apiVersion: apps/v1\nkind: Deployment        \nmetadata:\n  name: argocd-applicationset-controller\nspec:\n  template:\n    spec:\n      affinity:\n        podAntiAffinity:\n          preferredDuringSchedulingIgnoredDuringExecution:\n            - podAffinityTerm:\n                labelSelector:\n                  matchLabels:\n                    app.kubernetes.io/name: argocd-applicationset-controller\n                    akuity.io/argo-cd-instance-id: REPLACE_ME\n                topologyKey: karpenter.sh/capacity-type\n              weight: 100"
    target:
      group: apps
      kind: Deployment
      name: argocd-applicationset-controller
  - patch: "apiVersion: apps/v1\nkind: Deployment        \nmetadata:\n  name: argocd-dex-server\nspec:\n  template:\n    spec:\n      affinity:\n        podAntiAffinity:\n          preferredDuringSchedulingIgnoredDuringExecution:\n            - podAffinityTerm:\n                labelSelector:\n                  matchLabels:\n                    app.kubernetes.io/name: argocd-dex-server\n                    akuity.io/argo-cd-instance-id: REPLACE_ME\n                topologyKey: karpenter.sh/capacity-type\n              weight: 100"
    target:
      group: apps
      kind: Deployment
      name: argocd-dex-server
  - patch: "apiVersion: apps/v1\nkind: Deployment        \nmetadata:\n  name: argocd-image-updater\nspec:\n  template:\n    spec:\n      affinity:\n        podAntiAffinity:\n          preferredDuringSchedulingIgnoredDuringExecution:\n            - podAffinityTerm:\n                labelSelector:\n                  matchLabels:\n                    app.kubernetes.io/name: argocd-image-updater\n                    akuity.io/argo-cd-instance-id: REPLACE_ME\n                topologyKey: karpenter.sh/capacity-type\n              weight: 100"
    target:
      group: apps
      kind: Deployment
      name: argocd-image-updater
  - patch: "apiVersion: apps/v1\nkind: Deployment        \nmetadata:\n  name: argocd-redis-ha-haproxy\nspec:\n  template:\n    spec:\n      affinity:\n        podAntiAffinity:\n          preferredDuringSchedulingIgnoredDuringExecution:\n            - podAffinityTerm:\n                labelSelector:\n                  matchLabels:\n                    app.kubernetes.io/name: argocd-redis-ha-haproxy\n                    akuity.io/argo-cd-instance-id: REPLACE_ME\n                topologyKey: karpenter.sh/capacity-type\n              weight: 100"
    target:
      group: apps
      kind: Deployment
      name: argocd-redis-ha-haproxy
  - patch: "apiVersion: apps/v1\nkind: Deployment        \nmetadata:\n  name: argocd-repo-server\nspec:\n  template:\n    spec:\n      affinity:\n        podAntiAffinity:\n          preferredDuringSchedulingIgnoredDuringExecution:\n            - podAffinityTerm:\n                labelSelector:\n                  matchLabels:\n                    app.kubernetes.io/name: argocd-repo-server\n                    akuity.io/argo-cd-instance-id: REPLACE_ME\n                topologyKey: karpenter.sh/capacity-type\n              weight: 100"
    target:
      group: apps
      kind: Deployment
      name: argocd-repo-server
  - patch: "apiVersion: apps/v1\nkind: Deployment        \nmetadata:\n  name: k3s\nspec:\n  template:\n    spec:\n      affinity:\n        podAntiAffinity:\n          preferredDuringSchedulingIgnoredDuringExecution:\n            - podAffinityTerm:\n                labelSelector:\n                  matchLabels:\n                    app.kubernetes.io/name: k3s\n                    akuity.io/argo-cd-instance-id: REPLACE_ME\n                topologyKey: karpenter.sh/capacity-type\n              weight: 100"
    target:
      group: apps
      kind: Deployment
      name: k3s
  - patch: "apiVersion: apps/v1\nkind: Deployment        \nmetadata:\n  name: k3s-proxy\nspec:\n  template:\n    spec:\n      affinity:\n        podAntiAffinity:\n          preferredDuringSchedulingIgnoredDuringExecution:\n            - podAffinityTerm:\n                labelSelector:\n                  matchLabels:\n                    app.kubernetes.io/name: k3s-proxy\n                    akuity.io/argo-cd-instance-id: REPLACE_ME\n                topologyKey: karpenter.sh/capacity-type\n              weight: 100"
    target:
      group: apps
      kind: Deployment
      name: k3s-proxy
  - patch: "apiVersion: apps/v1\nkind: Deployment        \nmetadata:\n  name: k3s-webhook\nspec:\n  template:\n    spec:\n      affinity:\n        podAntiAffinity:\n          preferredDuringSchedulingIgnoredDuringExecution:\n            - podAffinityTerm:\n                labelSelector:\n                  matchLabels:\n                    app.kubernetes.io/name: k3s-webhook\n                    akuity.io/argo-cd-instance-id: REPLACE_ME\n                topologyKey: karpenter.sh/capacity-type\n              weight: 100"
    target:
      group: apps
      kind: Deployment
      name: k3s-webhook
  - patch: "apiVersion: apps/v1\nkind: Deployment        \nmetadata:\n  name: pgpool\nspec:\n  template:\n    spec:\n      affinity:\n        podAntiAffinity:\n          preferredDuringSchedulingIgnoredDuringExecution:\n            - podAffinityTerm:\n                labelSelector:\n                  matchLabels:\n                    app.kubernetes.io/name: pgpool\n                    akuity.io/argo-cd-instance-id: REPLACE_ME\n                topologyKey: karpenter.sh/capacity-type\n              weight: 100"
    target:
      group: apps
      kind: Deployment
      name: pgpool
  - patch: "apiVersion: apps/v1\nkind: StatefulSet        \nmetadata:\n  name: argocd-redis-ha-server\nspec:\n  template:\n    spec:\n      affinity:\n        podAntiAffinity:\n          preferredDuringSchedulingIgnoredDuringExecution:\n            - podAffinityTerm:\n                labelSelector:\n                  matchLabels:\n                    app.kubernetes.io/name: argocd-redis-ha\n                    akuity.io/argo-cd-instance-id: REPLACE_ME\n                topologyKey: karpenter.sh/capacity-type\n              weight: 100"
    target:
      group: apps
      kind: StatefulSet
      name: argocd-redis-ha-server
  replacements:
  - source:
      fieldPath: metadata.labels.akuity\.io/argo-cd-instance-id
      kind: Deployment
      name: argocd-server
    targets:
    - fieldPaths:
      - spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution.0.podAffinityTerm.labelSelector.matchLabels.akuity\.io/argo-cd-instance-id
      select:
        kind: Deployment
        name: argocd-server
  - source:
      fieldPath: metadata.labels.akuity\.io/argo-cd-instance-id
      kind: Deployment
      name: agent-server
    targets:
    - fieldPaths:
      - spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution.0.podAffinityTerm.labelSelector.matchLabels.akuity\.io/argo-cd-instance-id
      select:
        kind: Deployment
        name: agent-server
  - source:
      fieldPath: metadata.labels.akuity\.io/argo-cd-instance-id
      kind: Deployment
      name: argocd-application-controller
    targets:
    - fieldPaths:
      - spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution.0.podAffinityTerm.labelSelector.matchLabels.akuity\.io/argo-cd-instance-id
      select:
        kind: Deployment
        name: argocd-application-controller
  - source:
      fieldPath: metadata.labels.akuity\.io/argo-cd-instance-id
      kind: Deployment
      name: argocd-applicationset-controller
    targets:
    - fieldPaths:
      - spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution.0.podAffinityTerm.labelSelector.matchLabels.akuity\.io/argo-cd-instance-id
      select:
        kind: Deployment
        name: argocd-applicationset-controller
  - source:
      fieldPath: metadata.labels.akuity\.io/argo-cd-instance-id
      kind: Deployment
      name: argocd-dex-server
    targets:
    - fieldPaths:
      - spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution.0.podAffinityTerm.labelSelector.matchLabels.akuity\.io/argo-cd-instance-id
      select:
        kind: Deployment
        name: argocd-dex-server
  - source:
      fieldPath: metadata.labels.akuity\.io/argo-cd-instance-id
      kind: Deployment
      name: argocd-image-updater
    targets:
    - fieldPaths:
      - spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution.0.podAffinityTerm.labelSelector.matchLabels.akuity\.io/argo-cd-instance-id
      select:
        kind: Deployment
        name: argocd-image-updater
  - source:
      fieldPath: metadata.labels.akuity\.io/argo-cd-instance-id
      kind: Deployment
      name: argocd-redis-ha-haproxy
    targets:
    - fieldPaths:
      - spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution.0.podAffinityTerm.labelSelector.matchLabels.akuity\.io/argo-cd-instance-id
      select:
        kind: Deployment
        name: argocd-redis-ha-haproxy
  - source:
      fieldPath: metadata.labels.akuity\.io/argo-cd-instance-id
      kind: Deployment
      name: argocd-repo-server
    targets:
    - fieldPaths:
      - spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution.0.podAffinityTerm.labelSelector.matchLabels.akuity\.io/argo-cd-instance-id
      select:
        kind: Deployment
        name: argocd-repo-server
  - source:
      fieldPath: metadata.labels.akuity\.io/argo-cd-instance-id
      kind: Deployment
      name: k3s
    targets:
    - fieldPaths:
      - spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution.0.podAffinityTerm.labelSelector.matchLabels.akuity\.io/argo-cd-instance-id
      select:
        kind: Deployment
        name: k3s
  - source:
      fieldPath: metadata.labels.akuity\.io/argo-cd-instance-id
      kind: Deployment
      name: k3s-proxy
    targets:
    - fieldPaths:
      - spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution.0.podAffinityTerm.labelSelector.matchLabels.akuity\.io/argo-cd-instance-id
      select:
        kind: Deployment
        name: k3s-proxy
  - source:
      fieldPath: metadata.labels.akuity\.io/argo-cd-instance-id
      kind: Deployment
      name: k3s-webhook
    targets:
    - fieldPaths:
      - spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution.0.podAffinityTerm.labelSelector.matchLabels.akuity\.io/argo-cd-instance-id
      select:
        kind: Deployment
        name: k3s-webhook
  - source:
      fieldPath: metadata.labels.akuity\.io/argo-cd-instance-id
      kind: Deployment
      name: pgpool
    targets:
    - fieldPaths:
      - spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution.0.podAffinityTerm.labelSelector.matchLabels.akuity\.io/argo-cd-instance-id
      select:
        kind: Deployment
        name: pgpool
  - source:
      fieldPath: metadata.labels.akuity\.io/argo-cd-instance-id
      kind: StatefulSet
      name: argocd-redis-ha-server
    targets:
    - fieldPaths:
      - spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution.0.podAffinityTerm.labelSelector.matchLabels.akuity\.io/argo-cd-instance-id
      select:
        kind: StatefulSet
        name: argocd-redis-ha-server
