package autoscaler

import (
	"github.com/go-logr/logr"
	"k8s.io/client-go/kubernetes"

	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

type argocdInstanceData struct {
	id                      string
	connectedClusters       uint32
	tenantApplications      uint32
	applicationK8sResources uint32
	internalSpec            *models.InstanceInternalSpec
}

type ArgocdInstanceAutoscaler struct {
	log        *logr.Logger
	repoSet    client.RepoSet
	ScalingMap *dynamicScalingMap
	k8sClient  kubernetes.Interface
}

type AutoscalerResources struct {
	Cpu      float64
	Memory   uint64
	Replicas uint32
}

type autoscalerThreshold struct {
	thresholdValue uint32 // Resource threshold value
	scaleDownValue uint32 // Resource scale down threshold value (thresholdValue - N%)
	types          autoscalerTypes
}

type autoscalerTypes struct {
	k3sType                   string
	k3sProxyType              string
	applicationControllerType string
	redisType                 string
}

type dynamicScalingMap struct {
	// "k3s.small":  {cpu: .. , memory: .. , replicas: ..}
	// "k3s.medium": {cpu: .. , memory: .. , replicas: ..}
	// ..
	K3sTypes map[string]AutoscalerResources

	// "k3s-proxy.small":  {cpu: .. , memory: .. , replicas: ..}
	// "k3s-proxy.medium": {cpu: .. , memory: .. , replicas: ..}
	// ..
	K3sProxyTypes map[string]AutoscalerResources

	// "application-controller.small":  {cpu: .. , memory: .. }
	// "application-controller.medium": {cpu: .. , memory: .. }
	// ..
	ApplicationControllerTypes map[string]AutoscalerResources

	// "redis.small":  {cpu: .. , memory: .. }
	// "redis.medium": {cpu: .. , memory: .. }
	// ..
	RedisTypes map[string]AutoscalerResources

	// [{"thresholdValue": 10, "k3sType": "k3s.small"},
	//  {"thresholdValue": 20, "k3sType": "k3s.medium"},
	//  .. ]
	clustersThresholds []autoscalerThreshold

	// [{"thresholdValue": 150, "k3sType": "k3s.small", "redisType": "redis.small"},
	//  {"thresholdValue": 500, "k3sType": "k3s.medium", "redisType": "redis.medium"},
	//  .. ]
	appsThresholds []autoscalerThreshold

	// [{"thresholdValue": 1000, "k3sType": "k3s.small"},
	//  {"thresholdValue": 3000, "k3sType": "k3s.medium"},
	//  .. ]
	resourcesThresholds []autoscalerThreshold

	// "instanceId1": {"k3sType": "..", "redisType": ".."}
	// "instanceId2": {"k3sType": "..", "redisType": ".."}
	// ..
	instanceOverrides map[string]autoscalerTypes

	// Org IDs opted-in to the Autoscaler
	orgsEnabled map[string]bool

	// Org IDs opted-out from the Autoscaler
	orgsDisabled map[string]bool
}
