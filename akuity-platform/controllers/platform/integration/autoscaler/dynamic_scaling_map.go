package autoscaler

import (
	"sort"
)

const (
	// When scaling down - waiting for resources to fall below the threshold by 20%
	scaleDownThreshold = 0.2
)

// selectK3sType - selects the K3s type based on the number of tenant's connected
// Clusters, Argo CD Applications, and Application's K8s resources
func (m *dynamicScalingMap) selectK3sType(instanceId string, connectedClusters, tenantApplications, applicationK8sResources uint32, currentK3sType string) string {
	if instanceTypes, ok := m.instanceOverrides[instanceId]; ok && instanceTypes.k3sType != "" {
		// Instance override for K3s type is available
		return instanceTypes.k3sType
	}

	isScalingDown := func(t autoscalerThreshold) bool {
		// Scaling down if matching threshold's K3s type is "less than" the current K3s type
		return m.compareK3sTypes(t.types.k3sType, currentK3sType) < 0
	}

	k3sTypeByClusters := m.selectThreshold(connectedClusters, m.clustersThresholds, isScalingDown).types.k3sType
	k3sTypeByApplications := m.selectThreshold(tenantApplications, m.appsThresholds, isScalingDown).types.k3sType
	k3sTypeByResources := m.selectThreshold(applicationK8sResources, m.resourcesThresholds, isScalingDown).types.k3sType

	// All 3 selected K3s types are identical
	if m.compareK3sTypes(k3sTypeByClusters, k3sTypeByApplications) == 0 &&
		m.compareK3sTypes(k3sTypeByApplications, k3sTypeByResources) == 0 {
		return k3sTypeByClusters
	}

	// K3s types selected are mixed ("k3s.small", "k3s.medium") - picking the largest one
	types := []string{k3sTypeByClusters, k3sTypeByApplications, k3sTypeByResources}
	m.sortTypes(types, func(leftType, rightType string) int {
		return m.compareK3sTypes(leftType, rightType)
	})
	return last(types)
}

// selectK3sProxyType - selects the K3s Proxy type based on the number of tenant's connected
// Clusters, Argo CD Applications, and Application's K8s resources
func (m *dynamicScalingMap) selectK3sProxyType(instanceId string, connectedClusters, tenantApplications, applicationK8sResources uint32, currentK3sProxyType string) string {
	if instanceTypes, ok := m.instanceOverrides[instanceId]; ok && instanceTypes.k3sProxyType != "" {
		// Instance override for K3s type is available
		return instanceTypes.k3sProxyType
	}

	isScalingDown := func(t autoscalerThreshold) bool {
		// Scaling down if matching threshold's K3s Proxy type is "less than" the current K3s proxy type
		return m.compareK3sProxyTypes(t.types.k3sProxyType, currentK3sProxyType) < 0
	}

	k3sProxyTypeByClusters := m.selectThreshold(connectedClusters, m.clustersThresholds, isScalingDown).types.k3sProxyType
	k3sProxyTypeByApplications := m.selectThreshold(tenantApplications, m.appsThresholds, isScalingDown).types.k3sProxyType
	k3sProxyTypeByResources := m.selectThreshold(applicationK8sResources, m.resourcesThresholds, isScalingDown).types.k3sProxyType

	// All 3 selected K3s types are identical
	if m.compareK3sProxyTypes(k3sProxyTypeByClusters, k3sProxyTypeByApplications) == 0 &&
		m.compareK3sProxyTypes(k3sProxyTypeByApplications, k3sProxyTypeByResources) == 0 {
		return k3sProxyTypeByClusters
	}

	// K3s Proxy types selected are mixed ("k3s-proxy.small", "k3s-proxy.medium") - picking the largest one
	types := []string{k3sProxyTypeByClusters, k3sProxyTypeByApplications, k3sProxyTypeByResources}
	m.sortTypes(types, func(leftType, rightType string) int {
		return m.compareK3sProxyTypes(leftType, rightType)
	})
	return last(types)
}

// selectApplicationControllerType - selects the Application Controller type based on the number of tenant's Argo CD Applications
func (m *dynamicScalingMap) selectApplicationControllerType(instanceId string, tenantApplications uint32, currentApplicationControllerType string) string {
	if instanceTypes, ok := m.instanceOverrides[instanceId]; ok && instanceTypes.applicationControllerType != "" {
		// Instance override for Application Controller type available
		return instanceTypes.applicationControllerType
	}

	isScalingDown := func(t autoscalerThreshold) bool {
		// Scaling down if matching threshold's Application Controller type is "less than" the current Application Controller type
		return m.compareApplicationControllerTypes(t.types.applicationControllerType, currentApplicationControllerType) < 0
	}

	return m.selectThreshold(tenantApplications, m.appsThresholds, isScalingDown).types.applicationControllerType
}

// selectRedisType - selects the Redis type based on the number of tenant's Argo CD Applications
func (m *dynamicScalingMap) selectRedisType(instanceId string, tenantApplications uint32, currentRedisType string) string {
	if instanceTypes, ok := m.instanceOverrides[instanceId]; ok && instanceTypes.redisType != "" {
		// Instance override for Redis type available
		return instanceTypes.redisType
	}

	isScalingDown := func(t autoscalerThreshold) bool {
		// Scaling down if matching threshold's Redis type is "less than" the current Redis type
		return m.compareRedisTypes(t.types.redisType, currentRedisType) < 0
	}

	return m.selectThreshold(tenantApplications, m.appsThresholds, isScalingDown).types.redisType
}

// selectThreshold - selects the threshold matching the value and the thresholds provided.
// The value of the threshold returned is either a) greater than or equal to the value provided or
// b) it's the largest threshold available, if none of the thresholds provided is greater than or equal
// to the value provided.
func (m *dynamicScalingMap) selectThreshold(currentValue uint32, thresholds []autoscalerThreshold, isScalingDown func(autoscalerThreshold) bool) autoscalerThreshold {
	var candidateThreshold autoscalerThreshold

	// All thresholds are sorted by their values, in the increasing order
	for _, threshold := range thresholds {
		candidateThreshold = threshold
		if currentValue <= threshold.thresholdValue {
			// Scaling down *only if* the resource current value has fallen well below the threshold
			// (less than or equal to scaleDownValue which is set to 80% of the threshold's original value)
			if isScalingDown(candidateThreshold) && (currentValue > threshold.scaleDownValue) {
				// The resource current value is below the threshold but it is not less than or equal to scaleDownValue
				// (i.e it has not fallen well below the threshold) - continue to the next threshold
				continue
			}

			return candidateThreshold
		}
	}

	// When the current value is higher than any of existing thresholds - selecting the last (and largest) threshold
	return candidateThreshold
}

// sortTypes - sorts the type provided using the comparator function specified
func (m *dynamicScalingMap) sortTypes(types []string, comparator func(typeLeft, typeRight string) int) {
	sort.Slice(types, func(i, j int) bool { return comparator(types[i], types[j]) < 0 })
}

func (m *dynamicScalingMap) compareK3sTypes(leftType, rightType string) int {
	return m.compareTypes(leftType, rightType, m.K3sTypes)
}

func (m *dynamicScalingMap) compareK3sProxyTypes(leftType, rightType string) int {
	return m.compareTypes(leftType, rightType, m.K3sProxyTypes)
}

func (m *dynamicScalingMap) compareApplicationControllerTypes(leftType, rightType string) int {
	return m.compareTypes(leftType, rightType, m.ApplicationControllerTypes)
}

func (m *dynamicScalingMap) compareRedisTypes(leftType, rightType string) int {
	return m.compareTypes(leftType, rightType, m.RedisTypes)
}

// compareTypesNoReplicas - compares two types.
// Returns a negative integer, zero, or a positive integer as the first argument (left type) is
// less than, equal to, or greater than the second (right type).
func (m *dynamicScalingMap) compareTypes(leftType, rightType string, allTypes map[string]AutoscalerResources) int {
	// Identically named types
	if leftType == rightType {
		return 0
	}

	leftTypeResources, leftTypeExists := allTypes[leftType]
	rightTypeResources, rightTypeExists := allTypes[rightType]

	if !leftTypeExists {
		// Both types are unknown
		if !rightTypeExists {
			return 0
		}
		// Unknown type on the left is "less than" the known type on the right
		return -1
	}

	if !rightTypeExists {
		// Unknown type on the right is "less than" the known type on the left
		return 1
	}

	// Types are different but their cpu and memory resources are equal
	if leftTypeResources.Cpu == rightTypeResources.Cpu &&
		leftTypeResources.Memory == rightTypeResources.Memory {
		return int(leftTypeResources.Replicas) - int(rightTypeResources.Replicas)
	}

	// Type on the left is "less than" the type on the right
	if leftTypeResources.Cpu <= rightTypeResources.Cpu &&
		leftTypeResources.Memory <= rightTypeResources.Memory {
		return -1
	}

	// Type on the left is "greater than" the type on the right
	return 1
}
