package autoscaler

import (
	"fmt"
	"sort"
	"strconv"
	"strings"
)

// newDynamicScalingMap - processes a K8s ConfigMap data into a dynamicScalingMap
// (see ./charts/akuity-platform/templates/platform-controller/autoscaler-cm.yaml)
// ----------------------------------------------------------------------------------------------------
// data:
//  k3s.small:  "cpu: 0.20, memory: 750Mi, replicas: 2"
//  k3s.medium: "cpu: 0.50, memory: 1.5Gi, replicas: 2"
//  k3s.xlarge: "cpu: 3.00, memory: 3Gi, replicas: 2"
//
//  k3s-proxy.small:  "cpu: 0.01, memory: 50Mi, replicas: 2"
//  k3s-proxy.medium: "cpu: 0.05, memory: 250Mi, replicas: 2"
//  k3s-proxy.xlarge: "cpu: 0.50, memory: 1.5Gi, replicas: 2"
//
//  application-controller.small:  "cpu: 0.01, memory: 50Mi"
//  application-controller.medium: "cpu: 0.05, memory: 250Mi"
//  application-controller.large:  "cpu: 0.2, memory: 500Mi"
//  application-controller.xlarge: "cpu: 3.0, memory: 1.5Gi"
//
//  redis.small:  "cpu: 0.05, memory: 500Mi"
//  redis.medium: "cpu: 0.10, memory: 2Gi"
//  redis.large:  "cpu: 0.20, memory: 3Gi"
//  redis.xlarge: "cpu: 0.50, memory: 4Gi"
//
//  threshold.clusters.10:  k3s.small, k3s-proxy.small
//  threshold.clusters.100: k3s.medium, k3s-proxy.medium
//  threshold.clusters.300: k3s.xlarge, k3s-proxy.xlarge
//
//  threshold.apps.10:   k3s.small, k3s-proxy.small, redis.small, application-controller.small
//  threshold.apps.100:  k3s.medium, k3s-proxy.medium, redis.medium, application-controller.medium
//  threshold.apps.500:  k3s.medium, k3s-proxy.medium, redis.large, application-controller.large
//  threshold.apps.1000: k3s.xlarge, k3s-proxy.xlarge, redis.xlarge, application-controller.xlarge
//
//  threshold.resources.1000:  k3s.small, k3s-proxy.small
//  threshold.resources.10000: k3s.medium, k3s-proxy.medium
//  threshold.resources.40000: k3s.xlarge, k3s-proxy.xlarge
//
//  instance.id: k3s.small, application-controller.small
//
//  orgs.enabled:  OrgId, OrgId, OrgId, ..
//  orgs.disabled: OrgId, OrgId, OrgId, ..

func newDynamicScalingMap(configMapData map[string]string) (*dynamicScalingMap, error) {
	if len(configMapData) < 1 {
		return nil, fmt.Errorf("scaling ConfigMap is empty")
	}

	scalingMap := dynamicScalingMap{
		K3sTypes:                   map[string]AutoscalerResources{},
		K3sProxyTypes:              map[string]AutoscalerResources{},
		ApplicationControllerTypes: map[string]AutoscalerResources{},
		RedisTypes:                 map[string]AutoscalerResources{},
		clustersThresholds:         []autoscalerThreshold{},
		appsThresholds:             []autoscalerThreshold{},
		resourcesThresholds:        []autoscalerThreshold{},
		instanceOverrides:          map[string]autoscalerTypes{},
		orgsEnabled:                map[string]bool{},
		orgsDisabled:               map[string]bool{},
	}

	// Making a copy as we're going to delete the entries processed from the ConfigMap
	configMapDataCopy := make(map[string]string)
	for key, value := range configMapData {
		configMapDataCopy[key] = value
	}

	for _, mapReaderFunc := range []func(configMap *map[string]string, scalingMap *dynamicScalingMap) error{
		processTypes,
		processThresholds,
		processInstances,
		processOrganizations,
	} {
		if err := mapReaderFunc(&configMapDataCopy, &scalingMap); err != nil {
			return nil, err
		}
	}

	// When we're done processing the ConfigMap - it should be empty as every processed entry is removed
	if len(configMapDataCopy) > 0 {
		return nil, fmt.Errorf("unprocessed entries left in a ConfigMap: %v", configMapDataCopy)
	}

	if len(scalingMap.K3sTypes) < 1 {
		return nil, fmt.Errorf("missing K3s types")
	}
	if len(scalingMap.K3sProxyTypes) < 1 {
		return nil, fmt.Errorf("missing K3s Proxy types")
	}

	if len(scalingMap.ApplicationControllerTypes) < 1 {
		return nil, fmt.Errorf("missing Application Controller types")
	}

	if len(scalingMap.RedisTypes) < 1 {
		return nil, fmt.Errorf("missing Redis types")
	}

	if len(scalingMap.clustersThresholds) < 1 {
		return nil, fmt.Errorf("missing Clusters thresholds")
	}

	if len(scalingMap.appsThresholds) < 1 {
		return nil, fmt.Errorf("missing Applications thresholds")
	}

	if len(scalingMap.resourcesThresholds) < 1 {
		return nil, fmt.Errorf("missing Resources thresholds")
	}

	return &scalingMap, nil
}

// processTypes - processes resources types entries in the ConfigMap specified and populates the scalingMap accordingly
func processTypes(configMap *map[string]string, scalingMap *dynamicScalingMap) error {
	if configMap == nil || scalingMap == nil {
		return fmt.Errorf("nil configMap or scalingMap")
	}

	// Type prefix => Collection of all types to populate
	typesMapping := map[string]map[string]AutoscalerResources{
		"k3s.":                    scalingMap.K3sTypes,
		"k3s-proxy.":              scalingMap.K3sProxyTypes,
		"application-controller.": scalingMap.ApplicationControllerTypes,
		"redis.":                  scalingMap.RedisTypes,
	}

	// "k3s.small"                    => "cpu: 0.1,  memory: 750Mi, replicas: 2"
	// "k3s-proxy.small"              => "cpu: 0.01, memory: 50Mi, replicas: 2"
	// "application-controller.small" => "cpu: 0.01, memory: 50Mi"
	// "redis.small"                  => "cpu: 0.2,  memory: 500Mi"

configMapKeys:
	for key, value := range *configMap {
		for typePrefix, typesMap := range typesMapping {
			if strings.HasPrefix(key, typePrefix) {
				if _, ok := typesMap[key]; ok {
					return fmt.Errorf("duplicate autoscaling type %q", key)
				}

				// K3s types (K3s, K3s Proxy) require replicas
				replicasRequired := strings.HasPrefix(key, "k3s")
				resources, err := processTypeResources(key, value, replicasRequired)
				if err != nil {
					return err
				}

				typesMap[key] = *resources
				delete(*configMap, key)
				continue configMapKeys
			}
		}
	}

	return nil
}

// processTypeResources - processes a scaling map type entry:
// k3s.xlarge: "cpu: 3, memory: 3Gi, replicas: 2"
// k3s-proxy.xlarge: "cpu: 3, memory: 3Gi, replicas: 2"
// application-controller.medium: "cpu: 0.05, memory: 250Mi"
// redis.small: "cpu: 0.2, memory: 500Mi"
func processTypeResources(typeName, typeResources string, replicasRequired bool) (*AutoscalerResources, error) {
	// typeName      - "k3s.small"
	// typeResources - "cpu: 0.1, memory: 750Mi, replicas: 2"

	if len(strings.TrimSpace(typeName)) < 1 {
		return nil, fmt.Errorf("empty typeName for typeResources %q", typeResources)
	}

	if len(strings.TrimSpace(typeResources)) < 1 {
		return nil, fmt.Errorf("empty resources for type %q", typeName)
	}

	var resources AutoscalerResources
	errorPrefix := fmt.Sprintf("type %q resources %q", typeName, typeResources)

	// "cpu: 0.1, memory: 750Mi, replicas: 2" => ["cpu: 0.1", "memory: 750Mi", "replicas: 2"]
	for _, resourceString := range split(typeResources, ",") {
		// "cpu: 0.1" => ["cpu", "0.1"]
		resourceSplit := split(resourceString, ":")
		if len(resourceSplit) != 2 {
			return nil, fmt.Errorf("%s - invalid resource entry %q, must be in '<resourceName>: <resourceValue>' format",
				errorPrefix, resourceString)
		}

		resourceName := resourceSplit[0]
		resourceValue := resourceSplit[1]

		switch resourceName {
		case "cpu":
			cpuFloat, err := strconv.ParseFloat(resourceValue, 64)
			if err != nil {
				return nil, fmt.Errorf("%s - failed to convert 'cpu' value %q to float64: %w", errorPrefix, resourceValue, err)
			}
			if cpuFloat <= 0.0 {
				return nil, fmt.Errorf("%s - 'cpu' value %q is not positive", errorPrefix, resourceValue)
			}
			resources.Cpu = cpuFloat
		case "memory":
			memoryBytes, err := memoryValueFromString(resourceValue)
			if err != nil {
				return nil, fmt.Errorf("%s - failed to convert 'memory' value %q to Quantity: %w", errorPrefix, resourceValue, err)
			}
			resources.Memory = memoryBytes
		case "replicas":
			replicasInt, err := strconv.ParseInt(resourceValue, 10, 32)
			if err != nil {
				return nil, fmt.Errorf("%s - failed to convert 'replicas' value %q to int32: %w", errorPrefix, resourceValue, err)
			}
			if replicasInt < 1 {
				return nil, fmt.Errorf("%s - 'replicas' value %q is not positive", errorPrefix, resourceValue)
			}
			resources.Replicas = uint32(replicasInt)
		default:
			return nil, fmt.Errorf("%s - unknown resource %q", errorPrefix, resourceName)
		}
	}

	if resources.Cpu == 0.0 {
		return nil, fmt.Errorf("%s - missing 'cpu' value", errorPrefix)
	}

	if resources.Memory == 0 {
		return nil, fmt.Errorf("%s - missing 'memory' value", errorPrefix)
	}

	if replicasRequired && resources.Replicas == 0 {
		return nil, fmt.Errorf("%s - missing 'replicas' value", errorPrefix)
	}

	if !replicasRequired && resources.Replicas > 0 {
		return nil, fmt.Errorf("%s - unexpected 'replicas' value", errorPrefix)
	}

	return &resources, nil
}

// processThresholds - processes thresholds entries in the ConfigMap specified and populates the scalingMap accordingly
func processThresholds(configMap *map[string]string, scalingMap *dynamicScalingMap) error {
	for key, value := range *configMap {
		// "threshold.resources.1000" => "k3s.small"
		if strings.HasPrefix(key, "threshold.") {
			if err := processThresholdValue(key, value, scalingMap); err != nil {
				return err
			}
			delete(*configMap, key)
			continue
		}
	}

	// Sorting all thresholds so they can be iterated upon in the increasing order
	sortThresholds(scalingMap.clustersThresholds)
	sortThresholds(scalingMap.appsThresholds)
	sortThresholds(scalingMap.resourcesThresholds)

	return nil
}

// processInstances - processes instance override entries in the ConfigMap specified
// and populates the scalingMap accordingly
func processInstances(configMap *map[string]string, scalingMap *dynamicScalingMap) error {
	for key, value := range *configMap {
		// "instance.id" => "k3s.medium, redis.medium"
		if strings.HasPrefix(key, "instance.") {
			if err := processInstanceOverride(key, value, scalingMap); err != nil {
				return err
			}
			delete(*configMap, key)
			continue
		}
	}

	return nil
}

// processOrganizations - processes organizations opt-in and opt-out entries in the ConfigMap specified
// and populates the scalingMap accordingly
func processOrganizations(configMap *map[string]string, scalingMap *dynamicScalingMap) error {
	for key, value := range *configMap {
		//  orgs.enabled:  OrgId, OrgId, OrgId, ..
		if key == "orgs.enabled" {
			scalingMap.orgsEnabled = sliceToMap(split(value, ","))
			delete(*configMap, key)
			continue
		}

		//  orgs.disabled: OrgId, OrgId, OrgId, ..
		if key == "orgs.disabled" {
			scalingMap.orgsDisabled = sliceToMap(split(value, ","))
			delete(*configMap, key)
			continue
		}
	}

	return nil
}

// processThresholdValue - processes a scaling map threshold entry:
// threshold.apps.150: k3s.small, redis.small
// threshold.resources.1000: k3s.small
func processThresholdValue(thresholdKey, typesList string, scalingMap *dynamicScalingMap) error {
	// thresholdKey  - "threshold.apps.10"
	// typesList     - "k3s.small, k3s-proxy.small, application-controller.small, redis.small"

	if len(strings.TrimSpace(thresholdKey)) < 1 {
		return fmt.Errorf("empty thresholdKey type for typesList %q", typesList)
	}

	if len(strings.TrimSpace(typesList)) < 1 {
		return fmt.Errorf("empty typesList for thresholdKey %q", thresholdKey)
	}

	// "threshold.apps.150" => ["threshold", "apps", "150"]
	thresholdKeySplit := split(thresholdKey, ".")
	if len(thresholdKeySplit) != 3 {
		return fmt.Errorf("invalid threshold %q - must be in 'threshold.<clusters|apps|resources>.<N>' format",
			thresholdKey)
	}

	// "threshold.apps.150" => "apps"
	thresholdType := thresholdKeySplit[1]

	// "threshold.apps.150" => "150" => 150
	thresholdValueString := thresholdKeySplit[2]
	thresholdValue, err := strconv.ParseInt(thresholdValueString, 10, 32)
	if err != nil {
		return fmt.Errorf("unable to parse %q threshold value %q to int64: %w", thresholdKey, thresholdValueString, err)
	}

	if thresholdValue < 1 {
		return fmt.Errorf("%q threshold value %q is not positive", thresholdKey, thresholdValueString)
	}

	thresholdTypes, err := processTypesList(typesList, scalingMap)
	if err != nil {
		return err
	}

	newThreshold := autoscalerThreshold{
		thresholdValue: uint32(thresholdValue),
		scaleDownValue: uint32(float64(thresholdValue) * (1 - scaleDownThreshold)),
		types:          *thresholdTypes,
	}

	switch thresholdType {
	case "clusters":
		if scalingMap.clustersThresholds, err = appendThreshold(scalingMap.clustersThresholds, newThreshold, thresholdKey, typesList, false); err != nil {
			return err
		}
	case "apps":
		if scalingMap.appsThresholds, err = appendThreshold(scalingMap.appsThresholds, newThreshold, thresholdKey, typesList, true); err != nil {
			return err
		}
	case "resources":
		if scalingMap.resourcesThresholds, err = appendThreshold(scalingMap.resourcesThresholds, newThreshold, thresholdKey, typesList, false); err != nil {
			return err
		}
	default:
		return fmt.Errorf("threshold %q contains an unknown threshold type %q", thresholdKey, thresholdType)
	}

	return nil
}

// appendThreshold - validates the threshold provided and appends it to the list of thresholds, returning the new slice
func appendThreshold(previousThresholds []autoscalerThreshold, newThreshold autoscalerThreshold, thresholdKey, typesList string, isApplicationType bool) ([]autoscalerThreshold, error) {
	// Validating the new threshold value isn't already in the list

	for _, previousThreshold := range previousThresholds {
		if previousThreshold.thresholdValue == newThreshold.thresholdValue {
			return nil, fmt.Errorf("duplicate threshold %q", thresholdKey)
		}
	}

	k3sType := newThreshold.types.k3sType
	k3sProxyType := newThreshold.types.k3sProxyType
	applicationControllerType := newThreshold.types.applicationControllerType
	redisType := newThreshold.types.redisType

	// Validating the new threshold only contains expected types

	if k3sType == "" || k3sProxyType == "" {
		return nil, fmt.Errorf("threshold %q is missing K3s or K3s Proxy types in %q", thresholdKey, typesList)
	}

	if isApplicationType {
		if applicationControllerType == "" || redisType == "" {
			return nil, fmt.Errorf("threshold %q is missing Application Controller or Redis types in %q", thresholdKey, typesList)
		}
	} else if applicationControllerType != "" || redisType != "" {
		return nil, fmt.Errorf("threshold %q should NOT contain Application Controller or Redis types in %q", thresholdKey, typesList)
	}

	return append(previousThresholds, newThreshold), nil
}

// sortThresholds - sorts the thresholds provided in the increasing order
func sortThresholds(thresholds []autoscalerThreshold) {
	sort.Slice(thresholds, func(i, j int) bool {
		return thresholds[i].thresholdValue < thresholds[j].thresholdValue
	})
}

// processInstanceOverride - processes a scaling map instance override entry:
// instance.id1: k3s.medium, redis.medium
// instance.id2: k3s.small, redis.large
func processInstanceOverride(overrideKey, typesList string, scalingMap *dynamicScalingMap) error {
	if len(strings.TrimSpace(overrideKey)) < 1 {
		return fmt.Errorf("empty instance overrideKey type for typesList %q", typesList)
	}

	if len(strings.TrimSpace(typesList)) < 1 {
		return fmt.Errorf("empty typesList for instance overrideKey %q", overrideKey)
	}

	// "instance.id" => ["instance", "id"]
	keySplit := split(overrideKey, ".")
	if len(keySplit) != 2 {
		return fmt.Errorf("invalid instance override %q - must be in 'instance.<id>' format", overrideKey)
	}

	instanceId := keySplit[1]

	if len(instanceId) < 1 {
		return fmt.Errorf("empty instanceId for instance overrideKey %q", overrideKey)
	}

	if _, ok := scalingMap.instanceOverrides[instanceId]; ok {
		return fmt.Errorf("duplicate instance overrideKey %q - instance override for id %q already exsists", overrideKey, instanceId)
	}

	instanceTypes, err := processTypesList(typesList, scalingMap)
	if err != nil {
		return err
	}

	scalingMap.instanceOverrides[instanceId] = *instanceTypes
	return nil
}

// processTypesList - processes a list of types, like "k3s.small, k3s-proxy.small, application-controller.small, redis.small"
func processTypesList(typesList string, scalingMap *dynamicScalingMap) (*autoscalerTypes, error) {
	var types autoscalerTypes

	// "k3s.small, k3s-proxy.small, application-controller.small, redis.small" => ["k3s.small", "k3s-proxy.small", "application-controller.small", "redis.small"]
	typesSplit := split(typesList, ",")
	if len(typesSplit) < 1 {
		return nil, fmt.Errorf("no types specified in %q", typesList)
	}

	for _, resourceType := range typesSplit {
		// Is it a K3s type?
		if _, ok := scalingMap.K3sTypes[resourceType]; ok {
			if types.k3sType != "" {
				return nil, fmt.Errorf("duplicate K3s type %q in %q", resourceType, typesList)
			}
			types.k3sType = resourceType
			continue
		}

		// Is it a K3s Proxy type?
		if _, ok := scalingMap.K3sProxyTypes[resourceType]; ok {
			if types.k3sProxyType != "" {
				return nil, fmt.Errorf("duplicate K3s Proxy type %q in %q", resourceType, typesList)
			}
			types.k3sProxyType = resourceType
			continue
		}

		// Is it an Application Controller type?
		if _, ok := scalingMap.ApplicationControllerTypes[resourceType]; ok {
			if types.applicationControllerType != "" {
				return nil, fmt.Errorf("duplicate Application Controller type %q in %q", resourceType, typesList)
			}
			types.applicationControllerType = resourceType
			continue
		}

		// Is it a Redis type?
		if _, ok := scalingMap.RedisTypes[resourceType]; ok {
			if types.redisType != "" {
				return nil, fmt.Errorf("duplicate Redis type %q in %q", resourceType, typesList)
			}
			types.redisType = resourceType
			continue
		}

		return nil, fmt.Errorf("unknown resource type %q in %q", resourceType, typesList)
	}

	return &types, nil
}
