apiVersion: v1
kind: ConfigMap
metadata:
  name: autoscaler
data:
  k3s.small: "cpu: 0.1, memory: 750Mi, replicas: 2"
  k3s.medium: "cpu: 0.5, memory: 1.5Gi, replicas: 2"
  k3s.large: "cpu: 1, memory: 2Gi, replicas: 3"
  k3s.xlarge: "cpu: 3, memory: 3Gi, replicas: 4"

  k3s-proxy.small: "cpu: 0.01, memory: 50Mi, replicas: 2"
  k3s-proxy.medium: "cpu: 0.05, memory: 250Mi, replicas: 2"
  k3s-proxy.large: "cpu: 0.1, memory: 500Mi, replicas: 2"
  k3s-proxy.xlarge: "cpu: 0.5, memory: 1.5Gi, replicas: 2"

  application-controller.small:  "cpu: 0.01, memory: 50Mi"
  application-controller.medium: "cpu: 0.05, memory: 250Mi"
  application-controller.large:  "cpu: 0.2, memory: 500Mi"
  application-controller.xlarge: "cpu: 3.0, memory: 1.5Gi"

  redis.small: "cpu: 0.1, memory: 750Mi"
  redis.medium: "cpu: 0.5, memory: 1.5Gi"
  redis.large: "cpu: 1, memory: 2Gi"
  redis.xlarge: "cpu: 3, memory: 3Gi"

  threshold.clusters.10: k3s.small, k3s-proxy.small
  threshold.clusters.20: k3s.medium, k3s-proxy.medium
  threshold.clusters.50: k3s.large, k3s-proxy.large
  threshold.clusters.300: k3s.xlarge, k3s-proxy.xlarge

  threshold.apps.150: k3s.small, k3s-proxy.small, application-controller.small, redis.small
  threshold.apps.500: k3s.medium, k3s-proxy.medium, application-controller.medium, redis.medium
  threshold.apps.1000: k3s.large, k3s-proxy.large, application-controller.large, redis.large
  threshold.apps.3000: k3s.xlarge, k3s-proxy.xlarge, application-controller.xlarge, redis.xlarge

  threshold.resources.1000: k3s.small, k3s-proxy.small
  threshold.resources.3000: k3s.medium, k3s-proxy.medium
  threshold.resources.10000: k3s.large, k3s-proxy.large
  threshold.resources.40000: k3s.xlarge, k3s-proxy.xlarge

  instance.id1: k3s.xlarge
  instance.id2: k3s-proxy.xlarge
  instance.id3: application-controller.xlarge
  instance.id4: redis.xlarge
  instance.id5: k3s.xlarge, k3s-proxy.xlarge
  instance.id6: application-controller.xlarge, redis.xlarge
  instance.id7: k3s.large, k3s-proxy.large, application-controller.large, redis.large
  instance.id8: redis.medium, k3s.medium, k3s-proxy.medium, application-controller.medium

  orgs.enabled:  Id1, Id2
  orgs.disabled: Id3, Id4

  # Dummy types for test purposes only - same as "k3s.small" but with different number of replicas
  k3s.small.1: "cpu: 0.1, memory: 750Mi, replicas: 1"
  k3s.small.2: "cpu: 0.1, memory: 750Mi, replicas: 2"
  k3s.small.3: "cpu: 0.1, memory: 750Mi, replicas: 3"
