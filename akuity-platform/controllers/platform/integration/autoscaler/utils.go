package autoscaler

import (
	"encoding/json"
	"fmt"
	"math"
	"strings"

	"github.com/google/go-cmp/cmp"
	"k8s.io/apimachinery/pkg/api/resource"

	"github.com/akuityio/agent/pkg/client/apis/controlplane"
	kargocontrolplane "github.com/akuityio/agent/pkg/client/apis/kargo/controlplane"
)

// quantityToInt64 - converts the Quantity provided to int64 value
func quantityToInt64(quantity *resource.Quantity) int64 {
	if quantity == nil {
		return 0
	}
	if valueInt64, ok := quantity.AsInt64(); ok {
		return valueInt64
	}
	valueDec := quantity.AsDec()
	return int64(float64(valueDec.UnscaledBig().Int64()) * math.Pow10(-int(valueDec.Scale())))
}

// MemoryValueToString - converts the number of bytes to its canonical representation like "750Mi" or "1.5Gi"
func MemoryValueToString(memoryValue uint64) string {
	return resource.NewQuantity(int64(memoryValue), resource.BinarySI).String()
}

// memoryValueFromString - converts memory string like "750Mi" to the number of bytes
func memoryValueFromString(memoryValue string) (uint64, error) {
	memoryQuantity, err := resource.ParseQuantity(memoryValue)
	if err != nil {
		return 0, err
	}
	memoryBytes := quantityToInt64(&memoryQuantity)
	if memoryBytes < 1 {
		return 0, fmt.Errorf("'memory' value %q is not positive", memoryValue)
	}
	return uint64(memoryBytes), nil
}

func last(x []string) string {
	if len(x) < 1 {
		return ""
	}
	return x[len(x)-1]
}

// split - splits the string specified by the separator provided, trimming the tokens from spaces
func split(s, separator string) []string {
	var result []string
	for _, token := range strings.Split(s, separator) {
		tokenTrimmed := strings.TrimSpace(token)
		if len(tokenTrimmed) > 0 {
			result = append(result, tokenTrimmed)
		}
	}
	return result
}

// sliceToMap - converts a slice of strings to a map for a faster lookup
func sliceToMap(slice []string) map[string]bool {
	result := map[string]bool{}
	for _, s := range slice {
		result[s] = true
	}
	return result
}

// ValidateInstanceOverride validates the instance override String and converts it to a Go map:
//
//  1. Unmarshalls the Instance override JSON String to a Go map.
//  2. Unmarshalls the Instance override JSON String to the controlplane.DataValues struct.
//  3. Marshals the controlplane.DataValues struct back to JSON, and then unmarshalls the JSON String to a Go map.
//  4. Compares the original Go map (step 1) with the new map (step 3) to ensure they are identical
//     (=> all override values were processed by the controlplane.DataValues struct and there are no typos or unknown fields)
//
// The function returns an error if any of the steps fail.
func ValidateInstanceOverride(instanceOverrideJSON []byte) (map[string]any, error) {
	// Step 1

	var mapValues map[string]any
	if err := json.Unmarshal(instanceOverrideJSON, &mapValues); err != nil {
		return nil, err
	}

	if len(mapValues) == 0 {
		return nil, fmt.Errorf("instance override is empty")
	}

	// Step 2

	var dataValues controlplane.DataValues
	if err := json.Unmarshal(instanceOverrideJSON, &dataValues); err != nil {
		return nil, err
	}

	// Step 3

	dataValuesJson, err := json.MarshalIndent(dataValues, "", "  ")
	if err != nil {
		return nil, err
	}

	var jsonMapValues map[string]any
	if err := json.Unmarshal(dataValuesJson, &jsonMapValues); err != nil {
		return nil, err
	}

	// Step 4

	if !identicalMaps(mapValues, jsonMapValues) {
		return nil, fmt.Errorf("converting an Instance override => controlplane.DataValues => JSON Map "+
			"produced a result that is different from the original override, check for typos and unknown fields:\n"+
			"%s != %s",
			strings.TrimSpace(string(dataValuesJson)),
			strings.TrimSpace(string(instanceOverrideJSON)))
	}

	return jsonMapValues, nil
}

// ValidateKargoInstanceOverride validates the Kargo instance override String and converts it to a Go map:
func ValidateKargoInstanceOverride(instanceOverrideJSON []byte) (map[string]any, error) {
	// Step 1

	var mapValues map[string]any
	if err := json.Unmarshal(instanceOverrideJSON, &mapValues); err != nil {
		return nil, err
	}

	if len(mapValues) == 0 {
		return nil, fmt.Errorf("instance override is empty")
	}

	// Step 2

	var dataValues kargocontrolplane.DataValues
	if err := json.Unmarshal(instanceOverrideJSON, &dataValues); err != nil {
		return nil, err
	}

	// Step 3

	dataValuesJson, err := json.MarshalIndent(dataValues, "", "  ")
	if err != nil {
		return nil, err
	}

	var jsonMapValues map[string]any
	if err := json.Unmarshal(dataValuesJson, &jsonMapValues); err != nil {
		return nil, err
	}

	// Step 4

	if !identicalMaps(mapValues, jsonMapValues) {
		return nil, fmt.Errorf("converting an Instance override => controlplane.DataValues => JSON Map "+
			"produced a result that is different from the original override, check for typos and unknown fields:\n"+
			"%s != %s",
			strings.TrimSpace(string(dataValuesJson)),
			strings.TrimSpace(string(instanceOverrideJSON)))
	}

	return jsonMapValues, nil
}

// identicalMaps - determines if two Maps are identical (same keys and values)
func identicalMaps[K comparable](map1, map2 map[K]any) bool {
	return (len(map1) == len(map2)) && (cmp.Diff(map1, map2) == "")
}
