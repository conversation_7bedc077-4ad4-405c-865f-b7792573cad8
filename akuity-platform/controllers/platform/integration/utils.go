package integration

import (
	"context"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"math/big"
	"time"

	"github.com/go-logr/logr"
	"golang.org/x/mod/semver"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/utils/ptr"

	"github.com/akuityio/agent/pkg/client/apis/clusteragent"
	"github.com/akuityio/agent/pkg/client/apis/controlplane"
	"github.com/akuityio/akuity-platform/internal/utils/consts"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	featurev1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/features/v1"
)

// GetWebhookCert generates tls certificates for the k3s webhook and the proxy
func GetWebhookCert(instanceID string, kargo bool) (string, string, error) {
	return getWebhookCert(instanceID, kargo, false)
}

// GetKargoAgentWebhookCert generates tls certificates for the kargo analysis job webhook
func GetKargoAgentWebhookCert(ns string) (string, string, error) {
	return getWebhookCert(ns, false, true)
}

func getWebhookCert(ns string, kargoInstance, kargoAgent bool) (string, string, error) {
	priv, err := rsa.GenerateKey(rand.Reader, 4096)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate private key: %w", err)
	}

	keyUsage := x509.KeyUsageDigitalSignature | x509.KeyUsageKeyEncipherment

	notBefore := time.Now()
	notAfter := notBefore.Add(10 * consts.Year)

	serialNumberLimit := new(big.Int).Lsh(big.NewInt(1), 128)
	serialNumber, err := rand.Int(rand.Reader, serialNumberLimit)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate serial number: %w", err)
	}

	template := x509.Certificate{
		SerialNumber: serialNumber,
		Subject: pkix.Name{
			Organization: []string{"Akuity"},
		},
		NotBefore: notBefore,
		NotAfter:  notAfter,

		KeyUsage:              keyUsage,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
		BasicConstraintsValid: true,
	}

	template.DNSNames = ExpectedCertDNSNames(ns, kargoInstance, kargoAgent)
	template.IsCA = true
	template.KeyUsage |= x509.KeyUsageCertSign

	derBytes, err := x509.CreateCertificate(rand.Reader, &template, &template, &priv.PublicKey, priv)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate certificate: %w", err)
	}

	cert := pem.EncodeToMemory(&pem.Block{Type: "CERTIFICATE", Bytes: derBytes})
	if cert == nil {
		return "", "", fmt.Errorf("failed to convert certificate to pem")
	}

	privBytes, err := x509.MarshalPKCS8PrivateKey(priv)
	if err != nil {
		return "", "", fmt.Errorf("unable to marshal private key: %w", err)
	}
	key := pem.EncodeToMemory(&pem.Block{Type: "PRIVATE KEY", Bytes: privBytes})
	if key == nil {
		return "", "", fmt.Errorf("failed to convert hey to pem")
	}
	return string(key), string(cert), nil
}

func ExpectedCertDNSNames(ns string, kargoInstance, kargoAgent bool) []string {
	if kargoInstance {
		return []string{
			"k3s-webhook", "k3s-proxy", fmt.Sprintf("k3s-proxy.kargo-%v", ns), fmt.Sprintf("k3s-proxy.kargo-%v.svc", ns), fmt.Sprintf("k3s-proxy.kargo-%v.svc.cluster.local", ns), fmt.Sprintf("k3s-webhook.kargo-%v", ns), fmt.Sprintf("k3s-webhook.kargo-%v.svc", ns), fmt.Sprintf("k3s-webhook.kargo-%v.svc.cluster.local", ns),
			"kargo-webhooks-server", fmt.Sprintf("kargo-webhooks-server.kargo-%v", ns), fmt.Sprintf("kargo-webhooks-server.kargo-%v.svc", ns), fmt.Sprintf("kargo-webhooks-server.kargo-%v.svc.cluster.local", ns),
		}
	} else if kargoAgent {
		return []string{
			"kargo-webhook", fmt.Sprintf("kargo-webhook.%v", ns), fmt.Sprintf("kargo-webhook.%v.svc", ns), fmt.Sprintf("kargo-webhook.%v.svc.cluster.local", ns),
		}
	} else {
		return []string{
			"k3s-webhook", "k3s-proxy", fmt.Sprintf("k3s-proxy.argocd-%v", ns), fmt.Sprintf("k3s-proxy.argocd-%v.svc", ns), fmt.Sprintf("k3s-proxy.argocd-%v.svc.cluster.local", ns), fmt.Sprintf("k3s-webhook.argocd-%v", ns), fmt.Sprintf("k3s-webhook.argocd-%v.svc", ns), fmt.Sprintf("k3s-webhook.argocd-%v.svc.cluster.local", ns),
		}
	}
}

func isAKPImageVersion(version string) bool {
	isAKP, _ := misc.GetAKPVersion(version)
	return isAKP
}

// getArgoCDFeatures returns list of features that are enabled for a given ArgoCD version
func getArgoCDFeatures(version string, featureStatuses *featurev1.FeatureStatuses, appInAnyNamespaceEnabled bool) (*controlplane.DataValuesArgoCdFeatures, *clusteragent.DataValuesArgoCdFeatures) {
	isAKP, akpVersion := misc.GetAKPVersion(version)
	// list refs is available only in AK versions or in OSS Argo CD >= 2.10.0
	listRefs := isAKP || semver.Compare(version, "v2.10.0") >= 0 || version == "latest"
	// argocd api server can be pointed to delegated redis only in AK versions >= .25 or in OSS Argo CD >= 2.11.0
	delegateRedis := isAKP && akpVersion >= 25 || semver.Compare(version, "v2.11.0") >= 0 || version == "latest"

	return &controlplane.DataValuesArgoCdFeatures{
			RepoServerListRefs:        &listRefs,
			ArgocdServerDelegateRedis: &delegateRedis,
			AppInAnyNamespace:         &appInAnyNamespaceEnabled,
		}, &clusteragent.DataValuesArgoCdFeatures{
			RepoServerListRefs:        &listRefs,
			ArgocdServerDelegateRedis: &delegateRedis,
			RedisTrafficReduction:     ptr.To(featureStatuses.GetRedisTrafficReduction().Enabled()),
			AppInAnyNamespace:         &appInAnyNamespaceEnabled,
		}
}

func ParseCert(cert string) (*x509.Certificate, error) {
	block, _ := pem.Decode([]byte(cert))
	if block == nil {
		return nil, fmt.Errorf("failed to parse certificate PEM")
	}
	if block.Type != "CERTIFICATE" {
		return nil, fmt.Errorf("failed to parse certificate PEM type is not CERTIFICATE")
	}
	c, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse certificate: %w", err)
	}
	return c, nil
}

type CustomAgentCert struct {
	CommonCert string `json:"common-agent-cert.pem"`
	ArgocdCert string `json:"argocd-agent-cert.pem"`
	KargoCert  string `json:"kargo-agent-cert.pem"`
}

func ValidateAgentCert(cert string) error {
	certPEMBlock := []byte(cert)
	count := 0
	for len(certPEMBlock) > 0 {
		var certDERBlock *pem.Block
		certDERBlock, certPEMBlock = pem.Decode(certPEMBlock)
		if certDERBlock == nil {
			return fmt.Errorf("failed to parse certificate PEM")
		}
		if certDERBlock.Type != "CERTIFICATE" {
			return fmt.Errorf("failed to parse certificate PEM type is not CERTIFICATE")
		}
		_, err := x509.ParseCertificate(certDERBlock.Bytes)
		if err != nil {
			return fmt.Errorf("failed to parse x509 certificate: %w", err)
		}
		count++
	}
	if count == 0 {
		return fmt.Errorf("failed to parse certificate, no blocks found")
	}
	return nil
}

func GetCustomCerts(ctx context.Context, logger *logr.Logger, kubecClient kubernetes.Interface, ns, cmName string) (*CustomAgentCert, error) {
	cm, err := kubecClient.CoreV1().ConfigMaps(ns).Get(ctx, cmName, v1.GetOptions{})
	if err != nil {
		if k8serrors.IsNotFound(err) {
			// if cm is not there return empty certs
			return &CustomAgentCert{}, nil
		}
		return nil, fmt.Errorf("failed to get configmap %v: %w", cmName, err)
	}
	temp, err := json.Marshal(cm.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal configmap data: %w", err)
	}
	var customCert CustomAgentCert
	err = json.Unmarshal(temp, &customCert)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal configmap data: %w", err)
	}
	// validate the certs
	if customCert.CommonCert != "" {
		err := ValidateAgentCert(customCert.CommonCert)
		if err != nil {
			return nil, fmt.Errorf("failed to parse and rebuild common agent cert: %w", err)
		}
	}
	if customCert.ArgocdCert != "" {
		err := ValidateAgentCert(customCert.ArgocdCert)
		if err != nil {
			return nil, fmt.Errorf("failed to parse and rebuild argocd agent cert: %w", err)
		}
	}
	if customCert.KargoCert != "" {
		err := ValidateAgentCert(customCert.KargoCert)
		if err != nil {
			return nil, fmt.Errorf("failed to parse and rebuild kargo agent cert: %w", err)
		}
	}
	return &customCert, nil
}
