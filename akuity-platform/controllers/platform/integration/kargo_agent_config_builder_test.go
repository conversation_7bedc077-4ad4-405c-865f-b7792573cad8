package integration

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/license"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/io"
	"github.com/akuityio/akuity-platform/models/util/status"
)

func TestKargoSharedK3sDBConnectionAuth(t *testing.T) {
	t.Run("SharedK3sDBConnectionAuthFalse", func(t *testing.T) {
		reconciler, builder := newKargoTestBuilder(t, false)
		defer io.Close(reconciler)

		configurations, err := builder.buildAgentConfigurations(context.Background(), 1)
		require.NoError(t, err)

		postgres := configurations.K3s.Postgres
		assert.Equal(t, "localhost", postgres.GetHostname())
		assert.Equal(t, "kargo_instance_"+testInstanceId, postgres.GetUsername())
		assert.Equal(t, "my-password", postgres.GetPassword())
	})

	t.Run("SharedK3sDBConnectionAuthTrue", func(t *testing.T) {
		reconciler, builder := newKargoTestBuilder(t, true)
		defer io.Close(reconciler)

		configurations, err := builder.buildAgentConfigurations(context.Background(), 1)
		require.NoError(t, err)

		postgres := configurations.K3s.Postgres
		assert.Equal(t, "localhost", postgres.GetHostname())
		assert.Equal(t, "postgres", postgres.GetUsername())
		assert.Equal(t, "dbpassword", postgres.GetPassword())
	})
}

func newKargoTestBuilder(t *testing.T, sharedK3sDBConnectionAuth bool) (*kargoInstanceReconciler, *kargoAgentConfigBuilder) {
	reconciler, repoSet, _ := newTestKargoReconciler(t, common.TenantPhaseHealthy, sharedK3sDBConnectionAuth)
	instanceConfig, err := repoSet.KargoInstanceConfigs().GetByID(context.Background(), testInstanceId)
	require.NoError(t, err)
	instance, err := repoSet.KargoInstances().GetByID(context.Background(), testInstanceId)
	require.NoError(t, err)
	org, err := repoSet.Organizations().GetByID(context.Background(), instance.OrganizationOwner.String)
	require.NoError(t, err)
	featureSvc := features.NewService(repoSet, nil, false, false, features.FeatureGatesSourceDev, license.License{})
	return reconciler, newKargoAgentConfigBuilder(reconciler, instance, instanceConfig, status.CertificateStatus{}, featureSvc, org, reconciler.settings.IngressConfig, featureSvc.GetFeatureStatusesWithOrg(context.Background(), org))
}
