package integration

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"time"

	"github.com/go-logr/logr"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	v1 "k8s.io/client-go/informers/apps/v1"
	"k8s.io/utils/ptr"

	agentclient "github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/client/apis/kargo/controlplane"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/agent/pkg/kube"
	"github.com/akuityio/akuity-platform/controllers/shared/tenant"
	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/services/kargo"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/io"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	modelsstatus "github.com/akuityio/akuity-platform/models/util/status"
	featuresv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/features/v1"

	_ "embed"
)

const (
	// Schema and username cannot be numeric strings, e.g. 1, so we have to add a prefix to explicitly tell it's a string.
	kargoK3sDBSchemaUsernamePrefix = "kargo_instance_"

	forceReconcileKargoAgents = ` 
 		UPDATE kargo_agent 
    	SET 
 		   status_observed_generation = generation - 1,
 		   status_conditions = null
 		WHERE 
 			instance_id = $1;
	`

	setKargoAgentsToDeletion = ` 
 		UPDATE kargo_agent 
    	SET 
 		   deletion_timestamp = $1
 		WHERE 
 			instance_id = $2;
	`

	bumpKargoAgentGeneration = ` 
 		UPDATE kargo_agent 
    	SET 
			generation = generation + 1
 		WHERE 
 			instance_id = $1;
	`

	relationNotFoundError = "pq: relation .* does not exist"
)

var kargoStatusColumns = models.KargoInstanceStatusColumns

type kargoInstanceReconciler struct {
	featureSvc                  features.Service
	settings                    ControllerSettings
	stateClient                 tenant.KargoStateClient
	tenantsFactory              KargoTenantsFactory
	externalDNS                 *controlplane.DataValuesIngressExternalDns
	imagePullSecretGetter       func() (map[string][]byte, error)
	gracePeriodDuration         time.Duration
	sqlOpen                     func(string, string) (*sql.DB, error)
	instanceDeploymentsInformer v1.DeploymentInformer
	inCluster                   bool
	log                         *logr.Logger
	kargoVersions               []agentclient.ComponentVersion
	kargoUnstableVersion        *agentclient.ComponentVersion
}

func NewKargoInstanceReconciler(
	tenantsFactory KargoTenantsFactory,
	settings ControllerSettings,
	externalDNS *controlplane.DataValuesIngressExternalDns,
	stateClient tenant.KargoStateClient,
	imagePullSecretGetter func() (map[string][]byte, error),
	gracePeriodDuration time.Duration,
	instanceDeploymentsInformer v1.DeploymentInformer,
	featureSvc features.Service,
	kargoVersions []agentclient.ComponentVersion,
	kargoUnstableVersion *agentclient.ComponentVersion,
) *kargoInstanceReconciler {
	return &kargoInstanceReconciler{
		settings:                    settings,
		imagePullSecretGetter:       imagePullSecretGetter,
		stateClient:                 stateClient,
		tenantsFactory:              tenantsFactory,
		externalDNS:                 externalDNS,
		gracePeriodDuration:         gracePeriodDuration,
		sqlOpen:                     sql.Open,
		instanceDeploymentsInformer: instanceDeploymentsInformer,
		inCluster:                   !settings.EnableIngress,
		log:                         settings.Log,
		featureSvc:                  featureSvc,
		kargoVersions:               kargoVersions,
		kargoUnstableVersion:        kargoUnstableVersion,
	}
}

// Close closes Portal and K3s DBs, only used in unit tests
func (r *kargoInstanceReconciler) Close() error {
	if r.settings.PortalDBRawClient != nil {
		if err := r.settings.PortalDBRawClient.Close(); err != nil {
			return err
		}
	}
	if r.settings.K3sDBRawClient != nil {
		if err := r.settings.K3sDBRawClient.Close(); err != nil {
			return err
		}
	}
	return nil
}

func (r *kargoInstanceReconciler) ItemToID(item *models.KargoInstance) string {
	return item.ID
}

func (r *kargoInstanceReconciler) LogValuesFromID(id string) []interface{} {
	return []interface{}{"instance_id", id}
}

func (r *kargoInstanceReconciler) IDColumn() string {
	return models.KargoInstanceTableColumns.ID
}

func (r *kargoInstanceReconciler) LogValuesFromItem(item *models.KargoInstance) []interface{} {
	return []interface{}{}
}

func (r *kargoInstanceReconciler) Reconcile(ctx context.Context, instance *models.KargoInstance) error {
	status, err := instance.GetStatus()
	if err != nil {
		return fmt.Errorf("failed to get instance status: %w", err)
	}
	oldStatus := status.DeepCopy()
	reconcileError := r.reconcile(ctx, instance, &status)
	if !reflect.DeepEqual(status, oldStatus) {
		if err := instance.SetStatus(status); err != nil {
			return fmt.Errorf("failed to set instance status: %w", err)
		}
		if err := r.settings.RepoSet.KargoInstances().Update(context.Background(), instance, kargoStatusColumns...); err != nil {
			return fmt.Errorf("failed to persist cluster status: %w", err)
		}
	}
	return reconcileError
}

func (r *kargoInstanceReconciler) reconcile(ctx context.Context, instance *models.KargoInstance, status *models.KargoInstanceStatus) error {
	defer func() {
		status.ObservedGeneration = null.IntFrom(instance.Generation)
		if status.Health.Code == modelsstatus.HealthStatusCodeHealthy {
			status.Info.ReachedGeneration = instance.Generation
		}
	}()

	kargoTenant, err := r.tenantsFactory.NewKargoTenant(instance.ID)
	if err != nil {
		return fmt.Errorf("failed to create tenant for KargoInstance with id %s: %w", instance.ID, err)
	}

	if !instance.DeletionTimestamp.IsZero() && instance.DeletionTimestamp.Time.Before(time.Now()) {
		if err := r.deleteTenant(ctx, kargoTenant, instance.ID); err != nil {
			return fmt.Errorf("failed to delete tenant: %w", err)
		}
		return nil
	}

	// get instance config only after checking for deletion or else might get into partially deleted error state
	// where instance config is already deleted but instance entry is not
	instanceConfig, err := r.settings.RepoSet.KargoInstanceConfigs().GetByID(ctx, instance.ID)
	if err != nil {
		return err
	}

	unsupportedVersion := misc.IsKargoComponentVersionSupported(instanceConfig.Version.String, r.kargoVersions, r.kargoUnstableVersion, true) != nil

	generationMismatch := status.ObservedGeneration.Int != instance.Generation

	// set all relevant status conditions to false if generation mismatch
	// if unsupported version, we will not reconcile instance so we will not unset any conditions
	if generationMismatch && !unsupportedVersion {
		delete(status.Conditions, models.InstanceConditionTypeApplied)
	}

	// if instance namespace is lost (disaster scenario), force reconcile instance and all instance clusters
	if status.Conditions.IsEstablished(models.InstanceConditionTypeApplied) && !unsupportedVersion {
		deployments, err := r.instanceDeploymentsInformer.Lister().Deployments(common.KargoHostNamespace(instance.ID)).List(labels.Everything())
		if err != nil && !k8sErrors.IsNotFound(err) {
			return fmt.Errorf("failed to list instance deployments: %w", err)
		}
		if len(deployments) == 0 {
			delete(status.Conditions, models.InstanceConditionTypeApplied)

			if _, err := r.settings.PortalDBRawClient.Exec(forceReconcileKargoAgents, instance.ID); err != nil {
				return fmt.Errorf("failed to requeue clusters: %w", err)
			}
		}
	}

	health, err := r.getInstanceHealth(ctx, kargoTenant, instanceConfig)
	if err != nil {
		return fmt.Errorf("failed to get health for KargoInstance with id %s: %w", instance.ID, err)
	}
	status.Health = health

	if projectCount, stageCount, err := r.stateClient.GetStatsCount(ctx, instance.ID); err == nil {
		status.Info.Stats.ProjectCount = projectCount
		status.Info.Stats.StageCount = stageCount
	} else if ok, _ := regexp.MatchString(relationNotFoundError, err.Error()); !ok {
		// ignore relation not found error, schema might not be initialized yet
		r.log.Error(err, "failed to get project count")
	}

	requireCertInit := false
	var certStatus modelsstatus.CertificateStatus

	if !config.IsSelfHosted && instanceConfig.FQDN.String != "" {
		cert, err := getCertificateStatus(ctx, kargoTenant, getKargoInternalURL(instance.ID, r.settings.DomainSuffix), instanceConfig.FQDN.String)
		if err != nil && !k8sErrors.IsNotFound(err) {
			return err
		}
		if k8sErrors.IsNotFound(err) {
			requireCertInit = true
		}
		status.Info.CertificateStatus = cert
		certStatus = cert
	}

	// DEV: ensure no form of manifest apply/generation happens when this
	// condition is encountered or else it will error during manifest gen
	// stop reconciliation if instance version is unsupported
	if unsupportedVersion {
		r.log.Info("unsupported kargo version", "instance_id", instance.ID, "version", instanceConfig.Version.String)
		return nil
	}

	org, err := r.settings.RepoSet.Organizations().GetByID(ctx, instance.OrganizationOwner.String)
	if err != nil {
		return err
	}

	orgFeatureStatuses := r.featureSvc.GetFeatureStatusesWithOrg(ctx, org)

	if err := r.refreshRestrictions(ctx, instanceConfig, org); err != nil {
		return err
	}

	changed, err := r.updateInstanceConfigInternalSpec(ctx, kargoTenant, instanceConfig, orgFeatureStatuses)
	if err != nil {
		return err
	}
	if changed {
		// force next reconciliation
		return errorsutil.NewRetryableError(fmt.Errorf("kargo instance private spec updated, retry reconciliation"), "")
	}

	if !generationMismatch && r.featureSvc.GetFeatureStatuses(ctx, nil).GetK3SCertCnReset().Enabled() {
		yes, err := r.stateClient.NeedK3sCNReset(ctx, instance.ID)
		if err != nil {
			if ok, _ := regexp.MatchString(relationNotFoundError, err.Error()); !ok {
				// ignore relation not found error, schema might not be initialized yet
				return err
			}
		}
		if yes {
			r.log.Info("need K3s Cert CN Reset", "instance_id", instance.ID)
			status.Conditions.SetNotEstablished(models.InstanceConditionTypeInitialized, "NeedK3sCertCnReset", "K3s TLS cert CNs need to be reset")
		}
	}

	instanceApplied := status.Conditions.IsEstablished(models.InstanceConditionTypeApplied)
	instanceInitialized := status.Conditions.IsEstablished(models.InstanceConditionTypeInitialized)
	instancePruned := status.Conditions.IsEstablished(models.InstanceConditionTypePruned)
	reconciliationNecessary := generationMismatch || !instanceApplied || !instanceInitialized || requireCertInit

	var dataValues *controlplane.DataValues

	if reconciliationNecessary {
		dataValues, err = newKargoAgentConfigBuilder(r, instance, instanceConfig, certStatus, r.featureSvc, org, r.settings.IngressConfig, orgFeatureStatuses).buildAgentConfigurations(ctx, int32(instance.Generation))
		if err != nil {
			return fmt.Errorf("failed to build data values for KargoInstance with id %s: %w", instance.ID, err)
		}
		if r.settings.EnableIngress {
			if status.Hostname.String != dataValues.Ingress.KargoApi.GetFqdn() && r.settings.OverrideDefaultDomains {
				if _, err := r.settings.PortalDBRawClient.Exec(bumpKargoAgentGeneration, instance.ID); err != nil {
					return fmt.Errorf("failed to set new hostname when requeuing clusters: %w", err)
				}
			}
			status.Hostname = null.StringFrom(dataValues.Ingress.KargoApi.GetFqdn())
		} else {
			status.Hostname = null.StringFrom(fmt.Sprintf("kargo-api.kargo-%s.svc.cluster.local", instance.ID))
		}
	}

	if generationMismatch || !instanceApplied || requireCertInit {
		// instance should be re-initialized and re-pruned after re-applying the manifests
		delete(status.Conditions, models.InstanceConditionTypeInitialized)
		delete(status.Conditions, models.InstanceConditionTypePruned)
		if err := r.applyTenant(ctx, kargoTenant, dataValues); err != nil {
			status.Conditions.SetNotEstablished(models.InstanceConditionTypeApplied, "FailedToApplyManifests", err.Error())
			return fmt.Errorf("failed to apply tenant manifests: %w", err)
		}
		status.Info.AkuityServerVersion = dataValues.AgentServer.GetVersion()
		status.Conditions.SetEstablished(models.InstanceConditionTypeApplied)
	}

	if generationMismatch || !instanceInitialized {
		if err := r.initializeTenant(ctx, instance, instanceConfig, kargoTenant, dataValues); err != nil {
			status.Conditions.SetNotEstablished(models.InstanceConditionTypeInitialized, "FailedToInitializeTenant", err.Error())
			return errorsutil.NewRetryableError(err, "failed to initialize tenant")
		}
		status.Conditions.SetEstablished(models.InstanceConditionTypeInitialized)
		status.Info.RequestedAkuityServerVersion = ""
	}

	// Prune only when the instance is healthy
	if (generationMismatch || !instancePruned) && status.Health.Code == modelsstatus.HealthStatusCodeHealthy {
		if err := kargoTenant.PruneTenant(ctx, uint64(instance.Generation), false); err != nil {
			status.Conditions.SetNotEstablished(models.InstanceConditionTypePruned, "FailedToPruneTenant", err.Error())
			return errorsutil.NewRetryableError(err, "failed to prune tenant")
		}
		internalSpec, err := instanceConfig.GetInternalSpec()
		if err != nil {
			return err
		}
		if internalSpec.K3sSchemaMigration != 0 {
			internalSpec.K3sSchemaMigration = 0
			if err := instanceConfig.SetInternalSpec(internalSpec); err != nil {
				return err
			}
			if err := r.settings.RepoSet.KargoInstanceConfigs().Update(ctx, instanceConfig, models.KargoInstanceConfigColumns.InternalSpec); err != nil {
				return err
			}
		}
		status.Conditions.SetEstablished(models.InstanceConditionTypePruned)
	}

	return nil
}

func (r *kargoInstanceReconciler) refreshRestrictions(ctx context.Context, instanceConfig *models.KargoInstanceConfig, organization *models.Organization) error {
	maxStages, maxInstanceStages, currentStageCount, currentInstanceStageCount, expired, err := kargo.GetKargoLimits(ctx, r.gracePeriodDuration, r.settings.PortalDBRawClient, organization, instanceConfig)
	if err != nil {
		return err
	}
	canCreateStages := (maxStages == 0 || currentStageCount < maxStages) && !expired && (currentInstanceStageCount < maxInstanceStages || maxInstanceStages == -1)
	internalSpec, err := instanceConfig.GetInternalSpec()
	if err != nil {
		return err
	}

	// setting project creation flag to nil as it's no longer used
	internalSpec.CanCreateProjects = nil

	if internalSpec.GetCanCreateStages() != canCreateStages {
		internalSpec.CanCreateStages = ptr.To(canCreateStages)
		if err := instanceConfig.SetInternalSpec(internalSpec); err != nil {
			return err
		}
		if err := r.settings.RepoSet.KargoInstanceConfigs().Update(ctx, instanceConfig, models.KargoInstanceConfigColumns.InternalSpec); err != nil {
			return err
		}
	}
	return nil
}

func (r *kargoInstanceReconciler) initializeTenant(ctx context.Context, instance *models.KargoInstance, instanceConfig *models.KargoInstanceConfig, tenant KargoTenant, dataValues *controlplane.DataValues) error {
	if err := tenant.InitializeTenant(ctx, dataValues, kube.ApplyOpts{}, r.featureSvc.GetFeatureStatuses(ctx, nil).GetK3SCertCnReset().Enabled()); err != nil {
		return err
	}

	data, err := tenant.GetGitOpsKubeConfig(ctx, dataValues.Ingress.K3sProxy.GetFqdn(), r.inCluster)
	if err != nil {
		return err
	}

	privateSpec, err := instanceConfig.GetPrivateSpec()
	if err != nil {
		return err
	}

	if string(privateSpec.GitOpsKubeconfig) != string(data) {
		privateSpec.GitOpsKubeconfig = data
		if err := instanceConfig.SetPrivateSpec(privateSpec); err != nil {
			return err
		}
		if err := r.settings.RepoSet.KargoInstanceConfigs().Update(ctx, instanceConfig, models.KargoInstanceConfigColumns.PrivateSpec); err != nil {
			return err
		}
	}

	return r.createEventsTrigger(ctx, instance.ID)
}

func (r *kargoInstanceReconciler) getInstanceHealth(ctx context.Context, tenant KargoTenant, instanceConfig *models.KargoInstanceConfig) (modelsstatus.HealthStatus, error) {
	// if we cannot retrieve the tenant status in 5 seconds, we will return an unknown status
	ctx, cancel := context.WithTimeout(ctx, time.Second*5)
	defer cancel()
	tenantStatus, err := tenant.Status(ctx)
	if err != nil {
		return modelsstatus.HealthStatus{
			Code:    modelsstatus.HealthStatusCodeUnknown,
			Message: fmt.Sprintf("failed to get tenant status: %v", err),
		}, nil
	}

	switch tenantStatus.PriorityStatus {
	case common.TenantPhaseHealthy:
		return modelsstatus.HealthStatus{
			Code:    modelsstatus.HealthStatusCodeHealthy,
			Message: instanceHealthyMessage,
		}, nil
	case common.TenantPhaseProgressing:
		return modelsstatus.HealthStatus{
			Code:    modelsstatus.HealthStatusCodeProgressing,
			Message: fmt.Sprintf("Instance is progressing, waiting for %d workloads to start.", len(tenantStatus.Progressing)),
		}, nil
	case common.TenantPhaseDegraded:
		return modelsstatus.HealthStatus{
			Code:    modelsstatus.HealthStatusCodeDegraded,
			Message: instanceIsDegradedMessage,
		}, nil
	default:
		return modelsstatus.HealthStatus{
			Code:    modelsstatus.HealthStatusCodeUnknown,
			Message: fmt.Sprintf("%d tenant resources has unknown status", len(tenantStatus.Unknown)),
		}, nil
	}
}

// deleteTenant deletes the tenant resources in k8s and database entries for configs
// and all clusters details belonging to the tenant with provided instanceID
func (r *kargoInstanceReconciler) deleteTenant(ctx context.Context, tnt KargoTenant, instanceID string) error {
	// delete external dependencies, user initiated deletion would already have cleaned up these resources
	// but in case this was an automated or aims deletion we need to clean up
	// 1. Argocd direct kargo clusters
	updates, err := models.ArgoCDClusters(qm.Where("coalesce(spec -> 'directClusterSpec' ->> 'kargoInstanceID', '') = ?", instanceID)).UpdateAll(ctx, r.settings.PortalDBRawClient, models.M{"deletion_timestamp": time.Now().Format(time.RFC3339)})
	if err != nil {
		return fmt.Errorf("failed to delete argocd direct kargo cluster connection: %w", err)
	}
	if updates > 0 {
		return errorsutil.NewRetryableError(fmt.Errorf("deleting argocd direct kargo cluster connection: count=%d", updates), "")
	} else {
		// check if there are any argocd clusters that are not yet deleted
		clusterCount, err := models.ArgoCDClusters(qm.Where("coalesce(spec -> 'directClusterSpec' ->> 'kargoInstanceID', '') = ?", instanceID)).Count(ctx, r.settings.PortalDBRawClient)
		if err != nil {
			return err
		}
		if clusterCount > 0 {
			return errorsutil.NewRetryableError(fmt.Errorf("deleting argocd direct kargo cluster connection: count=%d", clusterCount), "")
		}
	}

	// kargo clusters need to be properly cleaned up and cannot just be deleted from db as the
	// akuity managed clusters have resources in akp argocd tenants which need to be cleaned up first
	clusterCount, err := r.settings.RepoSet.KargoAgents().Filter(models.KargoAgentWhere.InstanceID.EQ(instanceID)).Count(ctx)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return fmt.Errorf("failed to get tenant cluster entries: %w", err)
	}
	if !errors.Is(err, sql.ErrNoRows) && clusterCount > 0 {
		// set all clusters to be deleted
		if _, err := r.settings.PortalDBRawClient.Exec(setKargoAgentsToDeletion, time.Now(), instanceID); err != nil {
			return fmt.Errorf("failed to set deletion timestamp for tenant clusters: %w", err)
		}
		return errorsutil.NewRetryableError(fmt.Errorf("kargo tenant clusters are not deleted yet"), "")
	}

	if err := tnt.Delete(ctx); err != nil {
		// ignore error if ns already deleted in previous reconcile
		if !k8sErrors.IsNotFound(err) {
			return fmt.Errorf("failed to delete tenant ns: %w", err)
		}
	}

	schemaName := kargoK3sDBSchemaUsernamePrefix + instanceID
	if _, err := r.settings.K3sDBRawClient.ExecContext(ctx, fmt.Sprintf(`DROP SCHEMA IF EXISTS %s CASCADE`, schemaName)); err != nil {
		return fmt.Errorf("failed to delete tenant schema %q: %w", schemaName, err)
	}

	userName := schemaName
	if _, err := r.settings.K3sDBRawClient.ExecContext(ctx, fmt.Sprintf(`DROP USER IF EXISTS %s`, userName)); err != nil {
		return fmt.Errorf("failed to delete tenant user %q: %w", userName, err)
	}

	db, txBeginner := database.WithTxBeginner(r.settings.PortalDBRawClient)
	repoSet := client.NewRepoSet(db)
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := txBeginner.Begin(ctx)
	if err != nil {
		return err
	}

	if err := repoSet.KargoInstanceConfigs().Delete(ctx, instanceID); err != nil {
		// ignore error if tenant config already deleted in previous reconcile
		if !errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("failed to delete tenant instance config entry: %w", err)
		}
	}

	if err := repoSet.KargoPromotions().Filter(models.KargoPromotionWhere.InstanceID.EQ(instanceID)).DeleteAll(ctx); err != nil {
		// ignore error if sync events are already deleted in previous reconcile
		if !errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("failed to delete tenant promotion entries: %w", err)
		}
	}

	if err := repoSet.AIConversations().Filter(models.AiConversationWhere.KargoInstanceID.EQ(null.StringFrom(instanceID))).DeleteAll(ctx); err != nil {
		// ignore error if already deleted in previous reconcile
		if !errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("failed to delete tenant ai conversations: %w", err)
		}
	}

	if err := repoSet.KargoInstances().Delete(ctx, instanceID); err != nil {
		return fmt.Errorf("failed to delete tenant instance: %w", err)
	}

	return tx.Commit()
}

//go:embed functions_kargo.sql
var kargoSqlFunctions string

var kargoEventsTriggerSQL = []string{
	`DROP TRIGGER IF EXISTS on_kine_inserted ON kargo_instance_%[1]s.kine;`,

	`CREATE TRIGGER on_kine_inserted
		AFTER INSERT ON kargo_instance_%[1]s.kine
		FOR EACH ROW
	EXECUTE PROCEDURE kargo_instance_%[1]s.notify_kine_inserted('%[1]s');`,
}

func (r *kargoInstanceReconciler) createEventsTrigger(ctx context.Context, instanceID string) error {
	instanceConfig, err := r.settings.RepoSet.KargoInstanceConfigs().Filter(models.KargoInstanceConfigWhere.InstanceID.EQ(instanceID)).One(ctx)
	if err != nil {
		return err
	}
	dbInfo, err := database.ExtractDBInfo(r.settings.K3sDBConnection)
	if err != nil {
		return err
	}

	privateSpec, err := instanceConfig.GetPrivateSpec()
	if err != nil {
		return err
	}

	// Tenant schema is identical to tenant's username
	postgresSchema := privateSpec.K3sUsername
	postgresUsername := privateSpec.K3sUsername
	postgresPassword := privateSpec.K3sPassword

	if r.settings.SharedK3sDBConnectionAuth {
		// During DB migration - all tenants temporarily use a shared K3sDBConnection auth to connect to RDS Proxy
		postgresUsername = dbInfo.User
		postgresPassword = dbInfo.Password
	}

	connectionString := fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=%s&&search_path=%s",
		postgresUsername,
		postgresPassword,
		dbInfo.Host,
		dbInfo.Port,
		dbInfo.DBName,
		dbInfo.SSLMode,
		postgresSchema)
	k3sExecutor, err := r.sqlOpen("postgres", connectionString)
	if err != nil {
		return err
	}
	defer io.Close(k3sExecutor)

	separator := "$$ LANGUAGE plpgsql;"
	for _, functionSQL := range strings.Split(kargoSqlFunctions, separator) {
		if strings.TrimSpace(functionSQL) == "" {
			continue
		}

		functionSQL += separator
		functionSQL = strings.Replace(functionSQL, "CREATE OR REPLACE FUNCTION ", fmt.Sprintf("CREATE OR REPLACE FUNCTION kargo_instance_%s.", instanceID), 1)
		if _, err := k3sExecutor.ExecContext(ctx, functionSQL); err != nil {
			return err
		}
	}

	for _, statement := range kargoEventsTriggerSQL {
		if _, err := k3sExecutor.ExecContext(ctx, fmt.Sprintf(statement, instanceID)); err != nil {
			return err
		}
	}
	return nil
}

// applyTenant applies the Kargo tenant manifests in the cluster.
func (r *kargoInstanceReconciler) applyTenant(ctx context.Context, tenant KargoTenant, dataValues *controlplane.DataValues) error {
	return tenant.Apply(ctx, dataValues, kube.ApplyOpts{})
}

func (r *kargoInstanceReconciler) updateInstanceConfigInternalSpec(ctx context.Context, tenant KargoTenant, instanceConfig *models.KargoInstanceConfig, orgFeatureStatuses *featuresv1.FeatureStatuses) (bool, error) {
	privateSpec, err := instanceConfig.GetPrivateSpec()
	if err != nil {
		return false, err
	}

	internalSpec, err := instanceConfig.GetInternalSpec()
	if err != nil {
		return false, err
	}

	privateSpecModified, err := r.modifyKargoPrivateSpec(ctx, tenant, &privateSpec, instanceConfig, orgFeatureStatuses)
	if err != nil {
		return false, err
	}

	internalSpecModified, err := r.modifyKargoInternalSpec(ctx, internalSpec, instanceConfig)
	if err != nil {
		return false, err
	}

	columns := []string{}
	if privateSpecModified {
		if err := instanceConfig.SetPrivateSpec(privateSpec); err != nil {
			return false, err
		}
		columns = append(columns, models.KargoInstanceConfigColumns.PrivateSpec)
	}
	if internalSpecModified {
		if err := instanceConfig.SetInternalSpec(internalSpec); err != nil {
			return false, err
		}
		columns = append(columns, models.KargoInstanceConfigColumns.InternalSpec)
	}

	if len(columns) > 0 {
		logging.GetContextLogger(ctx).Info("regenerating private spec", "columns", columns)
		return true, r.settings.RepoSet.KargoInstanceConfigs().Update(ctx, instanceConfig, columns...)
	}

	return false, nil
}

func (r *kargoInstanceReconciler) modifyKargoInternalSpec(ctx context.Context, internalSpec *models.KargoInstanceInternalSpec, instanceConfig *models.KargoInstanceConfig) (bool, error) {
	separator := r.settings.GetDomainSeparator()
	spec, err := instanceConfig.GetSpec()
	if err != nil {
		return false, err
	}

	// calculate argocd urls for the kargo instance
	agentsWithRemoteArgoInstances, err := r.settings.RepoSet.KargoAgents(models.KargoAgentWhere.InstanceID.EQ(instanceConfig.InstanceID), models.KargoAgentWhere.RemoteArgocdInstanceID.IsNotNull()).ListAll(ctx, models.KargoAgentColumns.ID, models.KargoAgentColumns.Name, models.KargoAgentColumns.RemoteArgocdInstanceID)
	if err != nil {
		return false, err
	}

	agentsWithSelfManagedArgoInstances, err := r.settings.RepoSet.KargoAgents(models.KargoAgentWhere.InstanceID.EQ(instanceConfig.InstanceID), qm.Where("COALESCE(spec->> 'selfManagedArgocdURL', '') <> ''")).ListAll(ctx, models.KargoAgentColumns.ID, models.KargoAgentColumns.Name, models.KargoAgentColumns.Spec)
	if err != nil {
		return false, err
	}

	// if multiple agents use the same argocd instance (which is common if user has more has 1 agent) this will help reduce db calls
	cacheURLMap := map[string]string{}

	argocdURLS := map[string]string{}
	for _, agent := range agentsWithRemoteArgoInstances {
		if agent.RemoteArgocdInstanceID.String == "" {
			continue
		}
		cachedURL, ok := cacheURLMap[agent.RemoteArgocdInstanceID.String]
		if !ok {
			remoteArgocdInstanceCfgs, err := r.settings.RepoSet.ArgoCDInstanceConfigs(models.ArgoCDInstanceConfigWhere.InstanceID.EQ(agent.RemoteArgocdInstanceID.String)).ListAll(ctx, models.ArgoCDInstanceConfigColumns.InstanceID, models.ArgoCDInstanceConfigColumns.Subdomain, models.ArgoCDInstanceConfigColumns.FQDN)
			if err != nil {
				return false, err
			}
			if len(remoteArgocdInstanceCfgs) != 1 {
				return false, errors.New("expected exactly one remote argocd instance config with id=" + agent.RemoteArgocdInstanceID.String)
			}
			remoteArgocdInstance, err := r.settings.RepoSet.ArgoCDInstances(models.ArgoCDInstanceWhere.ID.EQ(agent.RemoteArgocdInstanceID.String)).ListAll(ctx, models.ArgoCDInstanceColumns.Shard)
			if err != nil {
				return false, err
			}
			if len(remoteArgocdInstance) != 1 {
				return false, errors.New("expected exactly one remote argocd instance with id=" + agent.RemoteArgocdInstanceID.String)
			}
			argocdURL := getArgocdInstanceURL(remoteArgocdInstanceCfgs[0], separator, getShardDomain(remoteArgocdInstance[0].Shard), r.settings.DomainSuffix)
			cacheURLMap[agent.RemoteArgocdInstanceID.String] = argocdURL
			cachedURL = argocdURL
		}

		if spec.DefaultShardAgentID == agent.ID {
			argocdURLS[""] = cachedURL
		}
		// since 1.7 we allow the default shard name to be also specified in the stage
		argocdURLS[agent.Name] = cachedURL

	}

	for _, agent := range agentsWithSelfManagedArgoInstances {
		agentSpec, err := agent.GetSpec()
		if err != nil {
			return false, err
		}

		if agentSpec.SelfManagedArgocdURL == "" {
			continue
		}

		if spec.DefaultShardAgentID == agent.ID {
			argocdURLS[""] = agentSpec.SelfManagedArgocdURL
		}
		// since 1.7 we allow the default shard name to be also specified in the stage
		argocdURLS[agent.Name] = agentSpec.SelfManagedArgocdURL

	}

	// check if urls changed
	modified := false
	if len(internalSpec.ArgoCDUrls) == len(argocdURLS) {
		for k, v := range argocdURLS {
			if internalSpec.ArgoCDUrls[k] != v {
				modified = true
				break
			}
		}
		if !modified {
			return false, nil
		}
	}

	// set new urls
	internalSpec.ArgoCDUrls = argocdURLS
	return true, nil
}

func (r *kargoInstanceReconciler) modifyKargoPrivateSpec(ctx context.Context, kargoTenant KargoTenant, privateSpec *models.KargoInstanceConfigPrivateSpec, instanceConfig *models.KargoInstanceConfig, orgFeatureStatuses *featuresv1.FeatureStatuses) (bool, error) {
	privateSpecModified := false

	if privateSpec.FqdnVersion == "" {
		privateSpec.FqdnVersion = "1"
		privateSpecModified = true
	}

	if privateSpec.K3sUsername == "" || privateSpec.K3sPassword == "" {
		username := kargoK3sDBSchemaUsernamePrefix + instanceConfig.InstanceID
		password, err := database.RandomAlphabetString()
		if err != nil {
			return false, err
		}
		privateSpec.K3sUsername = username
		privateSpec.K3sPassword = password
		privateSpecModified = true
	}

	if privateSpec.WebhookKey == "" || privateSpec.WebhookCert == "" {
		webhookKey, webhookCert, err := GetWebhookCert(instanceConfig.InstanceID, true)
		if err != nil {
			return false, err
		}
		privateSpec.WebhookKey = webhookKey
		privateSpec.WebhookCert = webhookCert
		privateSpecModified = true
	} else {
		cert, err := ParseCert(privateSpec.WebhookCert)
		if err != nil {
			return false, err
		}
		// if webhook cert is expiring in 5 days, renew it
		if cert.NotAfter.Before(time.Now().AddDate(0, 0, 5)) {
			webhookKey, webhookCert, err := GetWebhookCert(instanceConfig.InstanceID, false)
			if err != nil {
				return false, err
			}
			privateSpec.WebhookKey = webhookKey
			privateSpec.WebhookCert = webhookCert
			privateSpecModified = true
		}
	}

	if privateSpec.K3sToken == "" {
		k3sToken, err := client.NanoID(client.DefaultNanoIDLength)
		if err != nil {
			return false, err
		}
		privateSpec.K3sToken = k3sToken
		privateSpecModified = true
	}
	if privateSpec.KargoServerSecretKey == "" {
		data, err := argocd.MakeServerSignature()
		if err != nil {
			return false, err
		}
		privateSpec.KargoServerSecretKey = string(data)
		privateSpecModified = true
	}

	if orgFeatureStatuses.GetAiSupportEngineer().Enabled() {
		// Kargo support admin/oidc/k8s sa token validation.
		// admin may not be enabled, it's safer for us to use sa token for kargo rbac
		token := kargoTenant.AKPAdminServiceAccountToken(ctx)
		if token != "" && privateSpec.AkpAdminKargoToken != token {
			privateSpec.AkpAdminKargoToken = token
			privateSpecModified = true
		}
	} else if privateSpec.AkpAdminKargoToken != "" {
		privateSpec.AkpAdminKargoToken = ""
		privateSpecModified = true
	}

	return privateSpecModified, nil
}

func getKargoSubdomain(config *models.KargoInstanceConfig) string {
	if config.Subdomain.String != "" {
		return config.Subdomain.String
	}
	return config.InstanceID
}
