package integration

import (
	"context"
	"database/sql"
	"fmt"

	"k8s.io/client-go/rest"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/ai"
	"github.com/akuityio/akuity-platform/internal/services/features"
	loggingutil "github.com/akuityio/akuity-platform/internal/utils/logging"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

type aiConversationStepReconciler struct {
	featureSvc     features.Service
	repoSet        client.RepoSet
	db             *sql.DB
	cfg            config.AIConfig
	hostRestConfig *rest.Config
}

func NewAiConversationStepReconciler(featureSvc features.Service, repoSet client.RepoSet, db *sql.DB, cfg config.AIConfig, hostRestConfig *rest.Config) *aiConversationStepReconciler {
	return &aiConversationStepReconciler{
		featureSvc:     featureSvc,
		repoSet:        repoSet,
		db:             db,
		cfg:            cfg,
		hostRestConfig: hostRestConfig,
	}
}

func (r *aiConversationStepReconciler) ItemToID(item *models.AiConversation) string {
	return item.ID
}

func (r *aiConversationStepReconciler) IDColumn() string {
	return fmt.Sprintf("%s as %s", models.AiConversationTableColumns.ID, models.AiConversationColumns.ID)
}

func (r *aiConversationStepReconciler) LogValuesFromID(id string) []interface{} {
	return []interface{}{"ai_conversation_id", id}
}

func (r *aiConversationStepReconciler) LogValuesFromItem(item *models.AiConversation) []interface{} {
	return []interface{}{"ai_conversation_id", item.ID}
}

func (r *aiConversationStepReconciler) Reconcile(ctx context.Context, item *models.AiConversation) error {
	featureStatus := r.featureSvc.GetFeatureStatuses(ctx, &item.OrganizationID)
	srv, err := ai.NewService(r.db, r.repoSet, featureStatus, item.OrganizationID, r.cfg, r.hostRestConfig, loggingutil.GetContextLogger(ctx), ai.ServiceOptionWithInstanceID(item.InstanceID.String), ai.ServiceOptionWithKargoInstanceID(item.KargoInstanceID.String))
	if err != nil {
		return err
	}

	return srv.GenerateConversationStepSummary(ctx, item.ID)
}
