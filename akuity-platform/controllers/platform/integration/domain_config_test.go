package integration

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestDomainConfig_GenerateDomain(t *testing.T) {
	tests := []struct {
		name        string
		config      domainConfig
		serviceType string
		expected    string
	}{
		{
			name: "default domain with instance ID",
			config: domainConfig{
				InstanceID:    "f47ac10b",
				DefaultDomain: "cdsvcs.mydomain.com",
			},
			serviceType: "agentsvr",
			expected:    "f47ac10b-agentsvr.cdsvcs.mydomain.com",
		},
		{
			name: "default domain with cache service",
			config: domainConfig{
				InstanceID:    "f47ac10b",
				DefaultDomain: "cdsvcs.mydomain.com",
			},
			serviceType: "cache",
			expected:    "f47ac10b-cache.cdsvcs.mydomain.com",
		},
		{
			name: "default domain with custom subdomain",
			config: domainConfig{
				InstanceID:             "f47ac10b",
				DefaultDomain:          "cdsvcs.mydomain.com",
				Subdomain:              "testtesttest",
				OverrideDefaultDomains: true,
			},
			serviceType: "agentsvr",
			expected:    "testtesttest-agentsvr.cdsvcs.mydomain.com",
		},
		{
			name: "default domain with hyphens and instance ID",
			config: domainConfig{
				InstanceID:    "f47ac10b",
				DefaultDomain: "cdsvcs.mydomain.com",
				UseHyphens:    true,
			},
			serviceType: "agentsvr",
			expected:    "f47ac10b-agentsvr.cdsvcs.mydomain.com",
		},
		{
			name: "default domain with hyphens and custom subdomain",
			config: domainConfig{
				InstanceID:             "f47ac10b",
				DefaultDomain:          "cdsvcs.mydomain.com",
				Subdomain:              "testtesttest",
				OverrideDefaultDomains: true,
				UseHyphens:             true,
			},
			serviceType: "agentsvr",
			expected:    "testtesttest-agentsvr.cdsvcs.mydomain.com",
		},

		{
			name: "customer domain with agentsvr",
			config: domainConfig{
				InstanceID:     "f47ac10b",
				CustomerDomain: "customer-domain.com",
				DefaultDomain:  "cdsvcs.mydomain.com",
			},
			serviceType: "agentsvr",
			expected:    "f47ac10b-agentsvr.cdsvcs.mydomain.com",
		},
		{
			name: "customer domain with agentsvr, override true",
			config: domainConfig{
				InstanceID:             "f47ac10b",
				CustomerDomain:         "customer-domain.com",
				DefaultDomain:          "cdsvcs.mydomain.com",
				OverrideDefaultDomains: true,
			},
			serviceType: "agentsvr",
			expected:    "agentsvr.customer-domain.com",
		},
		{
			name: "customer domain with cache",
			config: domainConfig{
				InstanceID:     "f47ac10b",
				CustomerDomain: "customer-domain.com",
				DefaultDomain:  "cdsvcs.mydomain.com",
			},
			serviceType: "cache",
			expected:    "f47ac10b-cache.cdsvcs.mydomain.com",
		},
		{
			name: "customer domain with cache, override true",
			config: domainConfig{
				InstanceID:             "f47ac10b",
				CustomerDomain:         "customer-domain.com",
				DefaultDomain:          "cdsvcs.mydomain.com",
				OverrideDefaultDomains: true,
			},
			serviceType: "cache",
			expected:    "cache.customer-domain.com",
		},
		{
			name: "customer domain with hyphens",
			config: domainConfig{
				InstanceID:     "f47ac10b",
				CustomerDomain: "customer-domain.com",
				DefaultDomain:  "cdsvcs.mydomain.com",
				UseHyphens:     true,
			},
			serviceType: "agentsvr",
			expected:    "f47ac10b-agentsvr.cdsvcs.mydomain.com",
		},
		{
			name: "customer domain with hyphens, override true",
			config: domainConfig{
				InstanceID:             "f47ac10b",
				CustomerDomain:         "customer-domain.com",
				DefaultDomain:          "cdsvcs.mydomain.com",
				UseHyphens:             true,
				OverrideDefaultDomains: true,
			},
			serviceType: "agentsvr",
			expected:    "agentsvr-customer-domain.com",
		},
		{
			name: "customer domain with hyphens and cache",
			config: domainConfig{
				InstanceID:     "f47ac10b",
				CustomerDomain: "customer-domain.com",
				DefaultDomain:  "cdsvcs.mydomain.com",
				UseHyphens:     true,
			},
			serviceType: "cache",
			expected:    "f47ac10b-cache.cdsvcs.mydomain.com",
		},
		{
			name: "customer domain with hyphens and cache, override true",
			config: domainConfig{
				InstanceID:             "f47ac10b",
				CustomerDomain:         "customer-domain.com",
				DefaultDomain:          "cdsvcs.mydomain.com",
				UseHyphens:             true,
				OverrideDefaultDomains: true,
			},
			serviceType: "cache",
			expected:    "cache-customer-domain.com",
		},

		// Edge cases
		{
			name: "empty subdomain with OverrideDefaultDomains true falls back to instance ID",
			config: domainConfig{
				InstanceID:             "f47ac10b",
				DefaultDomain:          "cdsvcs.mydomain.com",
				Subdomain:              "",
				OverrideDefaultDomains: true,
			},
			serviceType: "agentsvr",
			expected:    "f47ac10b-agentsvr.cdsvcs.mydomain.com",
		},
		{
			name: "customer domain takes precedence over subdomain settings",
			config: domainConfig{
				InstanceID:             "f47ac10b",
				CustomerDomain:         "example.com",
				DefaultDomain:          "cdsvcs.mydomain.com",
				Subdomain:              "testtesttest",
				OverrideDefaultDomains: true,
			},
			serviceType: "agentsvr",
			expected:    "agentsvr.example.com",
		},
		{
			name: "complex customer domain",
			config: domainConfig{
				InstanceID:     "f47ac10b",
				CustomerDomain: "api.staging.company.com",
				DefaultDomain:  "cdsvcs.mydomain.com",
			},
			serviceType: "agentsvr",
			expected:    "f47ac10b-agentsvr.cdsvcs.mydomain.com",
		},
		{
			name: "complex customer domain, override true",
			config: domainConfig{
				InstanceID:             "f47ac10b",
				CustomerDomain:         "api.staging.company.com",
				DefaultDomain:          "cdsvcs.mydomain.com",
				OverrideDefaultDomains: true,
			},
			serviceType: "agentsvr",
			expected:    "agentsvr.api.staging.company.com",
		},
		{
			name: "complex customer domain with hyphens",
			config: domainConfig{
				InstanceID:     "f47ac10b",
				CustomerDomain: "api.staging.company.com",
				DefaultDomain:  "cdsvcs.mydomain.com",
				UseHyphens:     true,
			},
			serviceType: "agentsvr",
			expected:    "f47ac10b-agentsvr.cdsvcs.mydomain.com",
		},
		{
			name: "complex customer domain with hyphens, override true",
			config: domainConfig{
				InstanceID:             "f47ac10b",
				CustomerDomain:         "api.staging.company.com",
				DefaultDomain:          "cdsvcs.mydomain.com",
				UseHyphens:             true,
				OverrideDefaultDomains: true,
			},
			serviceType: "agentsvr",
			expected:    "agentsvr-api.staging.company.com",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.config.GenerateDomain(tt.serviceType)
			assert.Equal(t, tt.expected, result)
		})
	}
}
