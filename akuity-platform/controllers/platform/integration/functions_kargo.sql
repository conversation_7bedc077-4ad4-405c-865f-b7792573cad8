CREATE OR REPLACE FUNCTION notify_kine_inserted() RETURNS TRIGGER AS $$
BEGIN
    if NEW.name like '/registry/events/%/%' then
        PERFORM pg_notify(
                'default.on_kargo_instance_changed',
                json_build_object(
                        'instance_id', TG_ARGV[0]::text,
                        'type', 'k8s_event_created'::text
                    )::text
            );
    elsif NEW.name like '/registry/configmaps/kargo/agent-%.status' then
        PERFORM pg_notify(
                'default.on_kargo_instance_changed',
                json_build_object(
                        'instance_id', TG_ARGV[0]::text,
                        'cluster_name', substring(NEW.name, '/registry/configmaps/kargo/agent-(.*).status'),
                        'type', 'cluster_status_changed'::text
                    )::text
            );
    elsif NEW.name like '/registry/secrets/kargo/agent-%.data' then
        PERFORM pg_notify(
                'default.on_kargo_instance_changed',
                json_build_object(
                        'instance_id', TG_ARGV[0]::text,
                        'cluster_name', substring(NEW.name, '/registry/secrets/kargo/agent-(.*).data'),
                        'type', 'cluster_status_changed'::text
                    )::text
            );
    end if;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;
