{"values": {"kustomization": {"kind": "Kustomization", "patches": [{"patch": "- op: add\n  path: /spec/ingress/-\n  value:\n    from:\n     - namespaceSelector:\n         matchLabels:\n           kubernetes.io/metadata.name: monitoring", "target": {"kind": "NetworkPolicy", "name": "agent-server-network-policy", "group": "networking.k8s.io"}}], "replacements": [{"source": {"fieldPath": "metadata.labels.akuity\\.io/argo-cd-instance-id", "kind": "Deployment", "name": "argocd-server"}, "targets": [{"fieldPaths": ["spec.template.spec.affinity.podAntiAffinity.preferredDuringSchedulingIgnoredDuringExecution.0.podAffinityTerm.labelSelector.matchLabels.akuity\\.io/argo-cd-instance-id"], "select": {"kind": "Deployment", "name": "argocd-server"}}]}], "apiVersion": "kustomize.config.k8s.io/v1beta1"}}, "k3s_type": "k3s.small", "k3s_image": "quay.io/akuity/rancher/k3s:v1.24.12-k3s1", "redis_type": "redis.small", "canCreateApps": true, "k3s_proxy_type": "k3s-proxy.small", "application_controller_type": "application-controller.small"}