package integration

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"

	agentclient "github.com/akuityio/agent/pkg/client"
	tenanttesting "github.com/akuityio/akuity-platform/controllers/shared/tenant/testing"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/license"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/version"
	modelstesting "github.com/akuityio/akuity-platform/models/client/testing"
	"github.com/akuityio/akuity-platform/models/models"
)

func TestReconcile_AgentReconciledSuccessfully(t *testing.T) {
	tenantsFactory := &fakeKargoTenant{}
	repoSet := modelstesting.NewInMemoryRepoSet()
	agent := &models.KargoAgent{ID: "123", Name: "my-instance", InstanceID: "kargo-1", Generation: 1}
	require.NoError(t, agent.SetSpec(models.KargoAgentSpec{SizeVersion: 1, Size: models.ClusterSizeSmall, TargetVersion: version.GetLatestAgentVersion()}))
	repoSet.KargoAgentsRepo.Items[agent.ID] = agent
	repoSet.OrganizationsRepo.Items[agent.ID] = &models.Organization{ID: "123", Name: "my-org"}
	repoSet.KargoInstancesRepo.Items["kargo-1"] = &models.KargoInstance{ID: "kargo-1", OrganizationOwner: null.StringFrom("123")}
	repoSet.KargoInstanceConfigsRepo.Items["kargo-1"] = &models.KargoInstanceConfig{InstanceID: "kargo-1", Version: null.StringFrom("latest")}
	featureSvc := features.NewService(repoSet, nil, false, false, features.FeatureGatesSourceDev, license.License{})

	reconciler := NewKargoAgentReconciler(tenantsFactory, ControllerSettings{
		RepoSet:        repoSet,
		EnableIngress:  false,
		InstanceConfig: config.InstanceConfig{},
		DomainSuffix:   "akuity.cloud",
		Shard:          "",
	}, &tenanttesting.FakeKargoTenantStateClient{}, "", featureSvc, []agentclient.ComponentVersion{{Version: "latest"}}, &agentclient.ComponentVersion{})

	require.NotNil(t, reconciler.Reconcile(context.Background(), agent))

	agent, err := repoSet.KargoAgents().GetByID(context.Background(), "123")
	require.NoError(t, err)

	status, err := agent.GetStatus()
	require.NoError(t, err)

	// waiting for agent webhook certs
	assert.True(t, !status.Conditions.IsEstablished(models.ClusterConditionTypeConfigured))
	assert.True(t, tenantsFactory.ReceivedAgentConfig == nil)

	require.NoError(t, reconciler.Reconcile(context.Background(), agent))

	agent, err = repoSet.KargoAgents().GetByID(context.Background(), "123")
	require.NoError(t, err)

	status, err = agent.GetStatus()
	require.NoError(t, err)

	assert.True(t, status.Conditions.IsEstablished(models.ClusterConditionTypeConfigured))
	assert.Equal(t, "my-instance", tenantsFactory.ReceivedAgentConfig.Name)
}

func TestKargoAgent_ItemToID(t *testing.T) {
	repoSet := modelstesting.NewInMemoryRepoSet()
	featureSvc := features.NewService(repoSet, nil, false, false, features.FeatureGatesSourceDev, license.License{})
	reconciler := NewKargoAgentReconciler(&fakeKargoTenant{}, ControllerSettings{
		RepoSet:        modelstesting.NewInMemoryRepoSet(),
		EnableIngress:  false,
		InstanceConfig: config.InstanceConfig{},
		DomainSuffix:   "akuity.cloud",
		Shard:          "",
	}, &tenanttesting.FakeKargoTenantStateClient{}, "", featureSvc, []agentclient.ComponentVersion{{Version: "latest"}}, &agentclient.ComponentVersion{})

	id := reconciler.ItemToID(&models.KargoAgent{ID: "123"})
	assert.Equal(t, "123", id)
}
