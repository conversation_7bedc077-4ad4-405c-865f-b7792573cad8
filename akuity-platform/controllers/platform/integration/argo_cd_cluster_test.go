package integration

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/akuity-platform/controllers/platform/integration/clusterautoscaler/testing"
	tenanttesting "github.com/akuityio/akuity-platform/controllers/shared/tenant/testing"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/license"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/version"
	modelstesting "github.com/akuityio/akuity-platform/models/client/testing"
	"github.com/akuityio/akuity-platform/models/models"
)

func TestReconcile_ClusterReconciledSuccessfully(t *testing.T) {
	tenantsFactory := &fakeTenant{}
	repoSet := modelstesting.NewInMemoryRepoSet()
	cluster := &models.ArgoCDCluster{ID: "123", Name: "my-cluster", InstanceID: "argocd-1", SequenceID: 0, Generation: 1}
	require.NoError(t, cluster.SetSpec(models.ClusterSpec{SizeVersion: 1, Size: models.ClusterSizeSmall, TargetVersion: version.GetLatestAgentVersion()}))
	repoSet.ArgoCDClustersRepo.Items[cluster.ID] = cluster
	repoSet.OrganizationsRepo.Items[cluster.ID] = &models.Organization{ID: "123", Name: "my-org"}
	repoSet.ArgoCDInstancesRepo.Items["argocd-1"] = &models.ArgoCDInstance{ID: "argocd-1", OrganizationOwner: "123"}
	repoSet.ArgoCDInstanceConfigsRepo.Items["argocd-1"] = &models.ArgoCDInstanceConfig{InstanceID: "argocd-1", Version: null.StringFrom("latest")}

	reconciler := NewArgoCDClusterReconciler(tenantsFactory, ControllerSettings{
		RepoSet:        repoSet,
		EnableIngress:  false,
		InstanceConfig: config.InstanceConfig{},
		DomainSuffix:   "akuity.cloud",
		Shard:          "",
	}, &tenanttesting.FakeTenantStateClient{}, features.NewService(repoSet, nil, false, false, features.FeatureGatesSourceDev, license.License{}), clusterautoscalertesting.FakeAutoscaler{}, "", []client.ComponentVersion{{Version: "latest"}})
	require.NoError(t, reconciler.Reconcile(context.Background(), cluster))

	cluster, err := repoSet.ArgoCDClusters().GetByID(context.Background(), "123")
	require.NoError(t, err)

	status, err := cluster.GetStatus()
	require.NoError(t, err)

	assert.True(t, status.Conditions.IsEstablished(models.ClusterConditionTypeConfigured))
	assert.Equal(t, "my-cluster", tenantsFactory.ReceivedClusterConfig.Name)
	assert.Equal(t, uint64(1030), tenantsFactory.ReceivedClusterConfig.ClusterPort)
	assert.Equal(t, uint64(1031), tenantsFactory.ReceivedClusterConfig.RepoServerPort)
}

func TestArgoCDCluster_ItemToID(t *testing.T) {
	repoSet := modelstesting.NewInMemoryRepoSet()
	reconciler := NewArgoCDClusterReconciler(&fakeTenant{}, ControllerSettings{
		RepoSet:        modelstesting.NewInMemoryRepoSet(),
		EnableIngress:  false,
		InstanceConfig: config.InstanceConfig{},
		DomainSuffix:   "akuity.cloud",
		Shard:          "",
	}, &tenanttesting.FakeTenantStateClient{}, features.NewService(repoSet, nil, false, false, features.FeatureGatesSourceDev, license.License{}), clusterautoscalertesting.FakeAutoscaler{}, "", []client.ComponentVersion{{Version: "latest"}})

	id := reconciler.ItemToID(&models.ArgoCDCluster{ID: "123"})

	assert.Equal(t, "123", id)
}
