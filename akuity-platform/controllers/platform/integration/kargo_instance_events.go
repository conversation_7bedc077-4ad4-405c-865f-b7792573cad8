package integration

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"strings"
	"time"

	"github.com/go-logr/logr"
	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/types"

	"github.com/akuityio/akuity-platform/controllers/platform/metrics"
	"github.com/akuityio/akuity-platform/controllers/shared/tenant"
	"github.com/akuityio/akuity-platform/internal/kargo"
	"github.com/akuityio/akuity-platform/internal/services/notifications"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

const (
	kargoEventPrefix = "event.kargo.akuity.io/"
)

var skipMap = map[string]bool{
	kargo.AnnotationKeyEventActor:                  true,
	kargo.AnnotationKeyEventStageName:              true,
	kargo.AnnotationKeyEventProject:                true,
	kargo.AnnotationKeyEventPromotionName:          true,
	kargo.AnnotationKeyEventPromotionCreateTime:    true,
	kargo.AnnotationKeyEventVerificationFinishTime: true,
	kargo.AnnotationKeyEventVerificationStartTime:  true,
	kargo.AnnotationKeyEventFreightAlias:           true,
	kargo.AnnotationKeyEventFreightName:            true,
	kargo.AnnotationKeyEventFreightCreateTime:      true,
}

type kargoInstanceEventsReconciler struct {
	tenantStateClient tenant.KargoStateClient
	repoSet           client.RepoSet
	db                *sql.DB
	eventHandlers     []func(instanceID string, event v1.Event)
	metricsRegistry   *metrics.ControllerMetricsRegistry
	log               *logr.Logger
}

func NewKargoInstanceEventsReconciler(metricsRegistry *metrics.ControllerMetricsRegistry, tenantStateClient tenant.KargoStateClient, settings ControllerSettings) *kargoInstanceEventsReconciler {
	return &kargoInstanceEventsReconciler{
		tenantStateClient: tenantStateClient,
		repoSet:           settings.RepoSet,
		db:                settings.PortalDBRawClient,
		metricsRegistry:   metricsRegistry,
		log:               settings.Log,
	}
}

func (r *kargoInstanceEventsReconciler) AddEventHandler(f func(instanceID string, event v1.Event)) {
	r.eventHandlers = append(r.eventHandlers, f)
}

func (r *kargoInstanceEventsReconciler) ItemToID(item *models.KargoInstance) string {
	return item.ID
}

func (r *kargoInstanceEventsReconciler) IDColumn() string {
	return models.KargoInstanceTableColumns.ID
}

func (r *kargoInstanceEventsReconciler) LogValuesFromID(id string) []interface{} {
	return []interface{}{"instance_id", id}
}

func (r *kargoInstanceEventsReconciler) LogValuesFromItem(_ *models.KargoInstance) []interface{} {
	return []interface{}{}
}

func (r *kargoInstanceEventsReconciler) reconcileEvents(ctx context.Context, instance *models.KargoInstance, recentID int) (int, error) {
	db, txBeginner := database.WithTxBeginner(r.db)
	repoSet := client.NewRepoSet(db)
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := txBeginner.Begin(ctx)
	if err != nil {
		return 0, err
	}

	createdWebhookEvent, err := notifications.ShouldCreateOrgAuditEvent(ctx, instance.OrganizationOwner.String, repoSet)
	if err != nil {
		return 0, err
	}

	auditLogs, promos, newRecentID, err := r.processEvents(ctx, instance, recentID)
	if err != nil {
		return 0, err
	}

	existingAuditLogs := make(map[string]bool)
	for _, log := range auditLogs {
		key := keyForAuditLog(log)
		if existingAuditLogs[key] {
			continue
		}
		existingAuditLogs[key] = true
		if err := notifications.CreateAuditLogWithEvent(ctx, instance.OrganizationOwner.String, repoSet, log, &createdWebhookEvent); err != nil {
			return 0, err
		}
	}

	for _, promo := range promos {
		if err := repoSet.KargoPromotions().Create(ctx, promo); err != nil {
			return 0, err
		}
	}

	return newRecentID, tx.Commit()
}

func (r *kargoInstanceEventsReconciler) Reconcile(ctx context.Context, instance *models.KargoInstance) error {
	processedEventInfo, err := instance.GetRecentProcessedEventInfo()
	if err != nil {
		return err
	}

	newRecentID, processEventsErr := r.reconcileEvents(ctx, instance, processedEventInfo.EventId)
	if processEventsErr != nil {
		processedEventInfo.Error = processEventsErr.Error()
	} else {
		processedEventInfo.Error = ""
		processedEventInfo.EventId = newRecentID
	}

	if err := updateKargoInstanceStatusRecentProcessedEventInfo(context.Background(), instance, r.repoSet, processedEventInfo); err != nil {
		return err
	}

	return processEventsErr
}

func updateKargoInstanceStatusRecentProcessedEventInfo(ctx context.Context, instance *models.KargoInstance, repoSet client.RepoSet, info models.StatusRecentProcessedEventInfo) error {
	if err := instance.SetRecentProcessedEventInfo(info); err != nil {
		return err
	}

	return repoSet.KargoInstances().Update(ctx, instance, models.KargoInstanceColumns.StatusRecentProcessedEventInfo)
}

func (r *kargoInstanceEventsReconciler) processEvents(ctx context.Context, instance *models.KargoInstance, recentID int) ([]*models.AuditLog, []*models.KargoPromotion, int, error) {
	var auditLogs []*models.AuditLog
	var promotions []*models.KargoPromotion
	for {
		events, err := r.tenantStateClient.ListEventsSinceID(ctx, instance.ID, recentID, eventProcessingBatchSize)
		if err != nil {
			return nil, nil, 0, err
		}
		if len(events) == 0 {
			break
		}

		for _, event := range events {
			for _, handler := range r.eventHandlers {
				handler(instance.ID, event.Event)
			}
			recentID = event.ID

			var operation *models.AuditLog
			var promo *models.KargoPromotion
			if event.Reason == kargo.EventReasonPromotionCreated || event.Reason == kargo.EventReasonPromotionFailed || event.Reason == kargo.EventReasonPromotionSucceeded || event.Reason == kargo.EventReasonPromotionErrored {
				if operation, promo, err = r.processPromotion(ctx, instance, event); err != nil {
					return nil, nil, 0, err
				}
			} else if event.Reason == kargo.EventReasonFreightApproved || strings.HasPrefix(event.Reason, kargo.EventReasonFreightVerificationPrefix) {
				if operation, err = r.processFreight(ctx, instance, event); err != nil {
					return nil, nil, 0, err
				}
			}
			if operation != nil {
				auditLogs = append(auditLogs, operation)
			}
			if promo != nil {
				promotions = append(promotions, promo)
			}
		}
	}
	return auditLogs, promotions, recentID, nil
}

func truncateString(str string, count int) string {
	if len(str) <= count {
		return str
	}
	return str[:count]
}

func (r *kargoInstanceEventsReconciler) processPromotion(ctx context.Context, instance *models.KargoInstance, event tenant.Event) (*models.AuditLog, *models.KargoPromotion, error) {
	auditlog := &models.AuditLog{
		OrganizationID: null.StringFrom(instance.OrganizationOwner.String),
		Timestamp:      event.LastTimestamp.Time,
		Action:         models.CamelToKebabCase(strings.TrimPrefix(event.Reason, kargo.PromotionKind)),
	}
	extraDetails, err := getMarshalledKargoAnnotationDetails(event)
	if err != nil {
		return nil, nil, err
	}
	if err := auditlog.SetDetails(&models.AuditDetails{Message: event.Message, Patch: string(extraDetails)}); err != nil {
		return nil, nil, err
	}

	actor := event.Annotations[kargo.AnnotationKeyEventActor]
	stageName := event.Annotations[kargo.AnnotationKeyEventStageName]
	if actor == "" {
		actor = string(models.UnknownActor)
	}
	if err := auditlog.SetActor(&models.AuditActor{
		ID:   actor,
		Type: models.KargoActor,
	}); err != nil {
		return nil, nil, err
	}

	objectType := models.KargoPromotionAuditObject
	res := kargo.PromotionKind

	projectName := event.Annotations[kargo.AnnotationKeyEventProject]
	if err := auditlog.SetObject(&models.AuditObject{
		ParentID: &models.AuditParentObjID{Name: stageName, ParentName: instance.Name, ProjectName: projectName},
		ID:       &models.AuditObjID{Group: kargo.APIVersion, Kind: res, Name: event.InvolvedObject.Name},
		Type:     objectType,
	}); err != nil {
		return nil, nil, err
	}

	var promotion *models.KargoPromotion
	// process promotion completion events
	if event.Reason == kargo.EventReasonPromotionFailed || event.Reason == kargo.EventReasonPromotionSucceeded || event.Reason == kargo.EventReasonPromotionErrored {
		creationTime, err := time.Parse(time.RFC3339, event.Annotations[kargo.AnnotationKeyEventPromotionCreateTime])
		if err != nil {
			return nil, nil, err
		}
		phase := "Succeeded"
		if event.Reason != kargo.EventReasonPromotionSucceeded {
			phase = "Failed"
		}
		// TODO: when oss has 1 promo -> multiple stages, loop and duplicate each event for every stage
		promotion = &models.KargoPromotion{
			InstanceID:    instance.ID,
			ProjectName:   projectName,
			StartTime:     creationTime,
			EndTime:       event.CreationTimestamp.Time,
			ResultPhase:   phase,
			ResultMessage: truncateString(event.Message, 500),
			PromotionName: event.Annotations[kargo.AnnotationKeyEventPromotionName],
			StageName:     event.Annotations[kargo.AnnotationKeyEventStageName],
		}
		initiatedBy := models.OperationInitiator{
			Username:  actor,
			Automated: false,
		}
		if strings.HasPrefix(actor, kargo.EventActorControllerPrefix) {
			initiatedBy.Automated = true
		}
		var applications []types.NamespacedName
		var applicationNames []string
		if v, ok := event.Annotations[kargo.AnnotationKeyEventApplications]; ok {
			if err := json.Unmarshal([]byte(v), &applications); err != nil {
				return nil, nil, err
			}
			applicationNames = lo.Map(applications, func(item types.NamespacedName, _ int) string {
				return item.Name
			})
		}
		if err := promotion.SetDetails(&models.KargoPromotionDetails{
			InitiatedBy:      initiatedBy,
			PromotionStatus:  event.Reason,
			PromotionEndTime: event.CreationTimestamp.Format(time.RFC3339),
			FreightDetails: &models.FreightDetails{
				FreightName:         event.Annotations[kargo.AnnotationKeyEventFreightName],
				FreightAlias:        event.Annotations[kargo.AnnotationKeyEventFreightAlias],
				FreightCreationTime: event.Annotations[kargo.AnnotationKeyEventFreightCreateTime],
			},
			Miscellaneous: getKargoAnnotationDetails(event, skipMap),
			Applications:  applicationNames,
		}); err != nil {
			return nil, nil, err
		}
	}

	return auditlog, promotion, nil
}

func (r *kargoInstanceEventsReconciler) processFreight(ctx context.Context, instance *models.KargoInstance, event tenant.Event) (*models.AuditLog, error) {
	auditlog := &models.AuditLog{
		OrganizationID: null.StringFrom(instance.OrganizationOwner.String),
		Timestamp:      event.LastTimestamp.Time,
		Action:         models.CamelToKebabCase(strings.TrimPrefix(event.Reason, kargo.FreightKind)),
	}
	extraDetails, err := getMarshalledKargoAnnotationDetails(event)
	if err != nil {
		return nil, err
	}
	if err := auditlog.SetDetails(&models.AuditDetails{Message: event.Message, Patch: string(extraDetails)}); err != nil {
		return nil, err
	}

	actor := event.Annotations[kargo.AnnotationKeyEventActor]
	stageName := event.Annotations[kargo.AnnotationKeyEventStageName]
	if actor == "" {
		actor = string(models.UnknownActor)
	}
	if err := auditlog.SetActor(&models.AuditActor{
		ID:   actor,
		Type: models.KargoActor,
	}); err != nil {
		return nil, err
	}

	objectType := models.KargoFreightAuditObject
	res := kargo.FreightKind
	projectName := event.Annotations[kargo.AnnotationKeyEventProject]
	if err := auditlog.SetObject(&models.AuditObject{
		ParentID: &models.AuditParentObjID{Name: stageName, ParentName: instance.Name, ProjectName: projectName},
		ID:       &models.AuditObjID{Group: kargo.APIVersion, Kind: res, Name: event.InvolvedObject.Name},
		Type:     objectType,
	}); err != nil {
		return nil, err
	}

	analysisRunName := event.Annotations[kargo.AnnotationKeyEventAnalysisRunName]

	// handle freight verification events, update existing promotion with verification result status
	// this is only possible when there is an analysis run specified in the event
	if analysisRunName != "" && strings.HasPrefix(event.Reason, kargo.EventReasonFreightVerificationPrefix) {
		if !strings.HasPrefix(actor, kargo.EventActorControllerPrefix) {
			// if user created verification event then ignore it
			return auditlog, nil
		}
		promotionName := event.Annotations[kargo.AnnotationKeyEventPromotionName]
		freightName := event.Annotations[kargo.AnnotationKeyEventFreightName]
		// if promotion name is missing skip updating promotion and log about the issue
		if promotionName == "" {
			r.log.Error(errors.New("promotion name not found for verification event"), "", "freightName", freightName, "stageName", stageName, "instanceID", instance.ID, "projectName", projectName)
			return auditlog, nil
		}
		promotion, err := r.repoSet.KargoPromotions().Filter(models.KargoPromotionWhere.PromotionName.EQ(promotionName), models.KargoPromotionWhere.InstanceID.EQ(instance.ID), models.KargoPromotionWhere.ProjectName.EQ(projectName)).ListAll(ctx)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				r.log.Error(errors.New("promotion not found for verification event"), "", "stageName", stageName, "freightName", freightName, "promotionName", promotionName, "projectName", projectName, "instanceID", instance.ID)
			}
			return nil, err
		}
		if len(promotion) == 0 {
			r.log.Error(errors.New("promotion not found for verification event"), "", "stageName", stageName, "freightName", freightName, "promotionName", promotionName, "projectName", projectName, "instanceID", instance.ID)
			return nil, errors.New("promotion not found for verification event")
		}
		// edge case where completion time might not be set in the event
		var completionTime *time.Time
		if event.Annotations[kargo.AnnotationKeyEventVerificationFinishTime] != "" {
			temp, err := time.Parse(time.RFC3339, event.Annotations[kargo.AnnotationKeyEventVerificationFinishTime])
			if err != nil {
				return nil, err
			}
			completionTime = &temp
		}
		phase := "Succeeded"
		if event.Reason != kargo.EventReasonFreightVerificationSucceeded {
			phase = "Failed"
		}
		for _, promo := range promotion {
			if phase != "Succeeded" {
				promo.ResultMessage = truncateString(event.Message, 500) // update message to failed verification message
			}
			promo.ResultPhase = phase
			if completionTime != nil {
				promo.EndTime = *completionTime
			}
			details, err := promo.GetDetails()
			if err != nil {
				return nil, err
			}
			details.FreightDetails.VerificationStatus = event.Reason
			details.FreightDetails.VerificationStartTime = event.Annotations[kargo.AnnotationKeyEventVerificationStartTime]
			details.FreightDetails.VerificationEndTime = event.Annotations[kargo.AnnotationKeyEventVerificationFinishTime]
			if err := promo.SetDetails(details); err != nil {
				return nil, err
			}
			if err := r.repoSet.KargoPromotions().Update(ctx, promo); err != nil {
				return nil, err
			}
		}
	}
	return auditlog, nil
}

func getMarshalledKargoAnnotationDetails(event tenant.Event) ([]byte, error) {
	return json.Marshal(getKargoAnnotationDetails(event, nil))
}

func getKargoAnnotationDetails(event tenant.Event, skipMap map[string]bool) map[string]interface{} {
	finalMap := map[string]interface{}{}
	for key, value := range event.Annotations {
		if skipMap != nil && skipMap[key] {
			continue
		}
		if strings.HasPrefix(key, kargoEventPrefix) {
			finalMap[strings.TrimPrefix(key, kargoEventPrefix)] = value
		}
	}
	return finalMap
}
