package integration

import (
	"context"
	"crypto/sha256"
	"database/sql"
	"encoding/hex"
	"errors"
	"fmt"
	"regexp"
	"strings"

	"github.com/volatiletech/null/v8"
	v1 "k8s.io/api/core/v1"
	"k8s.io/utils/ptr"

	"github.com/akuityio/akuity-platform/controllers/platform/metrics"
	"github.com/akuityio/akuity-platform/controllers/shared/tenant"
	"github.com/akuityio/akuity-platform/internal/argoproj"
	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	"github.com/akuityio/akuity-platform/internal/services/notifications"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

const (
	eventProcessingBatchSize = 100
	ignoreUpdatePrefixHealth = "Updated health status"
	ignoreUpdatePrefixSync   = "Updated sync status"
	defaultArgocdNS          = "argocd"
)

var (
	syncOperationCompletedPattern = regexp.MustCompile(`^(Partial sync|Sync) operation to (\S*) (failed|succeeded).*$`)
	resourceCreatedPattern        = regexp.MustCompile(`^(.*) created (.*)$`)
	argocdResDeletedPattern       = regexp.MustCompile(`^(.*) deleted (.*)$`)
	resourceDeletedPattern        = regexp.MustCompile(`^(.*) deleted resource (.*)/(.*) '(.*)'$`)
	resourcePatchedPattern        = regexp.MustCompile(`^(.*) patched resource (.*)/(.*) '(.*)'$`)
	resourceRanActionPattern      = regexp.MustCompile(`^(.*) ran action (.*) (.*)/(.*)/(.*)$`)
	appUpdatePattern              = regexp.MustCompile(`^(.*) updated application spec$`)
	projectUpdatePattern          = regexp.MustCompile(`^(.*) updated project$`)
	appTerminateUpdatePattern     = regexp.MustCompile(`^(.*) terminated running operation$`)
	appSyncInitiatedPattern       = regexp.MustCompile(`^(.*) initiated (.*)sync (.*)$`)
	appAutoSyncInitiatedPattern   = regexp.MustCompile(`^Initiated automated sync to '(.*)'$`)
	appRollbackInitiatedPattern   = regexp.MustCompile(`^(.*) initiated rollback to (.*)$`)
)

type argocdInstanceEventsReconciler struct {
	tenantStateClient tenant.StateClient
	repoSet           client.RepoSet
	db                *sql.DB
	eventHandlers     []func(instanceID string, event v1.Event)
	metricsRegistry   *metrics.ControllerMetricsRegistry
}

type appsCache struct {
	apps              map[string]*argocd.Application
	tenantStateClient tenant.StateClient
}

func (c *appsCache) Get(ctx context.Context, instanceID, appName, ns string) (*argocd.Application, error) {
	app, ok := c.apps[namespacedAppName(appName, ns)]
	if ok {
		return app, nil
	}
	app, err := c.tenantStateClient.GetApplication(ctx, instanceID, appName, ns)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	c.apps[namespacedAppName(appName, ns)] = app
	return app, nil
}

func NewArgoCDInstanceEventsReconciler(metricsRegistry *metrics.ControllerMetricsRegistry, tenantStateClient tenant.StateClient, settings ControllerSettings) *argocdInstanceEventsReconciler {
	return &argocdInstanceEventsReconciler{
		tenantStateClient: tenantStateClient,
		repoSet:           settings.RepoSet,
		db:                settings.PortalDBRawClient,
		metricsRegistry:   metricsRegistry,
	}
}

func (r *argocdInstanceEventsReconciler) AddEventHandler(f func(instanceID string, event v1.Event)) {
	r.eventHandlers = append(r.eventHandlers, f)
}

func (r *argocdInstanceEventsReconciler) ItemToID(item *models.ArgoCDInstance) string {
	return item.ID
}

func (r *argocdInstanceEventsReconciler) IDColumn() string {
	return models.ArgoCDInstanceTableColumns.ID
}

func (r *argocdInstanceEventsReconciler) LogValuesFromID(id string) []interface{} {
	return []interface{}{"instance_id", id}
}

func (r *argocdInstanceEventsReconciler) LogValuesFromItem(_ *models.ArgoCDInstance) []interface{} {
	return []interface{}{}
}

func keyForOperation(op *models.ArgoCDSyncOperation) string {
	sum := sha256.Sum256(op.Details.JSON)
	sumString := hex.EncodeToString(sum[0:])
	return fmt.Sprintf("%s-%s-%s", op.StartTime.String(), op.EndTime.String(), sumString)
}

func keyForAuditLog(al *models.AuditLog) string {
	sum := sha256.Sum256(al.Details.JSON)
	sumString := hex.EncodeToString(sum[0:])
	return fmt.Sprintf("%s-%s-%s", al.Timestamp.String(), al.Action, sumString)
}

func (r *argocdInstanceEventsReconciler) reconcileEvents(ctx context.Context, instance *models.ArgoCDInstance, recentID int) (int, error) {
	db, txBeginner := database.WithTxBeginner(r.db)
	repoSet := client.NewRepoSet(db)
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := txBeginner.Begin(ctx)
	if err != nil {
		return 0, err
	}

	createdWebhookEvent, err := notifications.ShouldCreateOrgAuditEvent(ctx, instance.OrganizationOwner, repoSet)
	if err != nil {
		return 0, err
	}

	syncOperations, auditLogs, newRecentID, err := r.processEvents(ctx, instance, recentID)
	if err != nil {
		return 0, err
	}

	existingOperations := make(map[string]bool)
	for _, operation := range syncOperations {
		key := keyForOperation(operation)
		if existingOperations[key] {
			continue
		}
		existingOperations[key] = true
		if err := repoSet.ArgoCDSyncOperations().Create(ctx, operation); err != nil {
			return 0, err
		}
	}

	existingAuditLogs := make(map[string]bool)
	for _, log := range auditLogs {
		key := keyForAuditLog(log)
		if existingAuditLogs[key] {
			continue
		}
		existingAuditLogs[key] = true
		if err := notifications.CreateAuditLogWithEvent(ctx, instance.OrganizationOwner, repoSet, log, &createdWebhookEvent); err != nil {
			return 0, err
		}
	}
	return newRecentID, tx.Commit()
}

func (r *argocdInstanceEventsReconciler) Reconcile(ctx context.Context, instance *models.ArgoCDInstance) error {
	processedEventInfo, err := instance.GetRecentProcessedEventInfo()
	if err != nil {
		return err
	}

	newRecentID, processEventsErr := r.reconcileEvents(ctx, instance, processedEventInfo.EventId)
	if processEventsErr != nil {
		processedEventInfo.Error = processEventsErr.Error()
	} else {
		processedEventInfo.Error = ""
		processedEventInfo.EventId = newRecentID
	}

	if err := updateInstanceStatusRecentProcessedEventInfo(context.Background(), instance, r.repoSet, processedEventInfo); err != nil {
		return err
	}

	return processEventsErr
}

func updateInstanceStatusRecentProcessedEventInfo(ctx context.Context, instance *models.ArgoCDInstance, repoSet client.RepoSet, info models.StatusRecentProcessedEventInfo) error {
	if err := instance.SetRecentProcessedEventInfo(info); err != nil {
		return err
	}

	return repoSet.ArgoCDInstances().Update(ctx, instance, models.ArgoCDInstanceColumns.StatusRecentProcessedEventInfo)
}

func (r *argocdInstanceEventsReconciler) processEvents(ctx context.Context, instance *models.ArgoCDInstance, recentID int) ([]*models.ArgoCDSyncOperation, []*models.AuditLog, int, error) {
	var syncOperations []*models.ArgoCDSyncOperation
	var auditLogs []*models.AuditLog
	apps := &appsCache{apps: map[string]*argocd.Application{}, tenantStateClient: r.tenantStateClient}
	for {
		events, err := r.tenantStateClient.ListEventsSinceID(ctx, instance.ID, recentID, eventProcessingBatchSize)
		if err != nil {
			return nil, nil, 0, err
		}
		if len(events) == 0 {
			break
		}

		for _, event := range events {
			for _, handler := range r.eventHandlers {
				handler(instance.ID, event.Event)
			}
			recentID = event.ID

			if event.InvolvedObject.Kind == argoproj.ApplicationKind && event.InvolvedObject.APIVersion == argoproj.APIVersion && event.Reason == argoproj.EventReasonOperationCompleted {
				if operation, err := r.processSyncCompleted(ctx, instance, event, apps); err != nil {
					return nil, nil, 0, err
				} else if operation != nil {
					syncOperations = append(syncOperations, operation)
				}
			} else if event.InvolvedObject.Kind == argoproj.ApplicationKind && event.InvolvedObject.APIVersion == argoproj.APIVersion && event.Reason == argoproj.EventReasonOperationStarted {
				if operation, err := r.processOperationStarted(instance, event); err != nil {
					return nil, nil, 0, err
				} else if operation != nil {
					auditLogs = append(auditLogs, operation)
				}
			} else if (event.InvolvedObject.Kind == argoproj.ApplicationKind || event.InvolvedObject.Kind == argoproj.ProjectKind) && event.InvolvedObject.APIVersion == argoproj.APIVersion && event.Reason == argoproj.EventReasonResourceCreated {
				if operation, err := r.processResourceCreated(instance, event); err != nil {
					return nil, nil, 0, err
				} else if operation != nil {
					auditLogs = append(auditLogs, operation)
				}
			} else if (event.InvolvedObject.Kind == argoproj.ApplicationKind || event.InvolvedObject.Kind == argoproj.ProjectKind) && event.InvolvedObject.APIVersion == argoproj.APIVersion && event.Reason == argoproj.EventReasonResourceUpdated && !strings.HasPrefix(event.Message, ignoreUpdatePrefixHealth) && !strings.HasPrefix(event.Message, ignoreUpdatePrefixSync) {
				if operation, err := r.processResourceUpdated(ctx, instance, event, apps); err != nil {
					return nil, nil, 0, err
				} else if operation != nil {
					auditLogs = append(auditLogs, operation)
				}
			} else if (event.InvolvedObject.Kind == argoproj.ApplicationKind || event.InvolvedObject.Kind == argoproj.ProjectKind) && event.InvolvedObject.APIVersion == argoproj.APIVersion && event.Reason == argoproj.EventReasonResourceDeleted {
				if operation, err := r.processResourceDeleted(ctx, instance, event, apps); err != nil {
					return nil, nil, 0, err
				} else if operation != nil {
					auditLogs = append(auditLogs, operation)
				}
			} else if event.InvolvedObject.Kind == argoproj.ApplicationKind && event.InvolvedObject.APIVersion == argoproj.APIVersion && event.Reason == argoproj.EventReasonResourceActionRan {
				// only needed for app resource
				if operation, err := r.processResourceActionRan(ctx, instance, event, apps); err != nil {
					return nil, nil, 0, err
				} else if operation != nil {
					auditLogs = append(auditLogs, operation)
				}
			}
		}
	}
	return syncOperations, auditLogs, recentID, nil
}

func (r *argocdInstanceEventsReconciler) processSyncCompleted(ctx context.Context, instance *models.ArgoCDInstance, event tenant.Event, apps *appsCache) (*models.ArgoCDSyncOperation, error) {
	operation := &models.ArgoCDSyncOperation{
		InstanceID:      instance.ID,
		ApplicationName: namespacedAppName(event.InvolvedObject.Name, event.InvolvedObject.Namespace),
		StartTime:       event.FirstTimestamp.Time,
		EndTime:         event.FirstTimestamp.Time,
	}

	if matchGroups := syncOperationCompletedPattern.FindStringSubmatch(event.Message); len(matchGroups) == 4 {
		revision := matchGroups[2]
		switch matchGroups[3] {
		case "succeeded":
			operation.ResultPhase = "Succeeded"
		default:
			operation.ResultPhase = "Failed"
		}

		operationDetails := &models.ArgoCDSyncOperationDetails{Revision: revision}

		app, err := apps.Get(ctx, instance.ID, event.InvolvedObject.Name, event.InvolvedObject.Namespace)
		if err != nil {
			return nil, err
		}

		if app != nil {
			operationDetails.Project = app.Spec.Project
			operationDetails.Labels = app.Labels

			if app.Status.OperationState != nil && app.Status.OperationState.Operation.Sync != nil && app.Status.OperationState.Operation.Sync.Revision == revision {
				operationDetails.SyncOptions = app.Status.OperationState.Operation.Sync.SyncOptions
				operationDetails.Prune = app.Status.OperationState.Operation.Sync.Prune
				operationDetails.DryRun = app.Status.OperationState.Operation.Sync.DryRun
				operationDetails.InitiatedBy = models.OperationInitiator{
					Username:  app.Status.OperationState.Operation.InitiatedBy.Username,
					Automated: app.Status.OperationState.Operation.InitiatedBy.Automated,
				}

				if app.Status.OperationState.SyncResult != nil {
					operationDetails.Repository = app.Status.OperationState.SyncResult.Source.RepoURL
				}

				operation.ResultMessage = truncateString(app.Status.OperationState.Message, 500)
				operation.ResultPhase = app.Status.OperationState.Phase
				operation.StartTime = app.Status.OperationState.StartedAt.Time
				if app.Status.OperationState.FinishedAt != nil {
					operation.EndTime = app.Status.OperationState.FinishedAt.Time
				}
			}
		}
		operation.Duration = null.IntFrom(int(operation.EndTime.Sub(operation.StartTime).Seconds()))
		if err := operation.SetDetails(operationDetails); err != nil {
			return nil, err
		}
		if r.metricsRegistry != nil {
			r.metricsRegistry.IncAppSyncCount(operation.ResultPhase)
		}
	} else {
		return nil, nil
	}
	return operation, nil
}

func (r *argocdInstanceEventsReconciler) processResourceCreated(instance *models.ArgoCDInstance, event tenant.Event) (*models.AuditLog, error) {
	auditlog := &models.AuditLog{
		OrganizationID: null.StringFrom(instance.OrganizationOwner),
		Timestamp:      event.LastTimestamp.Time,
		Action:         models.ResourceCreatedAction,
	}
	if err := auditlog.SetDetails(&models.AuditDetails{Message: event.Message}); err != nil {
		return nil, err
	}

	if matchGroups := resourceCreatedPattern.FindStringSubmatch(event.Message); len(matchGroups) == 3 {
		actor := matchGroups[1]
		resource := matchGroups[2]
		if err := auditlog.SetActor(&models.AuditActor{
			ID:   actor,
			Type: models.ArgoCDUserActor,
		}); err != nil {
			return nil, err
		}

		// resource is always project or application for created action
		objectType := models.ArgoCDProjectAuditObject
		res := argoproj.ProjectKind
		name := event.InvolvedObject.Name
		if strings.ToLower(resource) == "application" {
			objectType = models.ArgoCDAppAuditObject
			res = argoproj.ApplicationKind
			name = namespacedAppName(event.InvolvedObject.Name, event.InvolvedObject.Namespace)
		}

		if err := auditlog.SetObject(&models.AuditObject{
			ParentID: &models.AuditParentObjID{Name: instance.Name},
			ID:       &models.AuditObjID{Group: argoproj.APIVersion, Kind: res, Name: name},
			Type:     objectType,
		}); err != nil {
			return nil, err
		}
	} else {
		return nil, nil
	}
	return auditlog, nil
}

func namespacedAppName(name, ns string) string {
	if ns != defaultArgocdNS {
		return fmt.Sprintf("%s/%s", ns, name)
	}
	return name
}

func (r *argocdInstanceEventsReconciler) processResourceUpdated(ctx context.Context, instance *models.ArgoCDInstance, event tenant.Event, apps *appsCache) (*models.AuditLog, error) {
	auditlog := &models.AuditLog{
		OrganizationID: null.StringFrom(instance.OrganizationOwner),
		Timestamp:      event.LastTimestamp.Time,
		Action:         models.ResourceUpdatedAction,
	}
	if err := auditlog.SetDetails(&models.AuditDetails{Message: event.Message}); err != nil {
		return nil, err
	}

	// general k8s resource patch event
	if matchGroups := resourcePatchedPattern.FindStringSubmatch(event.Message); len(matchGroups) == 5 {
		actor := matchGroups[1]
		grp, kind, name := matchGroups[2], matchGroups[3], matchGroups[4]
		if err := auditlog.SetActor(&models.AuditActor{
			ID:   actor,
			Type: models.ArgoCDUserActor,
		}); err != nil {
			return nil, err
		}
		app, err := apps.Get(ctx, instance.ID, event.InvolvedObject.Name, event.InvolvedObject.Namespace)
		if err != nil {
			return nil, err
		}
		if app == nil {
			return nil, nil
		}
		if err := auditlog.SetObject(&models.AuditObject{
			ParentID: &models.AuditParentObjID{Name: argocd.GetNameFromDestination(app.Spec.Destination), ParentName: instance.Name, ApplicationName: namespacedAppName(app.Name, app.Namespace)},
			ID:       &models.AuditObjID{Group: grp, Kind: kind, Name: name},
			Type:     models.K8sResourceAuditObject,
		}); err != nil {
			return nil, err
		}
	} else if matchGroups = appUpdatePattern.FindStringSubmatch(event.Message); len(matchGroups) == 2 {
		// app spec update event
		actor := matchGroups[1]
		if err := auditlog.SetActor(&models.AuditActor{
			ID:   actor,
			Type: models.ArgoCDUserActor,
		}); err != nil {
			return nil, err
		}
		if err := auditlog.SetObject(&models.AuditObject{
			ParentID: &models.AuditParentObjID{Name: instance.Name},
			ID:       &models.AuditObjID{Group: argoproj.APIVersion, Kind: argoproj.ApplicationKind, Name: namespacedAppName(event.InvolvedObject.Name, event.InvolvedObject.Namespace)},
			Type:     models.ArgoCDAppAuditObject,
		}); err != nil {
			return nil, err
		}
	} else if matchGroups = appTerminateUpdatePattern.FindStringSubmatch(event.Message); len(matchGroups) == 2 {
		// app terminated event
		actor := matchGroups[1]
		if err := auditlog.SetActor(&models.AuditActor{
			ID:   actor,
			Type: models.ArgoCDUserActor,
		}); err != nil {
			return nil, err
		}
		if err := auditlog.SetObject(&models.AuditObject{
			ParentID: &models.AuditParentObjID{Name: instance.Name},
			ID:       &models.AuditObjID{Group: argoproj.APIVersion, Kind: argoproj.ApplicationKind, Name: namespacedAppName(event.InvolvedObject.Name, event.InvolvedObject.Namespace)},
			Type:     models.ArgoCDAppAuditObject,
		}); err != nil {
			return nil, err
		}
	} else if matchGroups = projectUpdatePattern.FindStringSubmatch(event.Message); len(matchGroups) == 2 {
		// project update event
		actor := matchGroups[1]
		if err := auditlog.SetActor(&models.AuditActor{
			ID:   actor,
			Type: models.ArgoCDUserActor,
		}); err != nil {
			return nil, err
		}
		if err := auditlog.SetObject(&models.AuditObject{
			ParentID: &models.AuditParentObjID{Name: instance.Name},
			ID:       &models.AuditObjID{Group: argoproj.APIVersion, Kind: argoproj.ProjectKind, Name: event.InvolvedObject.Name},
			Type:     models.ArgoCDProjectAuditObject,
		}); err != nil {
			return nil, err
		}
	} else {
		return nil, nil
	}

	return auditlog, nil
}

func (r *argocdInstanceEventsReconciler) processResourceDeleted(ctx context.Context, instance *models.ArgoCDInstance, event tenant.Event, apps *appsCache) (*models.AuditLog, error) {
	auditlog := &models.AuditLog{
		OrganizationID: null.StringFrom(instance.OrganizationOwner),
		Timestamp:      event.LastTimestamp.Time,
		Action:         models.ResourceDeletedAction,
	}
	if err := auditlog.SetDetails(&models.AuditDetails{Message: event.Message}); err != nil {
		return nil, err
	}

	if matchGroups := resourceDeletedPattern.FindStringSubmatch(event.Message); len(matchGroups) == 5 {
		// general k8s resource deletion event
		actor := matchGroups[1]
		grp, kind, name := matchGroups[2], matchGroups[3], matchGroups[4]
		if err := auditlog.SetActor(&models.AuditActor{
			ID:   actor,
			Type: models.ArgoCDUserActor,
		}); err != nil {
			return nil, err
		}
		app, err := apps.Get(ctx, instance.ID, event.InvolvedObject.Name, event.InvolvedObject.Namespace)
		if err != nil {
			return nil, err
		}
		if app == nil {
			return nil, nil
		}
		if err := auditlog.SetObject(&models.AuditObject{
			ParentID: &models.AuditParentObjID{Name: argocd.GetNameFromDestination(app.Spec.Destination), ParentName: instance.Name, ApplicationName: namespacedAppName(app.Name, app.Namespace)},
			ID:       &models.AuditObjID{Group: grp, Kind: kind, Name: name},
			Type:     models.K8sResourceAuditObject,
		}); err != nil {
			return nil, err
		}
	} else if matchGroups = argocdResDeletedPattern.FindStringSubmatch(event.Message); len(matchGroups) == 3 {
		// project/app deletion event
		actor := matchGroups[1]
		resource := matchGroups[2]
		if err := auditlog.SetActor(&models.AuditActor{
			ID:   actor,
			Type: models.ArgoCDUserActor,
		}); err != nil {
			return nil, err
		}
		objectType := models.ArgoCDProjectAuditObject
		res := argoproj.ProjectKind
		name := event.InvolvedObject.Name
		if strings.ToLower(resource) == "application" {
			objectType = models.ArgoCDAppAuditObject
			res = argoproj.ApplicationKind
			name = namespacedAppName(event.InvolvedObject.Name, event.InvolvedObject.Namespace)
		}
		if err := auditlog.SetObject(&models.AuditObject{
			ParentID: &models.AuditParentObjID{Name: instance.Name},
			ID:       &models.AuditObjID{Group: argoproj.APIVersion, Kind: res, Name: name},
			Type:     objectType,
		}); err != nil {
			return nil, err
		}
	} else {
		return nil, nil
	}
	return auditlog, nil
}

func (r *argocdInstanceEventsReconciler) processResourceActionRan(ctx context.Context, instance *models.ArgoCDInstance, event tenant.Event, apps *appsCache) (*models.AuditLog, error) {
	auditlog := &models.AuditLog{
		OrganizationID: null.StringFrom(instance.OrganizationOwner),
		Timestamp:      event.LastTimestamp.Time,
		Action:         models.ResourceActionRanAction,
	}
	if err := auditlog.SetDetails(&models.AuditDetails{Message: event.Message}); err != nil {
		return nil, err
	}

	// only general resource action-run event
	if matchGroups := resourceRanActionPattern.FindStringSubmatch(event.Message); len(matchGroups) == 6 {
		actor := matchGroups[1]
		if err := auditlog.SetDetails(&models.AuditDetails{Message: event.Message, ActionType: ptr.To(matchGroups[2])}); err != nil {
			return nil, err
		}
		grp, kind, name := matchGroups[3], matchGroups[4], matchGroups[5]
		if err := auditlog.SetActor(&models.AuditActor{
			ID:   actor,
			Type: models.ArgoCDUserActor,
		}); err != nil {
			return nil, err
		}
		app, err := apps.Get(ctx, instance.ID, event.InvolvedObject.Name, event.InvolvedObject.Namespace)
		if err != nil {
			return nil, err
		}
		if app == nil {
			return nil, nil
		}
		if err := auditlog.SetObject(&models.AuditObject{
			ParentID: &models.AuditParentObjID{Name: argocd.GetNameFromDestination(app.Spec.Destination), ParentName: instance.Name, ApplicationName: namespacedAppName(app.Name, app.Namespace)},
			ID:       &models.AuditObjID{Group: grp, Kind: kind, Name: name},
			Type:     models.K8sResourceAuditObject,
		}); err != nil {
			return nil, err
		}
	} else {
		return nil, nil
	}
	return auditlog, nil
}

func (r *argocdInstanceEventsReconciler) processOperationStarted(instance *models.ArgoCDInstance, event tenant.Event) (*models.AuditLog, error) {
	auditlog := &models.AuditLog{
		OrganizationID: null.StringFrom(instance.OrganizationOwner),
		Timestamp:      event.LastTimestamp.Time,
	}
	if err := auditlog.SetDetails(&models.AuditDetails{Message: event.Message}); err != nil {
		return nil, err
	}

	if matchGroups := appSyncInitiatedPattern.FindStringSubmatch(event.Message); len(matchGroups) == 4 {
		// general k8s resource deletion event
		actor := matchGroups[1]
		if err := auditlog.SetActor(&models.AuditActor{
			ID:   actor,
			Type: models.ArgoCDUserActor,
		}); err != nil {
			return nil, err
		}
		auditlog.Action = models.AppSyncStarted
		if err := auditlog.SetObject(&models.AuditObject{
			ParentID: &models.AuditParentObjID{Name: instance.Name},
			ID:       &models.AuditObjID{Group: argoproj.APIVersion, Kind: argoproj.ApplicationKind, Name: namespacedAppName(event.InvolvedObject.Name, event.InvolvedObject.Namespace)},
			Type:     models.ArgoCDAppAuditObject,
		}); err != nil {
			return nil, err
		}
	} else if matchGroups = appAutoSyncInitiatedPattern.FindStringSubmatch(event.Message); len(matchGroups) == 2 {
		if err := auditlog.SetActor(&models.AuditActor{
			ID:   string(models.ArgoCDAutoSyncActor),
			Type: models.ArgoCDAutoSyncActor,
		}); err != nil {
			return nil, err
		}
		auditlog.Action = models.AppSyncStarted
		if err := auditlog.SetObject(&models.AuditObject{
			ParentID: &models.AuditParentObjID{Name: instance.Name},
			ID:       &models.AuditObjID{Group: argoproj.APIVersion, Kind: argoproj.ApplicationKind, Name: namespacedAppName(event.InvolvedObject.Name, event.InvolvedObject.Namespace)},
			Type:     models.ArgoCDAppAuditObject,
		}); err != nil {
			return nil, err
		}
	} else if matchGroups = appRollbackInitiatedPattern.FindStringSubmatch(event.Message); len(matchGroups) == 3 {
		// general k8s resource deletion event
		actor := matchGroups[1]
		if err := auditlog.SetActor(&models.AuditActor{
			ID:   actor,
			Type: models.ArgoCDUserActor,
		}); err != nil {
			return nil, err
		}
		auditlog.Action = models.AppRollbackStarted
		if err := auditlog.SetObject(&models.AuditObject{
			ParentID: &models.AuditParentObjID{Name: instance.Name},
			ID:       &models.AuditObjID{Group: argoproj.APIVersion, Kind: argoproj.ApplicationKind, Name: namespacedAppName(event.InvolvedObject.Name, event.InvolvedObject.Namespace)},
			Type:     models.ArgoCDAppAuditObject,
		}); err != nil {
			return nil, err
		}
	} else {
		return nil, nil
	}
	return auditlog, nil
}
