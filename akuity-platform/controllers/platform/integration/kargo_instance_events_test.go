package integration

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/akuityio/akuity-platform/controllers/shared/tenant"
	"github.com/akuityio/akuity-platform/internal/kargo"
	"github.com/akuityio/akuity-platform/models/models"
)

func TestProcessPromotion(t *testing.T) {
	r := kargoInstanceEventsReconciler{}
	_, operation, err := r.processPromotion(context.Background(),
		&models.KargoInstance{ID: "abc"},
		tenant.Event{Event: v1.Event{
			Message:        "Promotion to abc succeeded",
			InvolvedObject: v1.ObjectReference{Name: "app1"},
			Reason:         kargo.EventReasonPromotionSucceeded,
			ObjectMeta: metav1.ObjectMeta{
				Annotations: map[string]string{
					kargo.AnnotationKeyEventPromotionCreateTime: time.Now().Format(time.RFC3339),
				},
			},
		}})
	require.NoError(t, err)
	assert.Equal(t, "Succeeded", operation.ResultPhase)

	details, err := operation.GetDetails()
	require.NoError(t, err)
	assert.Equal(t, "PromotionSucceeded", details.PromotionStatus)
}

func TestProcessFreight(t *testing.T) {
	r := kargoInstanceEventsReconciler{}
	operation, err := r.processFreight(context.Background(),
		&models.KargoInstance{ID: "abc"},
		tenant.Event{Event: v1.Event{
			Message:        "Freight to abc succeeded",
			InvolvedObject: v1.ObjectReference{Name: "app1"},
			Reason:         kargo.EventReasonFreightVerificationPrefix,
			ObjectMeta: metav1.ObjectMeta{
				Annotations: map[string]string{
					kargo.AnnotationKeyEventFreightCreateTime: time.Now().Format(time.RFC3339),
					kargo.AnnotationKeyEventActor:             "test",
				},
			},
		}})
	require.NoError(t, err)
	actor, err := operation.GetActor()
	require.NoError(t, err)
	assert.Equal(t, "test", actor.ID)
	assert.Equal(t, operation.Action, "verification")
}
