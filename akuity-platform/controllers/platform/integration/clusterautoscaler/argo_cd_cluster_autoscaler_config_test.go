package clusterautoscaler_test

import (
	"errors"
	"fmt"
	"maps"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes/fake"
	fakecorev1 "k8s.io/client-go/kubernetes/typed/core/v1/fake"
	k8stesting "k8s.io/client-go/testing"

	"github.com/akuityio/akuity-platform/controllers/platform/integration/clusterautoscaler"
)

func TestNewConfig(t *testing.T) {
	t.Run("logger is nil", func(t *testing.T) {
		_, err := clusterautoscaler.NewConfig(nil, nil)
		require.Error(t, err)
		assert.Equal(t, "failed to build Cluster Autoscaler config, logger cannot be nil", err.Error())
	})

	t.Run("k8s client is nil", func(t *testing.T) {
		_, err := clusterautoscaler.NewConfig(log, nil)
		require.Error(t, err)
		assert.Equal(t, "failed to build Cluster Autoscaler config, k8s client cannot be nil", err.Error())
	})

	t.Run("configmap doesn't exist", func(t *testing.T) {
		k8sClient := fake.NewSimpleClientset()

		_, err := clusterautoscaler.NewConfig(log, k8sClient)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "failed to read Cluster Autoscaler ConfigMap")
	})

	t.Run("error getting configmap", func(t *testing.T) {
		k8sClient := fake.NewSimpleClientset()
		k8sClient.CoreV1().(*fakecorev1.FakeCoreV1).PrependReactor("get", "configmaps", func(action k8stesting.Action) (handled bool, ret runtime.Object, err error) {
			return true, &v1.ConfigMap{}, errors.New("error getting configmap")
		})

		_, err := clusterautoscaler.NewConfig(log, k8sClient)
		require.Error(t, err)
		assert.Equal(t, "failed to read Cluster Autoscaler ConfigMap: error getting configmap", err.Error())
	})

	t.Run("config built successfully", func(t *testing.T) {
		k8sClient := k8sClientWithConfigMap(validConfigMap)

		_, err := clusterautoscaler.NewConfig(log, k8sClient)
		require.NoError(t, err)
	})
}

func TestInvalidConfigMapData(t *testing.T) {
	t.Run("app controller min cpu setting missing", func(t *testing.T) {
		cm := maps.Clone[map[string]string](validConfigMap)
		delete(cm, clusterautoscaler.AppCtrlrMinCpuKey)
		k8sClient := k8sClientWithConfigMap(cm)

		_, err := clusterautoscaler.NewConfig(log, k8sClient)
		require.Error(t, err)
		assert.Equal(t, fmt.Sprintf("%s must be set in the Cluster Autoscaler configmap", clusterautoscaler.AppCtrlrMinCpuKey), err.Error())
	})

	t.Run("app controller min cpu setting invalid", func(t *testing.T) {
		cm := maps.Clone[map[string]string](validConfigMap)
		cm[clusterautoscaler.AppCtrlrMinCpuKey] = "invalid"
		k8sClient := k8sClientWithConfigMap(cm)

		_, err := clusterautoscaler.NewConfig(log, k8sClient)
		require.Error(t, err)
		assert.Equal(t, fmt.Sprintf("%s in the Cluster Autoscaler configmap must be a valid CPU quantity", clusterautoscaler.AppCtrlrMinCpuKey), err.Error())
	})

	t.Run("app controller min memory setting missing", func(t *testing.T) {
		cm := maps.Clone[map[string]string](validConfigMap)
		delete(cm, clusterautoscaler.AppCtrlrMinMemKey)
		k8sClient := k8sClientWithConfigMap(cm)

		_, err := clusterautoscaler.NewConfig(log, k8sClient)
		require.Error(t, err)
		assert.Equal(t, fmt.Sprintf("%s must be set in the Cluster Autoscaler configmap", clusterautoscaler.AppCtrlrMinMemKey), err.Error())
	})

	t.Run("app controller min memory setting invalid", func(t *testing.T) {
		cm := maps.Clone[map[string]string](validConfigMap)
		cm[clusterautoscaler.AppCtrlrMinMemKey] = "invalid"
		k8sClient := k8sClientWithConfigMap(cm)

		_, err := clusterautoscaler.NewConfig(log, k8sClient)
		require.Error(t, err)
		assert.Equal(t, fmt.Sprintf("%s in the Cluster Autoscaler configmap must be a valid Memory quantity", clusterautoscaler.AppCtrlrMinMemKey), err.Error())
	})

	t.Run("app controller multiplier cpu setting missing", func(t *testing.T) {
		cm := maps.Clone[map[string]string](validConfigMap)
		delete(cm, clusterautoscaler.AppCtrlrMultiplierCpuKey)
		k8sClient := k8sClientWithConfigMap(cm)

		_, err := clusterautoscaler.NewConfig(log, k8sClient)
		require.Error(t, err)
		assert.Equal(t, fmt.Sprintf("%s must be set in the Cluster Autoscaler configmap", clusterautoscaler.AppCtrlrMultiplierCpuKey), err.Error())
	})

	t.Run("app controller multiplier cpu setting invalid", func(t *testing.T) {
		cm := maps.Clone[map[string]string](validConfigMap)
		cm[clusterautoscaler.AppCtrlrMultiplierCpuKey] = "invalid"
		k8sClient := k8sClientWithConfigMap(cm)

		_, err := clusterautoscaler.NewConfig(log, k8sClient)
		require.Error(t, err)
		assert.Equal(t, fmt.Sprintf("%s in the Cluster Autoscaler configmap must be a number", clusterautoscaler.AppCtrlrMultiplierCpuKey), err.Error())
	})

	t.Run("app controller multiplier memory setting missing", func(t *testing.T) {
		cm := maps.Clone[map[string]string](validConfigMap)
		delete(cm, clusterautoscaler.AppCtrlrMultiplierMemKey)
		k8sClient := k8sClientWithConfigMap(cm)

		_, err := clusterautoscaler.NewConfig(log, k8sClient)
		require.Error(t, err)
		assert.Equal(t, fmt.Sprintf("%s must be set in the Cluster Autoscaler configmap", clusterautoscaler.AppCtrlrMultiplierMemKey), err.Error())
	})

	t.Run("app controller multiplier memory setting invalid", func(t *testing.T) {
		cm := maps.Clone[map[string]string](validConfigMap)
		cm[clusterautoscaler.AppCtrlrMultiplierMemKey] = "invalid"
		k8sClient := k8sClientWithConfigMap(cm)

		_, err := clusterautoscaler.NewConfig(log, k8sClient)
		require.Error(t, err)
		assert.Equal(t, fmt.Sprintf("%s in the Cluster Autoscaler configmap must be a number", clusterautoscaler.AppCtrlrMultiplierMemKey), err.Error())
	})
}
