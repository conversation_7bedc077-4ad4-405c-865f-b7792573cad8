package clusterautoscaler_test

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes/fake"
	fakecorev1 "k8s.io/client-go/kubernetes/typed/core/v1/fake"
	k8stesting "k8s.io/client-go/testing"

	"github.com/akuityio/akuity-platform/controllers/platform/integration/clusterautoscaler"
	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	fakereposet "github.com/akuityio/akuity-platform/models/client/testing"
	"github.com/akuityio/akuity-platform/models/models"
)

func TestNewAutoscaler(t *testing.T) {
	k8sClient := k8sClientWithConfigMap(validConfigMap)
	autoscalerConfig := autoscalerConfig()

	t.Run("logger is nil", func(t *testing.T) {
		_, err := clusterautoscaler.NewAutoscaler(nil, autoscalerConfig, nil, nil)
		require.Error(t, err)
		assert.Equal(t, "failed to build Cluster Autoscaler, logger cannot be nil", err.Error())
	})

	t.Run("k8s client is nil", func(t *testing.T) {
		_, err := clusterautoscaler.NewAutoscaler(log, autoscalerConfig, nil, nil)
		require.Error(t, err)
		assert.Equal(t, "failed to build Cluster Autoscaler, k8sClient cannot be nil", err.Error())
	})

	t.Run("repo set is nil", func(t *testing.T) {
		_, err := clusterautoscaler.NewAutoscaler(log, autoscalerConfig, k8sClient, nil)
		require.Error(t, err)
		assert.Equal(t, "failed to build Cluster Autoscaler, repoSet cannot be nil", err.Error())
	})

	t.Run("autoscaler built successfully", func(t *testing.T) {
		_, err := clusterautoscaler.NewAutoscaler(log, autoscalerConfig, k8sClient, fakereposet.NewInMemoryRepoSet())
		require.NoError(t, err)
	})
}

func TestAutoscaleCluster(t *testing.T) {
	k8sClient := k8sClientWithConfigMap(validConfigMap)
	autoscalerConfig := autoscalerConfig()
	scaler := autoscaler(autoscalerConfig, k8sClient)

	t.Run("cluster is empty", func(t *testing.T) {
		scaled, err := scaler.AutoscaleCluster(context.TODO(), nil, nil)
		require.Error(t, err)
		assert.False(t, scaled)
		assert.Equal(t, "cluster cannot be empty", err.Error())
	})

	t.Run("cannot get cluster spec", func(t *testing.T) {
		cluster := &models.ArgoCDCluster{ID: clusterID, Spec: null.JSONFrom([]byte("{'&A&"))}
		scaled, err := scaler.AutoscaleCluster(context.TODO(), cluster, nil)
		require.Error(t, err)
		assert.False(t, scaled)
		assert.True(t, strings.Contains(err.Error(), fmt.Sprintf("failed to get cluster %q spec:", clusterID)))
	})

	t.Run("does not scale if cluster size is not auto", func(t *testing.T) {
		clusterSpec, err := json.Marshal(&models.ClusterSpec{Size: models.ClusterSizeLarge})
		require.NoError(t, err)

		cluster := &models.ArgoCDCluster{Spec: null.JSONFrom(clusterSpec)}

		scaled, err := scaler.AutoscaleCluster(context.TODO(), cluster, nil)
		require.NoError(t, err)
		assert.False(t, scaled)
	})

	t.Run("does not scale if auto upgrade is disabled", func(t *testing.T) {
		clusterSpec, err := json.Marshal(&models.ClusterSpec{Size: models.ClusterSizeAuto})
		require.NoError(t, err)

		cluster := &models.ArgoCDCluster{
			Spec:                null.JSONFrom(clusterSpec),
			AutoUpgradeDisabled: true,
		}

		scaled, err := scaler.AutoscaleCluster(context.TODO(), cluster, nil)
		require.NoError(t, err)
		assert.False(t, scaled)
	})

	t.Run("cannot get internal cluster spec", func(t *testing.T) {
		clusterSpec, err := json.Marshal(&models.ClusterSpec{Size: models.ClusterSizeAuto})
		require.NoError(t, err)

		cluster := &models.ArgoCDCluster{
			ID:           clusterID,
			Spec:         null.JSONFrom(clusterSpec),
			InternalSpec: null.JSONFrom([]byte("{'&A&")),
		}

		scaled, err := scaler.AutoscaleCluster(context.TODO(), cluster, nil)
		require.Error(t, err)
		assert.False(t, scaled)
		assert.True(t, strings.Contains(err.Error(), fmt.Sprintf("failed to get cluster %q internal spec:", clusterID)))
	})

	t.Run("return false and no error when app controller resources do not need to be scaled", func(t *testing.T) {
		clusterSpec, err := json.Marshal(&models.ClusterSpec{Size: models.ClusterSizeAuto})
		require.NoError(t, err)

		// current app controller resources are the minimums
		appControllerResources := corev1.ResourceRequirements{
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    *resource.NewMilliQuantity(int64(autoscalerConfig.ApplicationController.ResourceMinimum.CPU.AsApproximateFloat64()*1000), resource.BinarySI),
				corev1.ResourceMemory: *resource.NewQuantity(int64(autoscalerConfig.ApplicationController.ResourceMinimum.Memory.AsApproximateFloat64()), resource.BinarySI),
			},
			Limits: corev1.ResourceList{
				corev1.ResourceMemory: *resource.NewQuantity(int64(autoscalerConfig.ApplicationController.ResourceMinimum.Memory.AsApproximateFloat64()), resource.BinarySI),
			},
		}

		// cluster internal spec stores the current app controller resources
		clusterData := &clusterautoscaler.ArgocdClusterData{}
		clusterData.InternalSpec = &models.ClusterInternalSpec{}
		clusterData.InternalSpec.AutoAgentResources = &models.AgentResources{
			ControllerRequirements: appControllerResources,
		}

		cluster := &models.ArgoCDCluster{
			ID:   clusterID,
			Spec: null.JSONFrom(clusterSpec),
		}

		err = cluster.SetInternalSpec(*clusterData.InternalSpec)
		require.NoError(t, err)

		// The stubbed fakeGetNumberOfManagedResourcesFunc will result in the same minimum resources as
		// above being calculated for the app controller. Therefore, resources will not be changed.
		scaled, err := scaler.AutoscaleCluster(context.TODO(), cluster, fakeGetClusterInfoFunc)
		require.NoError(t, err)
		assert.False(t, scaled)
	})

	t.Run("return true and no error when app controller resources need to be scaled", func(t *testing.T) {
		clusterSpec, err := json.Marshal(&models.ClusterSpec{SizeVersion: 1, Size: models.ClusterSizeAuto})
		require.NoError(t, err)

		// current app controller resources are the minimums
		appControllerResources := corev1.ResourceRequirements{
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    *resource.NewMilliQuantity(int64(autoscalerConfig.ApplicationController.ResourceMinimum.CPU.AsApproximateFloat64()*1000), resource.BinarySI),
				corev1.ResourceMemory: *resource.NewQuantity(int64(autoscalerConfig.ApplicationController.ResourceMinimum.Memory.AsApproximateFloat64()), resource.BinarySI),
			},
			Limits: corev1.ResourceList{
				corev1.ResourceMemory: *resource.NewQuantity(int64(autoscalerConfig.ApplicationController.ResourceMinimum.Memory.AsApproximateFloat64()), resource.BinarySI),
			},
		}

		// cluster internal spec stores the current app controller resources
		clusterData := &clusterautoscaler.ArgocdClusterData{}
		clusterData.InternalSpec = &models.ClusterInternalSpec{}
		clusterData.InternalSpec.AutoAgentResources = &models.AgentResources{
			ControllerRequirements: appControllerResources,
		}

		cluster := &models.ArgoCDCluster{
			ID:   clusterID,
			Spec: null.JSONFrom(clusterSpec),
		}

		err = cluster.SetInternalSpec(*clusterData.InternalSpec)
		require.NoError(t, err)

		// The stubbed fakeGetVeryLargeNumberOfManagedResourcesFunc will result in the larger resources than
		// the above current resources being calculated for the app controller. Therefore, resources will be changed.
		scaled, err := scaler.AutoscaleCluster(context.TODO(), cluster, fakeGetVeryLargeNumberOfManagedResourcesFunc)
		require.NoError(t, err)
		assert.True(t, scaled)
	})
}

func TestBuildApplicationControllerResources(t *testing.T) {
	k8sClient := k8sClientWithConfigMap(validConfigMap)
	autoscalerConfig := autoscalerConfig()
	scaler := autoscaler(autoscalerConfig, k8sClient)
	ctx := context.Background()

	t.Run("uses min memory and cpu when calculated resources are smaller than min", func(t *testing.T) {
		expectedResources := corev1.ResourceRequirements{
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    *resource.NewMilliQuantity(int64(autoscalerConfig.ApplicationController.ResourceMinimum.CPU.AsApproximateFloat64()*1000), resource.BinarySI),
				corev1.ResourceMemory: *resource.NewQuantity(int64(autoscalerConfig.ApplicationController.ResourceMinimum.Memory.AsApproximateFloat64()), resource.BinarySI),
			},
			Limits: corev1.ResourceList{
				corev1.ResourceMemory: *resource.NewQuantity(int64(autoscalerConfig.ApplicationController.ResourceMinimum.Memory.AsApproximateFloat64()), resource.BinarySI),
			},
		}

		changed, resources, err := scaler.BuildApplicationControllerResources(ctx, &models.ArgoCDCluster{}, corev1.ResourceRequirements{}, fakeGetClusterInfoFunc)
		require.NoError(t, err)
		assert.True(t, changed)
		assert.Equal(t, expectedResources, resources)
	})

	t.Run("uses calculated resources when they are larger than the min resources", func(t *testing.T) {
		cpu := largeNumberOfManagedResources * autoscalerConfig.ApplicationController.ResourceMultiplier.CPU
		memory := largeNumberOfManagedResources * autoscalerConfig.ApplicationController.ResourceMultiplier.Memory * clusterautoscaler.BytesPerMebibyte

		expectedResources := corev1.ResourceRequirements{
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    *resource.NewMilliQuantity(int64(cpu), resource.BinarySI),
				corev1.ResourceMemory: *resource.NewQuantity(int64(memory), resource.BinarySI),
			},
			Limits: corev1.ResourceList{
				corev1.ResourceMemory: *resource.NewQuantity(int64(memory), resource.BinarySI),
			},
		}

		changed, resources, err := scaler.BuildApplicationControllerResources(ctx, &models.ArgoCDCluster{}, corev1.ResourceRequirements{}, fakeGetVeryLargeNumberOfManagedResourcesFunc)
		require.NoError(t, err)
		assert.True(t, changed)
		assert.Equal(t, expectedResources, resources)
	})

	t.Run("use new resources when they are greater than the current resources", func(t *testing.T) {
		currentResources := corev1.ResourceRequirements{
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    *resource.NewMilliQuantity(int64(1), resource.BinarySI),
				corev1.ResourceMemory: *resource.NewQuantity(int64(1), resource.BinarySI),
			},
			Limits: corev1.ResourceList{
				corev1.ResourceMemory: *resource.NewQuantity(int64(1), resource.BinarySI),
			},
		}

		cpu := largeNumberOfManagedResources * autoscalerConfig.ApplicationController.ResourceMultiplier.CPU
		memory := largeNumberOfManagedResources * autoscalerConfig.ApplicationController.ResourceMultiplier.Memory * clusterautoscaler.BytesPerMebibyte
		expectedResources := corev1.ResourceRequirements{
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    *resource.NewMilliQuantity(int64(cpu), resource.BinarySI),
				corev1.ResourceMemory: *resource.NewQuantity(int64(memory), resource.BinarySI),
			},
			Limits: corev1.ResourceList{
				corev1.ResourceMemory: *resource.NewQuantity(int64(memory), resource.BinarySI),
			},
		}

		changed, newResources, err := scaler.BuildApplicationControllerResources(ctx, &models.ArgoCDCluster{}, currentResources, fakeGetVeryLargeNumberOfManagedResourcesFunc)
		require.NoError(t, err)
		assert.True(t, changed)
		assert.Equal(t, expectedResources, newResources)
	})

	t.Run("uses current resources when new resources are smaller but not smaller than the scale down threshold", func(t *testing.T) {
		cpu := largeNumberOfManagedResources * autoscalerConfig.ApplicationController.ResourceMultiplier.CPU
		memory := largeNumberOfManagedResources * autoscalerConfig.ApplicationController.ResourceMultiplier.Memory * clusterautoscaler.BytesPerMebibyte

		currentResources := corev1.ResourceRequirements{
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    *resource.NewMilliQuantity(int64(cpu*(2-clusterautoscaler.ScaleDownThreshold)), resource.BinarySI),
				corev1.ResourceMemory: *resource.NewQuantity(int64(memory*(2-clusterautoscaler.ScaleDownThreshold)), resource.BinarySI),
			},
			Limits: corev1.ResourceList{
				corev1.ResourceMemory: *resource.NewQuantity(int64(memory*(2-clusterautoscaler.ScaleDownThreshold)), resource.BinarySI),
			},
		}

		changed, newResources, err := scaler.BuildApplicationControllerResources(ctx, &models.ArgoCDCluster{}, currentResources, fakeGetVeryLargeNumberOfManagedResourcesFunc)
		require.NoError(t, err)
		assert.False(t, changed)
		assert.Equal(t, currentResources, newResources)
	})

	t.Run("uses new resources when new resources are smaller and smaller than the scale down threshold", func(t *testing.T) {
		cpu := largeNumberOfManagedResources * autoscalerConfig.ApplicationController.ResourceMultiplier.CPU
		memory := largeNumberOfManagedResources * autoscalerConfig.ApplicationController.ResourceMultiplier.Memory * clusterautoscaler.BytesPerMebibyte

		expectedResources := corev1.ResourceRequirements{
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    *resource.NewMilliQuantity(int64(cpu), resource.BinarySI),
				corev1.ResourceMemory: *resource.NewQuantity(int64(memory), resource.BinarySI),
			},
			Limits: corev1.ResourceList{
				corev1.ResourceMemory: *resource.NewQuantity(int64(memory), resource.BinarySI),
			},
		}

		currentResources := corev1.ResourceRequirements{
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    *resource.NewMilliQuantity(int64(cpu*(2-clusterautoscaler.ScaleDownThreshold+0.1)), resource.BinarySI),
				corev1.ResourceMemory: *resource.NewQuantity(int64(memory*(2-clusterautoscaler.ScaleDownThreshold+0.1)), resource.BinarySI),
			},
			Limits: corev1.ResourceList{
				corev1.ResourceMemory: *resource.NewQuantity(int64(memory*(2-clusterautoscaler.ScaleDownThreshold+0.1)), resource.BinarySI),
			},
		}

		changed, newResources, err := scaler.BuildApplicationControllerResources(ctx, &models.ArgoCDCluster{}, currentResources, fakeGetVeryLargeNumberOfManagedResourcesFunc)
		require.NoError(t, err)
		assert.True(t, changed)
		assert.Equal(t, expectedResources, newResources)
	})
}

func TestGetNumberOfManagedResourceObjects(t *testing.T) {
	autoscalerConfig := autoscalerConfig()
	ctx := context.Background()

	t.Run("error getting secret", func(t *testing.T) {
		k8sClient := fake.NewSimpleClientset()
		k8sClient.CoreV1().(*fakecorev1.FakeCoreV1).PrependReactor("get", "secrets", func(action k8stesting.Action) (handled bool, ret runtime.Object, err error) {
			return true, &corev1.Secret{}, errors.New("error getting secret")
		})
		scaler := autoscaler(autoscalerConfig, k8sClient)

		_, err := scaler.GetClusterInfo(ctx, &models.ArgoCDCluster{}, fakeGetClusterInfoFromCacheFunc)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "k8s API returned an error")
	})

	t.Run("secret not found", func(t *testing.T) {
		k8sClient := fake.NewSimpleClientset()
		scaler := autoscaler(autoscalerConfig, k8sClient)

		_, err := scaler.GetClusterInfo(ctx, &models.ArgoCDCluster{}, fakeGetClusterInfoFromCacheFunc)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "k8s API returned an error")
	})

	t.Run("error getting secret", func(t *testing.T) {
		k8sClient := fake.NewSimpleClientset()
		k8sClient.CoreV1().(*fakecorev1.FakeCoreV1).PrependReactor("get", "secrets", func(action k8stesting.Action) (handled bool, ret runtime.Object, err error) {
			return true, &corev1.Secret{}, errors.New("error getting secret")
		})
		scaler := autoscaler(autoscalerConfig, k8sClient)

		_, err := scaler.GetClusterInfo(ctx, &models.ArgoCDCluster{}, fakeGetClusterInfoFromCacheFunc)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "k8s API returned an error")
	})

	t.Run("secret does not contain expected key", func(t *testing.T) {
		k8sClient := fake.NewSimpleClientset()
		k8sClient.CoreV1().(*fakecorev1.FakeCoreV1).PrependReactor("get", "secrets", func(action k8stesting.Action) (handled bool, ret runtime.Object, err error) {
			return true, &corev1.Secret{}, nil
		})
		scaler := autoscaler(autoscalerConfig, k8sClient)

		_, err := scaler.GetClusterInfo(ctx, &models.ArgoCDCluster{}, fakeGetClusterInfoFromCacheFunc)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "expected key missing")
	})

	t.Run("gets number of managed resources successfully", func(t *testing.T) {
		k8sClient := fake.NewSimpleClientset()
		k8sClient.CoreV1().(*fakecorev1.FakeCoreV1).PrependReactor("get", "secrets", func(action k8stesting.Action) (handled bool, ret runtime.Object, err error) {
			return true, &corev1.Secret{
				Data: map[string][]byte{
					clusterautoscaler.RedisPasswordSecretKey: []byte("fake"),
				},
			}, nil
		})
		scaler := autoscaler(autoscalerConfig, k8sClient)

		clusterInfo, err := scaler.GetClusterInfo(ctx, &models.ArgoCDCluster{}, fakeGetClusterInfoFromCacheFunc)
		require.NoError(t, err)
		assert.Equal(t, int64(10), clusterInfo.CacheInfo.ResourcesCount)
	})
}

func fakeGetClusterInfoFromCacheFunc(ctx context.Context, password string, cluster *models.ArgoCDCluster) (argocd.ClusterInfo, error) {
	return argocd.ClusterInfo{
		CacheInfo: argocd.ClusterCacheInfo{ResourcesCount: 10},
		ConnectionState: argocd.ConnectionState{
			Status: argocd.ConnectionStatusSuccessful,
		},
	}, nil
}

func fakeGetClusterInfoFunc(ctx context.Context, cluster *models.ArgoCDCluster, getClustreInfoFromRedis clusterautoscaler.GetClusterInfoFromRedisFunc) (argocd.ClusterInfo, error) {
	return argocd.ClusterInfo{
		CacheInfo: argocd.ClusterCacheInfo{ResourcesCount: 10},
		ConnectionState: argocd.ConnectionState{
			Status: argocd.ConnectionStatusSuccessful,
		},
	}, nil
}

func fakeGetVeryLargeNumberOfManagedResourcesFunc(ctx context.Context, cluster *models.ArgoCDCluster, getclustreInfoFromRedis clusterautoscaler.GetClusterInfoFromRedisFunc) (argocd.ClusterInfo, error) {
	return argocd.ClusterInfo{
		CacheInfo: argocd.ClusterCacheInfo{ResourcesCount: int64(largeNumberOfManagedResources)},
		ConnectionState: argocd.ConnectionState{
			Status: argocd.ConnectionStatusSuccessful,
		},
	}, nil
}
