package clusterautoscaler_test

import (
	"github.com/go-logr/logr"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/fake"
	fakecorev1 "k8s.io/client-go/kubernetes/typed/core/v1/fake"
	k8stesting "k8s.io/client-go/testing"

	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/controllers/platform/integration/clusterautoscaler"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	fakereposet "github.com/akuityio/akuity-platform/models/client/testing"
)

var (
	log            *logr.Logger
	clusterID      = "foo"
	validConfigMap = map[string]string{
		"application-controller.resources.minimum.cpu":       "0.05",
		"application-controller.resources.minimum.memory":    "100Mi",
		"application-controller.resources.maximum.cpu":       "6.0",
		"application-controller.resources.maximum.memory":    "16Gi",
		"application-controller.resources.multiplier.cpu":    "0.04",
		"application-controller.resources.multiplier.memory": "0.1",
		"repo-server.resources.minimum.cpu":                  "250m",
		"repo-server.resources.minimum.memory":               "256Mi",
		"repo-server.resources.maximum.cpu":                  "4.0",
		"repo-server.resources.maximum.memory":               "6Gi",
		"repo-server.resources.multiplier.cpu":               "1.5",
		"repo-server.resources.multiplier.memory":            "1.5",
		"repo-server.replicas.inc-threshold":                 "10",
		"repo-server.replicas.minimum":                       "1",
		"repo-server.replicas.maximum":                       "10",
		"repo-server.resources.inc-threshold":                "80",
		"repo-server.resources.dec-threshold":                "30",
	}
	largeNumberOfManagedResources float64 = 140000
)

func init() {
	logger, err := logging.NewLogger(
		logging.WithDebug(),
	)
	if err != nil {
		panic("could not init logger for tests")
	}
	l := logr.New(logger.GetSink())
	log = &l
}

func k8sClientWithConfigMap(configMapData map[string]string) kubernetes.Interface {
	k8sClient := fake.NewSimpleClientset()
	k8sClient.CoreV1().(*fakecorev1.FakeCoreV1).PrependReactor("get", "configmaps", func(action k8stesting.Action) (handled bool, ret runtime.Object, err error) {
		return true, &v1.ConfigMap{Data: configMapData}, nil
	})
	return k8sClient
}

func autoscalerConfig() common.AutoScalerConfig {
	k8sClient := k8sClientWithConfigMap(validConfigMap)
	autoscalerConfig, err := clusterautoscaler.NewConfig(log, k8sClient)
	if err != nil {
		panic(err)
	}

	return autoscalerConfig
}

func autoscaler(config common.AutoScalerConfig, k8sClient kubernetes.Interface) clusterautoscaler.Autoscaler {
	scaler, err := clusterautoscaler.NewAutoscaler(log, config, k8sClient, fakereposet.NewInMemoryRepoSet())
	if err != nil {
		panic(err)
	}

	return scaler
}
