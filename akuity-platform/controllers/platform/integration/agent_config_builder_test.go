package integration

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gopkg.in/yaml.v3"
	"k8s.io/utils/ptr"

	"github.com/akuityio/agent/pkg/client/apis/controlplane"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/license"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/io"
	"github.com/akuityio/akuity-platform/models/models"

	_ "embed"
)

func TestSharedK3sDBConnectionAuth(t *testing.T) {
	t.Run("SharedK3sDBConnectionAuthFalse", func(t *testing.T) {
		reconciler, builder := newTestBuilder(t, false)
		defer io.Close(reconciler)

		configurations, err := builder.buildAgentConfigurations(context.Background(), 1)
		require.NoError(t, err)

		postgres := configurations.K3s.Postgres
		assert.Equal(t, "localhost", postgres.GetHostname())
		assert.Equal(t, "instance_"+testInstanceId, postgres.GetUsername())
		assert.Equal(t, "my-password", postgres.GetPassword())
	})

	t.Run("SharedK3sDBConnectionAuthTrue", func(t *testing.T) {
		reconciler, builder := newTestBuilder(t, true)
		defer io.Close(reconciler)

		configurations, err := builder.buildAgentConfigurations(context.Background(), 1)
		require.NoError(t, err)

		postgres := configurations.K3s.Postgres
		assert.Equal(t, "localhost", postgres.GetHostname())
		assert.Equal(t, "postgres", postgres.GetUsername())
		assert.Equal(t, "dbpassword", postgres.GetPassword())
	})
}

func TestGenerateCustomizationForCrossplaneCustomResources(t *testing.T) {
	customizations := generateCustomizationForCrossplaneCustomResources(models.CrossplaneExtension{
		Resources: []models.CrossplaneExtensionResource{},
	}, []models.ResourceCustomization{})

	require.Empty(t, customizations)

	ext := models.CrossplaneExtension{
		Resources: []models.CrossplaneExtensionResource{
			{
				Group: "*.crossplane.io/*",
			},
		},
	}

	customizations = generateCustomizationForCrossplaneCustomResources(ext, customizations)
	require.Equal(t, []models.ResourceCustomization{
		{
			Group:  "*.crossplane.io",
			Kind:   "*",
			Health: crossplaneCustomization,
		},
	}, customizations)

	ext = models.CrossplaneExtension{
		Resources: []models.CrossplaneExtensionResource{
			{
				Group: "*.crossplane.io/*",
			},
			{
				Group: "*.akuity.io/*",
			},
		},
	}

	customizations = generateCustomizationForCrossplaneCustomResources(ext, customizations)
	require.Equal(t, []models.ResourceCustomization{
		{
			Group:  "*.crossplane.io",
			Kind:   "*",
			Health: crossplaneCustomization,
		},
		{
			Group:  "*.akuity.io",
			Kind:   "*",
			Health: crossplaneCustomization,
		},
	}, customizations)
}

func TestCrossplaneResourceCustomization(t *testing.T) {
	rec, builder := newTestBuilder(t, false)
	defer io.Close(rec)

	require.NoError(t, builder.instanceConfig.SetSpec(models.InstanceConfigSpec{
		CrossplaneExtension: models.CrossplaneExtension{
			Resources: []models.CrossplaneExtensionResource{
				{
					Group: "*.crossplane.io/*",
				},
				{
					Group: "*.akuity.io/*",
				},
			},
		},
	}))

	dv, err := builder.buildAgentConfigurations(context.Background(), 1)
	require.NoError(t, err)

	cm := dv.Config.ArgoCd.(map[string]interface{})
	method, ok := cm[resourceTrackingMethod]

	require.True(t, ok)
	require.Equal(t, method.(string), "annotation")

	customizations, ok := cm[deprecatedResourceCustomizationAttributeInArgocdConfigMap]

	require.True(t, ok)
	require.Equal(t, "'*.akuity.io/*':\n  health.lua: |2\n\n    health_status = {\n      status = \"Progressing\",\n      message = \"Provisioning ...\"\n    }\n\n    local function contains(table, val)\n      for i, v in ipairs(table) do\n    \tif v == val then\n    \t  return true\n    \tend\n      end\n      return false\n    end\n\n    local has_no_status = {\n      \"Composition\",\n      \"CompositionRevision\",\n      \"DeploymentRuntimeConfig\",\n      \"ControllerConfig\",\n      \"ProviderConfig\",\n      \"ProviderConfigUsage\"\n    }\n    if obj.status == nil or next(obj.status) == nil and contains(has_no_status, obj.kind) then\n      health_status.status = \"Healthy\"\n      health_status.message = \"Resource is up-to-date.\"\n      return health_status\n    end\n\n    if obj.status == nil or next(obj.status) == nil or obj.status.conditions == nil then\n      if obj.kind == \"ProviderConfig\" and obj.status.users ~= nil then\n    \thealth_status.status = \"Healthy\"\n    \thealth_status.message = \"Resource is in use.\"\n    \treturn health_status\n      end\n      return health_status\n    end\n\n    for i, condition in ipairs(obj.status.conditions) do\n      if condition.type == \"LastAsyncOperation\" then\n    \tif condition.status == \"False\" then\n    \t  health_status.status = \"Degraded\"\n    \t  health_status.message = condition.message\n    \t  return health_status\n    \tend\n      end\n\n      if condition.type == \"Synced\" then\n    \tif condition.status == \"False\" then\n    \t  health_status.status = \"Degraded\"\n    \t  health_status.message = condition.message\n    \t  return health_status\n    \tend\n      end\n\n      if contains({\"Ready\", \"Healthy\", \"Offered\", \"Established\"}, condition.type) then\n    \tif condition.status == \"True\" then\n    \t  health_status.status = \"Healthy\"\n    \t  health_status.message = \"Resource is up-to-date.\"\n    \t  return health_status\n    \tend\n      end\n    end\n\n    return health_status\n'*.crossplane.io/*':\n  health.lua: |2\n\n    health_status = {\n      status = \"Progressing\",\n      message = \"Provisioning ...\"\n    }\n\n    local function contains(table, val)\n      for i, v in ipairs(table) do\n    \tif v == val then\n    \t  return true\n    \tend\n      end\n      return false\n    end\n\n    local has_no_status = {\n      \"Composition\",\n      \"CompositionRevision\",\n      \"DeploymentRuntimeConfig\",\n      \"ControllerConfig\",\n      \"ProviderConfig\",\n      \"ProviderConfigUsage\"\n    }\n    if obj.status == nil or next(obj.status) == nil and contains(has_no_status, obj.kind) then\n      health_status.status = \"Healthy\"\n      health_status.message = \"Resource is up-to-date.\"\n      return health_status\n    end\n\n    if obj.status == nil or next(obj.status) == nil or obj.status.conditions == nil then\n      if obj.kind == \"ProviderConfig\" and obj.status.users ~= nil then\n    \thealth_status.status = \"Healthy\"\n    \thealth_status.message = \"Resource is in use.\"\n    \treturn health_status\n      end\n      return health_status\n    end\n\n    for i, condition in ipairs(obj.status.conditions) do\n      if condition.type == \"LastAsyncOperation\" then\n    \tif condition.status == \"False\" then\n    \t  health_status.status = \"Degraded\"\n    \t  health_status.message = condition.message\n    \t  return health_status\n    \tend\n      end\n\n      if condition.type == \"Synced\" then\n    \tif condition.status == \"False\" then\n    \t  health_status.status = \"Degraded\"\n    \t  health_status.message = condition.message\n    \t  return health_status\n    \tend\n      end\n\n      if contains({\"Ready\", \"Healthy\", \"Offered\", \"Established\"}, condition.type) then\n    \tif condition.status == \"True\" then\n    \t  health_status.status = \"Healthy\"\n    \t  health_status.message = \"Resource is up-to-date.\"\n    \t  return health_status\n    \tend\n      end\n    end\n\n    return health_status\n", customizations)
}

func newTestBuilder(t *testing.T, sharedK3sDBConnectionAuth bool) (*argocdInstanceReconciler, *agentConfigBuilder) {
	reconciler, repoSet, _ := newTestReconciler(t, common.TenantPhaseHealthy, sharedK3sDBConnectionAuth)
	instanceConfig, err := repoSet.ArgoCDInstanceConfigs().GetByID(context.Background(), testInstanceId)
	require.NoError(t, err)
	instance, err := repoSet.ArgoCDInstances().GetByID(context.Background(), testInstanceId)
	require.NoError(t, err)
	org, err := repoSet.Organizations().GetByID(context.Background(), instance.OrganizationOwner)
	require.NoError(t, err)
	instanceData := &cdInstanceRelatedData{
		instanceConfig: instanceConfig,
		organization:   org,
		instance:       instance,
	}
	return reconciler, newAgentConfigBuilder(reconciler, instanceData, features.NewService(repoSet, nil, false, false, features.FeatureGatesSourceDev, license.License{}), reconciler.settings.IngressConfig)
}

func TestValueToMap(t *testing.T) {
	testCases := []struct {
		name  string
		input any
	}{
		{"01", map[string]any{"key": nil}},
		{"02", map[string]any{"key": "value"}},
		{"03", map[string]any{"key": []string{"1", "2"}}},
		{"04", map[string]any{"key1": []string{"1", "2"}, "key2": "value"}},
		{"05", controlplane.DataValues{Kustomization: map[string]any{"key": "value"}}},
		{"06", controlplane.DataValues{
			AgentServer: &controlplane.DataValuesAgentServer{
				ImageHost: ptr.To("AAA"),
				ImageRepo: ptr.To("BBB"),
				Replicas:  ptr.To(int32(1)),
			},
			K3s: &controlplane.DataValuesK3s{
				Image: ptr.To("CCC"),
				Token: ptr.To("DDD"),
			},
		}},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			inputBytes, err := json.Marshal(tt.input)
			require.NoError(t, err)

			m, err := valueToMap(tt.input)
			require.NoError(t, err)

			mapBytes, err := json.Marshal(m)
			require.NoError(t, err)

			require.Equal(t, string(inputBytes), string(mapBytes))
		})
	}
}

var (
	//go:embed agent_config_builder_test_instance_values.yaml
	instanceValues []byte
	//go:embed agent_config_builder_test_internal_spec.json
	internalSpec []byte
)

func TestMergeMaps(t *testing.T) {
	testCases := []struct {
		name     string
		m1       map[string]any
		m2       map[string]any
		m2IntoM1 map[string]any
		m1IntoM2 map[string]any
	}{
		{
			"01",
			map[string]any{"key1": "value1"},
			map[string]any{"key1": "value2"},
			map[string]any{"key1": "value2"},
			map[string]any{"key1": "value1"},
		},
		{
			"02",
			map[string]any{"key1": "value1"},
			map[string]any{"key2": "value2"},
			map[string]any{"key1": "value1", "key2": "value2"},
			map[string]any{"key1": "value1", "key2": "value2"},
		},
		{
			"03",
			map[string]any{"key": []any{"1", "2"}},
			map[string]any{"key": []any{"3", "4"}},
			map[string]any{"key": []any{"1", "2", "3", "4"}},
			map[string]any{"key": []any{"3", "4", "1", "2"}},
		},
		{
			"04",
			map[string]any{"key1": map[string]any{"key2": []any{"1", "2"}}},
			map[string]any{"key1": map[string]any{"key2": []any{"3", "4"}}},
			map[string]any{"key1": map[string]any{"key2": []any{"1", "2", "3", "4"}}},
			map[string]any{"key1": map[string]any{"key2": []any{"3", "4", "1", "2"}}},
		},
		{
			"05",
			map[string]any{
				"a": "b",
				"c": []any{3, 4, 5},
				"d": []any{100, 200},
				"key": map[string]any{
					"key1": "value1",
					"key2": []any{"1", "2"},
				},
				"key1": map[string]any{
					"key1": "value1",
					"key2": []any{"1", "2"},
				},
			},
			map[string]any{
				"a": 7,
				"c": []any{4, 5, 6},
				"e": []any{200, 300},
				"key": map[string]any{
					"key2": []any{"3", "4"},
					"key3": "value3",
				},
				"key2": map[string]any{
					"key1": "value1",
					"key2": []any{"1", "2"},
				},
			},
			map[string]any{
				"a": 7,                       // Second map overwrites first map's value
				"c": []any{3, 4, 5, 4, 5, 6}, // Merges two slices from both maps
				"d": []any{100, 200},         // First map only
				"e": []any{200, 300},         // Second map only
				"key": map[string]any{
					"key1": "value1",                  // First map only
					"key2": []any{"1", "2", "3", "4"}, // Merges two slices from both maps
					"key3": "value3",                  // Second map only
				},
				"key1": map[string]any{ // First map only
					"key1": "value1",
					"key2": []any{"1", "2"},
				},
				"key2": map[string]any{ // Second map only
					"key1": "value1",
					"key2": []any{"1", "2"},
				},
			},
			map[string]any{
				"a": "b",                     // Second map overwrites first map's value
				"c": []any{4, 5, 6, 3, 4, 5}, // Merges two slices from both maps
				"d": []any{100, 200},         // First map only
				"e": []any{200, 300},         // Second map only
				"key": map[string]any{
					"key1": "value1",                  // First map only
					"key2": []any{"3", "4", "1", "2"}, // Merges two slices from both maps
					"key3": "value3",                  // Second map only
				},
				"key1": map[string]any{ // First map only
					"key1": "value1",
					"key2": []any{"1", "2"},
				},
				"key2": map[string]any{ // Second map only
					"key1": "value1",
					"key2": []any{"1", "2"},
				},
			},
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			require.Equal(t, tt.m2IntoM1, mergeMaps(tt.m1, tt.m2))
			require.Equal(t, tt.m2IntoM1, mergeMaps(map[string]any{}, map[string]any{}, tt.m1, map[string]any{}, tt.m2, map[string]any{}))

			require.Equal(t, tt.m1IntoM2, mergeMaps(tt.m2, tt.m1))
			require.Equal(t, tt.m1IntoM2, mergeMaps(map[string]any{}, map[string]any{}, tt.m2, map[string]any{}, tt.m1, map[string]any{}))
		})
	}

	// Merges agent_config_builder_test_instance_values.yaml with agent_config_builder_test_internal_spec.json
	// Simulates the scenario where a tenant has both instance values and internal_spec kustomization patches
	t.Run("InstanceValuesWithInternalSpec", func(t *testing.T) {
		var dataValues controlplane.DataValues
		require.NoError(t, yaml.Unmarshal(instanceValues, &dataValues))

		var instanceInternalSpec models.InstanceInternalSpec
		require.NoError(t, json.Unmarshal(internalSpec, &instanceInternalSpec))

		dataValuesMap, err := valueToMap(dataValues)
		require.NoError(t, err)

		patchedValueBytes, err := json.Marshal(mergeMaps(dataValuesMap, instanceInternalSpec.Values))
		require.NoError(t, err)

		var patchedDataValues controlplane.DataValues
		require.NoError(t, json.Unmarshal(patchedValueBytes, &patchedDataValues))

		// 13 patches in agent_config_builder_test_instance_values.yaml
		// 1 patch    in agent_config_builder_test_internal_spec.json
		require.Len(t, patchedDataValues.Kustomization.(map[string]any)["patches"], 14)

		// 13 replacements in agent_config_builder_test_instance_values.yaml
		// 1 replacement   in agent_config_builder_test_internal_spec.json
		require.Len(t, patchedDataValues.Kustomization.(map[string]any)["replacements"], 14)
	})
}
