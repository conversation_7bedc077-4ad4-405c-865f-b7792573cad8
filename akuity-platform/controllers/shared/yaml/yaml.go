package yaml

import (
	"fmt"
	"regexp"
	"strings"

	"gopkg.in/yaml.v3"
)

var (
	ErrPathNotFound = fmt.Errorf("yaml path not found")
	// validYAMLPathRegex validates YAML paths supporting both dot and bracket notation:
	// - Dot notation: supports identifiers with letters, numbers, underscores (e.g., metadata.labels.app)
	// - Bracket notation: supports quoted keys with special characters (e.g., metadata.annotations["k8s.io/xyz"])
	// - Mixed notation: allows combination of both formats (e.g., metadata["labels"].app)
	validYAMLPathRegex = regexp.MustCompile(`^([a-zA-Z][a-zA-Z0-9_-]*|\[\s*"[^"]+"\s*\]|\[\s*'[^']+'\s*\])(\.[a-zA-Z_][a-zA-Z0-9_-]*|\[\s*"[^"]+"\s*\]|\[\s*'[^']+'\s*\])*$`)
)

// ValidateYAMLPath checks if a YAML path is valid
func ValidateYAMLPath(path string) error {
	// Check for empty path
	if path == "" {
		return fmt.Errorf("YAML path cannot be empty")
	}

	// Use regex to validate path format
	if !validYAMLPathRegex.MatchString(path) {
		return fmt.Errorf("invalid YAML path format: %s", path)
	}

	return nil
}

// ParseYAMLPath splits a path into individual keys, handling both dot and bracket notation
func ParseYAMLPath(path string) ([]string, error) {
	if err := ValidateYAMLPath(path); err != nil {
		return nil, err
	}

	// Regular expression to extract keys from dot notation and bracket notation
	segmentRegex := regexp.MustCompile(`([a-zA-Z][a-zA-Z0-9_-]*)|\[\s*["']([^"']+)["']\s*\]`)

	// Extract all key segments
	matches := segmentRegex.FindAllStringSubmatch(path, -1)
	if len(matches) == 0 {
		return nil, fmt.Errorf("invalid path format")
	}

	var keys []string
	for _, match := range matches {
		if match[1] != "" {
			// Dot notation match
			keys = append(keys, match[1])
		} else if match[2] != "" {
			// Bracket notation match
			keys = append(keys, match[2])
		}
	}

	for _, key := range keys {
		if key == "" {
			return nil, fmt.Errorf("invalid path: empty key found")
		}
	}

	return keys, nil
}

// ExtractYamlPathValueFromBytes extracts a value from YAML bytes using a parsed path
func ExtractYamlPathValueFromBytes(yamlBytes []byte, path string) (string, error) {
	var data map[string]interface{}
	if err := yaml.Unmarshal(yamlBytes, &data); err != nil {
		return "", fmt.Errorf("failed to parse YAML: %v", err)
	}
	return ExtractYAMLPathValue(data, path)
}

// ExtractYAMLPathValue extracts a value from data struct using a parsed path
func ExtractYAMLPathValue(data map[string]interface{}, path string) (string, error) {
	// Parse the path into keys
	keys, err := ParseYAMLPath(path)
	if err != nil {
		return "", fmt.Errorf("invalid path: %v", err)
	}

	// Traverse the nested structure
	current := data
	for i, key := range keys {
		// If we're at the last key, try to convert the value to YAML
		if i == len(keys)-1 {
			value, exists := current[key]
			if !exists {
				return "", fmt.Errorf("key not found: %s, %w", path, ErrPathNotFound)
			}

			// Convert the value to YAML
			yamlValue, err := yaml.Marshal(value)
			if err != nil {
				return "", fmt.Errorf("failed to convert value to YAML: %v", err)
			}
			return strings.Trim(string(yamlValue), "\n"), nil
		}

		// For intermediate keys, ensure they are maps
		nextMap, ok := current[key].(map[string]interface{})
		if !ok {
			return "", fmt.Errorf("invalid path: '%s' is not a map, %w", key, ErrPathNotFound)
		}
		current = nextMap
	}

	return "", fmt.Errorf("unexpected error traversing path")
}

// SetYAMLPathValueFromBytes sets a value in YAML bytes at a specified path
func SetYAMLPathValueFromBytes(destYAML []byte, path string, valueYAML []byte) ([]byte, error) {
	var data map[string]interface{}
	if err := yaml.Unmarshal(destYAML, &data); err != nil {
		return nil, fmt.Errorf("failed to parse destination YAML: %v", err)
	}
	if err := SetYAMLPathValue(data, path, valueYAML); err != nil {
		return nil, err
	}
	return yaml.Marshal(data)
}

// SetYAMLPathValue sets a value in data struct at a specified path
func SetYAMLPathValue(data map[string]interface{}, path string, valueYAML []byte) error {
	// Parse the path into keys
	keys, err := ParseYAMLPath(path)
	if err != nil {
		return fmt.Errorf("invalid path: %v", err)
	}

	// Check if the value is an empty string
	var value interface{}
	if len(valueYAML) > 0 {
		if err := yaml.Unmarshal(valueYAML, &value); err != nil {
			return fmt.Errorf("failed to unmarshal value: %v", err)
		}
	}

	// Special handling for nil value
	if value == nil {
		// Recursive function to remove empty paths
		var removePath func(current map[string]interface{}, pathKeys []string) bool
		removePath = func(current map[string]interface{}, pathKeys []string) bool {
			if len(pathKeys) == 0 {
				return false
			}

			currentKey := pathKeys[0]
			remainingKeys := pathKeys[1:]

			// If this is the last key, remove it
			if len(remainingKeys) == 0 {
				delete(current, currentKey)
				return len(current) == 0
			}

			// Check if the next level exists and is a map
			nextLevel, ok := current[currentKey].(map[string]interface{})
			if !ok {
				return false
			}

			// Recursively try to remove the path
			isEmpty := removePath(nextLevel, remainingKeys)

			// If the next level is now empty, remove this level too
			if isEmpty {
				delete(current, currentKey)
				return len(current) == 0
			}

			return false
		}

		// Start recursive removal from the root
		removePath(data, keys)
	} else {
		// Normal path traversal and value setting
		current := data
		for i, key := range keys {
			// If we're at the last key, set the value
			if i == len(keys)-1 {
				current[key] = value
				break
			}

			// For intermediate keys, ensure they are maps
			nextMap, ok := current[key].(map[string]interface{})
			if !ok {
				// If the key doesn't exist or is not a map, create it
				nextMap = make(map[string]interface{})
				current[key] = nextMap
			}
			current = nextMap
		}
	}

	return nil
}
