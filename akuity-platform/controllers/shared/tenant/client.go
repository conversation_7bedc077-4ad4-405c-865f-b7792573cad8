package tenant

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"iter"
	"strconv"
	"strings"
	"time"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	v1 "k8s.io/api/core/v1"

	agentclient "github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	"github.com/akuityio/akuity-platform/internal/utils/io"
	"github.com/akuityio/akuity-platform/models/models"
)

type Event struct {
	v1.Event
	ID int
}

// StateClient defines method to extract tenant state
type StateClient interface {
	GetAgentState(ctx context.Context, cluster *models.ArgoCDCluster) (*models.AgentState, error)
	GetClusterData(ctx context.Context, cluster *models.ArgoCDCluster) (*ClusterData, error)
	GetApplicationInfo(ctx context.Context, instanceID string, isAppOfApps func(app argocd.Application) bool) (*models.ApplicationsStatus, error)
	ListEventsSinceID(ctx context.Context, instanceID string, sinceID, count int) ([]Event, error)
	GetApplication(ctx context.Context, instanceID, name, ns string) (*argocd.Application, error)
	GetApplicationsCount(ctx context.Context, instanceID, clusterName string) (int, error)
	GetApplications(ctx context.Context, instanceID string, q ...Query) (iter.Seq2[*argocd.Application, error], error)
	NeedK3sCNReset(ctx context.Context, instanceID string) (bool, error)
}

type k3sStateClient struct {
	db boil.ContextExecutor
}

type ClusterData struct {
	Generation    int64
	Manifest      string
	UpgraderImage string
}

func NewK3SStateClient(db boil.ContextExecutor) *k3sStateClient {
	return &k3sStateClient{
		db: db,
	}
}

func (c *k3sStateClient) NeedK3sCNReset(ctx context.Context, instanceID string) (bool, error) {
	secret, err := SecretsGR.One(ctx, false, c.db, instanceID, Query{Name: "k3s-serving", Namespace: "kube-system"})
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return false, nil
		}
		return false, err
	}
	_, greater := agentclient.K3sCNCountAboveLimit(secret, common.MaxK3sCnCount)
	return greater, nil
}

func (c *k3sStateClient) GetAgentState(ctx context.Context, cluster *models.ArgoCDCluster) (*models.AgentState, error) {
	cm, err := ConfigMapsGR.One(ctx, false, c.db, cluster.InstanceID, Query{Name: "cluster-" + cluster.Name + agentclient.ClusterSuffixStatusConfigMapName})
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	var state *models.AgentState
	if cm != nil {
		observedAt, err := time.Parse(time.RFC3339, cm.Data["observedAt"])
		if err != nil {
			return nil, nil
		}
		lastUserAppliedGeneration, err := strconv.ParseInt(cm.Data[agentclient.LastUserAppliedGenerationDataKey], 10, 64)
		if err != nil {
			lastUserAppliedGeneration = -1
		}
		healthStatus := agentclient.AggregatedHealthResponse{}
		if json.Unmarshal([]byte(cm.Data["status"]), &healthStatus) == nil {
			resources := map[string]common.Resources{}
			if res, ok := cm.Data["agentResources"]; ok {
				if err := json.Unmarshal([]byte(res), &resources); err != nil {
					return nil, err
				}
			}
			state = &models.AgentState{
				Version:                   cm.Data["agentVersion"],
				ArgoCDVersion:             cm.Data["argocdVersion"],
				ObservedAt:                null.TimeFrom(observedAt),
				Status:                    &healthStatus,
				AgentIDs:                  getAgentIDs(cm.GetAnnotations()),
				LastUserAppliedGeneration: lastUserAppliedGeneration,
				Resources:                 resources,
			}
		}
	}
	return state, nil
}

func (c *k3sStateClient) GetApplicationsCount(ctx context.Context, instanceID, clusterName string) (int, error) {
	appsCount, err := ApplicationsGR.Count(ctx, false, c.db, instanceID, Query{Mods: []qm.QueryMod{
		qm.Where(`name LIKE '/registry/argoproj.io/applications/%/%'`), // make sure row is application CR to make sure jsonb conversion won't fail
		qm.Where("convert_from(value, 'UTF-8')::jsonb->'metadata'->'labels'->>'cluster' = ?", clusterName),
	}})
	if err != nil {
		return 0, err
	}

	return int(appsCount), nil
}

func (c *k3sStateClient) GetApplications(ctx context.Context, instanceID string, q ...Query) (iter.Seq2[*argocd.Application, error], error) {
	apps, closer, err := ApplicationsGR.Iterate(ctx, false, c.db, instanceID, q...)
	if err != nil {
		return nil, err
	}

	return func(yield func(*argocd.Application, error) bool) {
		defer io.Close(closer)
		for {
			row, err := apps()
			if err != nil {
				yield(nil, err)
				break
			}
			if row == nil {
				break
			}
			if !yield(&row.Item, nil) {
				break
			}
		}
	}, nil
}

func (c *k3sStateClient) GetApplicationInfo(ctx context.Context, instanceID string, isAppOfApps func(app argocd.Application) bool) (*models.ApplicationsStatus, error) {
	apps, closer, err := ApplicationsGR.Iterate(ctx, false, c.db, instanceID)
	if err != nil {
		return nil, err
	}
	defer io.Close(closer)

	appInfo := &models.ApplicationsStatus{
		Health:     models.ApplicationsHealth{},
		SyncStatus: models.ApplicationsSyncStatus{},
		Warnings:   0,
		Errors:     0,
	}

	for {
		// iterate over each row separately instead of loading all the apps in one go
		row, err := apps()
		if err != nil {
			return nil, err
		}
		if row == nil {
			break
		}
		app := row.Item

		// count normal apps and app of apps
		if isAppOfApps(app) {
			appInfo.AppOfAppCount++
		} else {
			appInfo.Count++
		}

		appInfo.ResourcesCount += len(app.Status.Resources)
		if app.Operation != nil || app.Status.OperationState != nil && app.Status.OperationState.Phase == "Running" {
			appInfo.InProgressSyncCount++
		}

		if len(app.Status.Conditions) > 0 {
			for _, condition := range app.Status.Conditions {
				if isError(condition) {
					appInfo.Errors++
					break
				} else if isWarning(condition) {
					appInfo.Warnings++
					break
				}
			}
		}

		switch app.Status.Health.Status {
		case argocd.HealthStatusHealthy:
			appInfo.Health.Healthy++
		case argocd.HealthStatusDegraded:
			appInfo.Health.Degraded++
		case argocd.HealthStatusProgressing:
			appInfo.Health.Progressing++
		case argocd.HealthStatusUnknown:
			appInfo.Health.Unknown++
		case argocd.HealthStatusSuspended:
			appInfo.Health.Suspended++
		case argocd.HealthStatusMissing:
			appInfo.Health.Missing++
		case "":
			appInfo.Health.Unknown++
		}

		switch app.Status.Sync.Status {
		case argocd.SyncStatusCodeSynced:
			appInfo.SyncStatus.Synced++
		case argocd.SyncStatusCodeOutOfSync:
			appInfo.SyncStatus.OutOfSync++
		case argocd.SyncStatusCodeUnknown:
			appInfo.SyncStatus.Unknown++
		case "":
			appInfo.SyncStatus.Unknown++
		}

	}

	return appInfo, nil
}

func (c *k3sStateClient) GetClusterData(ctx context.Context, cluster *models.ArgoCDCluster) (*ClusterData, error) {
	secret, err := SecretsGR.One(ctx, false, c.db, cluster.InstanceID, Query{Name: "cluster-" + cluster.Name + agentclient.ClusterSuffixDataSecretName})
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	var clusterData *ClusterData
	if secret != nil {
		generation, err := strconv.ParseInt(string(secret.Data[agentclient.ClusterDataGenerationField]), 10, 64)
		if err == nil {
			clusterData = &ClusterData{
				Generation:    generation,
				Manifest:      string(secret.Data[agentclient.ClusterDataManifestField]),
				UpgraderImage: string(secret.Data[agentclient.ClusterDataUpgraderImageField]),
			}
		}
	}
	return clusterData, nil
}

func (c *k3sStateClient) ListEventsSinceID(ctx context.Context, instanceID string, sinceID, count int) ([]Event, error) {
	eventRows, err := EventsGR.List(ctx, false, c.db, instanceID, Query{Limit: count, Mods: []qm.QueryMod{qm.Where("id > ?", sinceID)}})
	if err != nil {
		return nil, err
	}

	var events []Event
	for i := range eventRows {
		events = append(events, Event{ID: eventRows[i].ID, Event: eventRows[i].Item})
	}

	return events, nil
}

func (c *k3sStateClient) GetApplication(ctx context.Context, instanceID, name, ns string) (*argocd.Application, error) {
	return ApplicationsGR.One(ctx, false, c.db, instanceID, Query{Name: name, Namespace: ns})
}

// StateClient defines method to extract tenant state
type KargoStateClient interface {
	GetAgentState(ctx context.Context, cluster *models.KargoAgent) (*models.KargoAgentState, error)
	GetClusterData(ctx context.Context, cluster *models.KargoAgent) (*ClusterData, error)
	GetStatsCount(ctx context.Context, instanceID string) (int, int, error)
	ListEventsSinceID(ctx context.Context, instanceID string, sinceID, count int) ([]Event, error)
	NeedK3sCNReset(ctx context.Context, instanceID string) (bool, error)
}

type kargoK3sStateClient struct {
	db boil.ContextExecutor
}

func NewKargoStateClient(db boil.ContextExecutor) *kargoK3sStateClient {
	return &kargoK3sStateClient{
		db: db,
	}
}

func (c *kargoK3sStateClient) GetAgentState(ctx context.Context, cluster *models.KargoAgent) (*models.KargoAgentState, error) {
	cm, err := ConfigMapsGR.One(ctx, true, c.db, cluster.InstanceID, Query{Name: "agent-" + cluster.Name + agentclient.ClusterSuffixStatusConfigMapName})
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	var state *models.KargoAgentState
	if cm != nil {
		lastUserAppliedGeneration, err := strconv.ParseInt(cm.Data[agentclient.LastUserAppliedGenerationDataKey], 10, 64)
		if err != nil {
			lastUserAppliedGeneration = -1
		}
		observedAt, err := time.Parse(time.RFC3339, cm.Data["observedAt"])
		if err != nil {
			return nil, nil
		}
		healthStatus := agentclient.AggregatedHealthResponse{}
		if json.Unmarshal([]byte(cm.Data["status"]), &healthStatus) == nil {
			state = &models.KargoAgentState{
				Version:                   cm.Data["agentVersion"],
				KargoVersion:              cm.Data["kargoVersion"],
				ObservedAt:                null.TimeFrom(observedAt),
				Status:                    &healthStatus,
				AgentIDs:                  getAgentIDs(cm.GetAnnotations()),
				LastUserAppliedGeneration: lastUserAppliedGeneration,
			}
		}
	}
	return state, nil
}

func getAgentIDs(annotations map[string]string) []string {
	var agentIDs []string
	for key := range annotations {
		if common.AgentUIDAnnotationRegexp.MatchString(key) {
			match := common.AgentUIDAnnotationRegexp.FindStringSubmatch(key)
			if len(match) > 1 {
				agentIDs = append(agentIDs, match[1])
			}
		}
	}
	return agentIDs
}

func isError(condition argocd.ApplicationCondition) bool {
	return strings.HasSuffix(condition.Type, "Error")
}

func isWarning(condition argocd.ApplicationCondition) bool {
	return strings.HasSuffix(condition.Type, "Warning")
}

func (c *kargoK3sStateClient) GetClusterData(ctx context.Context, cluster *models.KargoAgent) (*ClusterData, error) {
	secret, err := SecretsGR.One(ctx, true, c.db, cluster.InstanceID, Query{Name: "agent-" + cluster.Name + agentclient.ClusterSuffixDataSecretName})
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	var clusterData *ClusterData
	if secret != nil {
		generation, err := strconv.ParseInt(string(secret.Data[agentclient.ClusterDataGenerationField]), 10, 64)
		if err == nil {
			clusterData = &ClusterData{
				Generation:    generation,
				Manifest:      string(secret.Data[agentclient.ClusterDataManifestField]),
				UpgraderImage: string(secret.Data[agentclient.ClusterDataUpgraderImageField]),
			}
		}
	}
	return clusterData, nil
}

func (c *kargoK3sStateClient) GetStatsCount(ctx context.Context, instanceID string) (int, int, error) {
	projectCount, err := KargoProjectsGR.Count(ctx, true, c.db, instanceID)
	if err != nil {
		return 0, 0, err
	}

	stageCount, err := KargoStagesGR.Count(ctx, true, c.db, instanceID)
	if err != nil {
		return 0, 0, err
	}
	return int(projectCount), int(stageCount), nil
}

func (c *kargoK3sStateClient) ListEventsSinceID(ctx context.Context, instanceID string, sinceID, count int) ([]Event, error) {
	eventRows, err := EventsGR.List(ctx, true, c.db, instanceID, Query{Limit: count, Mods: []qm.QueryMod{qm.Where("id > ?", sinceID)}})
	if err != nil {
		return nil, err
	}

	var events []Event
	for i := range eventRows {
		events = append(events, Event{ID: eventRows[i].ID, Event: eventRows[i].Item})
	}

	return events, nil
}

func (c *kargoK3sStateClient) NeedK3sCNReset(ctx context.Context, instanceID string) (bool, error) {
	secret, err := SecretsGR.One(ctx, true, c.db, instanceID, Query{Name: "k3s-serving", Namespace: "kube-system"})
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return false, nil
		}
		return false, err
	}
	_, greater := agentclient.K3sCNCountAboveLimit(secret, common.MaxK3sCnCount)
	return greater, nil
}
