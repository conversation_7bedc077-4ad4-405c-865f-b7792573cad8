package testing

import (
	"context"
	"iter"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/akuity-platform/controllers/shared/tenant"
	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	"github.com/akuityio/akuity-platform/models/models"
)

type FakeTenantStateClient struct{}

func (f FakeTenantStateClient) GetAgentState(ctx context.Context, cluster *models.ArgoCDCluster) (*models.AgentState, error) {
	return &models.AgentState{
		Status: &client.AggregatedHealthResponse{MinObservedGeneration: 1},
	}, nil
}

func (f FakeTenantStateClient) GetClusterData(ctx context.Context, cluster *models.ArgoCDCluster) (*tenant.ClusterData, error) {
	return &tenant.ClusterData{}, nil
}

func (f FakeTenantStateClient) NeedK3sCNReset(ctx context.Context, instanceID string) (bool, error) {
	return false, nil
}

func (f FakeTenantStateClient) GetApplicationInfo(ctx context.Context, instanceID string, isAppOfApps func(app argocd.Application) bool) (*models.ApplicationsStatus, error) {
	return &models.ApplicationsStatus{}, nil
}

func (f FakeTenantStateClient) ListEventsSinceID(ctx context.Context, instanceID string, sinceID, count int) ([]tenant.Event, error) {
	return nil, nil
}

func (f FakeTenantStateClient) GetApplication(ctx context.Context, instanceID, name, ns string) (*argocd.Application, error) {
	return nil, nil
}

func (f FakeTenantStateClient) GetApplicationsCount(ctx context.Context, instanceID, clusterName string) (int, error) {
	return 0, nil
}

func (f FakeTenantStateClient) GetApplications(ctx context.Context, instanceID string, q ...tenant.Query) (iter.Seq2[*argocd.Application, error], error) {
	return nil, nil
}
