package keymutex

import (
	"sync"
)

type KeyMutex struct {
	mutexes sync.Map // Map of string keys to *sync.Mutex
}

func (km *KeyMutex) Lock(key string) {
	// Load or store a mutex for the given key
	mutex, _ := km.mutexes.LoadOrStore(key, &sync.Mutex{})
	mutex.(*sync.Mutex).Lock() // Acquire the lock
}

func (km *KeyMutex) Unlock(key string) {
	if mutex, ok := km.mutexes.Load(key); ok {
		mutex.(*sync.Mutex).Unlock() // Release the lock
	}
}

func (km *KeyMutex) TryLock(key string) bool {
	mutex, _ := km.mutexes.LoadOrStore(key, &sync.Mutex{})
	return mutex.(*sync.Mutex).TryLock() // try to acquire the lock
}
