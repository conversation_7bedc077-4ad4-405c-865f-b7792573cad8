package keymutex

import (
	"fmt"
	"sync"
	"testing"
	"time"
)

func TestKeyMutex_BasicLockUnlock(t *testing.T) {
	km := &KeyMutex{}

	// Test basic lock/unlock
	km.Lock("test-key")
	km.Unlock("test-key")

	// Should be able to lock again
	km.Lock("test-key")
	km.Unlock("test-key")
}

func TestKeyMutex_TryLock(t *testing.T) {
	km := &KeyMutex{}

	// First try should succeed
	if !km.TryLock("test-key") {
		t.<PERSON><PERSON>("Expected TryLock to succeed on first attempt")
	}

	// Second try should fail
	if km.TryLock("test-key") {
		t.<PERSON>r("Expected TryLock to fail when lock is already held")
	}

	// Unlock and try again
	km.Unlock("test-key")
	if !km.TryLock("test-key") {
		t.<PERSON>r("Expected TryLock to succeed after unlock")
	}
}

func TestKeyMutex_MultipleKeys(t *testing.T) {
	km := &KeyMutex{}

	// Should be able to lock different keys simultaneously
	km.Lock("key1")
	km.Lock("key2")

	// Clean up
	km.Unlock("key1")
	km.Unlock("key2")
}

func TestKeyMutex_ConcurrentAccess(t *testing.T) {
	km := &KeyMutex{}
	const numGoroutines = 100
	const numIterations = 1000

	var wg sync.WaitGroup
	counter := 0

	// Launch multiple goroutines to increment counter
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < numIterations; j++ {
				km.Lock("counter")
				counter++
				km.Unlock("counter")
			}
		}()
	}

	wg.Wait()

	expected := numGoroutines * numIterations
	if counter != expected {
		t.Errorf("Expected counter to be %d, got %d", expected, counter)
	}
}

func TestKeyMutex_RaceCondition(t *testing.T) {
	km := &KeyMutex{}
	const numGoroutines = 10
	const numKeys = 5

	// Create channels to coordinate goroutine execution
	start := make(chan struct{})
	var wg sync.WaitGroup

	// Track which goroutine has which key locked
	lockHolders := make(map[string]int)
	lockHoldersMutex := &sync.Mutex{}

	// Launch multiple goroutines to compete for locks
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			// Wait for start signal
			<-start

			for j := 0; j < 100; j++ {
				key := fmt.Sprintf("key-%d", j%numKeys)

				for !km.TryLock(key) {
					// Wait for lock to be available
					time.Sleep(5 * time.Millisecond)
				}

				// Check and update lock holders
				lockHoldersMutex.Lock()
				if existing, exists := lockHolders[key]; exists {
					t.Errorf("Race condition detected: key %s already held by goroutine %d while goroutine %d attempted to lock", key, existing, id)
				}
				lockHolders[key] = id
				lockHoldersMutex.Unlock()

				// Simulate some work
				time.Sleep(time.Millisecond)

				// Remove this goroutine as the holder
				lockHoldersMutex.Lock()
				delete(lockHolders, key)
				lockHoldersMutex.Unlock()

				km.Unlock(key)
			}
		}(i)
	}

	// Start all goroutines simultaneously
	close(start)
	wg.Wait()
}

func TestKeyMutex_StressTest(t *testing.T) {
	km := &KeyMutex{}
	const numGoroutines = 20
	const numOperations = 1000

	// Create maps to track values for each key
	values := make(map[string]int)
	valuesMutex := &sync.Mutex{}

	// Create a wait group to track goroutines
	var wg sync.WaitGroup

	// Launch goroutines that will randomly lock/unlock and modify values
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			for j := 0; j < numOperations; j++ {
				// Randomly select a key
				key := fmt.Sprintf("key-%d", j%5)

				if km.TryLock(key) {
					// Update value for this key
					valuesMutex.Lock()
					values[key]++
					valuesMutex.Unlock()

					// Simulate some work
					time.Sleep(time.Microsecond)

					km.Unlock(key)
				}
			}
		}()
	}

	wg.Wait()

	// Verify that no locks are still held
	for i := 0; i < 5; i++ {
		key := fmt.Sprintf("key-%d", i)
		if !km.TryLock(key) {
			t.Errorf("Lock for key %s is still held after test completion", key)
		} else {
			km.Unlock(key)
		}
	}
}

func BenchmarkKeyMutex(b *testing.B) {
	km := &KeyMutex{}

	b.Run("Single Key Lock/Unlock", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			km.Lock("test-key")
			km.Unlock("test-key")
		}
	})

	b.Run("Multiple Keys Lock/Unlock", func(b *testing.B) {
		b.RunParallel(func(pb *testing.PB) {
			i := 0
			for pb.Next() {
				key := fmt.Sprintf("key-%d", i%100)
				km.Lock(key)
				km.Unlock(key)
				i++
			}
		})
	})

	b.Run("TryLock Contention", func(b *testing.B) {
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				if km.TryLock("contested-key") {
					km.Unlock("contested-key")
				}
			}
		})
	})
}
