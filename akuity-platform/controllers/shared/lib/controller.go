package lib

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/events"
)

func SubscribeToIds[E any](ctx context.Context, watcher database.Watcher[E], predicate func(event E) bool, getId func(event E) string) <-chan string {
	res := make(chan string)
	channel := watcher.Subscribe(ctx, predicate)
	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			case event := <-channel:
				res <- getId(event)
			}
		}
	}()
	return res
}

// Controller provides a generic controller interface
type Controller interface {
	Start(ctx context.Context, numWorkers int) error
}

type CanStart interface {
	Start(ctx context.Context) error
}

func EventSpecChanged[E events.IEvent](event E) bool {
	return event.HasSpecChanged()
}

func GetEventId[E events.IEvent](event E) string {
	return event.GetID()
}
