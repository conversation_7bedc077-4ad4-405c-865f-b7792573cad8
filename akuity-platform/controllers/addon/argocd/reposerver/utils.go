package reposerver

import (
	"bytes"
	"encoding/json"
	"strconv"

	"github.com/golang/protobuf/jsonpb" // nolint:staticcheck
	"github.com/golang/protobuf/proto"  // nolint:staticcheck
	corev1 "k8s.io/api/core/v1"

	typesv1 "github.com/akuityio/akuity-platform/controllers/addon/argocd/types/v1"
)

var jsonMarshaller = jsonpb.Marshaler{}

func SecretToRepository(secret *corev1.Secret) (*typesv1.Repository, error) {
	repository := &typesv1.Repository{
		Name:                       string(secret.Data["name"]),
		Repo:                       string(secret.Data["url"]),
		Username:                   string(secret.Data["username"]),
		Password:                   string(secret.Data["password"]),
		SSHPrivateKey:              string(secret.Data["sshPrivateKey"]),
		TLSClientCertData:          string(secret.Data["tlsClientCertData"]),
		TLSClientCertKey:           string(secret.Data["tlsClientCertKey"]),
		Type:                       string(secret.Data["type"]),
		GithubAppPrivateKey:        string(secret.Data["githubAppPrivateKey"]),
		GitHubAppEnterpriseBaseURL: string(secret.Data["githubAppEnterpriseBaseUrl"]),
		Proxy:                      string(secret.Data["proxy"]),
		NoProxy:                    string(secret.Data["noProxy"]),
		Project:                    string(secret.Data["project"]),
		GCPServiceAccountKey:       string(secret.Data["gcpServiceAccountKey"]),
	}

	insecureIgnoreHostKey, err := boolOrFalse(secret, "insecureIgnoreHostKey")
	if err != nil {
		return repository, err
	}
	repository.InsecureIgnoreHostKey = insecureIgnoreHostKey

	insecure, err := boolOrFalse(secret, "insecure")
	if err != nil {
		return repository, err
	}
	repository.Insecure = insecure

	enableLfs, err := boolOrFalse(secret, "enableLfs")
	if err != nil {
		return repository, err
	}
	repository.EnableLFS = enableLfs

	enableOCI, err := boolOrFalse(secret, "enableOCI")
	if err != nil {
		return repository, err
	}
	repository.EnableOCI = enableOCI

	githubAppID, err := intOrZero(secret, "githubAppID")
	if err != nil {
		return repository, err
	}
	repository.GithubAppId = githubAppID

	githubAppInstallationID, err := intOrZero(secret, "githubAppInstallationID")
	if err != nil {
		return repository, err
	}
	repository.GithubAppInstallationId = githubAppInstallationID

	forceBasicAuth, err := boolOrFalse(secret, "forceHttpBasicAuth")
	if err != nil {
		return repository, err
	}
	repository.ForceHttpBasicAuth = forceBasicAuth

	return repository, nil
}

func boolOrFalse(secret *corev1.Secret, key string) (bool, error) {
	val, present := secret.Data[key]
	if !present {
		return false, nil
	}

	return strconv.ParseBool(string(val))
}

func intOrZero(secret *corev1.Secret, key string) (int64, error) {
	val, present := secret.Data[key]
	if !present {
		return 0, nil
	}

	return strconv.ParseInt(string(val), 10, 64)
}

func ConvertToPrototype(val interface{}, protoVal proto.Message) error {
	data, err := json.Marshal(val)
	if err != nil {
		return err
	}
	return jsonpb.Unmarshal(bytes.NewBuffer(data), protoVal)
}

func ConvertFromPrototype(protoVal proto.Message, val interface{}) error {
	data, err := jsonMarshaller.MarshalToString(protoVal)
	if err != nil {
		return err
	}
	return json.Unmarshal([]byte(data), val)
}
