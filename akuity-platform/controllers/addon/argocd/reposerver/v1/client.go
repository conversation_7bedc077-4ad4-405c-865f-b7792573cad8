package v1

import (
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"regexp"
	"time"

	"github.com/go-logr/logr"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	grpc_retry "github.com/grpc-ecosystem/go-grpc-middleware/retry"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"

	"github.com/akuityio/agent/pkg/common"
	protoutil "github.com/akuityio/agent/pkg/repo-server"
	"github.com/akuityio/akuity-platform/controllers/addon/argocd/reposerver"
)

// MaxGRPCMessageSize contains max grpc message size
var MaxGRPCMessageSize = 100 * 1024 * 1024

type repoClientV1 struct {
	conn *grpc.ClientConn
	*protoutil.RepoProtoUtil
}

func (r *repoClientV1) requester(ctx context.Context, methodName string, req, resp any) error {
	mtd := r.FindMethodByName(methodName)
	if mtd == nil {
		return fmt.Errorf("can't find method: %s", methodName)
	}
	input := r.MsgFactory.NewMessage(mtd.GetInputType())
	if err := reposerver.ConvertToPrototype(req, input); err != nil {
		return err
	}
	grpcResp, err := r.CallGRPC(ctx, mtd, input, r.getConnection)
	if err != nil {
		return err
	}
	return reposerver.ConvertFromPrototype(grpcResp, resp)
}

func (r *repoClientV1) GetAppDetails(ctx context.Context, in *reposerver.RepoServerAppDetailsQuery, opts ...grpc.CallOption) (*reposerver.RepoAppDetailsResponse, error) {
	resp := &reposerver.RepoAppDetailsResponse{}
	err := r.requester(ctx, "GetAppDetails", in, resp)
	return resp, err
}

func (r *repoClientV1) TestRepository(ctx context.Context, in *reposerver.TestRepositoryRequest, opts ...grpc.CallOption) (*reposerver.TestRepositoryResponse, error) {
	resp := &reposerver.TestRepositoryResponse{}
	err := r.requester(ctx, "TestRepository", in, resp)
	return resp, err
}

func (r *repoClientV1) CommitFiles(ctx context.Context, in *reposerver.CommitFilesRequest, opts ...grpc.CallOption) (*reposerver.CommitFilesResponse, error) {
	resp := &reposerver.CommitFilesResponse{}
	err := r.requester(ctx, "CommitFiles", in, resp)
	return resp, err
}

func (r *repoClientV1) ResolveRevision(ctx context.Context, in *reposerver.ResolveRevisionRequest, opts ...grpc.CallOption) (*reposerver.ResolveRevisionResponse, error) {
	resp := &reposerver.ResolveRevisionResponse{}
	err := r.requester(ctx, "ResolveRevision", in, resp)
	return resp, err
}

func (r *repoClientV1) GetGitFiles(ctx context.Context, in *reposerver.GitFilesRequest, opts ...grpc.CallOption) (*reposerver.GitFilesResponse, error) {
	resp := &reposerver.GitFilesResponse{}
	err := r.requester(ctx, "GetGitFiles", in, resp)
	return resp, err
}

func (r *repoClientV1) GetGitDirectories(ctx context.Context, in *reposerver.GitDirectoriesRequest, opts ...grpc.CallOption) (*reposerver.GitDirectoriesResponse, error) {
	resp := &reposerver.GitDirectoriesResponse{}
	err := r.requester(ctx, "GetGitDirectories", in, resp)
	return resp, err
}

func (r *repoClientV1) DeleteFiles(ctx context.Context, in *reposerver.DeleteFilesRequest) (*reposerver.DeleteFilesResponse, error) {
	resp := &reposerver.DeleteFilesResponse{}
	err := r.requester(ctx, "DeleteFiles", in, resp)
	return resp, err
}

func (r *repoClientV1) getConnection() (*grpc.ClientConn, func(), error) {
	return r.conn, func() {}, nil
}

func withTimeout(duration time.Duration) grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		clientDeadline := time.Now().Add(duration)
		ctx, cancel := context.WithDeadline(ctx, clientDeadline)
		defer cancel()
		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

func newConnection(address string, timeoutSeconds int, plainText bool) (*grpc.ClientConn, error) {
	retryOpts := []grpc_retry.CallOption{
		grpc_retry.WithMax(3),
		grpc_retry.WithBackoff(grpc_retry.BackoffLinear(1000 * time.Millisecond)),
	}
	unaryInterceptors := []grpc.UnaryClientInterceptor{grpc_retry.UnaryClientInterceptor(retryOpts...)}
	if timeoutSeconds > 0 {
		unaryInterceptors = append(unaryInterceptors, withTimeout(time.Duration(timeoutSeconds)*time.Second))
	}
	opts := []grpc.DialOption{
		grpc.WithStreamInterceptor(grpc_retry.StreamClientInterceptor(retryOpts...)),
		grpc.WithUnaryInterceptor(grpc_middleware.ChainUnaryClient(unaryInterceptors...)),
		grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(MaxGRPCMessageSize), grpc.MaxCallSendMsgSize(MaxGRPCMessageSize)),
	}

	if !plainText {
		tlsC := &tls.Config{InsecureSkipVerify: true}
		opts = append(opts, grpc.WithTransportCredentials(credentials.NewTLS(tlsC)))
	} else {
		opts = append(opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	}

	conn, err := grpc.NewClient(address, opts...)
	if err != nil {
		return nil, err
	}
	return conn, nil
}

func NewRepoClientV1(logger logr.Logger, address, argoCDVersion string, timeoutSeconds int, plainText bool) (io.Closer, reposerver.IRepoClient, error) {
	conn, err := newConnection(address, timeoutSeconds, plainText)
	if err != nil {
		return nil, nil, err
	}
	repoServerVersion := protoutil.RepoVersionV2
	match, err := regexp.MatchString(common.RepoVersionV3Regex, argoCDVersion)
	if err != nil {
		return nil, nil, err
	}
	if match {
		repoServerVersion = protoutil.RepoVersionV3
	}
	protoUtil, err := protoutil.NewRepoProtoUtil(logger, nil, repoServerVersion)
	if err != nil {
		return nil, nil, err
	}
	// need to disable client streaming
	protoUtil.SetClientStreamDisabled(true)
	return conn, &repoClientV1{
		conn:          conn,
		RepoProtoUtil: protoUtil,
	}, err
}
