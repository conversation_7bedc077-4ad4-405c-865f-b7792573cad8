package integration

import (
	"bytes"
	"context"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"strings"
	"text/template"

	"github.com/Masterminds/sprig/v3"
	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing/transport"
	"github.com/go-logr/logr"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"google.golang.org/grpc/status"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/yaml"

	argocd_repo "github.com/akuityio/akuity-platform/controllers/addon/argocd/reposerver"
	typesv1 "github.com/akuityio/akuity-platform/controllers/addon/argocd/types/v1"
	"github.com/akuityio/akuity-platform/controllers/shared/tenant"
	yamlutils "github.com/akuityio/akuity-platform/controllers/shared/yaml"
	utilErrors "github.com/akuityio/akuity-platform/internal/utils/errors"
	ioutil "github.com/akuityio/akuity-platform/internal/utils/io"
	"github.com/akuityio/akuity-platform/internal/utils/kustomize"
	"github.com/akuityio/akuity-platform/models/models"
)

type chartDependency struct {
	Name       string `json:"name" yaml:"name"`
	Version    string `json:"version,omitempty" yaml:"version,omitempty"`
	Repository string `json:"repository,omitempty" yaml:"repository,omitempty"`
}

type helmChartInstallConfig struct {
	Dependencies []chartDependency `json:"dependencies"`
	Description  string            `json:"description"`
	Name         string            `json:"name"`
	Version      string            `json:"version"`
}

type chartTemplate struct {
	APIVersion string `yaml:"apiVersion" json:"apiVersion"`
	AppVersion string `yaml:"appVersion" json:"appVersion"`
	helmChartInstallConfig
}

func fromHelmChartInstallConfig(m *models.HelmChartInstallConfig) helmChartInstallConfig {
	deps := []chartDependency{}
	for _, d := range m.Dependencies {
		deps = append(deps, chartDependency{
			Name:       d.Name,
			Version:    d.Version,
			Repository: d.Repository,
		})
	}
	rv := helmChartInstallConfig{
		Dependencies: deps,
		Description:  m.Description,
		Name:         m.Name,
		Version:      m.Version,
	}
	return rv
}

const (
	SecretTypeLabel               = "argocd.argoproj.io/secret-type"
	SecretTypeCluster             = "cluster"
	SecretTypeRepo                = "repository"
	DefaultAddonsDir              = "addons"
	DefaultClusterOverrideDir     = "clusters"
	DefaultEnvOverrideDir         = "envs"
	HelmValuesFile1               = "values.yaml"
	HelmValuesFile2               = "values.yml"
	HelmAddonIdentifierFile1      = "Chart.yaml"
	HelmAddonIdentifierFile2      = "Chart.yml"
	KustomizeAddonIdentifierFile1 = "kustomization.yaml"
	KustomizeAddonIdentifierFile2 = "kustomization.yml"

	AddonMarketPlaceInstallForceRefresh = "force_refresh"
	ClusterAddonIDLabelKey              = "addon.akuity.io/cid"
	AddonIDLabelKey                     = "addon.akuity.io/id"
	TargetClusterKey                    = "addon.akuity.io/target-cluster"
	valuesTemplate                      = `# add chart specific values
{{- range .Dependencies }}
{{ .Name }}: {}
{{- end }}
`
)

var (
	sprigFuncMap           = sprig.GenericFuncMap()
	UpstreamApplicationsGR = tenant.Resource[typesv1.Application]{Group: "argoproj.io", Resource: "applications", Unmarshal: func(data []byte) (*typesv1.Application, error) {
		var res typesv1.Application
		return &res, json.Unmarshal(data, &res)
	}}
	UpstreamUnstructuredApplicationsGR = tenant.Resource[unstructured.Unstructured]{Group: "argoproj.io", Resource: "applications", Unmarshal: func(data []byte) (*unstructured.Unstructured, error) {
		var res unstructured.Unstructured
		if err := res.UnmarshalJSON(data); err != nil {
			return nil, err
		}
		res.SetGroupVersionKind(typesv1.ApplicationSchemaGroupVersionKind)
		return &res, nil
	}}
	errUnableToResolve = errors.New("unable to resolve")
	errGHError         = errors.New("remote: error: GH")
	gitErrors          = []error{
		transport.ErrRepositoryNotFound,
		transport.ErrEmptyRemoteRepository,
		transport.ErrAuthenticationRequired,
		transport.ErrAuthorizationFailed,
		transport.ErrEmptyUploadPackRequest,
		transport.ErrInvalidAuthMethod,
		transport.ErrAlreadyConnected,
		transport.ErrEmptyRemoteRepository,
		git.ErrBranchExists,
		git.ErrBranchExists,
		git.ErrBranchNotFound,
		git.ErrTagExists,
		git.ErrTagNotFound,
		git.ErrFetching,
		git.ErrInvalidReference,
		git.ErrRepositoryNotExists,
		git.ErrRepositoryIncomplete,
		git.ErrRepositoryAlreadyExists,
		git.ErrRemoteNotFound,
		git.ErrRemoteExists,
		git.ErrAnonymousRemoteName,
		git.ErrWorktreeNotProvided,
		git.ErrIsBareRepository,
		git.ErrUnableToResolveCommit,
		git.ErrPackedObjectsNotSupported,
		git.ErrSHA256NotSupported,
		git.ErrAlternatePathNotSupported,
		git.ErrUnsupportedMergeStrategy,
		git.ErrFastForwardMergeNotPossible,
		// argocd git client error
		errUnableToResolve,
		// github error
		errGHError,
	}
	errSomethingWentWrong = fmt.Errorf("something went wrong while processing request")
)

func GetRepository(ctx context.Context, log *logr.Logger, db boil.ContextExecutor, repoURL, instanceID string) (*typesv1.Repository, bool, error) {
	iterator, closer, err := tenant.SecretsGR.Iterate(ctx, false, db, instanceID)
	if err != nil {
		return nil, false, err
	}
	defer ioutil.Close(closer)
	var repo *typesv1.Repository
	found := false
	for {
		secret, err := iterator()
		if err != nil {
			return nil, false, err
		}
		if secret == nil {
			break
		}

		if secret.Item.Labels[SecretTypeLabel] != SecretTypeRepo {
			continue
		}

		if argocd_repo.SameURL(string(secret.Item.Data["url"]), repoURL) {
			repo, err = argocd_repo.SecretToRepository(&secret.Item)
			if err != nil {
				return nil, false, err
			}
			found = true
			break
		}
	}
	if repo == nil {
		repo = &typesv1.Repository{
			Repo: repoURL,
		}
	}
	return repo, found, nil
}

func ResolveRevision(ctx context.Context, log *logr.Logger, client argocd_repo.IRepoClient, repo *typesv1.Repository, revision string) (string, error) {
	resolvedRevision, err := client.ResolveRevision(ctx, &argocd_repo.ResolveRevisionRequest{
		Repo: repo,
		App: &typesv1.Application{
			Spec: typesv1.ApplicationSpec{
				Source: &typesv1.ApplicationSource{},
			},
		},
		AmbiguousRevision: revision,
		SourceIndex:       0,
	})
	if err != nil {
		return "", err
	}
	return resolvedRevision.Revision, nil
}

func GetAddons(ctx context.Context, log *logr.Logger, client argocd_repo.IRepoClient, repo *typesv1.Repository, revision string, existingAddonSpecs map[string]*models.AddonSpec, kustomizeBuildOps string) (map[string]*models.AddonSpec, error) {
	addons := map[string]*models.AddonSpec{}

	files, err := client.GetGitFiles(ctx, &argocd_repo.GitFilesRequest{
		Repo:                      repo,
		SubmoduleEnabled:          false,
		Revision:                  revision,
		Path:                      DefaultAddonsDir + fmt.Sprintf("/*/{%v}", strings.Join([]string{HelmValuesFile1, HelmAddonIdentifierFile1, KustomizeAddonIdentifierFile1}, ",")),
		NewGitFileGlobbingEnabled: true,
		NoRevisionCache:           true,
	})
	if err != nil {
		return nil, err
	}

	for filename := range files.Map {
		splits := strings.Split(strings.TrimPrefix(filename, DefaultAddonsDir+"/"), "/")
		if len(splits) == 2 {
			if splits[1] == HelmValuesFile1 {
				// ignore values.yaml only look for Chart.yaml in default dir, values may or may not be there
				continue
			}
			addonName := splits[0]
			src := models.ManifestSource{}
			typ := models.AddonTypeUnknown
			helmPath := DefaultAddonsDir + "/" + addonName + "/" + HelmValuesFile1
			kustomizePath := DefaultAddonsDir + "/" + addonName + "/" + KustomizeAddonIdentifierFile1
			addons[addonName] = &models.AddonSpec{
				Name:      addonName,
				AddonType: typ,
			}
			switch splits[1] {
			case HelmAddonIdentifierFile1:
				typ = models.AddonTypeHelm
				if spec, ok := existingAddonSpecs[addonName]; ok && spec.AddonType == models.AddonTypeHelm && spec.HelmValues != nil && len(spec.HelmValues.YamlPaths) > 0 && len(files.Map[helmPath]) > 0 {
					src, err = getHelmManifestSource(files.Map[helmPath], spec.HelmValues.YamlPaths)
					if err != nil {
						return nil, utilErrors.NewUserError(fmt.Errorf("error handling helm values file %v : %w", helmPath, err))
					}
					addons[addonName].HelmValues = spec.HelmValues
				}
				deps, err := getHelmChartDeps(files.Map[filename])
				if err != nil {
					return nil, utilErrors.NewUserError(fmt.Errorf("error handling helm chart.yaml file %v : %w", filename, err))
				}
				if src.HelmSource == nil {
					src.HelmSource = &models.HelmSource{
						ChartDependencies: deps,
						Values:            map[string]string{},
					}
				} else {
					src.HelmSource.ChartDependencies = deps
				}
				src.Path = helmPath
				chksum, err := checksum(files.Map[filename])
				if err != nil {
					return nil, err
				}
				addons[addonName].Checksum = chksum
			case KustomizeAddonIdentifierFile1:
				typ = models.AddonTypeKustomize

				sourcePath := DefaultAddonsDir + "/" + addonName
				src, err = getKustomizationManifestSource(ctx, log, sourcePath, client, repo, addonName, revision, kustomizeBuildOps, files.Map[kustomizePath])
				if err != nil {
					return nil, utilErrors.NewUserError(fmt.Errorf("error handling kustomize file %v : %w", kustomizePath, err))
				}
				src.Path = kustomizePath
			}
			addons[addonName].DefaultManifest = src
			addons[addonName].AddonType = typ
		}
	}

	clusterAddonOverrides, err := getAddonManifestSourceDetails(ctx, log, client, repo, revision, DefaultClusterOverrideDir, kustomizeBuildOps, addons)
	if err != nil {
		return nil, err
	}
	envAddonOverrides, err := getAddonManifestSourceDetails(ctx, log, client, repo, revision, DefaultEnvOverrideDir, kustomizeBuildOps, addons)
	if err != nil {
		return nil, err
	}
	for _, addon := range addons {
		if _, ok := clusterAddonOverrides[addon.Name]; ok {
			addon.ClusterOverrides = clusterAddonOverrides[addon.Name]
		}
		if _, ok := envAddonOverrides[addon.Name]; ok {
			addon.EnvOverrides = envAddonOverrides[addon.Name]
		}
	}

	return addons, nil
}

func sanitizeError(log *logr.Logger, err error, msg string) error {
	ignored := false
	var ignore *utilErrors.IgnoredError
	if errors.As(err, &ignore) {
		ignored = true
		err = ignore.Unwrap()
	}
	gitErr, ok := getGitError(err)
	if ok {
		switch {
		case errors.Is(gitErr, transport.ErrInvalidAuthMethod), errors.Is(gitErr, transport.ErrAuthenticationRequired), errors.Is(gitErr, transport.ErrAuthorizationFailed):
			// we don't want to retry auth error
			return gitErr
		default:
			return utilErrors.NewRetryableError(gitErr, "")
		}
	}

	var userErr *utilErrors.UserError
	if errors.As(err, &userErr) {
		return userErr
	}

	if !ignored {
		log.Error(err, msg)
	}

	if utilErrors.IsRetryable(err) {
		return utilErrors.NewRetryableError(errSomethingWentWrong, "")
	}

	return errSomethingWentWrong
}

func getGitError(err error) (error, bool) {
	if se, ok := status.FromError(err); ok {
		for _, e := range gitErrors {
			if strings.Contains(se.Err().Error(), e.Error()) {
				if errors.Is(e, errUnableToResolve) || errors.Is(e, errGHError) {
					return errors.New(se.Message()), true
				}
				return e, true
			}
		}
	}
	return err, false
}

func InstallAddon(ctx context.Context, log *logr.Logger, client argocd_repo.IRepoClient, repo *typesv1.Repository, instanceID, amiID string, generation int, addonConfig *models.AddonMarketplaceInstallConfig, addonStatus *models.AddonMarketplaceStatus, event models.AddonEvent) (models.AddonEvent, error) {
	op := "install"
	dirPath := DefaultAddonsDir + "/" + addonConfig.AddonName
	files, err := client.GetGitFiles(ctx, &argocd_repo.GitFilesRequest{
		Repo:                      repo,
		SubmoduleEnabled:          false,
		Revision:                  addonConfig.Revision,
		Path:                      dirPath + "/*",
		NewGitFileGlobbingEnabled: true,
		NoRevisionCache:           true,
	})
	if err != nil {
		return event, sanitizeError(log, err, "retrying get git files")
	}
	defaultSourcePath := dirPath + "/"
	chartPath := defaultSourcePath + HelmAddonIdentifierFile1
	if len(files.Map) > 0 {
		op = "update"
		event.Type = models.AddonEventTypeMarketplaceUpdateFailed
		// we need to check if content is unchanged
		if addonStatus.LastProcessedHash == "" {
			return event, utilErrors.NewUserError(fmt.Errorf("directory %s already exists", dirPath))
		}
		if addonStatus.LastProcessedHash != AddonMarketPlaceInstallForceRefresh {
			// we need to check checksum only when refresh not forced
			data, ok := files.Map[chartPath]
			if !ok {
				return event, utilErrors.NewUserError(fmt.Errorf("chart file %s not found", chartPath))
			}
			chksum, err := checksum(data)
			if err != nil {
				return event, sanitizeError(log, err, "can't get checksum")
			}
			if chksum != addonStatus.LastProcessedHash {
				return event, utilErrors.NewUserError(fmt.Errorf("chart file content manually changed, cannot be managed my Akuity Addons"))
			}
		}
	}
	updatedFiles := []*argocd_repo.FileDetails{}

	c := chartTemplate{
		APIVersion:             "v2",
		AppVersion:             "1",
		helmChartInstallConfig: fromHelmChartInstallConfig(addonConfig.HelmChartConfig),
	}
	data, err := yaml.Marshal(c)
	if err != nil {
		return event, sanitizeError(log, err, "can't marshal chart")
	}

	updatedFiles = append(updatedFiles, &argocd_repo.FileDetails{
		Path: chartPath,
		Data: data,
	})
	chksum, err := checksum(data)
	if err != nil {
		return event, sanitizeError(log, err, "can't get checksum")
	}

	valuesBuf := bytes.Buffer{}
	tmpl, err := template.New("chart").Parse(valuesTemplate)
	if err != nil {
		return event, sanitizeError(log, err, "can't parse values template")
	}
	if err := tmpl.Execute(&valuesBuf, addonConfig.HelmChartConfig); err != nil {
		return event, sanitizeError(log, err, "can't execute value template")
	}

	valuesPath := defaultSourcePath + HelmValuesFile1
	if _, ok := files.Map[valuesPath]; !ok {
		// create values file only if it doesn't exists already
		updatedFiles = append(updatedFiles, &argocd_repo.FileDetails{
			Path: valuesPath,
			Data: valuesBuf.Bytes(),
		})
	}

	if addonConfig.Overrides != nil {
		// check env overrides
		if len(addonConfig.Overrides.Envs) > 0 {
			envs := []string{}
			for env := range addonConfig.Overrides.Envs {
				envs = append(envs, env)
			}
			updates, err := bootstrapOverrides(ctx, log, client, repo, addonConfig.Revision, DefaultEnvOverrideDir, valuesBuf.String(), envs, addonConfig)
			if err != nil {
				return event, err
			}
			if len(updates) > 0 {
				updatedFiles = append(updatedFiles, updates...)
			}
		}
		// check cluster overrides
		if len(addonConfig.Overrides.Clusters) > 0 {
			clusters := []string{}
			for cluster := range addonConfig.Overrides.Clusters {
				clusters = append(clusters, cluster)
			}
			updates, err := bootstrapOverrides(ctx, log, client, repo, addonConfig.Revision, DefaultClusterOverrideDir, valuesBuf.String(), clusters, addonConfig)
			if err != nil {
				return event, err
			}
			if len(updates) > 0 {
				updatedFiles = append(updatedFiles, updates...)
			}
		}
	}

	_, err = client.CommitFiles(ctx, &argocd_repo.CommitFilesRequest{
		Repo:          repo,
		TargetBranch:  addonConfig.Revision,
		CommitMessage: fmt.Sprintf("%s addon %s(%s) from addon marketplace for argocd instance %v, revision %d", op, addonConfig.AddonName, amiID, instanceID, generation),
		Files:         updatedFiles,
		Username:      "Akuity Platform",
		Email:         "<EMAIL>",
	})
	if err != nil {
		return event, sanitizeError(log, err, "retrying to commit files to git")
	}
	addonStatus.LastProcessedHash = chksum
	event.Type = models.AddonEventTypeMarketplaceInstallSucceeded
	event.Message = "Addon installed successfully"
	if addonConfig.HelmChartConfig != nil {
		event.Dependencies = addonConfig.HelmChartConfig.Dependencies
	}
	if op == "update" {
		event.Type = models.AddonEventTypeMarketplaceUpdateSucceeded
		event.Message = "Addon updated successfully"
	}
	return event, nil
}

func bootstrapOverrides(ctx context.Context, log *logr.Logger, client argocd_repo.IRepoClient, repo *typesv1.Repository, revision, defaultSourcePath, fileData string, newDirs []string, config *models.AddonMarketplaceInstallConfig) ([]*argocd_repo.FileDetails, error) {
	// only handles helm values file for now
	files, err := client.GetGitFiles(ctx, &argocd_repo.GitFilesRequest{
		Repo:                      repo,
		SubmoduleEnabled:          false,
		Revision:                  revision,
		Path:                      defaultSourcePath + fmt.Sprintf("/*/%v/%v", config.AddonName, HelmValuesFile1),
		NewGitFileGlobbingEnabled: true,
		NoRevisionCache:           true,
	})
	if err != nil {
		return nil, sanitizeError(log, err, "retrying get git files")
	}
	if len(newDirs) != 0 {
		updatedFiles := []*argocd_repo.FileDetails{}
		for _, dir := range newDirs {
			sourcePath := defaultSourcePath + fmt.Sprintf("/%v/%v/%v", dir, config.AddonName, HelmValuesFile1)
			if _, ok := files.Map[sourcePath]; !ok {
				updatedFiles = append(updatedFiles, &argocd_repo.FileDetails{
					Path: sourcePath,
					Data: []byte(fileData),
				})
			}
		}
		return updatedFiles, nil
	}
	return nil, nil
}

func UpdateManifestSources(ctx context.Context, log *logr.Logger, client argocd_repo.IRepoClient, repo *typesv1.Repository, branch, instanceID string, addonSpec *models.AddonSpec, sourceUpdates *models.StatusSourceUpdate, generation int) (string, error) {
	updatedFiles := []*argocd_repo.FileDetails{}

	if sourceUpdates.DefaultManifestSource != nil {
		fileUpdate, err := handleSourceUpdate(ctx, log, client, repo, branch, DefaultAddonsDir+"/"+addonSpec.Name+"/", addonSpec.AddonType, addonSpec.DefaultManifest, *sourceUpdates.DefaultManifestSource)
		if err != nil {
			return "", err
		}
		updatedFiles = append(updatedFiles, fileUpdate...)
	}
	for env, source := range sourceUpdates.EnvOverrides {
		if addonSpec.EnvOverrides == nil {
			return "", utilErrors.NewUserError(fmt.Errorf("addon %v doesn't have env overrides set, cannot update source for env %v", addonSpec.Name, env))
		} else if _, ok := addonSpec.EnvOverrides[env]; !ok {
			return "", utilErrors.NewUserError(fmt.Errorf("addon %v doesn't have env overrides set for env %v, cannot update source", addonSpec.Name, env))
		}
		fileUpdate, err := handleSourceUpdate(ctx, log, client, repo, branch, DefaultEnvOverrideDir+"/"+env+"/"+addonSpec.Name+"/", addonSpec.AddonType, addonSpec.EnvOverrides[env], source)
		if err != nil {
			return "", err
		}
		updatedFiles = append(updatedFiles, fileUpdate...)
	}
	for cluster, source := range sourceUpdates.ClusterOverrides {
		if addonSpec.ClusterOverrides == nil {
			return "", utilErrors.NewUserError(fmt.Errorf("addon %v doesn't have cluster overrides set, cannot update source for cluster %v", addonSpec.Name, cluster))
		} else if _, ok := addonSpec.ClusterOverrides[cluster]; !ok {
			return "", utilErrors.NewUserError(fmt.Errorf("addon %v doesn't have cluster overrides set for cluster %v, cannot update source", addonSpec.Name, cluster))
		}
		fileUpdate, err := handleSourceUpdate(ctx, log, client, repo, branch, DefaultClusterOverrideDir+"/"+cluster+"/"+addonSpec.Name+"/", addonSpec.AddonType, addonSpec.ClusterOverrides[cluster], source)
		if err != nil {
			return "", err
		}
		updatedFiles = append(updatedFiles, fileUpdate...)
	}

	out, err := client.CommitFiles(ctx, &argocd_repo.CommitFilesRequest{
		Repo:          repo,
		TargetBranch:  branch,
		CommitMessage: fmt.Sprintf("updating addon sources for argocd instance %v, revision %d", instanceID, generation),
		Files:         updatedFiles,
		Username:      "Akuity Platform",
		Email:         "<EMAIL>",
	})
	if err != nil {
		return "", err
	}
	return out.CommitSha, nil
}

func getAddonManifestSourceDetails(ctx context.Context, log *logr.Logger, client argocd_repo.IRepoClient, repo *typesv1.Repository, revision, rootDir, kustomizeBuildOps string, addons map[string]*models.AddonSpec) (map[string]map[string]models.ManifestSource, error) {
	// finding all helm and kustomization files
	pathPattern := rootDir + fmt.Sprintf("/*/*/{%v}", strings.Join([]string{HelmValuesFile1, HelmAddonIdentifierFile1, KustomizeAddonIdentifierFile1}, ","))

	allPatternFiles, err := client.GetGitFiles(ctx, &argocd_repo.GitFilesRequest{
		Repo:                      repo,
		SubmoduleEnabled:          false,
		Revision:                  revision,
		Path:                      pathPattern,
		NewGitFileGlobbingEnabled: true,
		NoRevisionCache:           true,
	})
	if err != nil {
		return nil, err
	}

	sourceDetails := make(map[string]map[string]models.ManifestSource)

	files := map[string]map[string][]string{}

	for filename := range allPatternFiles.Map {
		splits := strings.Split(strings.TrimPrefix(filename, rootDir+"/"), "/")
		if len(splits) < 3 {
			return nil, utilErrors.NewUserError(fmt.Errorf("expected file path to be in format <rootDir>/<addonName>/<fileName>, got file path : %s", filename))
		}
		if len(files[splits[1]]) == 0 {
			files[splits[1]] = map[string][]string{}
		}
		if len(files[splits[1]][splits[0]]) == 0 {
			files[splits[1]][splits[0]] = []string{}
		}
		files[splits[1]][splits[0]] = append(files[splits[1]][splits[0]], filename)
	}

	for addonName, overrides := range files {
		for override, fileList := range overrides {
			if addon, ok := addons[addonName]; ok {
				src := models.ManifestSource{}
				if addon.AddonType == models.AddonTypeHelm {
					processed := false
					for _, filename := range fileList {
						if !strings.HasSuffix(filename, "/"+HelmAddonIdentifierFile1) && !strings.HasSuffix(filename, "/"+HelmValuesFile1) {
							continue
						}
						processed = true
						if strings.HasSuffix(filename, "/"+HelmValuesFile1) {
							var tempSrc models.ManifestSource
							if addon.HelmValues != nil && len(addon.HelmValues.YamlPaths) > 0 {
								tempSrc, err = getHelmManifestSource(allPatternFiles.Map[filename], addon.HelmValues.YamlPaths)
								if err != nil {
									return nil, utilErrors.NewUserError(fmt.Errorf("error handling helm values file %v : %w", filename, err))
								}
							}
							if src.HelmSource == nil {
								src = tempSrc
								src.Path = filename
							} else if tempSrc.HelmSource != nil {
								src.HelmSource.Values = tempSrc.HelmSource.Values
							}
						} else {
							deps, err := getHelmChartDeps(allPatternFiles.Map[filename])
							if err != nil {
								return nil, utilErrors.NewUserError(fmt.Errorf("error handling helm Chart file %v : %w", filename, err))
							}
							if src.HelmSource == nil {
								src.HelmSource = &models.HelmSource{
									ChartDependencies: deps,
									Values:            map[string]string{},
								}
								src.Path = filename
							} else {
								src.HelmSource.ChartDependencies = deps
							}
						}
					}
					if !processed {
						return nil, utilErrors.NewUserError(fmt.Errorf("expected helm values.yaml and optionally Chart.yaml file, for helm addon=%s, override=%v/%v", addonName, rootDir, override))
					}
				} else if addon.AddonType == models.AddonTypeKustomize {
					processed := false
					for _, filename := range fileList {
						if !strings.HasSuffix(filename, "/"+KustomizeAddonIdentifierFile1) {
							continue
						}
						processed = true
						sourcePath := strings.TrimSuffix(filename, "/"+KustomizeAddonIdentifierFile1)
						src, err = getKustomizationManifestSource(ctx, log, sourcePath, client, repo, addon.Name, revision, kustomizeBuildOps, allPatternFiles.Map[filename])
						if err != nil {
							return nil, utilErrors.NewUserError(fmt.Errorf("error handling kustomize file %v : %w", filename, err))
						}
						src.Path = filename
					}
					if !processed {
						return nil, utilErrors.NewUserError(fmt.Errorf("expected kustomization.yaml file, for kustomize addon=%s, override=%v/%v", addonName, rootDir, override))
					}
				}
				if sourceDetails[addonName] == nil {
					sourceDetails[addonName] = map[string]models.ManifestSource{}
				}
				sourceDetails[addonName][override] = src
			}
		}
	}

	return sourceDetails, nil
}

func getKustomizationManifestSource(ctx context.Context, log *logr.Logger, sourcePath string, client argocd_repo.IRepoClient, repo *typesv1.Repository, addonName, revision, kustomizeBuildOps string, dataBytes []byte) (models.ManifestSource, error) {
	req := &argocd_repo.RepoServerAppDetailsQuery{
		Repo: repo,
		Source: &typesv1.ApplicationSource{
			RepoURL:        repo.Repo,
			Path:           sourcePath,
			TargetRevision: revision,
		},
		KustomizeOptions: &argocd_repo.KustomizeOptions{BuildOptions: kustomizeBuildOps},
	}

	resp, err := client.GetAppDetails(ctx, req)
	if err != nil {
		log.Info("Warn: unable to get app details for kustomize images", "err", err, "addonName", addonName)
	}
	kustomizeSource := kustomize.Kustomization{}
	if err := kustomizeSource.Unmarshal(dataBytes); err != nil {
		return models.ManifestSource{}, fmt.Errorf("unmarshalling kustomization file : %w", err)
	}
	if resp.Kustomize != nil {
		for _, i := range resp.Kustomize.Images {
			s := strings.Split(i, ":")
			if len(s) != 2 {
				log.Error(fmt.Errorf("length should be 2, but have %d", len(s)), "unable to parse kustomize image", "image", i)
				continue
			}
			kustomizeSource.SetImage(kustomize.Image{
				Name:   s[0],
				NewTag: s[1],
			})
		}
	}

	return models.ManifestSource{
		KustomizeSource: &models.KustomizeSource{
			Images:     kustomizeSource.GetImagesInterface(),
			HelmCharts: kustomizeSource.GetHelmCharts(),
		},
	}, nil
}

func getHelmChartDeps(dataBytes []byte) ([]models.ChartDependency, error) {
	helmValues := []models.ChartDependency{}
	chart := map[string]interface{}{}
	if err := yaml.Unmarshal(dataBytes, &chart); err != nil {
		return nil, fmt.Errorf("failed to parse provided chart.yaml: %w", err)
	}

	if data, ok := chart["dependencies"]; ok {
		deps := data.([]interface{})
		for _, dep := range deps {
			depMap := dep.(map[string]interface{})
			helmValues = append(helmValues, models.ChartDependency{
				Name:       depMap["name"].(string),
				Version:    depMap["version"].(string),
				Repository: depMap["repository"].(string),
			})
		}
	}

	return helmValues, nil
}

func getHelmManifestSource(dataBytes []byte, valuePaths []string) (models.ManifestSource, error) {
	helmValues := map[string]string{}
	helmValuesFile := map[string]interface{}{}
	if err := yaml.Unmarshal(dataBytes, &helmValuesFile); err != nil {
		return models.ManifestSource{}, fmt.Errorf("failed to parse provided values yaml: %w", err)
	}
	for _, valPath := range valuePaths {
		val, err := yamlutils.ExtractYAMLPathValue(helmValuesFile, valPath)
		if err != nil {
			if errors.Is(err, yamlutils.ErrPathNotFound) {
				continue
			}
			return models.ManifestSource{}, fmt.Errorf("failed to read provided values yaml path: path=%v, %w", valPath, err)
		}
		helmValues[valPath] = val
	}
	return models.ManifestSource{
		HelmSource: &models.HelmSource{
			Values: helmValues,
		},
	}, nil
}

func handleSourceUpdate(ctx context.Context, log *logr.Logger, client argocd_repo.IRepoClient, repo *typesv1.Repository, revision, defaultSourcePath string, addonType models.AddonType, originalSource, sourceUpdates models.ManifestSource) ([]*argocd_repo.FileDetails, error) {
	if addonType == models.AddonTypeKustomize && sourceUpdates.KustomizeSource == nil {
		return []*argocd_repo.FileDetails{}, nil
	} else if addonType == models.AddonTypeHelm && sourceUpdates.HelmSource == nil {
		return []*argocd_repo.FileDetails{}, nil
	}

	updatedFiles := []*argocd_repo.FileDetails{}
	switch addonType {
	case models.AddonTypeKustomize:
		sourcePath := defaultSourcePath + KustomizeAddonIdentifierFile1
		files, err := client.GetGitFiles(ctx, &argocd_repo.GitFilesRequest{
			Repo:                      repo,
			SubmoduleEnabled:          false,
			Revision:                  revision,
			Path:                      sourcePath,
			NewGitFileGlobbingEnabled: true,
			NoRevisionCache:           true,
		})
		if err != nil {
			return nil, err
		}
		if _, ok := files.Map[sourcePath]; !ok {
			return nil, fmt.Errorf("couldn't find default source file %v", sourcePath)
		}
		updated, err := updateKustomizeSource(files.Map[sourcePath], sourceUpdates)
		if err != nil {
			return nil, fmt.Errorf("error updating default source file %v : %w", defaultSourcePath, err)
		}
		updatedFiles = append(updatedFiles, &argocd_repo.FileDetails{
			Path: sourcePath,
			Data: updated,
		})
	case models.AddonTypeHelm:
		files, err := client.GetGitFiles(ctx, &argocd_repo.GitFilesRequest{
			Repo:                      repo,
			SubmoduleEnabled:          false,
			Revision:                  revision,
			Path:                      defaultSourcePath + fmt.Sprintf("{%v,%v}", HelmValuesFile1, HelmAddonIdentifierFile1),
			NewGitFileGlobbingEnabled: true,
			NoRevisionCache:           true,
		})
		if err != nil {
			return nil, err
		}
		if len(sourceUpdates.HelmSource.Values) != 0 {
			sourcePath := defaultSourcePath + HelmValuesFile1
			if _, ok := files.Map[sourcePath]; ok {
				updated, err := updateHelmValuesSource(files.Map[sourcePath], sourceUpdates)
				if err != nil {
					return nil, fmt.Errorf("error updating default source file %v : %w", sourcePath, err)
				}
				updatedFiles = append(updatedFiles, &argocd_repo.FileDetails{
					Path: sourcePath,
					Data: updated,
				})
			}
		}

		if len(sourceUpdates.HelmSource.ChartDependencies) != 0 && len(originalSource.HelmSource.ChartDependencies) != 0 {
			sourcePath := defaultSourcePath + HelmAddonIdentifierFile1
			if _, ok := files.Map[sourcePath]; ok {
				updated, err := updateHelmChartDependencies(files.Map[sourcePath], sourceUpdates.HelmSource.ChartDependencies)
				if err != nil {
					return nil, fmt.Errorf("error updating default source file %v : %w", defaultSourcePath, err)
				}
				updatedFiles = append(updatedFiles, &argocd_repo.FileDetails{
					Path: sourcePath,
					Data: updated,
				})
			}
		}
	default:
		return nil, fmt.Errorf("addon type %v not supported for source updates", addonType)
	}

	return updatedFiles, nil
}

func updateKustomizeSource(dataBytes []byte, sourceUpdate models.ManifestSource) ([]byte, error) {
	kustomizeSource := kustomize.Kustomization{}
	if err := kustomizeSource.Unmarshal(dataBytes); err != nil {
		return nil, fmt.Errorf("unmarshalling kustomization file : %w", err)
	}

	kustomizeSource.UpdateImages(sourceUpdate.KustomizeSource.Images)
	kustomizeSource.UpdateHelmCharts(sourceUpdate.KustomizeSource.HelmCharts)

	return yaml.Marshal(kustomizeSource)
}

func updateHelmChartDependencies(dataBytes []byte, chartDeps []models.ChartDependency) ([]byte, error) {
	chart := map[string]interface{}{}
	if err := yaml.Unmarshal(dataBytes, &chart); err != nil {
		return nil, fmt.Errorf("failed to parse provided chart.yaml: %w", err)
	}

	if data, ok := chart["dependencies"]; ok {
		deps := data.([]interface{})
		for i, dep := range deps {
			depMap := dep.(map[string]interface{})
			for _, chartDep := range chartDeps {
				if depMap["name"] == chartDep.Name {
					depMap["version"] = chartDep.Version
				}
			}
			deps[i] = depMap
		}
		chart["dependencies"] = deps
	}
	return yaml.Marshal(chart)
}

func updateHelmValuesSource(dataBytes []byte, sourceUpdate models.ManifestSource) ([]byte, error) {
	if len(sourceUpdate.HelmSource.Values) == 0 {
		return dataBytes, nil
	}
	helmValuesFile := map[string]interface{}{}
	if err := yaml.Unmarshal(dataBytes, &helmValuesFile); err != nil {
		return nil, fmt.Errorf("failed to parse provided values yaml: %w", err)
	}

	for path, value := range sourceUpdate.HelmSource.Values {
		if err := yamlutils.SetYAMLPathValue(helmValuesFile, path, []byte(value)); err != nil {
			return nil, fmt.Errorf("failed to set provided values yaml path: path=%v, %w", path, err)
		}
	}
	return yaml.Marshal(helmValuesFile)
}

func GetAddonApplications(ctx context.Context, log *logr.Logger, db boil.ContextExecutor, addon *models.Addon, clusterNames []string) (map[string]typesv1.Application, error) {
	iterator, closer, err := UpstreamApplicationsGR.Iterate(ctx, false, db, addon.InstanceID)
	if err != nil {
		return nil, err
	}
	defer ioutil.Close(closer)

	res := map[string]typesv1.Application{}

	for {
		app, err := iterator()
		if err != nil {
			return nil, err
		}
		if app == nil {
			break
		}
		if app.Item.Labels[AddonIDLabelKey] == addon.ID {
			if len(clusterNames) > 0 {
				if slices.Contains(clusterNames, app.Item.Labels[TargetClusterKey]) {
					res[app.Item.Name] = app.Item
				}
			} else {
				res[app.Item.Name] = app.Item
			}
		}
	}
	return res, nil
}

func GetAddonUnstructuredApplications(ctx context.Context, log *logr.Logger, db boil.ContextExecutor, addon *models.Addon, clusterNames []string) (map[string]*unstructured.Unstructured, error) {
	iterator, closer, err := UpstreamUnstructuredApplicationsGR.Iterate(ctx, false, db, addon.InstanceID)
	if err != nil {
		return nil, err
	}
	defer ioutil.Close(closer)

	res := map[string]*unstructured.Unstructured{}

	for {
		app, err := iterator()
		if err != nil {
			return nil, err
		}
		if app == nil {
			break
		}
		if app.Item.GetLabels()[AddonIDLabelKey] == addon.ID {
			if len(clusterNames) > 0 {
				if slices.Contains(clusterNames, app.Item.GetLabels()[TargetClusterKey]) {
					res[app.Item.GetName()] = &app.Item
				}
			} else {
				res[app.Item.GetName()] = &app.Item
			}
		}
	}
	return res, nil
}

func GetApplication(ctx context.Context, log *logr.Logger, db boil.ContextExecutor, instanceID, appName string) (*typesv1.Application, error) {
	return UpstreamApplicationsGR.One(ctx, false, db, instanceID, tenant.Query{Name: appName})
}

func GetUnstructuredApplication(ctx context.Context, log *logr.Logger, db boil.ContextExecutor, instanceID, appName string) (*unstructured.Unstructured, error) {
	return UpstreamUnstructuredApplicationsGR.One(ctx, false, db, instanceID, tenant.Query{Name: appName})
}

func convertAppToUnstructured(app *typesv1.Application) (*unstructured.Unstructured, error) {
	unstructuredApp, err := runtime.DefaultUnstructuredConverter.ToUnstructured(app)
	if err != nil {
		return nil, err
	}
	uApp := &unstructured.Unstructured{Object: unstructuredApp}
	uApp.SetGroupVersionKind(typesv1.ApplicationSchemaGroupVersionKind)
	return uApp, nil
}

func TemplateString(str string, vals map[string]interface{}) (string, error) {
	textTemplate, err := template.New("text").Funcs(sprigFuncMap).Parse(str)
	if err != nil {
		return "", err
	}
	var textData bytes.Buffer
	if err := textTemplate.Execute(&textData, vals); err != nil {
		return "", err
	}
	return textData.String(), nil
}

func isClusterSelected(selector models.ClusterSelector, cluster *models.ArgoCDCluster) (bool, error) {
	spec, err := cluster.GetSpec()
	if err != nil {
		return false, err
	}
	// selection is based on whether the cluster matches any of the inclusion criteria (or op)
	// and whether it doesn't match any of the exclusion criteria (and op)
	selected := false
	positiveNameSelectors, negativeNameSelectors := models.SeparateNegativeFilters(selector.NameFilters)
	positiveLabelSelectors, negativeLabelSelectors := models.SeparateNegativeFilters(selector.LabelFilters)
	if len(positiveNameSelectors) > 0 || len(positiveLabelSelectors) > 0 {
		for _, s := range positiveLabelSelectors {
			if slices.Contains(s.Values, spec.Labels[*s.Key]) {
				selected = true
				break
			}
		}
		if !selected {
			for _, s := range positiveNameSelectors {
				if slices.Contains(s.Values, cluster.Name) {
					selected = true
					break
				}
			}
		}
	} else {
		// if no inclusions set select always
		selected = true
	}

	// if selected check for exclusions
	if selected {
		if len(negativeNameSelectors) > 0 || len(negativeLabelSelectors) > 0 {
			for _, s := range negativeLabelSelectors {
				if slices.Contains(s.Values, spec.Labels[*s.Key]) {
					return false, nil
				}
			}
			for _, s := range negativeNameSelectors {
				if slices.Contains(s.Values, cluster.Name) {
					return false, nil
				}
			}
		}
	}
	return selected, nil
}

func checksum(data []byte) (string, error) {
	hasher := sha1.New()
	if _, err := hasher.Write(data); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(hasher.Sum(nil)), nil
}

func DeleteGitAddon(ctx context.Context, log *logr.Logger, k3sDBRawClient boil.ContextExecutor, repoClient argocd_repo.IRepoClient, addonSpec *models.AddonSpec, instanceID, addonID, repoURL, revision string, generation int, status *models.AddonStatus) error {
	repo, found, err := GetRepository(ctx, log, k3sDBRawClient, repoURL, instanceID)
	if err != nil {
		status.Conditions.SetNotEstablished(models.AddonConditionTypeDeleteSuccessful, models.AddonConditionTypeSyncNotSuccessfulRepoNotFound, sanitizeError(log, err, "").Error())
		return err
	}
	if !found {
		err := fmt.Errorf("repository credentials not configured in argocd instance")
		status.Conditions.SetNotEstablished(models.AddonConditionTypeDeleteSuccessful, models.AddonConditionTypeSyncNotSuccessfulRepoNotFound, err.Error())
		return err
	}
	if !repo.HasCredentials() {
		err := fmt.Errorf("repository write credentials not configured in argocd instance")
		status.Conditions.SetNotEstablished(models.AddonConditionTypeDeleteSuccessful, models.AddonConditionTypeSyncNotSuccessfulRepoNotFound, err.Error())
		return err
	}

	deleteFiles := []string{DefaultAddonsDir + "/" + addonSpec.Name}
	for cluster := range addonSpec.ClusterOverrides {
		deleteFiles = append(deleteFiles, DefaultClusterOverrideDir+"/"+cluster+"/"+addonSpec.Name)
	}
	for env := range addonSpec.EnvOverrides {
		deleteFiles = append(deleteFiles, DefaultEnvOverrideDir+"/"+env+"/"+addonSpec.Name)
	}

	_, err = repoClient.DeleteFiles(ctx, &argocd_repo.DeleteFilesRequest{
		Repo:          repo,
		TargetBranch:  revision,
		CommitMessage: fmt.Sprintf("delete addon %s(%s) for argocd instance %v, revision: %d", addonSpec.Name, addonID, instanceID, generation),
		Paths:         deleteFiles,
		Username:      "Akuity Platform",
		Email:         "<EMAIL>",
	})
	if err != nil {
		status.Conditions.SetNotEstablished(models.AddonConditionTypeDeleteSuccessful, models.AddonConditionTypeSyncNotSuccessfulOperationFailed, sanitizeError(log, err, "unable to delete addon from git repo").Error())
		return err
	}
	return nil
}
