package integration

import (
	"context"
	"database/sql"
	"errors"
	"math/rand/v2"
	"time"

	"github.com/go-logr/logr"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	runtimeutil "k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"

	"github.com/akuityio/akuity-platform/controllers/shared/lib"
	"github.com/akuityio/akuity-platform/controllers/shared/tenant"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/version"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/events"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/models/util/dal"
)

type addonController struct {
	addonController                   database.TableController
	addonRepoController               database.TableController
	clusterAddonController            database.TableController
	addonMarketplaceInstallController database.TableController
	watchers                          []lib.CanStart
	log                               *logr.Logger
	settings                          ControllerSettings
	featSvc                           features.Service
}

type ControllerSettings struct {
	EnableIngress      bool
	K8SClientSet       kubernetes.Interface
	K8SRestConfig      *rest.Config
	RepoSet            client.RepoSet
	PortalDBRawClient  *sql.DB
	PortalDBConnection string
	K3sDBRawClient     *sql.DB
	K3sDBConnection    string
	Log                *logr.Logger
	Shard              string
}

func (s *ControllerSettings) GetPortalDBContext() database.DBContext {
	return database.NewPGDbContext(s.PortalDBRawClient, s.PortalDBConnection)
}

func (s *ControllerSettings) GetK3SDBContext() database.DBContext {
	return database.NewPGDbContext(s.K3sDBRawClient, s.K3sDBConnection)
}

func NewAddonController(
	log *logr.Logger,
	settings ControllerSettings,
	cfg config.AddonControllerConfig,
	featSvc features.Service,
) (*addonController, error) {
	addonReconcilerInstance := NewAddonReconciler(settings, featSvc, cfg)
	addonRepoReconcilerInstance := NewAddonRepoReconciler(settings, featSvc, cfg)
	clusterAddonReconcilerInstance := NewClusterAddonReconciler(settings, featSvc, cfg)
	addonMarketplaceInstallReconcilerInstance := NewAddonMarketplaceInstallReconciler(settings, featSvc, cfg)

	var addonWatcher database.Watcher[events.AddonEvent] = database.NewWatcher[events.AddonEvent](*log, settings.GetPortalDBContext(), events.AddonChannel)
	var addonRepoWatcher database.Watcher[events.AddonEvent] = database.NewWatcher[events.AddonEvent](*log, settings.GetPortalDBContext(), events.AddonRepoChannel)
	var clusterAddonRepoWatcher database.Watcher[events.AddonEvent] = database.NewWatcher[events.AddonEvent](*log, settings.GetPortalDBContext(), events.ClusterAddonChannel)
	var addonAppWatcher database.Watcher[tenant.AppUpdateEvent] = database.NewWatcher[tenant.AppUpdateEvent](*log, settings.GetK3SDBContext(), tenant.AppUpdateEventsChannel)
	var argocdClustersWatcher database.Watcher[events.ArgoCDClusterEvent] = database.NewWatcher[events.ArgoCDClusterEvent](*log, settings.GetPortalDBContext(), events.ArgoCDClusterChannel)
	var addonMarketplaceInstallWatcher database.Watcher[events.AddonEvent] = database.NewWatcher[events.AddonEvent](*log, settings.GetPortalDBContext(), events.AddonMarketplaceInstallChannel)

	addonErrors := promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "addon_errors",
			Help: "Total number of addon errors, per controller",
		}, []string{"controller"})

	addonEnqueueHeartbeat := promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "addon_enqueue_loop_duration_seconds",
		Help:    "Addon controller enqueue latencies distribution, per controller",
		Buckets: prometheus.LinearBuckets(0.01, 0.05, 40),
	}, []string{"controller"})

	addonHeartbeat := promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "addon_loop_duration_seconds",
		Help:    "Addon controller reconciliation latencies distribution, per controller",
		Buckets: prometheus.LinearBuckets(0.01, 0.05, 40),
	}, []string{"controller"})

	bc := &addonController{
		clusterAddonController: database.NewTableController[*models.ClusterAddon](
			func() dal.Repository[*models.ClusterAddon] {
				return settings.RepoSet.ClusterAddons(qm.Load(models.ClusterAddonRels.Addon))
			},
			*log,
			"cluster_addons",
			func(ctx context.Context) <-chan string {
				res := make(chan string)
				clusterAddonEvents := clusterAddonRepoWatcher.Subscribe(ctx, func(e events.AddonEvent) bool {
					return e.Shard == settings.Shard && lib.EventSpecChanged(e)
				})
				addonAppChangedEvents := addonAppWatcher.Subscribe(ctx, func(e tenant.AppUpdateEvent) bool {
					return e.ClusterAddonID != ""
				})
				go func() {
					for {
						select {
						case <-ctx.Done():
							return
						case event := <-clusterAddonEvents:
							res <- event.ID
						case event := <-addonAppChangedEvents:
							// validate the addon instance belongs to current shard
							_, err := settings.RepoSet.ArgoCDInstances(
								models.ArgoCDInstanceWhere.Shard.EQ(settings.Shard),
								models.ArgoCDInstanceWhere.ID.EQ(event.InstanceID),
							).One(ctx)
							if err == nil {
								res <- event.ClusterAddonID
							} else if !errors.Is(err, sql.ErrNoRows) {
								log.Error(err, "failed to extract addon id from addon app update event", "app_name", event.AppName, "instance", event.InstanceID)
							}
						}
					}
				}()
				return res
			},
			clusterAddonReconcilerInstance,
			addonErrors,
			addonEnqueueHeartbeat,
			addonHeartbeat,
			database.WithResyncDuration[*models.ClusterAddon](cfg.ResyncDuration),
			database.WithReconciliationTimeout[*models.ClusterAddon](cfg.ReconciliationTimeout),
			database.WithEnqueueAllDelayFunc[*models.ClusterAddon](func() time.Duration {
				// Jitter before executing enqueueAll() operation, between 0 and 20% of ResyncDuration
				return time.Duration(float64(cfg.ResyncDuration) * 0.2 * rand.Float64())
			}),
			database.WithEnqueueDelayFunc[*models.ClusterAddon](func(i, count int) time.Duration {
				// Jitter when enqueueing an item into a queue, between 0 and 1000 ms
				return time.Duration(float64(1000)*rand.Float64()) * time.Millisecond
			}),
		),

		addonController: database.NewTableController[*models.Addon](
			func() dal.Repository[*models.Addon] { return settings.RepoSet.Addons(qm.Load(models.AddonRels.Repo)) },
			*log,
			"addons",
			func(ctx context.Context) <-chan string {
				res := make(chan string)
				addonEvents := addonWatcher.Subscribe(ctx, func(e events.AddonEvent) bool {
					return e.Shard == settings.Shard && lib.EventSpecChanged(e)
				})
				argocdClustersEvents := argocdClustersWatcher.Subscribe(ctx, func(e events.ArgoCDClusterEvent) bool {
					return e.Shard == settings.Shard && (lib.EventSpecChanged(e) || e.Type == events.TypeDeleted)
				})
				go func() {
					for {
						select {
						case <-ctx.Done():
							return
						case event := <-addonEvents:
							res <- event.ID
						case event := <-argocdClustersEvents:
							// the shard is already verified in the event filter
							// so we just pull all the addons relating to the instance
							addons, err := settings.RepoSet.Addons(models.AddonWhere.InstanceID.EQ(event.InstanceID)).ListAll(ctx, models.AddonColumns.ID)
							if err == nil {
								for _, addon := range addons {
									res <- addon.ID
								}
							} else if !errors.Is(err, sql.ErrNoRows) {
								log.Error(err, "failed to extract addon ids from argocd cluster update event", "instance", event.InstanceID)
							}
						}
					}
				}()
				return res
			},
			addonReconcilerInstance,
			addonErrors,
			addonEnqueueHeartbeat,
			addonHeartbeat,
			database.WithResyncDuration[*models.Addon](cfg.ResyncDuration),
			database.WithReconciliationTimeout[*models.Addon](cfg.ReconciliationTimeout),
			database.WithEnqueueAllDelayFunc[*models.Addon](func() time.Duration {
				// Jitter before executing enqueueAll() operation, between 0 and 20% of ResyncDuration
				return time.Duration(float64(cfg.ResyncDuration) * 0.2 * rand.Float64())
			}),
			database.WithEnqueueDelayFunc[*models.Addon](func(i, count int) time.Duration {
				// Jitter when enqueueing an item into a queue, between 0 and 1000 ms
				return time.Duration(float64(1000)*rand.Float64()) * time.Millisecond
			}),
		),

		addonRepoController: database.NewTableController[*models.AddonRepo](
			func() dal.Repository[*models.AddonRepo] { return settings.RepoSet.AddonRepo() },
			*log,
			"addon_repo",
			func(ctx context.Context) <-chan string {
				return lib.SubscribeToIds(ctx, addonRepoWatcher, func(event events.AddonEvent) bool {
					return event.Shard == settings.Shard && lib.EventSpecChanged(event)
				}, lib.GetEventId)
			},
			addonRepoReconcilerInstance,
			addonErrors,
			addonEnqueueHeartbeat,
			addonHeartbeat,
			database.WithResyncDuration[*models.AddonRepo](cfg.ResyncDuration),
			database.WithReconciliationTimeout[*models.AddonRepo](cfg.ReconciliationTimeout),
			database.WithEnqueueAllDelayFunc[*models.AddonRepo](func() time.Duration {
				// Jitter before executing enqueueAll() operation, between 0 and 20% of ResyncDuration
				return time.Duration(float64(cfg.ResyncDuration) * 0.2 * rand.Float64())
			}),
			database.WithEnqueueDelayFunc[*models.AddonRepo](func(i, count int) time.Duration {
				// Jitter when enqueueing an item into a queue, between 0 and 1000 ms
				return time.Duration(float64(1000)*rand.Float64()) * time.Millisecond
			}),
		),
		addonMarketplaceInstallController: database.NewTableController[*models.AddonMarketplaceInstall](
			func() dal.Repository[*models.AddonMarketplaceInstall] {
				return settings.RepoSet.AddonMarketplaceInstalls(qm.Where(models.AddonMarketplaceInstallColumns.Generation + " != coalesce(status_processed_generation, 0)"))
			},
			*log,
			"addon_marketplace_install",
			func(ctx context.Context) <-chan string {
				return lib.SubscribeToIds(ctx, addonMarketplaceInstallWatcher, func(event events.AddonEvent) bool {
					return event.Shard == settings.Shard && lib.EventSpecChanged(event)
				}, lib.GetEventId)
			},
			addonMarketplaceInstallReconcilerInstance,
			addonErrors,
			addonEnqueueHeartbeat,
			addonHeartbeat,
			database.WithResyncDuration[*models.AddonMarketplaceInstall](cfg.ResyncDuration),
			database.WithReconciliationTimeout[*models.AddonMarketplaceInstall](cfg.ReconciliationTimeout),
			database.WithEnqueueAllDelayFunc[*models.AddonMarketplaceInstall](func() time.Duration {
				// Jitter before executing enqueueAll() operation, between 0 and 20% of ResyncDuration
				return time.Duration(float64(cfg.ResyncDuration) * 0.2 * rand.Float64())
			}),
			database.WithEnqueueDelayFunc[*models.AddonMarketplaceInstall](func(i, count int) time.Duration {
				// Jitter when enqueueing an item into a queue, between 0 and 1000 ms
				return time.Duration(float64(1000)*rand.Float64()) * time.Millisecond
			}),
		),
		watchers: []lib.CanStart{addonWatcher, addonRepoWatcher, clusterAddonRepoWatcher, addonMarketplaceInstallWatcher, addonAppWatcher, argocdClustersWatcher},
		log:      log,
		settings: settings,
		featSvc:  featSvc,
	}

	return bc, nil
}

func (bc *addonController) Init(ctx context.Context) error {
	if bc.featSvc.GetFeatureStatuses(ctx, nil).GetFleetManagement().Enabled() {
		for _, watcher := range bc.watchers {
			if err := watcher.Start(ctx); err != nil {
				return err
			}
		}
	}

	return nil
}

func (bc *addonController) Run(ctx context.Context, numWorkers int) error {
	versionInfo := version.GetVersion()

	defer runtimeutil.HandleCrash()

	if bc.featSvc.GetFeatureStatuses(ctx, nil).GetFleetManagement().Enabled() {
		bc.log.Info("Akuity Addon Controller Starting", "version", versionInfo.Version, "build_date", versionInfo.BuildDate)
		if err := bc.addonController.Start(ctx, numWorkers); err != nil {
			return err
		}
		if err := bc.addonRepoController.Start(ctx, numWorkers); err != nil {
			return err
		}
		if err := bc.clusterAddonController.Start(ctx, numWorkers); err != nil {
			return err
		}
		if err := bc.addonMarketplaceInstallController.Start(ctx, numWorkers); err != nil {
			return err
		}
	} else {
		bc.log.Info("Akuity Addon Controller Disabled", "version", versionInfo.Version, "build_date", versionInfo.BuildDate)
	}

	<-ctx.Done()
	return nil
}
