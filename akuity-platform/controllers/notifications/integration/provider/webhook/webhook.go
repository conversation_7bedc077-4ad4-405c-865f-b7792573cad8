package webhook

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/go-logr/logr"
	"github.com/pkg/errors"
	"github.com/volatiletech/null/v8"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/pkg/api/gateway/option"
	webhookv1 "github.com/akuityio/akuity-platform/pkg/api/gen/notifications/v1"
	"github.com/akuityio/akuity-platform/pkg/utils/webhook"
)

const (
	maxResponseSizeLimit = 1 << 14 // 16KB
	maxRetries           = 3
)

type Provider struct {
	logger     *logr.Logger
	httpClient *http.Client
	nowFn      func() time.Time
}

func NewProvider(
	logger *logr.Logger,
	httpClient *http.Client,
	nowFn func() time.Time,
) *Provider {
	return &Provider{
		logger:     logger,
		httpClient: httpClient,
		nowFn:      nowFn,
	}
}

func (p *Provider) SendNotification(
	ctx context.Context,
	reposet client.RepoSet,
	notification *models.Notification,
) error {
	event := notification.R.Event
	eventMetadata, err := event.GetMetadata()
	if err != nil {
		return errors.Wrap(err, "get event metadata")
	}
	notificationMetadata, err := notification.GetMetadata()
	if err != nil {
		return errors.Wrap(err, "get notification metadata")
	}

	// Get organization webhook config
	if notification.TargetID.String == "" {
		return fmt.Errorf("no config id in webhook notification")
	}
	cfg, err := reposet.OrganizationNotificationConfigs(
		models.OrganizationNotificationConfigWhere.OrganizationID.EQ(
			event.OrganizationID.String,
		),
	).GetByID(ctx, notification.TargetID.String)
	if err != nil {
		return errors.Wrap(err, "get notification config")
	}
	webhookCfg, err := cfg.GetOrganizationWebhookConfig()
	if err != nil {
		return errors.Wrap(err, "get webhook config")
	}

	var payload *webhookv1.WebhookEventPayload
	eventType, err := models.ConvertWebhookEventTypeToProto(event.EventType.String)
	if err != nil {
		return errors.Wrap(err, "convert event type")
	}
	payload = &webhookv1.WebhookEventPayload{
		EventTime:      timestamppb.New(event.CreationTimestamp),
		EventType:      eventType,
		EventId:        event.ID,
		OrganizationId: event.OrganizationID.String,
	}
	if err := eventMetadata.SetProtoMetadata(event.EventType.String, payload); err != nil {
		return errors.Wrap(err, "failed to set proto metadata")
	}

	encodedPayload, err := option.ServerMarshalOptions.Marshal(payload)
	if err != nil {
		return errors.Wrap(err, "marshal payload")
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, webhookCfg.URL, bytes.NewReader(encodedPayload))
	if err != nil {
		return errors.Wrap(err, "create request")
	}
	req.Header.Set("Content-Type", "application/json")

	// Add Signature if secret is provided
	if webhookCfg.Secret != "" {
		signature, err := webhook.ComputeSignature(webhookCfg.Secret, encodedPayload)
		if err != nil {
			return errors.Wrap(err, "compute signature")
		}
		req.Header.Set("X-Akuity-Signature-256", signature)
	}

	if notificationMetadata.WebhookMetadata == nil {
		notificationMetadata.WebhookMetadata = &models.WebhookMetadata{
			History: make([]models.WebhookHistory, 0, 1),
			Retries: 0,
		}
	} else {
		notificationMetadata.WebhookMetadata.Retries++
	}

	res := p.sendRequest(req)
	// Set last delivery timestamp to start time
	notification.LastDeliveryTimestamp = null.TimeFrom(res.startTime)
	// Initialize history with common data
	history := models.WebhookHistory{
		Generation:       notification.Generation,
		RequestHeaders:   compactHeaders(req.Header),
		Request:          string(encodedPayload),
		StartTimestampMs: res.startTime.UnixMilli(),
		EndTimestampMs:   res.endTime.UnixMilli(),
	}
	if res.err != nil {
		history.Error = errors.Wrap(res.err, "send request").Error()
		notificationMetadata.WebhookMetadata.History = append(
			notificationMetadata.WebhookMetadata.History,
			history,
		)
		return p.updateNotification(ctx, reposet, notification, notificationMetadata)
	}
	resp, err := io.ReadAll(io.LimitReader(res.resp.Body, maxResponseSizeLimit))
	if err != nil {
		history.Error = errors.Wrap(err, "read response body").Error()
		notificationMetadata.WebhookMetadata.History = append(
			notificationMetadata.WebhookMetadata.History,
			history,
		)
		return p.updateNotification(ctx, reposet, notification, notificationMetadata)
	}

	history.StatusCode = res.resp.StatusCode
	history.ResponseHeaders = compactHeaders(res.resp.Header)
	history.Response = string(resp)
	notificationMetadata.WebhookMetadata.History = append(
		notificationMetadata.WebhookMetadata.History,
		history,
	)
	notification.StatusIsDelivered = res.resp.StatusCode < 300
	return p.updateNotification(ctx, reposet, notification, notificationMetadata)
}

func (p *Provider) updateNotification(
	ctx context.Context,
	reposet client.RepoSet,
	notification *models.Notification,
	metadata models.NotificationMetadata,
) error {
	if !notification.StatusIsDelivered &&
		metadata.WebhookMetadata.Retries >= maxRetries {
		notification.StatusFailedDelivery = true
	}
	if err := notification.SetMetadata(metadata); err != nil {
		return errors.Wrap(err, "set metadata")
	}
	if err := reposet.Notifications().Update(ctx, notification); err != nil {
		return errors.Wrap(err, "update notification")
	}
	return nil
}

type sendResult struct {
	resp      *http.Response
	err       error
	startTime time.Time
	endTime   time.Time
}

func (p *Provider) sendRequest(req *http.Request) (res sendResult) {
	res.startTime = p.nowFn()
	defer func() {
		res.endTime = p.nowFn()
	}()

	res.resp, res.err = p.httpClient.Do(req)
	return res
}

func compactHeaders(headers http.Header) map[string]string {
	out := make(map[string]string, len(headers))
	for k, v := range headers {
		out[k] = strings.Join(v, ",")
	}
	return out
}
