package mail

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-logr/logr"
	"github.com/volatiletech/null/v8"
	"golang.org/x/time/rate"
	"gopkg.in/gomail.v2"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/notifications"
	k8sErrors "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/mail"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

const (
	MaxEmailRetries       = 4
	EmailNotVerifiedError = "Email address is not verified"

	// Rate limiting configuration
	DefaultEmailRateLimit = 15 // emails per second (staying under SES 25/second limit)
	DefaultEmailBurstSize = 20 // burst allowance for flexibility
)

// Global rate limiter for email notifications across all workers
var (
	emailRateLimiter *rate.Limiter
)

func init() {
	emailRateLimiter = rate.NewLimiter(rate.Limit(DefaultEmailRateLimit), DefaultEmailBurstSize)
}

type EmailProvider struct {
	logger     *logr.Logger
	mailer     mail.Mailer
	smtpConfig config.SMTPConfig
	tmpls      map[string]notifications.Template
	portlURL   string
}

func NewEmailProvider(logger *logr.Logger, cfg config.NotificationControllerConfig, tmpls map[string]notifications.Template) *EmailProvider {
	smtpConfig := cfg.EmailProvider
	mailer := gomail.NewDialer(smtpConfig.Host, smtpConfig.Port, smtpConfig.User, smtpConfig.Password)
	return &EmailProvider{
		logger:     logger,
		mailer:     mailer,
		smtpConfig: smtpConfig,
		tmpls:      tmpls,
		portlURL:   cfg.PortalURL,
	}
}

// SendNotification sends email notification to the target user
// if email is successfully sent, notification status is updated to delivered
// in case of error we will not force retry and wait for the resync duration to retry this email again
func (s *EmailProvider) SendNotification(ctx context.Context, repoSet client.RepoSet, notification *models.Notification) error {
	// the target for the notification is expected to be a user, get user email to send notification
	if notification.TargetID.String == "" {
		return fmt.Errorf("no user id mentioned in email notification")
	}

	notifMetadata, err := notification.GetMetadata()
	if err != nil {
		return err
	}
	// Check if we need to rate limit this notification
	delay, err := s.shouldRateLimit(notifMetadata)
	if err != nil {
		return err
	}
	if delay > 0 {
		if notifMetadata.EmailMetadata == nil {
			notifMetadata.EmailMetadata = &models.EmailMetadata{
				Retries: 0,
				History: []models.EmailHistory{},
			}
		}

		// Set the reservation time
		notifMetadata.EmailMetadata.ReservedUntil = time.Now().Add(delay)

		// Update the notification metadata in the database
		if err := notification.SetMetadata(notifMetadata); err != nil {
			return err
		}

		if err := repoSet.Notifications().Update(
			ctx,
			notification,
			models.NotificationColumns.StatusMetadata,
		); err != nil {
			return err
		}

		return k8sErrors.NewRetryableErrorWithBackoff(
			fmt.Errorf("email rate limited"),
			"email rate limited, will retry",
			delay,
		)
	}

	// the target for the notification is expected to be a user, get user email to send notification
	if notification.TargetID.String == "" {
		return fmt.Errorf("no user id mentioned in email notification")
	}

	user, err := repoSet.Users().GetByID(ctx, notification.TargetID.String)
	if err != nil {
		return err
	}

	varsBuilder := notifications.NotificationVars{
		RepoSet:   repoSet,
		PortalURL: s.portlURL,
	}

	eventMetadata, err := notification.R.Event.GetMetadata()
	if err != nil {
		return err
	}
	vars, err := varsBuilder.BuildNotificationVars(ctx, eventMetadata, notification)
	if err != nil {
		return err
	}

	if s.tmpls[notification.R.Event.EventType.String].Email == nil {
		return fmt.Errorf("no email template found for event type %s", notification.R.Event.EventType.String)
	}

	email, err := s.tmpls[notification.R.Event.EventType.String].Email.Format(vars)
	if err != nil {
		return err
	}

	m := gomail.NewMessage()
	m.SetAddressHeader("From", s.smtpConfig.NotificationEmail, "Akuity Platform")
	m.SetHeader("To", user.Email)
	// https://us-west-2.console.aws.amazon.com/ses/home?region=us-west-2#/configuration-sets/ses-history
	m.SetHeader("X-SES-CONFIGURATION-SET", "ses-history")

	m.SetHeader("Subject", email.Subject)
	m.SetBody("text/html", email.ParseBody)

	// Set last delivery timestamp to now
	notification.LastDeliveryTimestamp = null.TimeFrom(time.Now())
	if deliveryErr := s.mailer.DialAndSend(m); deliveryErr != nil &&
		!strings.Contains(deliveryErr.Error(), EmailNotVerifiedError) {
		s.logger.Error(deliveryErr, "error sending email notification", "notification_id", notification.ID)

		// updated failed try in history
		if notifMetadata.EmailMetadata == nil {
			notifMetadata.EmailMetadata = &models.EmailMetadata{
				Retries: 0,
				History: []models.EmailHistory{},
			}
		}
		notifMetadata.EmailMetadata.Retries++
		notifMetadata.EmailMetadata.History = append(notifMetadata.EmailMetadata.History, models.EmailHistory{
			TargetEmailAddress: user.Email,
			Error:              deliveryErr.Error(),
			AttemptTime:        notification.LastDeliveryTimestamp.Time,
		})

		if notifMetadata.EmailMetadata.Retries >= MaxEmailRetries {
			notification.StatusFailedDelivery = true
		}

		if err := notification.SetMetadata(notifMetadata); err != nil {
			return err
		}
		if err := repoSet.Notifications().Update(
			ctx,
			notification,
			models.NotificationColumns.LastDeliveryTimestamp,
			models.NotificationColumns.StatusMetadata,
			models.NotificationColumns.StatusFailedDelivery,
		); err != nil {
			return err
		}

		// Return retryable error for any delivery failure
		// Rate limiting will prevent overwhelming SES
		return k8sErrors.NewRetryableError(deliveryErr, "email delivery failed")
	}
	// update delivered status
	notification.StatusIsDelivered = true
	return repoSet.Notifications().Update(
		ctx,
		notification,
		models.NotificationColumns.LastDeliveryTimestamp,
		models.NotificationColumns.StatusIsDelivered,
	)
}

func (s *EmailProvider) checkRateLimit() (time.Duration, error) {
	if emailRateLimiter == nil {
		return 0, fmt.Errorf("rate limiter not initialized")
	}

	reservation := emailRateLimiter.Reserve()
	if !reservation.OK() {
		return 0, fmt.Errorf("rate limiter not available")
	}

	delay := reservation.Delay()
	return delay, nil
}

// shouldRateLimit determines if a notification should be rate limited and returns the delay
func (s *EmailProvider) shouldRateLimit(notifMetadata models.NotificationMetadata) (time.Duration, error) {
	// Check if already rate limited
	if notifMetadata.EmailMetadata != nil && !notifMetadata.EmailMetadata.ReservedUntil.IsZero() && time.Now().Before(notifMetadata.EmailMetadata.ReservedUntil) {
		return time.Until(notifMetadata.EmailMetadata.ReservedUntil), nil
	}

	// Check if we need to apply new rate limiting
	if notifMetadata.EmailMetadata == nil || notifMetadata.EmailMetadata.ReservedUntil.IsZero() {
		delay, err := s.checkRateLimit()
		if err != nil {
			return 0, err
		}
		return delay, nil
	}

	return 0, nil
}
