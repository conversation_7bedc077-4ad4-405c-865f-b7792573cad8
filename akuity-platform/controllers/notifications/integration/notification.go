package integration

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/go-logr/logr"

	"github.com/akuityio/akuity-platform/controllers/notifications/integration/provider"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

type notificationReconciler struct {
	log          *logr.Logger
	repoSet      client.RepoSet
	db           *sql.DB
	featSvc      features.Service
	domainSuffix string
	providers    map[string]provider.IProvider
}

func NewNotificationReconciler(
	log *logr.Logger,
	repoSet client.RepoSet,
	db *sql.DB,
	featSvc features.Service,
	domainSuffix string,
	cfg config.NotificationControllerConfig,
) (*notificationReconciler, error) {
	providers, err := provider.SetupProviders(log, cfg)
	if err != nil {
		return nil, err
	}

	return &notificationReconciler{
		log:          log,
		repoSet:      repoSet,
		db:           db,
		featSvc:      featSvc,
		domainSuffix: domainSuffix,
		providers:    providers,
	}, nil
}

func (r *notificationReconciler) ItemToID(item *models.Notification) string {
	return item.ID
}

func (r *notificationReconciler) IDColumn() string {
	return models.NotificationTableColumns.ID
}

func (r *notificationReconciler) LogValuesFromID(id string) []interface{} {
	return []interface{}{"notification_id", id}
}

func (r *notificationReconciler) LogValuesFromItem(item *models.Notification) []interface{} {
	return []interface{}{"target_id", item.TargetID, "event_id", item.EventID}
}

func (r *notificationReconciler) Reconcile(ctx context.Context, notification *models.Notification) error {
	if r.providers[notification.DeliveryMethod] != nil {
		// notification is modified by the provider and according to the result delivery attempt status is set
		if err := r.providers[notification.DeliveryMethod].SendNotification(ctx, r.repoSet, notification); err != nil {
			return err
		}
	} else {
		return fmt.Errorf("no provider found for delivery method %v", notification.DeliveryMethod)
	}
	return nil
}
