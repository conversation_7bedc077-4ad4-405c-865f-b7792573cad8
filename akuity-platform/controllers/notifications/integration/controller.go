package integration

import (
	"context"
	"database/sql"
	"math/rand/v2"
	"slices"
	"time"

	"github.com/go-logr/logr"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	runtimeutil "k8s.io/apimachinery/pkg/util/runtime"

	agentClient "github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/akuity-platform/controllers/shared/lib"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/version"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/events"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/models/util/dal"
)

var ignoredDeliveryTypes = []string{
	models.DeliveryMethodWeb,
}

type notificationController struct {
	orgEventController     database.TableController
	eventController        database.TableController
	notificationController database.TableController
	watchers               []lib.CanStart
	log                    *logr.Logger
	settings               ControllerSettings
	featureSvc             features.Service
}

type ControllerSettings struct {
	RepoSet            client.RepoSet
	PortalDBRawClient  *sql.DB
	PortalDBConnection string
	Log                *logr.Logger
}

func (s *ControllerSettings) GetPortalDBContext() database.DBContext {
	return database.NewPGDbContext(s.PortalDBRawClient, s.PortalDBConnection)
}

func eventNotProcessed(event events.EventNotificationEvent) bool {
	return !event.Processed
}

func reconcilableNotification(notification events.NotificationEvent) bool {
	return !notification.Delivered && !notification.FailedDelivery && !slices.Contains(ignoredDeliveryTypes, notification.DeliveryMethod)
}

func NewNotificationController(
	log *logr.Logger,
	settings ControllerSettings,
	cfg config.NotificationControllerConfig,
	argocdVersions []agentClient.ComponentVersion,
	kargoVersions []agentClient.ComponentVersion,
	featSvc features.Service,
) (*notificationController, error) {
	argocdLatestVersion, err := latestProductVersion(argocdVersions)
	if err != nil {
		return nil, err
	}
	kargoLatestVersion, err := latestProductVersion(kargoVersions)
	if err != nil {
		return nil, err
	}

	orgEventReconcilerInstance := NewOrgEventReconciler(log, settings.RepoSet, settings.PortalDBRawClient, featSvc, argocdLatestVersion, kargoLatestVersion, cfg.GracePeriodDuration)

	eventReconcilerInstance := NewEventReconciler(log, settings.RepoSet, settings.PortalDBRawClient, featSvc, cfg.DomainSuffix)
	var eventWatcher database.Watcher[events.EventNotificationEvent] = database.NewWatcher[events.EventNotificationEvent](*log, settings.GetPortalDBContext(), events.EventChannel)

	notificationReconcilerInstance, err := NewNotificationReconciler(log, settings.RepoSet, settings.PortalDBRawClient, featSvc, cfg.DomainSuffix, cfg)
	if err != nil {
		return nil, err
	}
	var notificationWatcher database.Watcher[events.NotificationEvent] = database.NewWatcher[events.NotificationEvent](*log, settings.GetPortalDBContext(), events.NotificationChannel)

	notificationErrors := promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "notification_errors",
			Help: "Total number of notification errors, per controller",
		}, []string{"controller"})

	notificationEnqueueHeartbeat := promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "notification_enqueue_loop_duration_seconds",
		Help:    "Notification controller enqueue latencies distribution, per controller",
		Buckets: prometheus.LinearBuckets(0.01, 0.05, 40),
	}, []string{"controller"})

	notificationHeartbeat := promauto.NewHistogramVec(prometheus.HistogramOpts{
		Name:    "notification_loop_duration_seconds",
		Help:    "Notification controller reconciliation latencies distribution, per controller",
		Buckets: prometheus.LinearBuckets(0.01, 0.05, 40),
	}, []string{"controller"})

	bc := &notificationController{
		watchers: []lib.CanStart{eventWatcher, notificationWatcher},
		orgEventController: database.NewTableController[*models.Organization](
			// only enqueue orgs that haven't been processed in cfg.OrgEventProcessingPeriod
			func() dal.Repository[*models.Organization] {
				return settings.RepoSet.Organizations(qm.Where("EXTRACT(EPOCH FROM NOW()) - coalesce((org_status->>'last_event_processed_at')::int, 0) >= ?", cfg.OrgEventProcessingPeriod.Seconds()))
			},
			*log,
			"org_events",
			func(ctx context.Context) <-chan string {
				// no db watch for this
				return make(chan string)
			},
			orgEventReconcilerInstance,
			notificationErrors,
			notificationEnqueueHeartbeat,
			notificationHeartbeat,
			database.WithResyncDuration[*models.Organization](cfg.OrgEventResyncDuration),
			database.WithReconciliationTimeout[*models.Organization](cfg.OrgEventReconciliationTimeout),
			database.WithEnqueueAllDelayFunc[*models.Organization](func() time.Duration {
				// Jitter before executing enqueueAll() operation, between 0 and 20% of ResyncDuration
				return time.Duration(float64(cfg.ResyncDuration) * 0.2 * rand.Float64())
			}),
			database.WithEnqueueDelayFunc[*models.Organization](func(i, count int) time.Duration {
				// Jitter when enqueueing an item into a queue, between 0 and 1000 ms
				return time.Duration(float64(1000)*rand.Float64()) * time.Millisecond
			}),
		),
		eventController: database.NewTableController[*models.Event](
			// only enqueue items which are not processed yet
			func() dal.Repository[*models.Event] {
				return settings.RepoSet.Events(models.EventWhere.StatusProcessed.NEQ(true))
			},
			*log,
			"events",
			func(ctx context.Context) <-chan string {
				return lib.SubscribeToIds(ctx, eventWatcher, eventNotProcessed, lib.GetEventId)
			},
			eventReconcilerInstance,
			notificationErrors,
			notificationEnqueueHeartbeat,
			notificationHeartbeat,
			database.WithResyncDuration[*models.Event](cfg.ResyncDuration),
			database.WithReconciliationTimeout[*models.Event](cfg.ReconciliationTimeout),
			database.WithEnqueueAllDelayFunc[*models.Event](func() time.Duration {
				// Jitter before executing enqueueAll() operation, between 0 and 20% of ResyncDuration
				return time.Duration(float64(cfg.ResyncDuration) * 0.2 * rand.Float64())
			}),
			database.WithEnqueueDelayFunc[*models.Event](func(i, count int) time.Duration {
				// Jitter when enqueueing an item into a queue, between 0 and 1000 ms
				return time.Duration(float64(1000)*rand.Float64()) * time.Millisecond
			}),
		),
		notificationController: database.NewTableController[*models.Notification](
			// only enqueue items which are not processed yet
			func() dal.Repository[*models.Notification] {
				return settings.RepoSet.Notifications(qm.Load(models.NotificationRels.Event), models.NotificationWhere.StatusIsDelivered.NEQ(true), models.NotificationWhere.StatusFailedDelivery.NEQ(true), models.NotificationWhere.DeliveryMethod.NIN(ignoredDeliveryTypes))
			},
			*log,
			"notifications",
			func(ctx context.Context) <-chan string {
				return lib.SubscribeToIds(ctx, notificationWatcher, reconcilableNotification, lib.GetEventId)
			},
			notificationReconcilerInstance,
			notificationErrors,
			notificationEnqueueHeartbeat,
			notificationHeartbeat,
			database.WithResyncDuration[*models.Notification](cfg.ResyncDuration),
			database.WithReconciliationTimeout[*models.Notification](cfg.ReconciliationTimeout),
			database.WithEnqueueAllDelayFunc[*models.Notification](func() time.Duration {
				// Jitter before executing enqueueAll() operation, between 0 and 20% of ResyncDuration
				return time.Duration(float64(cfg.ResyncDuration) * 0.2 * rand.Float64())
			}),
			database.WithEnqueueDelayFunc[*models.Notification](func(i, count int) time.Duration {
				// Jitter when enqueueing an item into a queue, between 0 and 1000 ms
				return time.Duration(float64(1000)*rand.Float64()) * time.Millisecond
			}),
		),
		settings:   settings,
		featureSvc: featSvc,
		log:        log,
	}

	return bc, nil
}

func (bc *notificationController) Init(ctx context.Context) error {
	if bc.featureSvc.GetFeatureStatuses(ctx, nil).GetNotification().Enabled() {
		for _, watcher := range bc.watchers {
			if err := watcher.Start(ctx); err != nil {
				return err
			}
		}
	}

	return nil
}

func (bc *notificationController) Run(ctx context.Context, numWorkers int) error {
	versionInfo := version.GetVersion()

	defer runtimeutil.HandleCrash()

	if bc.featureSvc.GetFeatureStatuses(ctx, nil).GetNotification().Enabled() {
		bc.log.Info("Akuity Notification Controller Starting", "version", versionInfo.Version, "build_date", versionInfo.BuildDate)
		if err := bc.eventController.Start(ctx, numWorkers); err != nil {
			return err
		}

		if err := bc.notificationController.Start(ctx, numWorkers); err != nil {
			return err
		}

		if err := bc.orgEventController.Start(ctx, numWorkers); err != nil {
			return err
		}
	} else {
		bc.log.Info("Akuity Notification Controller Disabled", "version", versionInfo.Version, "build_date", versionInfo.BuildDate)
	}

	<-ctx.Done()
	return nil
}
