package integration

import (
	"testing"

	"github.com/stretchr/testify/assert"

	agentClient "github.com/akuityio/agent/pkg/client"
)

func Test_latestProductVersion(t *testing.T) {
	type args struct {
		versions []agentClient.ComponentVersion
	}
	tests := []struct {
		name    string
		args    args
		want    latestProductVersionDetails
		wantErr bool
	}{
		{
			name: "no versions",
			args: args{
				versions: []agentClient.ComponentVersion{},
			},
			want:    latestProductVersionDetails{},
			wantErr: true,
		},
		{
			name: "no latest version",
			args: args{
				versions: []agentClient.ComponentVersion{
					{
						Version:    "latest",
						AKVersions: []agentClient.AKCustomVersion{{Version: "v1.0.0-ak.26"}},
					},
					{
						Version:    "v0.1.1-rc",
						AKVersions: []agentClient.AKCustomVersion{{Version: "v1.0.0-ak.26"}},
					},
				},
			},
			want:    latestProductVersionDetails{},
			wantErr: true,
		},
		{
			name: "latest version but no ak",
			args: args{
				versions: []agentClient.ComponentVersion{
					{
						Version:    "latest",
						AKVersions: []agentClient.AKCustomVersion{{Version: "v1.0.0"}},
					},
					{
						Version:    "v0.1.1",
						AKVersions: []agentClient.AKCustomVersion{},
					},
				},
			},
			want: latestProductVersionDetails{
				ComponentVersion: agentClient.ComponentVersion{
					Version:    "v0.1.1",
					AKVersions: []agentClient.AKCustomVersion{},
				},
				LatestAkVersion: -1,
			},
			wantErr: false,
		},
		{
			name: "latest version with ak",
			args: args{
				versions: []agentClient.ComponentVersion{
					{
						Version:    "latest",
						AKVersions: []agentClient.AKCustomVersion{{Version: "v1.0.0"}},
					},
					{
						Version:    "v0.1.1",
						AKVersions: []agentClient.AKCustomVersion{{Version: "v1.0.0-ak.25"}, {Version: "v1.0.0-ak.26"}},
					},
				},
			},
			want: latestProductVersionDetails{
				ComponentVersion: agentClient.ComponentVersion{
					Version:    "v0.1.1",
					AKVersions: []agentClient.AKCustomVersion{{Version: "v1.0.0-ak.25"}, {Version: "v1.0.0-ak.26"}},
				},
				LatestAkVersion: 26,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := latestProductVersion(tt.args.versions)
			if (err != nil) != tt.wantErr {
				t.Errorf("latestProductVersion() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !assert.Equal(t, got, tt.want) {
				t.Errorf("latestProductVersion() = %v, want %v", got, tt.want)
			}
		})
	}
}
