# Quick Start

## Pull the Helm Chart

From the directory container this file, run the following commands

```
export DOCKER_PASSWORD=<registry_key>
helm registry login us-docker.pkg.dev -u _json_key_base64 -p $DOCKER_PASSWORD
helm pull oci://us-docker.pkg.dev/akuity/akp-sh/charts/akuity-platform --untar --untardir charts
```

The final directory structure should look like this.
```
.
├── charts
│   └── akuity-platform
│       ├── config
│       ├── templates
│       └── test
├── kustomization.yaml
├── README.md
└── values.yaml
```

## Update the `values.yaml` File to Suit Your Environment

The values bundled here are a fairly typical AKP configuration. Update the variables in this file to match your environment. If additional customizations are required that are not supported by the chart, you can use `kustomization.yaml` to make those last-mile modifications.

A more comprehensive view of the defaults and options supported by the chart are available in the `charts/akuity-platform/values.yaml` file. Please connect with an Akuity representitive if you require assistance configuring and deploying the Akuity Platform.

## Deploy the Akuity Platform

Once you have configured your values and customizations, use the following command to deploy the Akuity Platform.
```
kustomize build --enable-helm | kubectl apply -f -
```

**Note:** This command should be run from the same directory that the `kustomization.yaml` and `values.yaml` files are in.