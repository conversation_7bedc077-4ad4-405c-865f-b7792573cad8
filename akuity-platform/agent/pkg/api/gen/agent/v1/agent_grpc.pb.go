// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             (unknown)
// source: agent/v1/agent.proto

package agentv1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	AgentService_UpdateClusterKubernetesInfo_FullMethodName = "/akuity.agent.v1.AgentService/UpdateClusterKubernetesInfo"
	AgentService_UpdateKubernetesResources_FullMethodName   = "/akuity.agent.v1.AgentService/UpdateKubernetesResources"
	AgentService_UpdateKubernetesEvents_FullMethodName      = "/akuity.agent.v1.AgentService/UpdateKubernetesEvents"
	AgentService_CollectLogs_FullMethodName                 = "/akuity.agent.v1.AgentService/CollectLogs"
)

// AgentServiceClient is the client API for AgentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AgentServiceClient interface {
	UpdateClusterKubernetesInfo(ctx context.Context, in *UpdateClusterKubernetesInfoRequest, opts ...grpc.CallOption) (*UpdateClusterKubernetesInfoResponse, error)
	UpdateKubernetesResources(ctx context.Context, in *UpdateKubernetesResourcesRequest, opts ...grpc.CallOption) (*UpdateKubernetesResourcesResponse, error)
	UpdateKubernetesEvents(ctx context.Context, in *UpdateKubernetesEventsRequest, opts ...grpc.CallOption) (*UpdateKubernetesEventsResponse, error)
	CollectLogs(ctx context.Context, opts ...grpc.CallOption) (AgentService_CollectLogsClient, error)
}

type agentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAgentServiceClient(cc grpc.ClientConnInterface) AgentServiceClient {
	return &agentServiceClient{cc}
}

func (c *agentServiceClient) UpdateClusterKubernetesInfo(ctx context.Context, in *UpdateClusterKubernetesInfoRequest, opts ...grpc.CallOption) (*UpdateClusterKubernetesInfoResponse, error) {
	out := new(UpdateClusterKubernetesInfoResponse)
	err := c.cc.Invoke(ctx, AgentService_UpdateClusterKubernetesInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentServiceClient) UpdateKubernetesResources(ctx context.Context, in *UpdateKubernetesResourcesRequest, opts ...grpc.CallOption) (*UpdateKubernetesResourcesResponse, error) {
	out := new(UpdateKubernetesResourcesResponse)
	err := c.cc.Invoke(ctx, AgentService_UpdateKubernetesResources_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentServiceClient) UpdateKubernetesEvents(ctx context.Context, in *UpdateKubernetesEventsRequest, opts ...grpc.CallOption) (*UpdateKubernetesEventsResponse, error) {
	out := new(UpdateKubernetesEventsResponse)
	err := c.cc.Invoke(ctx, AgentService_UpdateKubernetesEvents_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentServiceClient) CollectLogs(ctx context.Context, opts ...grpc.CallOption) (AgentService_CollectLogsClient, error) {
	stream, err := c.cc.NewStream(ctx, &AgentService_ServiceDesc.Streams[0], AgentService_CollectLogs_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &agentServiceCollectLogsClient{stream}
	return x, nil
}

type AgentService_CollectLogsClient interface {
	Send(*CollectLogsRequest) error
	CloseAndRecv() (*CollectLogsResponse, error)
	grpc.ClientStream
}

type agentServiceCollectLogsClient struct {
	grpc.ClientStream
}

func (x *agentServiceCollectLogsClient) Send(m *CollectLogsRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *agentServiceCollectLogsClient) CloseAndRecv() (*CollectLogsResponse, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(CollectLogsResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// AgentServiceServer is the server API for AgentService service.
// All implementations must embed UnimplementedAgentServiceServer
// for forward compatibility
type AgentServiceServer interface {
	UpdateClusterKubernetesInfo(context.Context, *UpdateClusterKubernetesInfoRequest) (*UpdateClusterKubernetesInfoResponse, error)
	UpdateKubernetesResources(context.Context, *UpdateKubernetesResourcesRequest) (*UpdateKubernetesResourcesResponse, error)
	UpdateKubernetesEvents(context.Context, *UpdateKubernetesEventsRequest) (*UpdateKubernetesEventsResponse, error)
	CollectLogs(AgentService_CollectLogsServer) error
	mustEmbedUnimplementedAgentServiceServer()
}

// UnimplementedAgentServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAgentServiceServer struct {
}

func (UnimplementedAgentServiceServer) UpdateClusterKubernetesInfo(context.Context, *UpdateClusterKubernetesInfoRequest) (*UpdateClusterKubernetesInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateClusterKubernetesInfo not implemented")
}
func (UnimplementedAgentServiceServer) UpdateKubernetesResources(context.Context, *UpdateKubernetesResourcesRequest) (*UpdateKubernetesResourcesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateKubernetesResources not implemented")
}
func (UnimplementedAgentServiceServer) UpdateKubernetesEvents(context.Context, *UpdateKubernetesEventsRequest) (*UpdateKubernetesEventsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateKubernetesEvents not implemented")
}
func (UnimplementedAgentServiceServer) CollectLogs(AgentService_CollectLogsServer) error {
	return status.Errorf(codes.Unimplemented, "method CollectLogs not implemented")
}
func (UnimplementedAgentServiceServer) mustEmbedUnimplementedAgentServiceServer() {}

// UnsafeAgentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AgentServiceServer will
// result in compilation errors.
type UnsafeAgentServiceServer interface {
	mustEmbedUnimplementedAgentServiceServer()
}

func RegisterAgentServiceServer(s grpc.ServiceRegistrar, srv AgentServiceServer) {
	s.RegisterService(&AgentService_ServiceDesc, srv)
}

func _AgentService_UpdateClusterKubernetesInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateClusterKubernetesInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServiceServer).UpdateClusterKubernetesInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentService_UpdateClusterKubernetesInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServiceServer).UpdateClusterKubernetesInfo(ctx, req.(*UpdateClusterKubernetesInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentService_UpdateKubernetesResources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateKubernetesResourcesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServiceServer).UpdateKubernetesResources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentService_UpdateKubernetesResources_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServiceServer).UpdateKubernetesResources(ctx, req.(*UpdateKubernetesResourcesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentService_UpdateKubernetesEvents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateKubernetesEventsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServiceServer).UpdateKubernetesEvents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentService_UpdateKubernetesEvents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServiceServer).UpdateKubernetesEvents(ctx, req.(*UpdateKubernetesEventsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentService_CollectLogs_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(AgentServiceServer).CollectLogs(&agentServiceCollectLogsServer{stream})
}

type AgentService_CollectLogsServer interface {
	SendAndClose(*CollectLogsResponse) error
	Recv() (*CollectLogsRequest, error)
	grpc.ServerStream
}

type agentServiceCollectLogsServer struct {
	grpc.ServerStream
}

func (x *agentServiceCollectLogsServer) SendAndClose(m *CollectLogsResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *agentServiceCollectLogsServer) Recv() (*CollectLogsRequest, error) {
	m := new(CollectLogsRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// AgentService_ServiceDesc is the grpc.ServiceDesc for AgentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AgentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "akuity.agent.v1.AgentService",
	HandlerType: (*AgentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateClusterKubernetesInfo",
			Handler:    _AgentService_UpdateClusterKubernetesInfo_Handler,
		},
		{
			MethodName: "UpdateKubernetesResources",
			Handler:    _AgentService_UpdateKubernetesResources_Handler,
		},
		{
			MethodName: "UpdateKubernetesEvents",
			Handler:    _AgentService_UpdateKubernetesEvents_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "CollectLogs",
			Handler:       _AgentService_CollectLogs_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "agent/v1/agent.proto",
}
