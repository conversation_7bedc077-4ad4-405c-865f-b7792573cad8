// Code generated by protoc-gen-grpc-gateway-client. DO NOT EDIT.
// source: agent/v1/agent.proto

package agentv1

import (
	context "context"
	fmt "fmt"
	gateway "github.com/akuity/grpc-gateway-client/pkg/grpc/gateway"
)

// AgentServiceGatewayClient is the interface for AgentService service client.
type AgentServiceGatewayClient interface {
	UpdateClusterKubernetesInfo(context.Context, *UpdateClusterKubernetesInfoRequest) (*UpdateClusterKubernetesInfoResponse, error)
	UpdateKubernetesResources(context.Context, *UpdateKubernetesResourcesRequest) (*UpdateKubernetesResourcesResponse, error)
	UpdateKubernetesEvents(context.Context, *UpdateKubernetesEventsRequest) (*UpdateKubernetesEventsResponse, error)
}

func NewAgentServiceGatewayClient(c gateway.Client) AgentServiceGatewayClient {
	return &agentServiceGatewayClient{
		gwc: c,
	}
}

type agentServiceGatewayClient struct {
	gwc gateway.Client
}

func (c *agentServiceGatewayClient) UpdateClusterKubernetesInfo(ctx context.Context, req *UpdateClusterKubernetesInfoRequest) (*UpdateClusterKubernetesInfoResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/agent-api/v1/clusters/{cluster_id}/k8s-info")
	gwReq.SetPathParam("cluster_id", fmt.Sprintf("%v", req.ClusterId))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateClusterKubernetesInfoResponse](ctx, gwReq)
}

func (c *agentServiceGatewayClient) UpdateKubernetesResources(ctx context.Context, req *UpdateKubernetesResourcesRequest) (*UpdateKubernetesResourcesResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/agent-api/v1/clusters/{cluster_id}/k8s-resources")
	gwReq.SetPathParam("cluster_id", fmt.Sprintf("%v", req.ClusterId))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateKubernetesResourcesResponse](ctx, gwReq)
}

func (c *agentServiceGatewayClient) UpdateKubernetesEvents(ctx context.Context, req *UpdateKubernetesEventsRequest) (*UpdateKubernetesEventsResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/agent-api/v1/clusters/{cluster_id}/k8s-events")
	gwReq.SetPathParam("cluster_id", fmt.Sprintf("%v", req.ClusterId))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateKubernetesEventsResponse](ctx, gwReq)
}
