package main

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/internal/akputil/agent"
	"github.com/akuityio/akuity-platform/internal/akputil/aggregator"
	"github.com/akuityio/akuity-platform/internal/akputil/archiver"
	"github.com/akuityio/akuity-platform/internal/akputil/cluster"
	"github.com/akuityio/akuity-platform/internal/akputil/gcpusage"
	"github.com/akuityio/akuity-platform/internal/akputil/hubspot"
	"github.com/akuityio/akuity-platform/internal/akputil/image"
	"github.com/akuityio/akuity-platform/internal/akputil/instance"
	"github.com/akuityio/akuity-platform/internal/akputil/kubevision"
	"github.com/akuityio/akuity-platform/internal/akputil/license"
	"github.com/akuityio/akuity-platform/internal/akputil/notification"
	"github.com/akuityio/akuity-platform/internal/akputil/organization"
	"github.com/akuityio/akuity-platform/internal/akputil/plans"
	"github.com/akuityio/akuity-platform/internal/akputil/sheets"
	"github.com/akuityio/akuity-platform/internal/akputil/tokens"
	"github.com/akuityio/akuity-platform/internal/akputil/user"
	"github.com/akuityio/akuity-platform/internal/akputil/verify"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/version"
)

func main() {
	cmd := &cobra.Command{
		Use: "akputil",
		Run: func(c *cobra.Command, args []string) {
			c.HelpFunc()(c, args)
		},
		DisableAutoGenTag: true,
	}
	cmd.AddCommand(agent.NewAgentCommand())
	cmd.AddCommand(version.NewVersionCommand())
	cmd.AddCommand(instance.NewInstanceCommand())
	cmd.AddCommand(cluster.NewClusterCommand())
	cmd.AddCommand(organization.NewOrganizationCommand())
	cmd.AddCommand(user.NewUserCommand())
	cmd.AddCommand(verify.NewVerifyCommand())
	cmd.AddCommand(archiver.NewArchiverCommand())
	cmd.AddCommand(aggregator.NewAggregatorCommand())
	cmd.AddCommand(notification.NewNotificationEventCommand())
	cmd.AddCommand(kubevision.NewKubeVisionCommand())
	cmd.AddCommand(tokens.NewTokenCommand())
	if !config.IsSelfHosted {
		cmd.AddCommand(license.NewLicenseCommand())
		cmd.AddCommand(hubspot.NewHubspotCommand())
		cmd.AddCommand(image.NewImageCommand())
		cmd.AddCommand(gcpusage.NewGCPUsageCmd())
		cmd.AddCommand(sheets.NewSheetsCommand())
		cmd.AddCommand(plans.NewPlansCommand())
	}

	if err := cmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}
