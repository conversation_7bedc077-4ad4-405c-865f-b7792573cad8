{{- if .Values.secret.create }}
apiVersion: v1
kind: Secret
metadata:
  name: akuity-platform
  namespace: '{{ .Release.Namespace }}'
  annotations:
    # force creation of the resource before any other hooks/resources
    argocd.argoproj.io/sync-wave: "-1"
    argocd.argoproj.io/hook: PreSync
data:
  DB_DATA_KEY: {{ .Values.database.dataKey | b64enc | quote }}
  PORTAL_DB_CONNECTION: {{ template "dbPortalConnectionString" .Values.database }}
  PORTAL_RO_DB_CONNECTION: {{ template "dbPortalConnectionStringReadOnly" .Values.database }}
  K3S_DB_CONNECTION: {{ template "dbK3SConnectionString" .Values.database }}
  K3S_RO_DB_CONNECTION: {{ template "dbK3SConnectionStringReadOnly" .Values.database }}
  SMTP_USER: {{ .Values.smtp.user | b64enc | quote }}
  SMTP_PASSWORD: {{ .Values.smtp.password | b64enc | quote }}
  LICENSE_KEY: {{ .Values.licenseKey | b64enc | quote }}
{{- if .Values.sso.oidc.enabled }}
  OIDC_CLIENT_SECRET: {{ .Values.sso.oidc.clientSecret | b64enc | quote }}
{{- end -}}
{{- end -}}
