{{- if and .Values.dragonflydb.serviceMonitor .Values.dragonflydb.enabled}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    group: dragonfly
  name: controller-manager-metrics-monitor
  namespace: dragonfly-operator-system
spec:
  endpoints:
    - port: https
      path: /metrics
      interval: 60s
      scheme: https
      bearerTokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
      tlsConfig:
        insecureSkipVerify: true
  selector:
    matchLabels:
      control-plane: controller-manager
{{- end }}
