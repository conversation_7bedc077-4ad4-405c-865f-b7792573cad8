apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: kubevision-event-cleaner-cronjob
  namespace: '{{ .Release.Namespace }}'
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: kubevision-event-cleaner
---
# Clean up KubeVision events records older than retention period, default 14 days
apiVersion: batch/v1
kind: CronJob
metadata:
  name: kubevision-event-cleaner
  namespace: '{{ .Release.Namespace }}'
spec:
  # Execute the job every 20 minutes
  schedule: "*/20 * * * *"
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      backoffLimit: 2
      # 48 hours for job logs to stay around
      ttlSecondsAfterFinished: 172800
      template:
        metadata:
          labels:
            app.kubernetes.io/name: kubevision-event-cleaner
        spec:
          serviceAccountName: kubevision
          activeDeadlineSeconds: 300
          imagePullSecrets:
          - name: akuity-pullsecrets
          containers:
          - name: kubevision-event-cleaner
            image: {{ include "akuity-platform.image" . }}
            imagePullPolicy: {{ .Values.kubeVision.imagePullPolicy | quote}}
            env:
            - name: AKPUTIL_METRICS_SERVER
            ports:
            - name: metrics
              protocol: TCP
              containerPort: 9504
            command: [akputil, kubevision, event-cleanup]
            args:
            - --retention-period=14
          restartPolicy: Never
