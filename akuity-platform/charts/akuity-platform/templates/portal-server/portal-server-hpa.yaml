{{- if .Values.portal.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: portal-server
  namespace: '{{ .Release.Namespace }}'
spec:
  minReplicas: {{ .Values.portal.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.portal.autoscaling.maxReplicas }}
  metrics:
  - resource:
      name: memory
      target:
        averageUtilization: {{ .Values.portal.autoscaling.targetMemoryUtilizationPercentage }}
        type: Utilization
    type: Resource
  - resource:
      name: cpu
      target:
        averageUtilization: {{ .Values.portal.autoscaling.targetCPUUtilizationPercentage }}
        type: Utilization
    type: Resource
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: portal-server
{{- end }}
