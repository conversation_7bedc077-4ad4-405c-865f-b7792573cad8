apiVersion: v1
kind: ConfigMap
metadata:
  name: portal-server
  namespace: '{{ .Release.Namespace }}'
data:
  PORTAL_URL: "{{ .Values.portal.url }}"
{{- if .Values.platformController.domainSuffix }}
  DOMAIN_SUFFIX: "{{- .Values.platformController.domainSuffix -}}"
{{ else }}
  DOMAIN_SUFFIX: "{{ template "ingressHostname" . }}"
{{- end }}
  SHUTDOWN_GRACE_PERIOD: 20s
  SMTP_HOST: "{{- .Values.smtp.host -}}"
  SMTP_PORT: "{{- .Values.smtp.port -}}"
  LIMIT_EMAIL_INVITATION_COUNT_PER_BATCH: "{{- .Values.portal.maxEmailInvitationsPerBatch -}}"
{{- if .Values.sso.oidc.enabled }}
  OIDC_ISSUER: "{{ template "oidcIssuer" . }}"
  OIDC_CLIENT_ID: "{{- .Values.sso.oidc.clientID -}}"
  OIDC_LOGOUT_URL: "{{- .Values.sso.oidc.logoutURL -}}"
  OIDC_SCOPES: "{{- .Values.sso.oidc.scopes -}}"
{{- if .Values.sso.oidc.insecureSkipTLSVerify }}
  OIDC_INSECURE_SKIP_TLS_VERIFY: "true"
{{- end -}}
{{- end -}}
{{- if .Values.sso.auth0.enabled }}
  AUTH0_DOMAIN: "{{- .Values.sso.auth0.domain -}}"
  AUTH0_AUDIENCE: "{{- .Values.sso.auth0.audience -}}"
  AUTH0_CLIENT_ID: "{{- .Values.sso.auth0.clientID -}}"
  AUTH0_CLI_CLIENT_ID: "{{- .Values.sso.auth0.cliClientID -}}"
  AUTH0_CALLBACK_URL: {{ .Values.portal.url }}/api/auth/callback
{{- end -}}
{{- range $key, $val := .Values.portal.env }}
  {{ $key }}: {{ $val | quote }}
{{- end }}
{{- if .Values.sso.roleFromGroups }}
  GLOBAL_OIDC_MEMBER_GROUPS: {{ .Values.sso.roleFromGroups.member | quote }}
  GLOBAL_OIDC_ADMIN_GROUPS: {{ .Values.sso.roleFromGroups.admin | quote }}
  GLOBAL_OIDC_OWNER_GROUPS: {{ .Values.sso.roleFromGroups.owner | quote }}
{{- end -}}
{{- if .Values.sso.roleTeamFromGroups }}
  GLOBAL_ROLE_TEAM_GROUPS: {{ .Values.sso.roleTeamFromGroups | toJson | quote }}
{{- end -}}
{{- if .Values.platformController.instanceSubDomains }}
  INSTANCE_SUBDOMAINS: "true"
{{- end -}}
