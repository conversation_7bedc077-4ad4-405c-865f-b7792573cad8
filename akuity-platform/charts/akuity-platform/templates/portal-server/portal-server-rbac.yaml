apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: portal-server
rules:
- apiGroups:
  - ''
  resources:
  - secrets
  - configmaps
  verbs:
  - get
- apiGroups:
  - ''
  resources:
  - configmaps
  verbs:
  - write
  - update
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: portal-server
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: portal-server
subjects:
  - kind: ServiceAccount
    name: portal-server
    namespace: {{ .Release.Namespace }}
