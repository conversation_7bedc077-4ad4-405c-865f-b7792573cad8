{{- if or (not .Values.platformController.shard) (eq .Values.platformController.shard "") }}
apiVersion: batch/v1
kind: Job
metadata:
  name: portaldb-update
  namespace: '{{ .Release.Namespace }}'
  annotations:
    helm.sh/hook: pre-upgrade
    helm.sh/hook-delete-policy: before-hook-creation
spec:
  ttlSecondsAfterFinished: 180
  template:
    spec:
      volumes:
        - name: changelogs
          emptyDir: {}
      imagePullSecrets:
      - name: akuity-pullsecrets
{{- if .Values.liquibase.image.secret.create }}
      - name: liquibase-pullsecrets
{{- end }}
      initContainers:
      - name: copy-changelogs
        image: {{ include "akuity-platform.image" . }}
        imagePullPolicy: "{{ .Values.portal.imagePullPolicy }}"
        command: [cp, -R, /changelogs/., /shared/changelogs]
        volumeMounts:
          - name: changelogs
            mountPath: /shared/changelogs/
      containers:
      - name: liquibase
        image: {{ .Values.liquibase.image.repository }}:{{ .Values.liquibase.image.tag }}
        command: [sh, -c]
        args:
          - |
            export $(echo "${PORTAL_DB_CONNECTION}" | tr ' ' '\n')
        {{- if .Values.database.createSchema }}
            docker-entrypoint.sh \
              --url=jdbc:postgresql://${host}:${port}/${dbname} \
              --username=${user} \
              --password=${password} \
              --log-format=TEXT \
              execute-sql \
              --sql="CREATE SCHEMA IF NOT EXISTS {{ .Values.database.schemaname }}" && \
        {{- end }}
            docker-entrypoint.sh \
              --url=jdbc:postgresql://${host}:${port}/${dbname} \
              --username=${user} \
              --password=${password} \
              --classpath=/changelogs \
              --changeLogFile=changelog-root.yaml \
              --log-format=TEXT \
              --default-schema-name={{ .Values.database.schemaname }} \
              --liquibase-schema-name={{ .Values.database.schemaname }} \
              update
        env:
          - name: PORTAL_DB_CONNECTION
            valueFrom:
              secretKeyRef:
                name: akuity-platform
                key: PORTAL_DB_CONNECTION
        volumeMounts:
          - name: changelogs
            mountPath: /changelogs/
      restartPolicy: Never
  backoffLimit: 4
{{- end }}
