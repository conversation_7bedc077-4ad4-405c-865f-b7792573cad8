{{- if .Values.liquibase.image.secret.create }}
apiVersion: v1
kind: Secret
metadata:
  name: liquibase-pullsecrets
  namespace: {{ .Release.Namespace }}
  annotations:
    # force creation of the resource before any other hooks/resources
    argocd.argoproj.io/sync-wave: "-1"
    argocd.argoproj.io/hook: PreSync
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: {{ template "imagePullSecret" .Values.liquibase }}
{{- end -}}
