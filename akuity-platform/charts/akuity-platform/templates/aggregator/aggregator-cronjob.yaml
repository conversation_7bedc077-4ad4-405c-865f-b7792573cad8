apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: records-aggregator-cronjob
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: records-aggregator
---
# Aggregate duplicated Audit Log records for the previous day
apiVersion: batch/v1
kind: CronJob
metadata:
  name: records-aggregator
  namespace: '{{ .Release.Namespace }}'
spec:
  # Execute the job daily, at 1 AM
  schedule: "0 1 * * *"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      backoffLimit: 2
      # 48 hours for job logs to stay around
      ttlSecondsAfterFinished: 172800
      template:
        metadata:
          labels:
            app.kubernetes.io/name: records-aggregator
        spec:
          serviceAccountName: records-aggregator
          activeDeadlineSeconds: 300
          imagePullSecrets:
          - name: akuity-pullsecrets
          containers:
          - name: aggregate-audit-sync-records
            image: {{ include "akuity-platform.image" . }}
            ports:
            - name: metrics
              protocol: TCP
              containerPort: 9504
            command: [akputil, aggregator, aggregate-audit-sync-records]
            args:
            - --aggregation-window=60
          restartPolicy: Never
