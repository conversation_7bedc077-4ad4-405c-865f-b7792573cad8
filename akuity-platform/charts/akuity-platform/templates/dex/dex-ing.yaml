{{- if and .Values.sso.dex.enabled .Values.sso.dex.ingress.enabled }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dex
  namespace: '{{ .Release.Namespace }}'
spec:
  ingressClassName: traefik-external
  rules:
    - host: {{ .Values.sso.dex.ingress.host }}
      http:
        paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: dex
              port:
                number: 80
{{- end }}
