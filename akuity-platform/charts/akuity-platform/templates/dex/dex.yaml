{{- if .Values.sso.dex.enabled }}
# This is an autogenerated file. DO NOT EDIT
apiVersion: v1
kind: ServiceAccount
metadata:
  name: dex
  namespace: '{{ .Release.Namespace }}'
---
apiVersion: v1
kind: Service
metadata:
  name: dex
  namespace: '{{ .Release.Namespace }}'
spec:
  ports:
  - appProtocol: http
    name: http
    port: 80
    protocol: TCP
    targetPort: http
  - appProtocol: http
    name: telemetry
    port: 5558
    protocol: TCP
    targetPort: telemetry
  selector:
    app.kubernetes.io/instance: dex
    app.kubernetes.io/name: dex
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dex
  namespace: '{{ .Release.Namespace }}'
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/instance: dex
      app.kubernetes.io/name: dex
  template:
    metadata:
      annotations:
        checksum/dexconfig: '{{ tpl (.Files.Get "config/dexconfig.yaml") . | sha256sum
          }}'
        checksum/dexsecret: '{{ printf "%s-%s" (include (print $.Template.BasePath
          "/dex/dex-secret.yaml") .) .Values.sso.oidc.clientSecret | sha256sum }}'
      labels:
        app.kubernetes.io/instance: dex
        app.kubernetes.io/name: dex
    spec:
      automountServiceAccountToken: false
      containers:
      - args:
        - dex
        - serve
        - --web-http-addr
        - 0.0.0.0:5556
        - --telemetry-addr
        - 0.0.0.0:5558
        - /etc/dex/config.yaml
        env:
        - name: OIDC_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: OIDC_CLIENT_SECRET
              name: akuity-platform
        envFrom:
        - secretRef:
            name: dex
        image: {{ .Values.sso.dex.image.repository }}:{{ .Values.sso.dex.image.tag }}
        imagePullPolicy: IfNotPresent
        livenessProbe:
          httpGet:
            path: /healthz/live
            port: telemetry
        name: dex
        ports:
        - containerPort: 5556
          name: http
          protocol: TCP
        - containerPort: 5558
          name: telemetry
          protocol: TCP
        readinessProbe:
          httpGet:
            path: /healthz/ready
            port: telemetry
{{- with .Values.sso.dex.resources }}
        resources:
{{- toYaml . | nindent 10 }}
{{- end }}
        securityContext:
          capabilities:
            drop:
            - ALL
          runAsNonRoot: true
{{- if not .Values.compatibility.openshift }}
          runAsUser: 1000
{{- end }}
        volumeMounts:
        - mountPath: /etc/dex
          name: config
          readOnly: true
{{- if .Values.sso.dex.image.secret.create }}
      imagePullSecrets:
      - name: dex-pullsecrets
{{- end }}
{{- if not .Values.compatibility.openshift }}
      securityContext:
        fsGroup: 2000
{{- end }}
      serviceAccountName: dex
      volumes:
      - configMap:
          name: dex
        name: config
{{- end }}
