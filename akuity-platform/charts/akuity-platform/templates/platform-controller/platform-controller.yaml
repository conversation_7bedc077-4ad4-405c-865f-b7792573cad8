apiVersion: apps/v1
kind: Deployment
metadata:
  name: platform-controller
  namespace: '{{ .Release.Namespace }}'
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: platform-controller
  template:
    metadata:
      labels:
        app.kubernetes.io/name: platform-controller
      annotations:
        checksum/akuity-platform-secret: {{ include (print $.Template.BasePath "/akuity-platform-secret.yaml") . | sha256sum }}
        checksum/platform-controller: {{ include (print $.Template.BasePath "/platform-controller/platform-controller-cm.yaml") . | sha256sum }}
        checksum/autoscaler: {{ include (print $.Template.BasePath "/platform-controller/autoscaler-cm.yaml") . | sha256sum }}
        checksum/argocd-cluster-autoscaler: {{ include (print $.Template.BasePath "/platform-controller/argocd-cluster-autoscaler-cm.yaml") . | sha256sum }}
        {{- if or (ne .Values.platformController.commonAgentCert "") (or (ne .Values.platformController.argocdAgentCert "") (ne .Values.platformController.kargoAgentCert "") ) }}
        checksum/agent-cert: {{ include (print $.Template.BasePath "/agent-cert-cm.yaml") . | sha256sum }}
        {{- end }}
    spec:
      serviceAccountName: platform-controller
{{- with .Values.platformController.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
{{- end }}
      volumes:
        - name: tmp
          emptyDir: {}
{{- if or .Values.instanceValues .Values.kargoInstanceValues}}
        - name: instance-values
          configMap:
            name: instance-values
{{- end}}
      containers:
        - name: platform-controller
          command: [akuity-platform, platform-controller]
          args:
          - --enable-ingress=true
          - --num-workers=50
{{- if .Values.platformController.portalIPs }}
          - --portal-ips={{ join "," .Values.platformController.portalIPs }}
{{- end }}
{{- if .Values.platformController.shard }}
          - --shard={{ .Values.platformController.shard }}
{{- end }}
{{- if .Values.agent.insecureSkipTLSVerify }}
          - --insecure
{{- end }}
          image: {{ include "akuity-platform.image" . }}
          imagePullPolicy: "{{ .Values.platformController.imagePullPolicy }}"
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            seccompProfile:
              type: RuntimeDefault
          volumeMounts:
          - mountPath: /tmp
            name: tmp
{{- if or .Values.instanceValues .Values.kargoInstanceValues}}
          - mountPath: /config/
            name: instance-values
{{- end }}
          env:
            - name: SKIP_SAFEGUARD
              value: "true"
            - name: TLS_TERMINATION_ENABLED
              value: {{ .Values.tls.terminationEnabled | quote }}
  {{- if .Values.instanceValues}}
            - name: INSTANCE_VALUES_FILE
              value: /config/instance-values.yaml
  {{- end }}
  {{- if .Values.kargoInstanceValues}}
            - name: KARGO_INSTANCE_VALUES_FILE
              value: /config/kargo-instance-values.yaml
  {{- end }}
            - name: CONTROLLER_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          envFrom:
            - secretRef:
                name: akuity-platform
            - configMapRef:
                name: platform-controller
{{- with .Values.platformController.resources }}
          resources:
{{- toYaml . | nindent 12 }}
{{- end }}
      imagePullSecrets:
      - name: akuity-pullsecrets
