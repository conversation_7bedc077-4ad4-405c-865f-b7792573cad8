{{- if or .Values.instanceValues .Values.kargoInstanceValues }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: instance-values
  namespace: '{{ .Release.Namespace }}'
data:
{{- if .Values.instanceValues}}
  instance-values.yaml: |
{{ .Values.instanceValues | toYaml | nindent 4 }}
{{- end -}}
{{- if .Values.kargoInstanceValues}}
  kargo-instance-values.yaml: |
{{ .Values.kargoInstanceValues | toYaml | nindent 4 }}
{{- end -}}
{{- end -}}
