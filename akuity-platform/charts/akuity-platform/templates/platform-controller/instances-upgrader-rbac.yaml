{{- if .Values.instanceUpgrader.enabled }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: instances-upgrader
  namespace: '{{ .Release.Namespace }}'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: instances-upgrader
  namespace: '{{ .Release.Namespace }}'
rules:
  - apiGroups:
      - ''
    resources:
      - secrets
      - configmaps
    verbs:
      - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: instances-upgrader
  namespace: '{{ .Release.Namespace }}'
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: instances-upgrader
subjects:
  - kind: ServiceAccount
    name: instances-upgrader
{{- end}}
