apiVersion: v1
kind: ConfigMap
metadata:
  name: platform-controller
  namespace: '{{ .Release.Namespace }}'
data:
{{- if .Values.platformController.domainSuffix }}
  DOMAIN_SUFFIX: "{{- .Values.platformController.domainSuffix -}}"
{{ else }}
  DOMAIN_SUFFIX: "{{ template "ingressHostname" . }}"
{{- end }}

  PORTAL_URL: "{{ .Values.portal.url }}"

  IMAGE_PULL_SECRET: akuity-pullsecrets
  AGENT_SERVER_IMAGE_HOST: "{{ template "agentServerImageHost" . }}"
{{- if .Values.image.argocd.host }}
  ARGOCD_IMAGE_HOST: "{{- .Values.image.argocd.host -}}"
{{- end -}}
{{- if .Values.image.argocd.repo }}
  ARGOCD_IMAGE_REPO: "{{- .Values.image.argocd.repo -}}"
{{- end -}}
{{- if .Values.compatibility.openshift }}
  OPENSHIFT_COMPATIBILITY: "true"
{{- end -}}
{{- range $key, $val := .Values.platformController.env }}
  {{ $key }}: {{ $val | quote }}
{{- end }}
  INSTANCE_SUBDOMAINS: "{{.Values.platformController.instanceSubDomains}}"
  OVERRIDE_DEFAULT_DOMAINS: "{{.Values.platformController.overrideDefaultDomains}}"
  SIDECAR_FEATURE_COMPATIBILITY: "{{.Values.compatibility.sidecarContainers}}"
  IPV6_ONLY_COMPATIBILITY: "{{.Values.compatibility.ipv6Only}}"
