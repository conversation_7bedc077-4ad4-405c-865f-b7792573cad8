apiVersion: v1
kind: ConfigMap
metadata:
  name: autoscaler
  namespace: '{{ .Release.Namespace }}'
data:
  k3s.small:  "cpu: 0.10, memory: 1Gi, replicas: 2"
  k3s.medium: "cpu: 0.25, memory: 1.5Gi, replicas: 2"
  k3s.large:  "cpu: 1.50, memory: 1.5Gi, replicas: 2"
  k3s.xlarge: "cpu: 2.00, memory: 2Gi, replicas: 3"

  k3s-proxy.small:  "cpu: 0.02, memory: 50Mi,  replicas: 2"
  k3s-proxy.medium: "cpu: 0.04, memory: 100Mi, replicas: 2"
  k3s-proxy.large:  "cpu: 0.10, memory: 250Mi, replicas: 2"
  k3s-proxy.xlarge: "cpu: 0.40, memory: 1.0Gi, replicas: 2"

  application-controller.small:  "cpu: 0.05, memory: 100Mi"
  application-controller.medium: "cpu: 0.10, memory: 150Mi"
  application-controller.large:  "cpu: 0.25, memory: 250Mi"
  application-controller.xlarge: "cpu: 4.00, memory: 1.5Gi"

  redis.small:  "cpu: 0.05, memory: 500Mi"
  redis.medium: "cpu: 0.05, memory: 2.5Gi"
  redis.large:  "cpu: 0.10, memory: 2.5Gi"
  redis.xlarge: "cpu: 0.30, memory: 4Gi"

  threshold.clusters.10:  k3s.small,  k3s-proxy.small
  threshold.clusters.100: k3s.medium, k3s-proxy.medium
  threshold.clusters.300: k3s.xlarge, k3s-proxy.xlarge

  threshold.apps.10:   k3s.small,  k3s-proxy.small,  application-controller.small,  redis.small
  threshold.apps.25:   k3s.small,  k3s-proxy.small,  application-controller.small,  redis.medium
  threshold.apps.100:  k3s.medium, k3s-proxy.medium, application-controller.medium, redis.medium
  threshold.apps.500:  k3s.medium, k3s-proxy.medium, application-controller.large,  redis.large
  threshold.apps.5000: k3s.xlarge, k3s-proxy.xlarge, application-controller.xlarge, redis.xlarge

  threshold.resources.1000:  k3s.small,  k3s-proxy.small
  threshold.resources.10000: k3s.medium, k3s-proxy.medium
  threshold.resources.40000: k3s.xlarge, k3s-proxy.xlarge
