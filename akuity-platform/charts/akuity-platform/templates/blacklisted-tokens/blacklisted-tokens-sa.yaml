apiVersion: v1
kind: ServiceAccount
metadata:
  name: blacklisted-tokens
  namespace: '{{ .Release.Namespace }}'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: blacklisted-tokens
  namespace: '{{ .Release.Namespace }}'
rules:
- apiGroups:
  - ''
  resources:
  - secrets
  verbs:
  - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: blacklisted-tokens
  namespace: '{{ .Release.Namespace }}'
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: blacklisted-tokens
subjects:
  - kind: ServiceAccount
    name: blacklisted-tokens
