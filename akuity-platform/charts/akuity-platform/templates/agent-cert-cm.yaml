{{- if or (ne .Values.platformController.commonAgentCert "") (or (ne .Values.platformController.argocdAgentCert "") (ne .Values.platformController.kargoAgentCert "") ) }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: agent-certs
  namespace: '{{ .Release.Namespace }}'
  annotations:
    # force creation of the resource before any other hooks/resources
    argocd.argoproj.io/sync-wave: "-1"
data:
  common-agent-cert.pem: {{ .Values.platformController.commonAgentCert | quote }}
  argocd-agent-cert.pem: {{ .Values.platformController.argocdAgentCert | quote }}
  kargo-agent-cert.pem: {{ .Values.platformController.kargoAgentCert | quote }}
{{- end }}