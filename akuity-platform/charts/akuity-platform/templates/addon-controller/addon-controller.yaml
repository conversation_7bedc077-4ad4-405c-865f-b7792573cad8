{{- if .Values.addonController.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: addon-controller
  namespace: '{{ .Release.Namespace }}'
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: addon-controller
  template:
    metadata:
      labels:
        app.kubernetes.io/name: addon-controller
      annotations:
        checksum/akuity-platform-secret: {{ include (print $.Template.BasePath "/akuity-platform-secret.yaml") . | sha256sum }}
        checksum/addon-controller: {{ include (print $.Template.BasePath "/addon-controller/addon-controller-cm.yaml") . | sha256sum }}
    spec:
      serviceAccountName: addon-controller
      volumes:
        - name: tmp
          emptyDir: {}
{{- with .Values.addonController.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
{{- end }}
      containers:
        - name: addon-controller
          command: [akuity-platform, addon-controller]
          args:
          - --num-workers=50
{{- if .Values.platformController.shard }}
          - --shard={{ .Values.platformController.shard }}
{{- end }}
{{- if .Values.agent.insecureSkipTLSVerify }}
          - --insecure
{{- end }}
          image: {{ include "akuity-platform.image" . }}
          imagePullPolicy: "{{ .Values.addonController.imagePullPolicy }}"
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            seccompProfile:
              type: RuntimeDefault
          volumeMounts:
          - mountPath: /tmp
            name: tmp
          env:
            - name: SKIP_SAFEGUARD
              value: "true"
            - name: CONTROLLER_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          envFrom:
            - secretRef:
                name: akuity-platform
            - configMapRef:
                name: platform-controller
            - configMapRef:
                name: addon-controller
{{- with .Values.addonController.resources }}
          resources:
{{- toYaml . | nindent 12 }}
{{- end }}
      imagePullSecrets:
      - name: akuity-pullsecrets
{{- end }}