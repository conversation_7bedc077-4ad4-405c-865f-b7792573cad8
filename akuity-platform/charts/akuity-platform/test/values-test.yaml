# Example values.yaml using most defaults and with Dex as a test OIDC provider
portal:
  url: https://selfhosted-tst.dev.akuity.io

tls:
  secret:
    create: false

database:
  host: selfhosted-tst.cluster-cijupqm3qalc.us-west-2.rds.amazonaws.com
  user: postgres
  password: password
  dataKey: m8PTL8tiENXfaOPqop78ljrdaoloXn+w/HeTIWMUgO4=

sso:
  oidc:
    enabled: true
    clientID: "aaabbbcccddd"
    clientSecret: "abc123"
    insecureSkipTLSVerify: true
  dex:
    enabled: true
