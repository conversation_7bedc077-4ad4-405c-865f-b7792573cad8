<!-- Everything below this point is automatically generated using https://github.com/bitnami-labs/readme-generator-for-helm. Any manual changes will be overwritten on the next release -->
<!-- To make changes to this file, modify the comments in the values.yaml file and re-run readme-generator-for-helm -->
## Parameters

### License Key

| Name         | Description | Value |
| ------------ | ----------- | ----- |
| `licenseKey` |             | `""`  |

### Image Parameters

| Name                     | Description                                                 | Value                                             |
| ------------------------ | ----------------------------------------------------------- | ------------------------------------------------- |
| `image.repository`       | Image repository of the Akuity Platform                     | `us-docker.pkg.dev/akuity/akp-sh/akuity-platform` |
| `image.tag`              | Overrides the image tag (default is the chart version)      | `""`                                              |
| `image.secret.create`    | Creates the 'akuity-pullsecrets' secret                     | `true`                                            |
| `image.username`         | Username to the Akuity Platform container registry          | `_json_key_base64`                                |
| `image.password`         | Password to the Akuity Platform container registry          | `""`                                              |
| `image.argocd.host`      | Overrides the Argo CD image host                            | `""`                                              |
| `image.argocd.repo`      | Overrides the Argo CD image repository                      | `""`                                              |
| `image.agentServer.host` | Overrides the agent server image host. If not set, the host | `nil`                                             |

### Portal Parameters

| Name                                                   | Description                                                                                                                        | Value       |
| ------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------- | ----------- |
| `portal.url`                                           | Public URL to portal (e.g. `https://akuity.example.com`)                                                                           | `""`        |
| `portal.imagePullPolicy`                               | Portal server image pull policy                                                                                                    | `Always`    |
| `portal.maxEmailInvitationsPerBatch`                   | Maximum number of invitation emails which can be sent in one go                                                                    | `5`         |
| `portal.autoscaling.enabled`                           | Enables horizontal pod autoscaling for the portal server                                                                           | `true`      |
| `portal.autoscaling.minReplicas`                       | Sets the minimum number of replicas                                                                                                | `3`         |
| `portal.autoscaling.maxReplicas`                       | Sets the maximum number of replicas                                                                                                | `10`        |
| `portal.autoscaling.targetCPUUtilizationPercentage`    | Sets the target CPU utilization percentage                                                                                         | `80`        |
| `portal.autoscaling.targetMemoryUtilizationPercentage` | Sets the target memory utilization percentage                                                                                      | `80`        |
| `portal.seed.organization.name`                        | Creates an organization with a given name                                                                                          | `undefined` |
| `portal.seed.organization.owner`                       | Creates an owner for the given organization with the given e-mail address.                                                         | `undefined` |
| `portal.resources`                                     | Resources limits and requests for the portal server containers                                                                     | `{}`        |
| `portal.tolerations`                                   | Tolerations for the portal-server pod                                                                                              | `[]`        |
| `portal.serviceAccount.annotations`                    | Annotations for the portal-server service account                                                                                  | `{}`        |
| `portal.env`                                           | Additional environment variables added to the portal server                                                                        | `{}`        |
| `portal.env.MIN_ORGANIZATION_NAME_LENGTH`              | The minimum length of an organization name that is allowed on the platform, minimum value is `2`, defaults to `4` if undefined     | `undefined` |
| `portal.env.MIN_CLUSTER_NAME_LENGTH`                   | The minimum length of a cluster name that is allowed on the platform, minimum value is `2`, defaults to `3` if undefined           | `undefined` |
| `portal.env.MIN_INSTANCE_NAME_LENGTH`                  | The minimum length of an Argo CD instance name that is allowed on the platform, minimum value is `2`, defaults to `3` if undefined | `undefined` |
| `portal.topologySpreadConstraints`                     | Sets topology spread constraints for the portal server deployment                                                                  | `undefined` |

### Platform controller Parameters

| Name                                            | Description                                                                                               | Value    |
| ----------------------------------------------- | --------------------------------------------------------------------------------------------------------- | -------- |
| `platformController.imagePullPolicy`            | Platform controller image pull policy                                                                     | `Always` |
| `platformController.domainSuffix`               | Platform controller domain suffix to use (defaults to hostname of `.portal.url`)                          | `""`     |
| `platformController.instanceSubDomains`         | Enables using nested subdomains for managed argocd/kargo instances                                        | `true`   |
| `platformController.overrideDefaultDomains`     | Enables setting subdomains for k3s, agent server etc. or setting the FQDN as a suffix for those services. | `false`  |
| `platformController.tolerations`                | Tolerations for the platform controller pod                                                               | `[]`     |
| `platformController.serviceAccount.annotations` | Annotations for the platform controller service account                                                   | `{}`     |

### Resources limits and requests for the platform controller containers

| Name                                                          | Description                                                                                                                                                                                                                                    | Value       |
| ------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------- |
| `platformController.env`                                      | Adds additional environment variables to the platform controller configmap                                                                                                                                                                     | `{}`        |
| `platformController.env.ARGOCD_APP_RESYNC_INTERVAL_SECONDS`   | Argo CD Application resync interval in seconds (if `0` or undefined then Argo CD built-in default is used)                                                                                                                                     | `undefined` |
| `platformController.env.AGENT_STATUS_UPDATE_INTERVAL_SECONDS` | Agent status update interval in seconds (if `0` or undefined then Agent built-in default is used)                                                                                                                                              | `undefined` |
| `platformController.env.SHARED_K3S_DB_CONNECTION_AUTH`        | Set to `true` for all tenants to use database.user and database.password credentials rather than a personal credentials for each tenant. This might be needed when connecting to the database through RDS Proxy which has a limit of 200 users | `undefined` |
| `platformController.commonAgentCert`                          | common agent cert provides the shared certificate for both *.cdsvcs.akuity.example.com as well as *.kargosvcs.akuity.example.com domains used by agents                                                                                        | `""`        |
| `platformController.argocdAgentCert`                          | argocd agent cert provides the certificate for only *.cdsvcs.akuity.example.com used by argocd agents                                                                                                                                          | `""`        |
| `platformController.kargoAgentCert`                           | kargo agent cert provides the certificate for only *.kargosvcs.akuity.example.com domains used by kargo agents                                                                                                                                 | `""`        |

### Notification controller Parameters

| Name                                                | Description                                                                    | Value    |
| --------------------------------------------------- | ------------------------------------------------------------------------------ | -------- |
| `notificationController.enabled`                    | Enabled the notification controller                                            | `false`  |
| `notificationController.imagePullPolicy`            | Notification controller image pull policy                                      | `Always` |
| `notificationController.tolerations`                | Tolerations for the notification controller pod                                | `[]`     |
| `notificationController.serviceAccount.annotations` | Annotations for the notification controller service account                    | `{}`     |
| `notificationController.resources`                  | Resources limits and requests for the notification controller containers       | `{}`     |
| `notificationController.env`                        | Adds additional environment variables to the notification controller configmap | `{}`     |
| `addonController.enabled`                           | Enabled the addon controller                                                   | `true`   |
| `addonController.imagePullPolicy`                   | Notification controller image pull policy                                      | `Always` |
| `addonController.tolerations`                       | Tolerations for the addon controller pod                                       | `[]`     |
| `addonController.serviceAccount.annotations`        | Annotations for the addon controller service account                           | `{}`     |
| `addonController.resources`                         | Resources limits and requests for the addon controller containers              | `{}`     |
| `addonController.env`                               | Adds additional environment variables to the addon controller configmap        | `{}`     |

### Secret Parameters

| Name            | Description                          | Value  |
| --------------- | ------------------------------------ | ------ |
| `secret.create` | Creates the 'akuity-platform' Secret | `true` |

### TLS Parameters

TLS configuration for ingress. These details are used for configuring Traefik with the proper certificates.

| Name                         | Description                                                                                                                                                                                                                                                              | Value  |
| ---------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ------ |
| `tls.terminationEnabled`     | Enable TLS for the Akuity Platform. TLS can be disabled to allow for SSL termination to be handled by a load balancer or other proxy/ingress before it reaches the Akuity Platform                                                                                       | `true` |
| `tls.secret.create`          | Creates the 'akuity-platform-tls' Secret used as the Traefik default certificate. Set to false if creating the secret in another way (e.g. cert-manager)                                                                                                                 | `true` |
| `tls.crt`                    | TLS certificate. Can be valid for multiple domains (e.g. https://akuity.example.com, https://\*.cd.akuity.example.com, https://\*.cdsvcs.akuity.example.com)                                                                                                             | `""`   |
| `tls.key`                    | TLS private key                                                                                                                                                                                                                                                          | `""`   |
| `tls.additionalCertificates` | List of additional TLS certificates to serve in the form of Kubernetes Secrets. This may be necessary if different certificates are used for different domains (e.g. https://akuity.example.com, https://\*.cd.akuity.example.com, https://\*.cdsvcs.akuity.example.com) | `[]`   |

### Database Parameters

| Name                    | Description                                                                                                                                                                                                                                                    | Value      |
| ----------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------- |
| `database.host`         | Database hostname                                                                                                                                                                                                                                              | `""`       |
| `database.port`         | Database port                                                                                                                                                                                                                                                  | `5432`     |
| `database.user`         | Database username                                                                                                                                                                                                                                              | `""`       |
| `database.password`     | Database password                                                                                                                                                                                                                                              | `""`       |
| `database.dbname`       | Database name                                                                                                                                                                                                                                                  | `postgres` |
| `database.schemaname`   | Schema name                                                                                                                                                                                                                                                    | `public`   |
| `database.createSchema` | create schema automatically                                                                                                                                                                                                                                    | `false`    |
| `database.dataKey`      | 256-bit base64 encoded encryption key used for envelope encryption of sensitive data columns. A random key can be generated with the following command: `openssl rand -base64 32`.  NOTE: loss of this key will result in permanent and irrevocable data loss! | `""`       |
| `database.readOnlyHost` | Database read-only hostname. Used for connection load balancing of read requests to read-only database replicas. If omitted, will default to the write hostname.                                                                                               | `""`       |
| `database.sslmode`      | Database SSL mode                                                                                                                                                                                                                                              | `require`  |

### SSO Parameters

Single Sign-On configuration. Either OIDC or auth0 must be configured.

| Name                                 | Description                                                                                                                                                                                        | Value                                 |
| ------------------------------------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------- |
| `sso.oidc.enabled`                   | Enable OIDC authentication                                                                                                                                                                         | `true`                                |
| `sso.oidc.issuer`                    | OIDC issuer URL. This value is ignored if dex is enabled and served as a subpath                                                                                                                   | `""`                                  |
| `sso.oidc.clientID`                  | OIDC client ID. If dex is enabled, value will be used as Dex's client ID                                                                                                                           | `""`                                  |
| `sso.oidc.clientSecret`              | OIDC client secret. If dex is enabled, value will be used as Dex's client secret                                                                                                                   | `""`                                  |
| `sso.oidc.scopes`                    | OIDC scopes to request (default: openid,profile,email,offline_access)                                                                                                                              | `openid,profile,email,offline_access` |
| `sso.oidc.logoutURL`                 | OIDC logout url                                                                                                                                                                                    | `""`                                  |
| `sso.oidc.insecureSkipTLSVerify`     | Skip TLS verification of the OIDC provider. This will be needed if dex is served as a subpath, and TLS is not yet configured.                                                                      | `false`                               |
| `sso.auth0.enabled`                  | Enable Auth0 configuration                                                                                                                                                                         | `false`                               |
| `sso.auth0.domain`                   | Auth0 domain (e.g. example.us.auth0.com)                                                                                                                                                           | `""`                                  |
| `sso.auth0.audience`                 | Auth0 Audience of the token                                                                                                                                                                        | `""`                                  |
| `sso.auth0.clientID`                 | Auth0 client id for portal service                                                                                                                                                                 | `nil`                                 |
| `sso.auth0.cliClientID`              | Auth0 client id for CLI                                                                                                                                                                            | `nil`                                 |
| `sso.dex.enabled`                    | Install dex                                                                                                                                                                                        | `false`                               |
| `sso.dex.image.repository`           | Image repository for Dex                                                                                                                                                                           | `ghcr.io/dexidp/dex`                  |
| `sso.dex.image.tag`                  | Overrides the Dex image tag                                                                                                                                                                        | `v2.35.3`                             |
| `sso.dex.image.secret.create`        | Creates the 'dex-pullsecrets' secret                                                                                                                                                               | `false`                               |
| `sso.dex.image.username`             | Username for the Dex container registry                                                                                                                                                            | `""`                                  |
| `sso.dex.image.password`             | Password for the Dex container registry                                                                                                                                                            | `""`                                  |
| `sso.dex.tolerations`                | Tolerations for the Dex pod                                                                                                                                                                        | `[]`                                  |
| `sso.dex.serviceAccount.annotations` | Annotations for the Dex service account                                                                                                                                                            | `{}`                                  |
| `sso.dex.secret.create`              | Creates the 'dex' Secret whose data values will be mounted as environment variables to the Dex Deployment                                                                                          | `true`                                |
| `sso.dex.secret.data`                | Secret data keys and plain-text values to set in the 'dex' Secret. These will be environment variables to dex so they can be referenced in the dex/config.yaml                                     | `{}`                                  |
| `sso.dex.resources`                  | Resources limits and requests for the Dex containers                                                                                                                                               | `{}`                                  |
| `sso.dex.issuerSubPath`              | Serve dex as a subpath of the portal URL (e.g. https://akuity.example.com/dex)                                                                                                                     | `true`                                |
| `sso.dex.ingress.enabled`            | Enable ingress to dex                                                                                                                                                                              | `false`                               |
| `sso.dex.ingress.host`               | Host value to dex ingress                                                                                                                                                                          | `""`                                  |
| `sso.dex.config`                     | Additional dex/config.yaml configuration. See https://dexidp.io/docs/ for dex documentation. Configuration can reference environment variables in the 'dex' Secret (e.g. $MICROSOFT_CLIENT_SECRET) | `{}`                                  |
| `sso.roleFromGroups`                 | Contains the SSO groups that will be automatically assigned roles                                                                                                                                  | `undefined`                           |
| `sso.roleFromGroups.member`          | Comma separated list of SSO groups that will be assigned the 'member' role                                                                                                                         | `""`                                  |
| `sso.roleFromGroups.admin`           | Comma separated list of SSO groups that will be assigned the 'admin' role                                                                                                                          | `""`                                  |
| `sso.roleFromGroups.owner`           | Comma separated list of SSO groups that will be assigned the 'owner' role                                                                                                                          | `""`                                  |
| `sso.roleTeamFromGroups`             | Contains the slice of SSO groups with org and team mapping                                                                                                                                         | `undefined`                           |
| `sso.roleTeamFromGroups.oidcGroup`   | SSO group name                                                                                                                                                                                     | `undefined`                           |
| `sso.roleTeamFromGroups.orgRole`     | org role(`member` or `owner`)                                                                                                                                                                      | `undefined`                           |
| `sso.roleTeamFromGroups.teams`       | slice of teams to be assigned                                                                                                                                                                      | `undefined`                           |
| `sso.roleTeamFromGroups.teams.name`  | name of the team                                                                                                                                                                                   | `undefined`                           |

### Traefik Parameters

Traefik is a required component of the Akuity Platform.

The Akuity Platform expects a `traefik-external` ingress class to be present and is installed with Traefik in this section.

| Name                                                    | Description                                                                                                                                                                                                                                                                                                                   | Value                                   |
| ------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------- |
| `traefik.enabled`                                       | Install Traefik                                                                                                                                                                                                                                                                                                               | `true`                                  |
| `traefik.service.annotations`                           | Annotations for the Traefik service. CAUTION: This has the potential to override annotations set by `aws.enabled` or `compatibility.ipv6`!                                                                                                                                                                                    | `{}`                                    |
| `traefik.tolerations`                                   | Tolerations for the Traefik pod                                                                                                                                                                                                                                                                                               | `[]`                                    |
| `traefik.serviceAccount.annotations`                    | Annotations for the Traefik service account                                                                                                                                                                                                                                                                                   | `{}`                                    |
| `traefik.image.repository`                              | Image repository for Traefik                                                                                                                                                                                                                                                                                                  | `public.ecr.aws/docker/library/traefik` |
| `traefik.image.tag`                                     | Overrides the Traefik image tag                                                                                                                                                                                                                                                                                               | `v3.3.6`                                |
| `traefik.image.secret.create`                           | Creates the 'traefik-pullsecrets' secret                                                                                                                                                                                                                                                                                      | `false`                                 |
| `traefik.image.username`                                | Username for the Traefik container registry                                                                                                                                                                                                                                                                                   | `""`                                    |
| `traefik.image.password`                                | Password for the Traefik container registry                                                                                                                                                                                                                                                                                   | `""`                                    |
| `traefik.crd.enabled`                                   | Install Traefik CRDs                                                                                                                                                                                                                                                                                                          | `true`                                  |
| `traefik.websecureRedirect`                             | Redirect 80 to 443. Only set this to false for testing purposes                                                                                                                                                                                                                                                               | `true`                                  |
| `traefik.forceLoadBalancer`                             | Forces the traefik service to be of type LoadBalancer. By default, if tls.terminationEnabled is true, the service will be of type LoadBalancer. If tls.terminationEnabled is false, the service will be of type ClusterIP. This allows you to override the default behavior and force the service to be of type LoadBalancer. | `false`                                 |
| `traefik.autoscaling.enabled`                           | Enables horizontal pod autoscaling for Traefik                                                                                                                                                                                                                                                                                | `true`                                  |
| `traefik.autoscaling.minReplicas`                       | Sets the minimum number of replicas                                                                                                                                                                                                                                                                                           | `3`                                     |
| `traefik.autoscaling.maxReplicas`                       | Sets the maximum number of replicas                                                                                                                                                                                                                                                                                           | `20`                                    |
| `traefik.autoscaling.targetCPUUtilizationPercentage`    | Sets the target CPU utilization percentage                                                                                                                                                                                                                                                                                    | `80`                                    |
| `traefik.autoscaling.targetMemoryUtilizationPercentage` | Sets the target memory utilization percentage                                                                                                                                                                                                                                                                                 | `80`                                    |
| `traefik.replicas`                                      | If autoscaling is not enabled, the number of replicas for the Traefik deployment                                                                                                                                                                                                                                              | `1`                                     |
| `traefik.resources`                                     | Resources limits and requests for the Traefik containers                                                                                                                                                                                                                                                                      | `{}`                                    |
| `traefik.topologySpreadConstraints`                     | Sets topology spread constraints for the Traefik deployment                                                                                                                                                                                                                                                                   | `undefined`                             |

### Other Parameters

| Name                            | Description                                          | Value                      |
| ------------------------------- | ---------------------------------------------------- | -------------------------- |
| `agent.insecureSkipTLSVerify`   | Skip TLS verification from agents to Akuity Platform | `false`                    |
| `aws.enabled`                   | Add AWS specific annotations to resources            | `true`                     |
| `kargoInstanceValues`           | Kargo instance parameters                            | `nil`                      |
| `instanceValues`                | Argo CD instance parameters                          | `nil`                      |
| `instanceValues.k3s`            | k3s parameters                                       | `undefined`                |
| `instanceValues.kustomization`  | Kustomizations to be applied to Argo CD instances    | `undefined`                |
| `instanceValues.k3s_proxy`      | k3s proxy parameters                                 | `undefined`                |
| `instanceValues.pgpool`         | pgpool parameters                                    | `undefined`                |
| `smtp.host`                     | SMTP host                                            | `""`                       |
| `smtp.port`                     | SMTP port                                            | `587`                      |
| `smtp.user`                     | SMTP username                                        | `""`                       |
| `smtp.password`                 | SMTP password                                        | `""`                       |
| `liquibase.image.repository`    | Image repository for Liquibase                       | `quay.io/akuity/liquibase` |
| `liquibase.image.tag`           | Overrides the Liquibase image tag                    | `4.32`                     |
| `liquibase.image.secret.create` | Creates the 'liquibase-pullsecrets' secret           | `false`                    |
| `liquibase.image.username`      | Username for the Liquibase container registry        | `""`                       |
| `liquibase.image.password`      | Password for the Liquibase container registry        | `""`                       |

### Dragonflydb

Dragonflydb Operator is used to deploy and manage Dragonfly instances, this should only be enabled for experimental purposes.

| Name                         | Description                                                                    | Value   |
| ---------------------------- | ------------------------------------------------------------------------------ | ------- |
| `dragonflydb.enabled`        | Installs the Dragonfly operator.                                               | `false` |
| `dragonflydb.replicas`       | The number of replicas for the Dragonfly deployment                            | `2`     |
| `dragonflydb.resources`      | Resources limits and requests for the Dragonfly operator containers            | `{}`    |
| `dragonflydb.serviceMonitor` | When set true then use a ServiceMonitor to configure scraping for the operator | `false` |

### Instance Upgrader Parameters

Instance Upgrader is a job that upgrades managed instances during Akuity Platform upgrade.

| Name                       | Description                       | Value  |
| -------------------------- | --------------------------------- | ------ |
| `instanceUpgrader.enabled` | Enables the instance upgrader job | `true` |

### Compatibility

Specify which compatibility modes will be required

| Name                              | Description                                                                                                                                                                    | Value   |
| --------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ------- |
| `compatibility.openshift`         | Enables Openshift compatibility. This option will modify platform install manifests, Argo CD instance manifests, and Kargo instance manifests to support running on Openshift. | `false` |
| `compatibility.sidecarContainers` | enables use of the Kubernetes sidecar containers feature in the platform workloads                                                                                             | `true`  |
| `compatibility.ipv6Only`          | Enables IPv6 only compatibility                                                                                                                                                | `false` |

### Log Cleanup Parameters

Log Cleaner is a  cron job that cleans up old logs stored from the database. These logs can be from different tasks like kargo analysis jobs etc.

| Name                         | Description                                                                           | Value       |
| ---------------------------- | ------------------------------------------------------------------------------------- | ----------- |
| `logCleaner.enabled`         | Enables the log cleaner job                                                           | `false`     |
| `logCleaner.dryRun`          | Enables dry run mode for the log cleaner job, when enabled the entries aren't deleted | `false`     |
| `logCleaner.imagePullPolicy` | image pull policy for the log cleaner                                                 | `Always`    |
| `logCleaner.schedule`        | Cron schedule for the log cleaner job                                                 | `0 0 * * *` |
| `logCleaner.resources`       | Resources limits and requests for the log cleaner job                                 | `{}`        |

### KubeVision Parameters

KubeVision is a cron job that cleans up old events

| Name                         | Description                      | Value    |
| ---------------------------- | -------------------------------- | -------- |
| `kubeVision.imagePullPolicy` | image pull policy for kubeVision | `Always` |

