- [Developer Guide](#developer-guide)
  - [Tools](#tools)
    - [Using Nix (optional)](#using-nix-optional)
    - [Apple M\* macOS](#apple-m-macos)
    - [Linux (or alternative for macOS)](#linux-or-alternative-for-macos)
  - [Private Git Repos Access](#private-git-repos-access)
  - [Google Artifact Registry Access](#google-artifact-registry-access)
  - [Setup GH Token](#setup-gh-token)
    - [1Password Alternative](#1password-alternative)
  - [Makefile env variables](#makefile-env-variables)
  - [Run Components Locally (preferred dev setup)](#run-components-locally-preferred-dev-setup)
    - [Debugging](#debugging)
    - [Authentication](#authentication)
  - [Upgrade Database Schema](#upgrade-database-schema)
  - [Install Akuity Platform locally using Helm](#install-akuity-platform-locally-using-helm)
    - [Orbstack](#orbstack)
    - [Use Latest AKP Images on GCR](#use-latest-akp-images-on-gcr)
    - [Use Images Built Locally](#use-images-built-locally)
    - [Connect to AKP Locally](#connect-to-akp-locally)
    - [Connecting ArgoCD \& Kargo](#connecting-argocd--kargo)
  - [Install Akuity Platform locally (Deprecated: Use helm method)](#install-akuity-platform-locally-deprecated-use-helm-method)
  - [Test local images in K3D](#test-local-images-in-k3d)
  - [E2E Tests](#e2e-tests)
  - [Testing the Platform in the Dev environment](#testing-the-platform-in-the-dev-environment)
  - [Testing the Agent in the Dev environment](#testing-the-agent-in-the-dev-environment)
  - [Testing Helm chart in the Dev environment](#testing-helm-chart-in-the-dev-environment)
  - [Developing Extensions](#developing-extensions)
  - [Release process](#release-process)
    - [Workflow: Publish Docs](#workflow-publish-docs)
  - [FAQ](#faq)
    - [My local setup on cluster doesn't work (unknown/unexpected issues)](#my-local-setup-on-cluster-doesnt-work-unknownunexpected-issues)

# Developer Guide

## Tools

In order to develop and test Akuity Cloud, you will need the following tools:

- [Make](https://www.gnu.org/software/make/) and [Golang](https://go.dev/)
- [kubectl](https://kubernetes.io/docs/tasks/tools/)
- [YTT](https://github.com/vmware-tanzu/carvel-ytt)
- If you want to run portal-server/platform-controller install [goreman](https://github.com/mattn/goreman)
- If you want to run UI locally install [node](https://nodejs.org/) & [pnpm](https://www.npmjs.com/package/pnpm)

You will also need to decide how to run your K8S cluster.

```
# To install all the necessary prerequisites, you can run the following command:

make install-tools-arm64
```

### Using Nix (optional)

If you have [Nix](https://nix.dev/) [installed](https://github.com/DeterminateSystems/nix-installer), you can use the included `flake.nix` to launch a development shell with all the necessary dependencies.

You can then either run `nix develop` or if you're using `direnv`, you can add the following lines to your `.envrc` file:

```bash
if ! has nix_direnv_version || ! nix_direnv_version 3.0.5; then
  source_url "https://raw.githubusercontent.com/nix-community/nix-direnv/3.0.5/direnvrc" "sha256-RuwIS+QKFj/T9M2TFXScjBsLR6V3A17YVoEW/Q6AZ1w="
fi
use flake
```

### Apple M* macOS

[Orbstack](https://orbstack.dev) is a new alternative to Docker Desktop, which leverages macOS APIs to be faster and more efficient than Docker Desktop. **This is the preferred approach for M1 macOS.**

Orbstack also bundles its own local Kubernetes (think Minikube alternative) which you can optionally run alongside its Docker engine.
By default it exposes all services locally using the standard Kubernetes `cluster.local` convention. Therefore, if using Orbstack,
we don't need to use Telepresence to expose our local Postgres services for the platform components to talk to. For more details on
how Orbstack does this, see their [documentation](https://docs.orbstack.dev/kubernetes/#cluster-local).

> NOTE 1: Orbstack has an [issue](https://github.com/orbstack/orbstack/issues/1452) on recent versions (15.0+) of macOS. You need to change local network access rules in your macOS system settings.
>
> NOTE 2: On macOS you can also install needed tools using `homebrew` and `make install-tools-arm64` command.

```bash
# to check all the tools installed and their version

make run-tools
git           : git version 2.45.1
go            : go version go1.24.3 darwin/arm64
ytt           : ytt version 0.49.0
goreman       : 0.3.15
docker        : 26.1.1
Docker Desktop: 4.30.0
k3d           : v5.6.3
k3s           : v1.28.8-k3s1
kubectl client: v1.30.1
kubectl server: v1.30.0+k3s1
psql          : psql (PostgreSQL) 16.3
telepresence  : v2.18.0
node          : v22.1.0
npm           : 10.7.0
pnpm          : 9.1.1
buf           : 1.31.0
gofumpt       : 0.8.0
```

### Linux (or alternative for macOS)

- [Telepresence](https://www.telepresence.io/)
- [Docker](https://www.docker.com/)
- K8S Cluster [Minikube](https://minikube.sigs.k8s.io/docs/start/) or [K3D](https://k3d.io/) (recommended)

## Private Git Repos Access

Connect to our repositories using [ssh](https://docs.github.com/en/authentication/connecting-to-github-with-ssh). After setting up ssh, run the following for Go to access our private repositories:

```bash
git config --global url."ssh://**************/".insteadOf "https://github.com/"
go env -w GOPRIVATE=github.com/akuityio
```

## Google Artifact Registry Access

Follow instructions in the [engineering wiki](https://drive.google.com/file/d/1X0Gn1bBzfp_Ox0MouB_eQe6k8hXw6UHs/view) to setup your local environment to push and pull images from `us-docker.pkg.dev/akuity-test` and `us-docker.pkg.dev/akuity`. You can find the read only credentials in the shared "Engineering" 1Password vault as well.

It is also useful to setup your credentials using the steps in the [Google Cloud docs](https://cloud.google.com/artifact-registry/docs/docker/authentication#standalone-helper).

## Setup GH Token

Add `./.gh_token` to this repo (ignored by `.gitignore`) with your GitHub personal access token(with `repo` access granted), generated at <https://github.com/settings/tokens>.
Make sure this file contains a single line only that is your GitHub token! This token is needed to build local images for AKP.

Add the following environment variable to your shell profile for E2E tests:

```bash
export GH_TOKEN=$(cat .gh_token)
export GITHUB_USERNAME=<your-github-username>
```

> NOTE: Please use [Tokens (classic)](https://github.com/settings/tokens) instead of [Fine-grained tokens](https://github.com/settings/tokens?type=beta) to avoid issues with permissions while running E2E tests.

### 1Password Alternative

Instead of adding a file to the repository, you can also store the GitHub token in 1Password and use the `op` command line tool to retrieve it. Then you can add the following to a file you source (or use `.envrc` with `direnv`):

```bash
# Please note that your secret and field names may be different, but the format and account should be the same.
export GH_TOKEN=$($(op read --account FKPSO4PFWNDEVALVCPKNZL3HRY "op://Employee/Akuity Platform Dev Github Token/credential"))
export GITHUB_USERNAME=<your-github-username>
```

Please note that you will need to [install the `op` command line tool](https://developer.1password.com/docs/cli/get-started/) and authenticate it with your 1Password account to run these steps.

## Makefile env variables

There are three env variables controlling `dev-env` and `dev-db-schema` targets:

- **`DEV_PORTAL_COMPONENTS`** (`false` by default)
  - `true` - `"make dev-env"` installs all `"manifests/apps"` components, pulling Docker images from `us-docker.pkg.dev/akuity/akp` (see `manifests/schema.yaml`)
  - `false` - `"make dev-env"` installs only `"manifests/apps/dev"` components (RBAC)
  - otherwise - `"make dev-env"` installs all `"manifests/apps"` components, importing local Docker images (see `manifests/values-local-images.yaml`) built with `PUSH_LATEST=true make image`
- **`DEV_DB_SCHEMA`** (`upgrade` by default)
  - `create` - `"make dev-db-schema"` creates new database schema
  - `upgrade` - `"make dev-db-schema"` upgrades the database schema using Liquibase
  - otherwise - `"make dev-db-schema"` performs no action

## Run Components Locally (preferred dev setup)

You can run platform locally to avoid building and pushing images. To setup the platform locally:

1. Run `make dev-setup`
2. Run `make dev-env DEV_PORTAL_COMPONENTS=false DEV_DB_SCHEMA=create`

This will setup databases in your cluster `$DEV_NAMESPACE` (`akuity-platform` by default).

After setting up, you can start the platform with `make goreman-start-dev-auth` or `make goreman-start-real-auth` (see [auth](#authentication) for more information). This will start the whole platform, provided that you have all required tools and accesses.

You can run different parts of the platform independently using `goreman start`. Components names are available in [Procfile](../Procfile). [Procfile](../Procfile) commands are just wrapper over `go run` so you can setup the same commands in your IDE (e.g. for debugging).

**If you ran local images before, you may need to adjust components running in your cluster.** In that case run `make dev-env-local` or:

```bash
kubectl scale deploy/platform-controller --replicas 0 -n akuity-platform
```

### Debugging

To debug `platform-controller` or `portal-server`:

- Comment out the corresponding entry in the `./Procfile` with `#`
- For Intellij IDEA or GoLand:
  - Run => "Edit Configurations"
  - Add new **Go Build** Debug configuration, like in `docs/debug-configuration.png`:
    - Run kind: `File`
    - Files: `../akuity-platform/cmd/akuity-platform/main.go`
    - Program arguments: `"platform-controller"` or `"portal-server"`
    - Working directory: `../akuity-platform`
    - Environment: Edit => Paste the `./.env` content
      - Remove the surrounding quotes (`'`) from `PORTAL_DB_CONNECTION` and `K3S_DB_CONNECTION`
      - The value should look like: `AUTH0_AUDIENCE=..;AUTH0_CALLBACK_URL='..';AUTH0_CLIENT_ID='..';AUTH0_DOMAIN=..;DB_DATA_KEY=..;DEV_AUTH=..;DEV_AUTH_EMAIL=..;FEATURES_SOURCE=..;K3S_DB_CONNECTION=..;PORTAL_DB_CONNECTION=..;PORTAL_URL=..;SMTP_PASSWORD=..;SMTP_PORT=..;SMTP_USER=..`
  - Run => Debug => Select the Configuration created

### Authentication

By default, the Portal uses "dev" authentication (`DEV_AUTH=true` in `.env`) when running locally to skip the usual Auth0 authentication. If dev authentication is enabled then portal automatically creates a `<EMAIL>` user.
You can override `code` query parameter with specific email, as in `http://localhost:3001/api/auth/callback?code=<EMAIL>`.
When running `portal-server` with `goreman` you should see the following messages:

```bash
13:26:19       portal-server | 2022-08-23T13:26:19.015-0700 INFO server/server.go:145 Dev Auth is enabled
```

or

```bash
13:21:55       portal-server | 2022-08-23T13:21:55.135-0700 INFO server/server.go:150 Dev Auth is disabled
```

You can run `goreman-start-dev-auth` or `goreman-start-real-auth` targets to switch between the two modes. When "dev" auth is disabled - the Portal uses the [Test tenant](https://manage.auth0.com/dashboard/us/akuity-test/) of Auth0 authentication (`akuity-test.us.auth0.com`).

## Upgrade Database Schema

Run `make dev-db-schema` to upgrade the database schema. Database is automatically upgraded when you run `make dev-env`.

If you require manual upgrade on a cluster (in case of `dev-up` used), this command can be used:

```bash
docker run --rm \
-v $(pwd)/models/liquibase/changelogs/:/changelogs \
liquibase/liquibase \
--url=**************************************************** \
--username=postgres \
--password=postgres \
--classpath=/changelogs \
--changeLogFile=changelog-root.yaml \
--log-format=TEXT \
update
```

postgres port should be forwarded to localhost.

## Install Akuity Platform locally using Helm

You will need the following environment variables set in your shell profile:

```bash
export DOCKER_USERNAME=<your-docker-username>
export DOCKER_PASSWORD=<your-docker-password>
```

You can optionally add the following lines to a file you source (or use `.envrc` with `direnv`) to use the shared read only credentials from 1Password:

```bash
export DOCKER_USERNAME=$(op read --account FKPSO4PFWNDEVALVCPKNZL3HRY "op://Engineering/GCR Read Only/username")
export DOCKER_PASSWORD=$(op read --account FKPSO4PFWNDEVALVCPKNZL3HRY "op://Engineering/GCR Read Only/credential")
```

Akuity Platform can be installed locally using our self-hosted helm chart. If not using orbstack, create a local cluster using k3d.

```bash
$ cat >/tmp/registries.yaml <<EOL
configs:
  us-docker.pkg.dev:
    auth:
      username: $DOCKER_USERNAME
      password: $DOCKER_PASSWORD
EOL
k3d cluster create akuity-platform --k3s-arg '--disable=traefik@server:*' -p "443:443@loadbalancer" -p "80:80@loadbalancer" --image rancher/k3s:v1.31.3-k3s1 --registry-config /tmp/registries.yaml
```

Make sure your local K8s cluster is up and running and you have `kubectl` access.

```bash
kubectl get all
```

### Orbstack

If running with orbstack, make sure to include an extra `--values ./charts/akuity-platform/test/values-orbstack.yaml` in the helm template commands below.

### Use Latest AKP Images on GCR

Run the following `helm template` and `kubectl apply` command to install Akuity Platform using the latest AKP images on GCR. Ensure you have `DOCKER_PASSWORD` env var set so the image pull secret will be set properly.

```bash
helm template ./charts/akuity-platform \
  --values ./charts/akuity-platform/test/values-dev.yaml \
  --set image.password=$DOCKER_PASSWORD \
  --set image.repository=us-docker.pkg.dev/akuity/akp/akuity-platform \
  --set image.tag=latest \
  --set portal.imagePullPolicy=IfNotPresent \
  --set platformController.imagePullPolicy=IfNotPresent \
  --namespace akuity-platform \
  | kubectl apply -f -
```

### Use Images Built Locally

First, build the images and load them to k3d. Note that if you make any code changes, you will need to rerun the commands below and delete the existing portal and platform pods to pull
in the rebuilt images.

```bash
PUSH_LATEST=true make image
docker images | head -3

k3d image import "akuity-platform:latest" -c "akuity-platform"
```

Run the following `helm template` and `kubectl apply` command to install Akuity Platform using the locally built images.

```bash
helm template ./charts/akuity-platform \
  --values ./charts/akuity-platform/test/values-dev.yaml \
  --set image.repository=akuity-platform \
  --set image.tag=latest \
  --namespace akuity-platform \
  | kubectl apply -f -
```

### Connect to AKP Locally

**Orbstack**

Akuity Platform is now available at <http://portal-server.akuity-platform.svc.cluster.local:9090/>. Use username: `<EMAIL>` and password: `password` to login.

**Telepresence**

```bash
telepresence helm install
telepresence quit --stop-daemons && telepresence connect
```

Akuity Platform is now available at <http://portal-server.akuity-platform:9090/>.

### Connecting ArgoCD & Kargo

When using the `make dev-up` command to setup AKP locally, the ArgoCD server and application controller are deployed with custom network policies that block the ArgoCD server from connecting to the Kargo cluster.
Please delete the following network policies to allow ArgoCD to connect to the bootstraped Kargo cluster:

```bash
kubectl delete networkpolicies argocd-application-controller-custom-network-policy argocd-server-custom-network-policy -n <argocd-namespace>
```

## Install Akuity Platform locally (Deprecated: Use helm method)

- Create a new k3d cluster:

  ```bash
  $ cat >/tmp/registries.yaml <<EOL
  configs:
    us-docker.pkg.dev:
      auth:
        username: $DOCKER_USERNAME
        password: $DOCKER_PASSWORD
  EOL
  $ k3d cluster create akuity-platform --image rancher/k3s:v1.31.3-k3s1 --registry-config /tmp/registries.yaml

  ```

- Before applying application manifests, you will need to apply setup manifests to configure image pull secret:

```bash
  # Expose your Docker credentials
  $ export DOCKER_USERNAME=".."
  $ export DOCKER_PASSWORD=".."
  $ make dev-setup
```

- Make sure your local K8s cluster is up and running and you have `kubectl` access:

  ```bash
  kubectl get all
  ```

- If running for the first time, run the following command to create the `akuity-platform` namespace and install all required components:

  ```bash
  make dev-env DEV_PORTAL_COMPONENTS=true DEV_DB_SCHEMA=create
  ```

- Otherwise, to build and run the platform controller and the portal server images while skipping any schema changes:

  ```bash
  $ PUSH_LATEST=true make image
  $ docker images | head -3
  REPOSITORY                                       TAG             IMAGE ID       CREATED          SIZE
  akuity-platform                                  00407-d52dae9   8d500285ab0a   5 seconds ago    108MB
  akuity-platform                                  latest          8d500285ab0a   5 seconds ago    108MB
  $ make dev-env-import
  ..
  NAME                                   READY   STATUS    RESTARTS   AGE
  postgres-5ccf4667c-5fg54               1/1     Running   0          14h
  platform-controller-69f6f57847-fdk9q   1/1     Running   0          11m
  portal-server-59ffdd4895-r2zr5         1/1     Running   0          11m
  $ kubectl delete pod $(kubectl get pod | grep -E "platform-controller|portal-server" | awk '{print $1}')
  pod "platform-controller-69f6f57847-fdk9q" deleted
  pod "portal-server-59ffdd4895-r2zr5" deleted
  $ kubectl get pod | grep -E "platform-controller|portal-server"
  platform-controller-69f6f57847-9r75n   1/1     Running   0          22s
  portal-server-59ffdd4895-72ndr         1/1     Running   0          22s
  ```

> Note: run `make dev-env DEV_PORTAL_COMPONENTS=false` if you prefer to run components [locally](#run-components-locally-preferred-dev-setup) outside K8s - this command will only run Postgres in a local K8s cluster.

> Note: the `dev-env` target also updates database. You can disable schema upgrade using `DEV_DB_SCHEMA=skip` parameter: `make dev-env DEV_DB_SCHEMA=skip`.

- Make sure everything is up and running. If you're getting `403 Forbidden` when pulling the images - make sure you exposed `DOCKER_USERNAME` and `DOCKER_PASSWORD` as specified
[here](https://app.clickup.com/36184061/v/dc/12g7zx-4640/12g7zx-6080?block=block-a17d4d0a-977f-4e57-8e2d-36fc5b4de4c6) (`make dev-setup` above).

  ```bash
  $ kubectl get deploy -n akuity-platform
  NAME                  READY   UP-TO-DATE   AVAILABLE   AGE
  postgres              1/1     1            1           5d3h
  platform-controller   1/1     1            1           7m40s
  portal-server         1/1     1            1           7m40s
  $ kubectl get pods -n akuity-platform
  NAME                                   READY   STATUS    RESTARTS   AGE
  postgres-5ccf4667c-9d4k5               1/1     Running   0          5d3h
  platform-controller-69f6f57847-8mcrm   1/1     Running   0          7m52s
  portal-server-59ffdd4895-t8wp2         1/1     Running   0          7m52s
  $ kubectl get svc -n akuity-platform
  NAME                  TYPE        CLUSTER-IP      EXTERNAL-IP   PORT(S)             AGE
  postgres              ClusterIP   ***********     <none>        5432/TCP            5d3h
  platform-controller   ClusterIP   *************   <none>        9500/TCP            87s
  portal-server         ClusterIP   ************    <none>        9501/TCP,9090/TCP   7m19s
  ```

- Expose the Portal Server:

  ```bash
  kubectl port-forward svc/portal-server 3001:9090 -n akuity-platform
  ```

Additionally, you need to disable cross-origin restrictions in your browser (if you use Chrome - check out [Allow CORS: Access-Control-Allow-Origin](https://chrome.google.com/webstore/detail/allow-cors-access-control/lhobafahddgcelffkeicbaginigeejlf/) plugin).
Now, you can access Portal UI on <http://localhost:3001/>

> NOTE: If using K3D for local development, the postgres PV and PVC store data to a directory in the K3D Docker container.
> If a change is made to the Liquibase changelog which requires a wipe of the database, you will need to exec into the K3D container
> and delete all data in the `/mnt/data` directory.

## Test local images in K3D

You can build an image locally and test it in K3D by using the `k3d image import` command. An example with the platform controller image is shown below:

```bash
$ k3d image import akuity-platform-controller:latest
```

You can then replace the appropriate `image` field in `values.yaml` with the image name from above.

Then, to ensure that K3D does not attempt to download this image from Docker Hub, add `imagePullPolicy: Never` to the appropriate deployment manifest.

## E2E Tests

The E2E tests are using the same setup and local development setup.

> WARN: tests aggressively clean up test environment on start: truncate all tables, drop schemas from k3s db, kill all argocd tenant namespaces.

It is recommended to setup a separate cluster for E2E tests. Here are the sample commands you need to setup e2e tests environment:

```bash
$ cat >/tmp/registries.yaml <<EOL
configs:
  us-docker.pkg.dev:
    auth:
      username: $DOCKER_USERNAME
      password: $DOCKER_PASSWORD
EOL
$ k3d cluster create akuity-e2e --image rancher/k3s:v1.31.3-k3s1 --registry-config /tmp/registries.yaml
$ make dev-setup dev-env DOCKER_USERNAME=$DOCKER_USERNAME DOCKER_PASSWORD=$DOCKER_PASSWORD
$ telepresence helm install && telepresence connect
$ goreman start
```

The environment is ready. Next, you can `make test-e2e` to run the tests. Each test cleanup environment on start so you can re-run multiple times without re-creating the environment.

## Testing the Platform in the Dev environment

In order to run a personal dev build of the [akuity-platform](https://github.com/akuityio/akuity-platform) in the [Dev environment](https://dev.akuity.io):

* Create a personal `us` **multi-region** repo in the `akuity-test` project: https://console.cloud.google.com/artifacts/browse/akuity-test?authuser=1&project=akuity-test (you may need to switch a Google user to that of Akuity to browse `akuity-test` repos) that is <u>_identical to your GitHub username_</u>
* Select the repo => `Permissions` => `Add Principal` => Add `"<EMAIL>"` Principal and assign it an `"Artifact Registry Writer"` Role. This allows GitHub Action to push images to your personal repo.
  * It may take some time for this action to go into effect. If running GitHub Action below [results](https://github.com/akuityio/akuity-platform/actions/runs/6660809448/job/18102865979#step:9:4419) in `403 Forbidden` - try running it again.
* _Commit your `akuity-platform` changes_ and push them to a personal [`"$USER/dev-image"`], handled by [`DevImage`](https://github.com/akuityio/akuity-platform/blob/main/.github/workflows/dev-image.yml) GitHub Workflow:
  ```
  git branch $USER/dev-image
  git push --force origin $USER/dev-image:$USER/dev-image
  ```
  * Watch [DevImage GitHub Action](https://github.com/akuityio/akuity-platform/actions/workflows/dev-image.yml) running for your commit ([example](https://github.com/akuityio/akuity-platform/actions/runs/6677387605/job/18147370371))
  * Wait for `"Run make image"` to complete and `"Echo image built"` to print out the image URL ([example](https://github.com/akuityio/akuity-platform/actions/runs/5802964543/job/15730225681#step:10:12)):
    ```
    Image built - [us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/akuity-platform:0.9.5-0.**************-fb56cbde388e]
    ```
  * A new personal platform image will show up in `us-docker.pkg.dev/akuity-test/<GitHub-User>/akp/akuity-platform`, created just now.
    ![Personal platform image](personal-platform-image.png "Personal platform image")
  * You could also build the platform image locally which takes significantly less time than [on GitHub](https://github.com/akuityio/akuity-platform/actions/workflows/dev-image.yml):
    ```
    docker buildx create --name multiplatform-builder
    cd portal/ui && pnpm install && NODE_ENV='production' pnpm run build && cd ../..
    cd aims/ui   && pnpm install && NODE_ENV='production' pnpm run build && cd ../..
    go mod tidy && go mod verify && go mod download
    make generate format akputil akuity-platform
    rm -rf vendor
    time PUSH_IMAGE=true \
         BUILDX_BUILDER=multiplatform-builder \
         IMAGE_REPO=us-docker.pkg.dev/akuity-test/<YOUR-REPO>/akp/akuity-platform \
         TAG="$(git rev-parse --short=12 HEAD)" \
         GITHUB_OUTPUT=/dev/stdout \
         make image
    ...
    View build details: docker-desktop://dashboard/build/multiplatform-builder/multiplatform-builder0/z71rrncp7fqbzanztkwnfcpy3
    akuity-platform-image=us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/akuity-platform:902e218bd335
    PUSH_IMAGE=true DISTROLESS=true IMAGE_REPO= TAG= GITHUB_OUTPUT=/dev/stdout    7.48s user 8.10s system 4% cpu 5:58.61 total
    ```

    If you want to build to test self-hosted, make sure you set `SELF_HOSTED=true` when running the `make image` command above. It is also recommended to use `akp-sh` instead of `akp` as the image name to avoid confusion with the normal image.
* Run [`Custom Dev Env Image`](https://github.com/akuityio/akuity-platform-deploy/actions/workflows/custom-dev-image.yml) GitHub Workflow to update the Dev env Akp image, providing your personal **Repo** and image **Tag** ([for example](https://github.com/akuityio/akuity-platform-deploy/actions/runs/9341782955/job/25708966026#step:7:1), `"us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/akuity-platform"` and `"0.9.5-0.**************-fb56cbde388e"`).
* Update [`04-dev.akuity-platform`](https://corp.cd.stage.akuity.io/applications/argocd/04-dev-aws.akuity-platform?view=pods) Argo CD App's [`targetRevision`](https://corp.cd.stage.akuity.io/applications/argocd/04-dev-aws.akuity-platform?view=pods&node=argoproj.io%2FApplication%2Fargocd%2F04-dev-aws.akuity-platform%2F0&tab=manifest) to `<Your-GitHub-User>/bookkeeper/env/dev` branch (where the previous GitHub Workflow has pushed an image update) instead of `bookkeeper/env/dev` and `Refresh` it. It will then `Auto-Sync` the changes.

  ![Updating targetRevision](target-revision.png "Updating targetRevision")
* List the platform's deployments in the Dev env to ensure your personal platform image has now been deployed:
  ```
  $ k get deployments.apps -o wide -n akuity-platform
  NAME                  READY   UP-TO-DATE   AVAILABLE   AGE    CONTAINERS            IMAGES                                                                                                SELECTOR
  aims-server           1/1     1            1           185d   aims-server           us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/akuity-platform:0.9.5-0.**************-fb56cbde388e   app.kubernetes.io/name=aims-server
  billing-controller    1/1     1            1           325d   billing-controller    us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/akuity-platform:0.9.5-0.**************-fb56cbde388e   app.kubernetes.io/name=billing-controller
  platform-controller   1/1     1            1           525d   platform-controller   us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/akuity-platform:0.9.5-0.**************-fb56cbde388e   app.kubernetes.io/name=platform-controller
  portal-server         1/1     1            1           525d   portal-server         us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/akuity-platform:0.9.5-0.**************-fb56cbde388e   app.kubernetes.io/name=portal-server
  ```
* You can also verify `image:` updates in Argo CD:
  * [`aims-server`](https://corp.cd.stage.akuity.io/applications/argocd/04-dev-aws.akuity-platform?view=pods&node=apps%2FDeployment%2Fakuity-platform%2Faims-server%2F0)
  * [`billing-controller`](https://corp.cd.stage.akuity.io/applications/argocd/04-dev-aws.akuity-platform?view=pods&node=apps%2FDeployment%2Fakuity-platform%2Fbilling-controller%2F0)
  * [`platform-controller`](https://corp.cd.stage.akuity.io/applications/argocd/04-dev-aws.akuity-platform?view=pods&node=apps%2FDeployment%2Fakuity-platform%2Fplatform-controller%2F0)
  * [`portal-server`](https://corp.cd.stage.akuity.io/applications/argocd/04-dev-aws.akuity-platform?view=pods&node=apps%2FDeployment%2Fakuity-platform%2Fportal-server%2F0)
  * [`smoketest-prober`](https://corp.cd.stage.akuity.io/applications/argocd/04-dev-aws.akuity-platform?view=pods&node=batch%2FCronJob%2Fakuity-platform%2Fsmoketest-prober%2F0)
* Navigate to [`dev.akuity.io`](https://dev.akuity.io/), sign-in with your Akuity Google account and observe the Dev image tag in the browser console:
  ```
  "Akuity UI version: v0.9.5-0.**************-fb56cbde388e"
  ```
* When you're done, revert the [`04-dev.akuity-platform`](https://corp.cd.stage.akuity.io/applications/argocd/04-dev-aws.akuity-platform?view=pods) Argo CD App [manifest](https://corp.cd.stage.akuity.io/applications/argocd/04-dev-aws.akuity-platform?view=pods&node=argoproj.io%2FApplication%2Fargocd%2F04-dev-aws.akuity-platform%2F0&tab=manifest) branch back to `bookkeeper/env/dev`.

## Testing the Agent in the Dev environment

In order to run a personal dev build of the [agent](https://github.com/akuityio/agent) in the [Dev environment](https://dev.akuity.io):

* _Commit your `agent` changes_ and push them to a personal `"$USER/dev-images"` branch, handled by [`DevImages`](https://github.com/akuityio/agent/blob/master/.github/workflows/dev-images.yaml) GitHub Workflow:
  ```
  git branch $USER/dev-images
  git push --force origin $USER/dev-images:$USER/dev-images
  ```
* Watch [DevImages GitHub Action](https://github.com/akuityio/agent/actions/workflows/dev-images.yaml) running for your commit ([example](https://github.com/akuityio/agent/actions/runs/6806971836/job/18509060804))
* Wait for `"Run make push-images"` to complete and `"Echo images built"` to print out the images URL ([example](https://github.com/akuityio/agent/actions/runs/6806971836/job/18509060804#step:11:9)):
  ```
  Images built - [us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/agent:0.4.24-0.20231108231110-1bf6f90b0a20] / [us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/agent-server:0.4.24-0.20231108231110-1bf6f90b0a20]
  ```
* A new personal agent images will show up, created just now:
  * `us-docker.pkg.dev/akuity-test/<GitHub-User>/akp/agent`

  ![Personal agent image](personal-agent-image.png "Personal agent image")

  * `us-docker.pkg.dev/akuity-test/<GitHub-User>/akp/agent-server`

  ![Personal agent-server image](personal-agent-server-image.png "Personal agent-server image")

* For `cluster-agent` (managed cluster `agent`) - update the `agent` image in the following **6** files:
  * [`agent/manifests/cluster-agent/schema.yaml`](https://github.com/akuityio/agent/blob/master/manifests/cluster-agent/schema.yaml):
  ```
  agent:
    image_host: us-docker.pkg.dev/akuity-test/evgeny-goldin/akp
    image_repo: agent
    version: 0.4.24-0.20231108231110-1bf6f90b0a20
  ```
  * [`agent/manifests/kargo-clusteragent/schema.yaml`](https://github.com/akuityio/agent/blob/master/manifests/kargo-clusteragent/schema.yaml):
  ```
  agent:
    image_host: us-docker.pkg.dev/akuity-test/evgeny-goldin/akp
    image_repo: agent
    version: 0.4.24-0.20231108231110-1bf6f90b0a20
  ```
  * [`agent/openapi/cluster-agent.yaml`](https://github.com/akuityio/agent/blob/master/openapi/cluster-agent.yaml):
  ```
  agent:
    properties:
      image_host:
        type: string
        default: us-docker.pkg.dev/akuity-test/evgeny-goldin/akp
      image_repo:
        type: string
        default: agent
      version:
        type: string
        default: 0.4.24-0.20231108231110-1bf6f90b0a20
  ```
  * [`agent/manifests/cluster-upgrader/schema.yaml`](https://github.com/akuityio/agent/blob/master/manifests/cluster-upgrader/schema.yaml):
  ```
  agent:
    image_host: us-docker.pkg.dev/akuity-test/evgeny-goldin/akp
    image_repo: agent
    version: 0.4.24-0.20231108231110-1bf6f90b0a20
  ```
  * [`agent/openapi/cluster-upgrader.yaml`](https://github.com/akuityio/agent/blob/master/openapi/cluster-upgrader.yaml):
  ```
  agent:
    properties:
      image_host:
        type: string
        default: us-docker.pkg.dev/akuity-test/evgeny-goldin/akp
      image_repo:
        type: string
        default: agent
      version:
        type: string
        default: 0.4.24-0.20231108231110-1bf6f90b0a20
  ```
  * [`agent/openapi/kargo-cluster-agent.yaml`](https://github.com/akuityio/agent/blob/master/openapi/kargo-cluster-agent.yaml):
  ```
  agent:
    properties:
      image_host:
        type: string
        default: us-docker.pkg.dev/akuity-test/evgeny-goldin/akp
      image_repo:
        type: string
        default: agent
      version:
        type: string
        default: 0.4.24-0.20231108231110-1bf6f90b0a20
  ```
* For `control-plane` (tenant's namespace `agent-server`) - update the `agent_server` image in the following **4** files:
  * [`agent/manifests/control-plane/schema.yaml`](https://github.com/akuityio/agent/blob/master/manifests/control-plane/schema.yaml):
  ```
  agent_server:
    image_host: us-docker.pkg.dev/akuity-test/evgeny-goldin/akp
    image_repo: agent-server
    version: 0.4.24-0.20231108231110-1bf6f90b0a20
  ```
  * [`agent/manifests/kargo-controlplane/schema.yaml`](https://github.com/akuityio/agent/blob/master/manifests/kargo-controlplane/schema.yaml):
  ```
  agent_server:
    image_host: us-docker.pkg.dev/akuity-test/evgeny-goldin/akp
    image_repo: agent-server
    version: 0.4.24-0.20231108231110-1bf6f90b0a20
  ```
  * [`agent/openapi/control-plane.yaml`](https://github.com/akuityio/agent/blob/master/openapi/control-plane.yaml):
  ```
  agent_server:
    properties:
      image_host:
        type: string
        default: us-docker.pkg.dev/akuity-test/evgeny-goldin/akp
      image_repo:
        type: string
        default: agent-server
      version:
        type: string
        default: 0.4.24-0.20231108231110-1bf6f90b0a20
  ```
  * [`agent/openapi/kargo-control-plane.yaml`](https://github.com/akuityio/agent/blob/master/openapi/kargo-control-plane.yaml):
  ```
  agent_server:
    properties:
      image_host:
        type: string
        default: us-docker.pkg.dev/akuity-test/evgeny-goldin/akp
      image_repo:
        type: string
        default: agent-server
      version:
        type: string
        default: 0.4.24-0.20231108231110-1bf6f90b0a20
  ```
* Run `$ make codegen` in the `agent` repo.
* For `agent` image update:
  * Verify changes were made in [`agent/pkg/client/apis/clusteragent/model_data_values_agent.go`](https://github.com/akuityio/agent/blob/master/pkg/client/apis/clusteragent/model_data_values_agent.go), [`agent/pkg/client/apis/clusterupgrader/model_data_values_agent.go`](https://github.com/akuityio/agent/blob/master/pkg/client/apis/clusterupgrader/model_data_values_agent.go), and [`agent/pkg/client/apis/kargo/clusteragent/model_data_values_agent.go`](https://github.com/akuityio/agent/blob/master/pkg/client/apis/kargo/clusteragent/model_data_values_agent.go)
    ```
    $ git status
      ..
        modified:   manifests/cluster-agent/schema.yaml
        modified:   manifests/cluster-upgrader/schema.yaml
        modified:   manifests/kargo-clusteragent/schema.yaml
        modified:   openapi/cluster-agent.yaml
        modified:   openapi/cluster-upgrader.yaml
        modified:   openapi/kargo-cluster-agent.yaml
        modified:   pkg/client/apis/clusteragent/model_data_values_agent.go
        modified:   pkg/client/apis/clusterupgrader/model_data_values_agent.go
        modified:   pkg/client/apis/kargo/clusteragent/model_data_values_agent.go
    ```
  * Update `SupportedAgentVersions` in [`agent/pkg/client/versions.go`](https://github.com/akuityio/agent/blob/master/pkg/client/versions.go)
    ```
    SupportedAgentVersions = append(getPatchVersions(CurrentAgentVersion), "0.4.24-0.20231108231110-1bf6f90b0a20")
    ```
  * Add `imagePullSecrets` to the following `agent` manifests (for a personal image to be downloaded to a managed cluster):
    * This should be any manifest using
      ```
      image: #@ "{}/{}:{}".format(data.values.agent.image_host, data.values.agent.image_repo, data.values.agent.version)
      ```
    * [`manifests/kargo-clusteragent/k8s-manifests/controller/overlay-syncer.yaml`](https://github.com/akuityio/agent/blob/master/manifests/kargo-clusteragent/k8s-manifests/controller/overlay-syncer.yaml)
    * [`manifests/cluster-upgrader/upgrader.yaml`](https://github.com/akuityio/agent/blob/master/manifests/cluster-upgrader/upgrader.yaml)
    * [`manifests/cluster-agent/notification-controller/overlay-notification-controller.yaml`](https://github.com/akuityio/agent/blob/master/manifests/cluster-agent/notification-controller/overlay-notification-controller.yaml)
    * [`manifests/cluster-agent/applicationset-controller/overlay-applicationset-controller.yaml`](https://github.com/akuityio/agent/blob/master/manifests/cluster-agent/applicationset-controller/overlay-applicationset-controller.yaml)
    * [`manifests/cluster-agent/application-controller/overlay-syncer.yaml`](https://github.com/akuityio/agent/blob/master/manifests/cluster-agent/application-controller/overlay-syncer.yaml)
    * [`manifests/cluster-agent/application-controller/overlay-application-controller.yaml`](https://github.com/akuityio/agent/blob/master/manifests/cluster-agent/application-controller/overlay-application-controller.yaml)
    * [`manifests/cluster-agent/agent/akuity-agent.yaml`](https://github.com/akuityio/agent/blob/master/manifests/cluster-agent/agent/akuity-agent.yaml)
    ```
    spec:
      template:
        spec:
          imagePullSecrets:
            - name: akuity-pullsecrets
    ```
    * Add `#@overlay/match missing_ok=True` for ytt overlays:
    ```
    spec:
      template:
        spec:
          #@overlay/match missing_ok=True
          imagePullSecrets:
            - name: akuity-pullsecrets
    ```
* For `agent_server` image update:
  * Verify changes were made in [`agent/pkg/client/apis/controlplane/model_data_values_agent_server.go`](https://github.com/akuityio/agent/blob/master/pkg/client/apis/controlplane/model_data_values_agent_server.go) and [`agent/pkg/client/apis/kargo/controlplane/model_data_values_agent_server.go`](https://github.com/akuityio/agent/blob/master/pkg/client/apis/kargo/controlplane/model_data_values_agent_server.go)
    ```
    $ git status
      ..
        modified:   manifests/control-plane/schema.yaml
        modified:   manifests/kargo-controlplane/schema.yaml
        modified:   openapi/control-plane.yaml
        modified:   openapi/kargo-control-plane.yaml
        modified:   pkg/client/apis/controlplane/model_data_values_agent_server.go
        modified:   pkg/client/apis/kargo/controlplane/model_data_values_agent_server.go
    ```
* _Commit and push your `agent` changes_ to a personal dev branch ([example commit](https://github.com/akuityio/agent/commit/b44ef5876c3d02dec13d963f3abecb2396731ef8)).
* Switch to agent's personal branch in the `akuity-platform` repo:
  ```
  $ go get github.com/akuityio/agent@<your-personal-branch>
  $ go mod tidy
  $ go mod verify
  ```
  * Verify the corresponding changes made in `akuity-platform/go.mod` and `akuity-platform/go.sum` refer to your agent's personal branch commit
* :bangbang: Remove the following env variables:
  * `AGENT_SERVER_IMAGE_HOST` in [`akuity-platform-deploy/base/charts/akuity-platform/templates/platform-controller/platform-controller-cm.yaml`](https://github.com/akuityio/akuity-platform-deploy/blob/main/base/charts/akuity-platform/templates/platform-controller/platform-controller-cm.yaml)
* Follow instructions in the [Testing the Platform in the Dev environment](#testing-the-platform-in-the-dev-environment) above to deploy `akuity-platform` and `akuity-platform-deploy` changes to the Dev env
* Create a new instance in the [Dev environment](https://dev.akuity.io) and verify its `Deployments` => `IMAGES`:
```
$ k get deployments -o wide -n argocd-8te1q9jkol5w52mk
NAME                               READY   UP-TO-DATE   AVAILABLE   AGE   CONTAINERS                                                        IMAGES
agent-server                       2/2     2            2           71m   agent-server                                                      us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/agent-server:0.4.24-0.20231108231110-1bf6f90b0a20
argocd-application-controller      1/1     1            1           71m   argocd-application-controller,repo-server-proxy                   quay.io/argoproj/argocd:v2.8.5,us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/agent-server:0.4.24-0.20231108231110-1bf6f90b0a20
argocd-applicationset-controller   1/1     1            1           71m   argocd-applicationset-controller,cplanesyncer,repo-server-proxy   quay.io/argoproj/argocd:v2.8.5,us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/agent-server:0.4.24-0.20231108231110-1bf6f90b0a20,us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/agent-server:0.4.24-0.20231108231110-1bf6f90b0a20
argocd-dex-server                  0/0     0            0           71m   dex                                                               quay.io/akuity/dexidp/dex:v2.37.0
argocd-image-updater               0/0     0            0           71m   argocd-image-updater,cplanesyncer                                 quay.io/argoprojlabs/argocd-image-updater:v0.12.2,us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/agent-server:0.4.24-0.20231108231110-1bf6f90b0a20
argocd-redis-ha-haproxy            2/2     2            2           71m   haproxy                                                           quay.io/akuity/haproxy:2.6.9
argocd-repo-server                 1/1     1            1           71m   argocd-repo-server,cplanesyncer                                   quay.io/argoproj/argocd:v2.8.5,us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/agent-server:0.4.24-0.20231108231110-1bf6f90b0a20
argocd-server                      2/2     2            2           71m   argocd-server,repo-server-proxy                                   quay.io/argoproj/argocd:v2.8.5,us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/agent-server:0.4.24-0.20231108231110-1bf6f90b0a20
k3s                                2/2     2            2           71m   k3s                                                               quay.io/akuity/rancher/k3s:v1.31.3-k3s1
k3s-proxy                          2/2     2            2           71m   proxy                                                             us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/agent-server:0.4.24-0.20231108231110-1bf6f90b0a20
k3s-webhook                        2/2     2            2           71m   webhook                                                           us-docker.pkg.dev/akuity-test/evgeny-goldin/akp/agent-server:0.4.24-0.20231108231110-1bf6f90b0a20
pgpool                             0/0     0            0           71m   pgpool                                                            pgpool/pgpool:4.3.1
```
* When testing a managed cluster `agent` - you can use a local K3d or OrbStack cluster. Make sure agent's `Namespace` has the `akuity-pullsecrets` K8s Secret:
  ```
  $ DEV_NAMESPACE=<agent-NS> ./hack/dev-setup.sh
  ```

## Testing Helm chart in the Dev environment

Akuity Helm chart is managed at [`akuity-platform/charts/akuity-platform`](https://github.com/akuityio/akuity-platform/tree/main/charts/akuity-platform) and is copied to [`akuity-platform-deploy/base/charts/akuity-platform`](https://github.com/akuityio/akuity-platform-deploy/tree/main/base/charts/akuity-platform) by [GitHub Actions](https://github.com/akuityio/akuity-platform/blob/36da3da61d3c5b90d47bed8ea0472e5c1b9f2205/.github/workflows/ci-build.yml#L152-L164).
In order to test it in the Dev environment:

* Copy and paste all chart files to [`akuity-platform-deploy/base/charts/akuity-platform`](https://github.com/akuityio/akuity-platform-deploy/tree/main/base/charts/akuity-platform)
* Update the Helm values file at the Dev env at [`akuity-platform-deploy/env/dev/aws/akuity-platform/values.yaml`](https://github.com/akuityio/akuity-platform-deploy/blob/main/env/dev/aws/akuity-platform/values.yaml)
* Run `$ make dev-env-gen` target in the `akuity-platform-deploy` repo. This will generate the Dev env manifests and store them in your personal `$USER/bookkeeper/env/dev` Git branch.
  ```
  $ make dev-env-gen
  ...
  >>> https://github.com/akuityio/akuity-platform-deploy/tree/evgeny/bookkeeper/env/dev
  >>> https://github.com/akuityio/akuity-platform-deploy/commits/evgeny/bookkeeper/env/dev
  >>> https://github.com/akuityio/akuity-platform-deploy/commit/30feae42dd95619fe1e01985ccc026b1007099aa
  >>> https://github.com/akuityio/akuity-platform-deploy/compare/bookkeeper/env/dev...evgeny/bookkeeper/env/dev
  ```
* You can click the last link in the `dev-env-gen` output to diff the original manifest with its newly generated version in your personal branch.
* To deploy the change to the Dev env - update the [`04-dev-aws.akuity-platform`](https://corp.cd.stage.akuity.io/applications/argocd/04-dev-aws.akuity-platform) Argo App and [switch its `source.targetRevision`](https://corp.cd.stage.akuity.io/applications/argocd/04-dev-aws.akuity-platform?view=tree&node=argoproj.io%2FApplication%2Fargocd%2F04-dev-aws.akuity-platform%2F0&tab=manifest&resource=) to your personal branch:
![Updating source.targetRevision](04-dev-aws.akuity-platform.png)

## Developing Extensions

To run a development server for testing Akuity extensions, you can use the `pnpm dev:extensions` command. To streamline development, you can use the `ARGOCD_SERVER` environment variable to specify which Argo CD you'll be testing the extension on:

```
ARGOCD_SERVER=http://argocd-server.argocd-<id>.svc.cluster.local pnpm dev:extensions
```

Once it's started, you can access it at  http://127.0.0.1:3002

> Note: use http://127.0.0.1:3002 instead of http://localhost:3002 to avoid issue caused by having argocd and portal cookies.

## Release process

The release process today is as follows:

* The [CI Build workflow](../.github/workflows/ci-build.yml) builds a Docker image on any merge to `main`. There are several other workflows that generally run, including publishing the docs with latest release updates that includes generating the self-hosted docs and charts documentation. If you'd like to read about the workflow for publishing the docs, go through the [Publish Docs](#workflow-publish-docs) workflow.
* When [Kargo](https://corp.kargo.stage.akuity.io/project/akuity-platform) detects new images, it will update the corresponding Kustomize overlays for the [dev](https://github.com/akuityio/akuity-platform-deploy/blob/main/env/dev/aws/akuity-platform/kustomization.yaml) and [test](https://github.com/akuityio/akuity-platform-deploy/blob/main/env/test/aws/akuity-platform/kustomization.yaml) stages. In turn, each stage has a corresponding Argo CD application ([dev](https://corp.cd.stage.akuity.io/applications/argocd/04-dev-aws.akuity-platform), [test](
  https://corp.cd.stage.akuity.io/applications/argocd/04-test-aws.akuity-platform)) which auto-syncs any changes to these manifests.
* In order to promote a given freight to `staging` or `prod`, compare the current freight deployed vs the new freight you'd like to deploy. In Github the simplest way to do so would be to give a link to `https://github.com/akuityio/akuity-platform/compare/${old freight sha}..${new freight sha}`. The current version deployed on a given stage can be checked in the Chrome (or Firefox) console. When the page initially loads you'll see something like
`Akuity UI version: v0.13.1-0.20240620084925-514f35cd7ba2-distroless`
* Ask the engineers who have done changes in the diff whether it is OK to promote
* Promote the freight to `staging` and verify that everything is working
* Verify that there are no events in the `Don't Push to Prod Events` during the timeframe you would like to deploy on
* Notify/ask devs to promote the freight to `prod`. Assuming you get a thumbs-up, please check `#on-call-stage` to verify that nothing has been broken in the meantime. Also, please check `#on-call-prod` to verify that there are no ongoing incidents before doing the final promotion.
* Once the prod promotion has started, Kargo will generate a PR in [akuity-platform-deploy](https://github.com/akuityio/akuity-platform-deploy). It will have a title similar to `updated env/prod/aws/akuity-platform/kustomization.yaml to use image us-docker.pkg.dev/akuity/akp/akuity-platform:0.13.1-0.20240620084925-514f35cd7ba2-distroless` (see [example](https://github.com/akuityio/akuity-platform-deploy/pull/1352/files))
* Once that PR has been merged, _another_ PR will be generated in the same repo. This will have a title similar to `bookkeeper/env/prod <-- latest batched changes` (see [example](https://github.com/akuityio/akuity-platform-deploy/pull/1353/files)).
* Once _that_ has been merged, the [prod app](https://corp.cd.stage.akuity.io/applications/04-prod-aws.akuity-platform) will be synced.
* Please check the status of the smoke tests, the results of which are outputted into `#on-call-prod`, as well as [Datadog](https://us5.datadoghq.com/logs?query=cluster_name%3Aakp-001-prd-usw2%20kube_namespace%3Aakuity-platform%20pod_name%3Asmoketest-prober%2A%20container_name%3Aprober%20%28%22failed%22%20%7C%7C%20%22succeeded%22%29%20-%22Failed%20to%20resolve%20group%20%27kvm%27%22%20&agg_m=count&agg_m_source=base&agg_t=count&cols=status%2Ckube_namespace%2Cservice%2Cpod_name%2Ccontainer_name%2Ccluster_name&fromUser=true&messageDisplay=inline&refresh_mode=sliding&storage=hot&stream_sort=time%2Cdesc&view=spans&viz=stream&from_ts=1711416136032&to_ts=1711502536032&live=true)
* ???
* Profit!

### Workflow: [Publish Docs](https://github.com/akuityio/akuity-platform/blob/main/.github/workflows/publish-docs.yml)

This [GitHub Actions workflow](../.github/workflows/publish-docs.yml) automates the process of updating and publishing API/CLI documentation and self-hosted documentation. The workflow is triggered either by a new release or manually via the `workflow_dispatch` trigger.

There are two jobs that have been defined in the workflow whose details have been mentioned below.

**Job: `publish-api-cli-docs`**

This job is responsible for generating and publishing the API and CLI documentation whenever a new release is published or the workflow is triggered manually. Following steps mentions details about this job:

- Clone the [main repository](https://github.com/akuityio/akuity-platform) (akuity-platform) to the GitHub runner, allowing access to the codebase for further processing.
- Install the specified Go version on the runner. This is required to run Go scripts for generating documentation.
- Cache the Go modules to speed up future runs of the workflow by avoiding the need to re-download dependencies.
- Configures Git to use a personal access token (PAT) for accessing private repositories. This is necessary for cloning and pushing to private repos.
- Clone the [documentation repository](https://github.com/akuityio/docs.akuity.io) into the `../docs.akuity.io` directory for further processing.
- Once the docs repository has been cloned, the next step involves generating the docs. In this step, we are going to delete any existing CLI documentation files to ensure that the new documentation is up-to-date. Once done, the CLI Documentation is regenerated using a Go script that stores the output in the appropriate directory. Finally, the Swagger API documentation is copied to the static directory of the documentation repository.
- For these changes to be visible on the docs site, a PR needs to be raised at the [documentation repository](https://github.com/akuityio/docs.akuity.io) by the [akuitybot](https://github.com/akuitybot).
  - Configures Git with the Akuity Bot's user information.
  - Creates a new branch with a unique name (based on the current timestamp) to avoid conflicts and ensure that multiple PRs can be raised at one point of time.
  - Commits the changes to this branch and pushes it to the remote repository.
  - Automatically creates a pull request to merge the changes with the main branch of the documentation repository.
  - This is an [example PR](https://github.com/akuityio/docs.akuity.io/pull/533) raised by the Akuity Bot for updating the self-hosted docs.

**Job: `publish-selfhosted-docs`**

This job is responsible for generating and publishing the self-hosted documentation, specifically the [changelog](https://docs.akuity.io/changelog/selfhosted/) and Helm chart documentation, whenever a new release is published or the workflow is triggered manually. The following steps outline the details of this job:

- Clone the [main repository](https://github.com/akuityio/akuity-platform) (akuity-platform) to the GitHub runner, allowing access to the necessary scripts and resources for generating self-hosted documentation.
- Clone the [documentation repository](https://github.com/akuityio/docs.akuity.io) into the docs directory for further processing. This repository will be updated with the generated self-hosted documentation.
- Install the [Helm README generator tool](https://github.com/bitnami/readme-generator-for-helm) globally via npm. This tool is used to generate documentation for Helm charts.
- Install the specified Go version on the runner. This is required to run Go scripts for generating documentation.
- Cache the Go modules to speed up future runs of the workflow by avoiding the need to re-download dependencies.
- Configure Git to use a personal access token (PAT) for accessing private repositories. This is necessary for cloning and pushing to private repos.
- Generate the self-hosted documentation by running custom Bash scripts. These scripts generate the changelog and Helm chart documentation for self-hosted environments, placing the generated documentation in the appropriate directories within the docs repository. The [`selfhosted-docs.sh`](https://github.com/akuityio/akuity-platform/blob/main/hack/selfhosted-docs/selfhosted-docs.sh) is the script that is run for generating the changelog and helm chart documentation. The script takes an argument and based on the argument, it generates the changelog and chart documentation.
- For these changes to be visible on the docs site, a PR needs to be raised at the documentation repository by the akuitybot.
  - Configures Git with the Akuity Bot's user information.
  - Creates a new branch with a unique name (based on the current timestamp) to avoid conflicts and ensure that multiple PRs can be raised at one point of time.
  - Commits the changes to this branch and pushes it to the remote repository.
  - Automatically creates a pull request to merge the changes with the main branch of the documentation repository.
  - This is an [example PR](https://github.com/akuityio/docs.akuity.io/pull/529) raised by the Akuity Bot for updating the self-hosted docs.

## FAQ

### My local setup on cluster doesn't work (unknown/unexpected issues)

It's probably the best idea to delete the whole cluster and try setting it up again.
