# macOS Notarizing Process

## Overview

Akuity binaries are notarized by Apple to prove to our users the executable was produced by our release process and checked by Apple for malicious code. The following describes the process for notarizing our assets.

We use [Quill](https://github.com/anchore/quill/), a cross platform CLI tool that makes it convenient to submit artifacts to the Apple API to be notarized. It is used as a superior alternative to [Gon](https://github.com/mitchellh/gon). This is because <PERSON><PERSON> must run on macOS (since it invokes the Apple `codesign` utility for notarization). Requiring macOS runners makes the CI process slower and more complicated, especially since [docker is not installed by default in macOS runners](https://github.com/actions/runner-images/issues/17).

## One-Time Setup Instructions

### 1. Subscribe to the Apple Developer Program

The first step is to enroll/subscribe to the Apple Developer Program ($99/year). Enrollment can be performed via Apple website:
https://developer.apple.com/support/enrollment/


### 2. Create App Store Connect API Access Key

Once enrolled, we need to generate an API key to give to Quill so that it may make API requests to the [Notary API](https://developer.apple.com/documentation/notaryapi).

a. Visit the "Users and Access" section of the [App Store Connect Portal](https://appstoreconnect.apple.com/access/api).

b. Select the Keys tab and choose key type "App Store Connect API".

c. Click the [+] to create a new Key, then download the `AuthKey_<KEY-ID>.p8` file.

![](notarizing/appstore-create-api-key.png)

d. Once the key is generated take note of the following information which will later be supplied to quill as environment variables:

| Description | Quill Env Var | Example |
|---|---|---|
| Issuer ID | `QUILL_NOTARY_ISSUER` | `bc6e95b5-2605-4eb7-a6cb-e8d42f234ce6` |
| Key ID | `QUILL_NOTARY_KEY_ID` | `XMGYH4V855` |
| Base64 contents of `AuthKey_<KEY-ID>.p8` | `QUILL_NOTARY_KEY` | ```-----BEGIN PRIVATE KEY-----```<br>```MIGTAgBAMBMGByqGSMxxxxxxxxxxx```<br>```xxxxxxxxxxxxxxxxxxxxxxxxxxxxx```<br>```xxxxxxxxxxxxxxxxxxxxxxxxxxxxx```<br>`-----END PRIVATE KEY-----` |

![](notarizing/appstore-api-keys.png)


### 3. Generate Certificate

To get a certificate from Apple, we must first generate a Certificate Signing Request (CSR):

a. On your Mac, open the Keychain Access utility
b. From the Keychain Access menu, select:

   `Certificate Assistant` > `Request a Certificate From a Certificate Authority...`

Then use your apple id email and give it a descriptive name. The CA Email Address can be left blank and the request should be saved to disk.

<img src="notarizing/csr-generate.png" width="200">

Once completed, you will save a `CertificateSigningRequest.certSigningRequest` file to a location of your choosing.

c. Navigate to the Apple Developer portal into the [Certificates, Identifiers & Profiles](https://developer.apple.com/account/resources/certificates/list) section. Click [+] to create a new certificate, select “Developer ID Application” from the options, and then continue.

![](notarizing/cert-create.png)

d. On the next screen, select the `G2 Sub-CA` profile type and upload the `CertificateSigningRequest.certSigningRequest` file from the earlier step.

![](notarizing/csr-upload.png)

e. Navigate to your newly created cert in and download the `.cer` file. 

![](notarizing/cert-download.png)

f. Once downloaded, double-click the the `.cer` file to install the certificate into your system. In Keychain Access, search for the installed certificate, right click it and export the `.p12` file to a location of your choosing. When exporting, you will be prompted to create a password to protect the cert, which makes the `.p12` file unusable without knowing the password.

![](notarizing/p12-export.png)

g. The `.p12` exported from Keychain only contains the last certificate in the chain and none of the intermediate certificates. Unfortunately this makes the certificate not very useful in linux CI, as it does not have access to intermediate certificate chain from Apple. To address this, Quill has a convenience command to recreate the `.p12` with the full chain, so that it can be used in CI:

```
export QUILL_LOG_LEVEL=debug
export QUILL_P12_PASSWORD=<password from previous step>
quill p12 attach-chain --keychain-path /Library/Keychains/System.keychain ./certificates.p12
```

Note that in the above command, `--keychain-path /Library/Keychains/System.keychain` argument was used instead of the default location (`/System/Library/Keychains/SystemRootCertificates.keychain`). This is because in my case, the SystemRoot Keychain did not have the intermediate certificates necessary to include in the chain. See [quill issue #16](https://github.com/anchore/quill/issues/16#issuecomment-1495321856).

The above will generate a new `.p12` file (e.g. `./certificates-with-chain.p12`), whose content will be stored in the CI sytem as a base64 encoded string. e.g.:

```
export QUILL_SIGN_P12=$(base64 -i ./certificates-with-chain.p12)
```

After this, all one-time setup steps have been completed.

### Sign and notarize

After completing the one-time setup instructions, you should have the following variables to supply to quill:

| Quill Env Var | Description | Example |
|---|---|---|
| `QUILL_NOTARY_KEY_ID` | App Store Connect API Key ID | `XMGYH4V855` |
| `QUILL_NOTARY_KEY` | App Store Connect API Key (base64 encoded content of `AuthKey_<KEY-ID>.p8` file) | base64 encoded string of:<br/>```-----BEGIN PRIVATE KEY-----```<br/>```MIGTAgBAMBMGByqGSMxxxxxxxxxxx```<br/>```xxxxxxxxxxxxxxxxxxxxxxxxxxxxx```<br/>```xxxxxxxxxxxxxxxxxxxxxxxxxxxxx```<br/>`-----END PRIVATE KEY-----` |
| `QUILL_NOTARY_ISSUER` | App Store Connect API Key Issuer ID | `bc6e95b5-2605-4eb7-a6cb-e8d42f234ce6` |
| `QUILL_SIGN_PASSWORD` | Password chosen to protect `.p12` file during export | `hunter2` |
| `QUILL_SIGN_P12` | `certificates-with-chain.p12` file contents (base64 encoded) | output of:<br/>`base64 -i ./certificates-with-chain.p12` |

In CI these will be stored as GitHub secrets which are accessed by the release workflow that invokes quill.

To sign and notarize a binary, run the following command:

```
quill sign-and-notarize ./akuity
```

### Troubleshooting

#### Installing additional intermediate certificates

In order to generate a valid `certificates-with-chain.p12` file, you may need to manually install Apple Intermediate Certificates downloaded from their [webpage](https://www.apple.com/certificateauthority/). I believe the important intermediate is `Developer ID - G2`.

#### System vs. SystemRoot Keychain

You may need to use a different `--keychain-path` argument to quill, depending on where the intermediate certificates were installed (System vs. SystemRoot). If they reside in System, then use `--keychain-path Library/Keychains/System.keychain` argument to quill. See [quill issue #16](https://github.com/anchore/quill/issues/16#issuecomment-1495321856)

#### Verify the binary

`codesign` can be used to check if the binary was properly signed:

Good:
```
$ codesign -vvv --deep --strict ./akuity
./akuity: valid on disk
./akuity: satisfies its Designated Requirement
```

Bad:
```
$ codesign -vvv --deep --strict ./akuity
./akuity-asdf: CSSMERR_TP_NOT_TRUSTED
In architecture: arm64
```
