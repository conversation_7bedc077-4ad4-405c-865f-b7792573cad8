# Notification Webhook Schema
<a name="top"></a>




<a name="akuity-notifications-webhook-v1-AuditEvent"></a>

### AuditEvent
`AuditEvent` contains metadata for an audit event.


| Field | Type | Description |
| ----- | ---- | ----------- |
| timestamp | [string](#string) | The time the event was triggered. |
| action | [string](#string) | The action that was performed. |
| actor | [AuditEvent.EventAuditActor](#akuity-notifications-webhook-v1-AuditEvent-EventAuditActor) | The actor that triggered the event. |
| object | [AuditEvent.EventAuditObject](#akuity-notifications-webhook-v1-AuditEvent-EventAuditObject) | The object that was acted upon. |
| details | [AuditEvent.EventAuditDetails](#akuity-notifications-webhook-v1-AuditEvent-EventAuditDetails) | The details of the event. |





<a name="akuity-notifications-webhook-v1-AuditEvent-EventAuditActor"></a>

### AuditEvent.EventAuditActor



| Field | Type | Description |
| ----- | ---- | ----------- |
| type | [string](#string) | The type of actor. |
| id | [string](#string) | The ID of the actor. |
| ip | [string](#string) | The IP address of the actor. |





<a name="akuity-notifications-webhook-v1-AuditEvent-EventAuditDetails"></a>

### AuditEvent.EventAuditDetails



| Field | Type | Description |
| ----- | ---- | ----------- |
| message | [string](#string) | The message associated with the event. |
| patch | [string](#string) | The patch associated with the event. |
| action_type | [string](#string) | The action type. |





<a name="akuity-notifications-webhook-v1-AuditEvent-EventAuditObject"></a>

### AuditEvent.EventAuditObject



| Field | Type | Description |
| ----- | ---- | ----------- |
| type | [string](#string) | The type of object. E.g. "team_member", "team", "custom_role", "kargo_instance", etc. |
| id | [AuditEvent.EventAuditObject.EventAuditObjId](#akuity-notifications-webhook-v1-AuditEvent-EventAuditObject-EventAuditObjId) | The ID of the object. |
| parent_id | [AuditEvent.EventAuditObject.EventAuditParentId](#akuity-notifications-webhook-v1-AuditEvent-EventAuditObject-EventAuditParentId) | The parent ID of the object. |





<a name="akuity-notifications-webhook-v1-AuditEvent-EventAuditObject-EventAuditObjId"></a>

### AuditEvent.EventAuditObject.EventAuditObjId



| Field | Type | Description |
| ----- | ---- | ----------- |
| name | [string](#string) | The name of the object. |
| kind | [string](#string) | The kind of object. |
| group | [string](#string) | The group of the object. |





<a name="akuity-notifications-webhook-v1-AuditEvent-EventAuditObject-EventAuditParentId"></a>

### AuditEvent.EventAuditObject.EventAuditParentId



| Field | Type | Description |
| ----- | ---- | ----------- |
| name | [string](#string) | The name of the parent. |
| parent_name | [string](#string) | The name of the parent object's parent (if present). |
| application_name | [string](#string) | The name of the application. |





<a name="akuity-notifications-webhook-v1-PingEvent"></a>

### PingEvent
`PingEvent` contains metadata for a ping event.


| Field | Type | Description |
| ----- | ---- | ----------- |
| notification_config_id | [string](#string) | NotificationConfigID is the organization notification config ID that triggered the ping |





<a name="akuity-notifications-webhook-v1-UsageUpdateEvent"></a>

### UsageUpdateEvent



| Field | Type | Description |
| ----- | ---- | ----------- |
| product | [string](#string) | The product associated with the usage update. E.g. "ArgoCD", "Kargo", "Akuity Agent". |
| usage_type | [string](#string) | The type of usage. E.g. "applications", "projects". |
| usage_threshold | [double](#double) | The usage threshold. |
| max_limit | [int64](#int64) | The maximum limit. |
| usage | [int64](#int64) | The current usage. |





<a name="akuity-notifications-webhook-v1-WebhookEventPayload"></a>

### WebhookEventPayload
`WebhookEventPayload` is the payload sent to the webhook endpoint.


| Field | Type | Description |
| ----- | ---- | ----------- |
| event_time | [google.protobuf.Timestamp](#google-protobuf-Timestamp) | The time the event was triggered. |
| event_type | [WebhookEventType](#akuity-notifications-webhook-v1-WebhookEventType) | The type of event. |
| organization_id | [string](#string) | The organization ID. |
| event_id | [string](#string) | The event ID. |
| ping_event | [PingEvent](#akuity-notifications-webhook-v1-PingEvent) | Metadata for a ping event. |
| usage_event | [UsageUpdateEvent](#akuity-notifications-webhook-v1-UsageUpdateEvent) | Metadata for a usage update event. |
| audit_event | [AuditEvent](#akuity-notifications-webhook-v1-AuditEvent) | Metadata for an audit event. |




 <!-- end messages -->

