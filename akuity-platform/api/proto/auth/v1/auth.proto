syntax = "proto3";

package akuity.auth.v1;

import "google/api/annotations.proto";

option go_package = "github.com/akuityio/akuity-platform/pkg/api/gen/auth/v1;authv1";

service AuthService {
  rpc GetDeviceCode(GetDeviceCodeRequest) returns (GetDeviceCodeResponse) {
    option (google.api.http) = {get: "/api/v1/auth/device-code"};
  }
  rpc GetDeviceToken(GetDeviceTokenRequest) returns (GetDeviceTokenResponse) {
    option (google.api.http) = {
      post: "/api/v1/auth/device-token"
      body: "*"
    };
  }
  rpc RefreshAccessToken(RefreshAccessTokenRequest) returns (RefreshAccessTokenResponse) {
    option (google.api.http) = {
      post: "/api/v1/auth/refresh-token"
      body: "*"
    };
  }
  rpc GetOIDCProviderDetails(GetOIDCProviderDetailsRequest) returns (GetOIDCProviderDetailsResponse) {
    option (google.api.http) = {get: "/api/v1/oidc/provider-details"};
  }
}

message GetDeviceCodeRequest {
  /* explicitly empty */
}

message GetDeviceCodeResponse {
  string device_code = 1;
  string user_code = 2;
  string verification_uri = 3;
  string verification_uri_complete = 4;
  int32 expires_in_seconds = 5;
  int32 interval_seconds = 6;
}

message GetDeviceTokenRequest {
  string device_code = 1;
}

message GetDeviceTokenResponse {
  string access_token = 1;
}

message RefreshAccessTokenRequest {
  /* explicitly empty */
}

message RefreshAccessTokenResponse {
  /* explicitly empty */
}

message GetOIDCProviderDetailsRequest {
  string discovery_url = 1;
}

message GetOIDCProviderDetailsResponse {
  string issuer = 1;
  string authorization_endpoint = 2;
  string token_endpoint = 3;
  string userinfo_endpoint = 4;
  string jwks_uri = 5;
}
