syntax = "proto3";

package akuity.accesscontrol.v1;

import "protoc-gen-openapiv2/options/annotations.proto";

option go_package = "github.com/akuityio/akuity-platform/pkg/api/gen/accesscontrol/v1;accesscontrolv1";
option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Akuity Platform API"
    version: "1.0"
  }
};

message Permissions {
  repeated string actions = 1;
  repeated string roles = 2;
  repeated string custom_roles = 3;
}
