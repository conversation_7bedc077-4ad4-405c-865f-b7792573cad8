syntax = "proto3";

package akuity.user.v1;

import "google/api/annotations.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "types/events/v1/events.proto";

option go_package = "github.com/akuityio/akuity-platform/pkg/api/gen/types/user/v1;userv1";

service UserService {
  rpc GetUser(GetUserRequest) returns (GetUserResponse) {
    option (google.api.http) = {get: "/api/v1/users/me"};
  }

  rpc UpdateUserUIPreferences(UpdateUserUIPreferencesRequest) returns (UpdateUserUIPreferencesResponse) {
    option (google.api.http) = {
      put: "/api/v1/users/me/ui-preferences"
      body: "*"
    };
  }

  rpc DeleteUser(DeleteUserRequest) returns (DeleteUserResponse) {
    option (google.api.http) = {delete: "/api/v1/users/me"};
  }

  rpc ListNotifications(ListNotificationsRequest) returns (ListNotificationsResponse) {
    option (google.api.http) = {get: "/api/v1/users/me/notifications"};
  }

  rpc WatchNotifications(WatchNotificationsRequest) returns (stream WatchNotificationsResponse) {
    option (google.api.http) = {get: "/api/v1/stream/users/me/notifications"};
  }

  rpc ReadNotifications(ReadNotificationsRequest) returns (ReadNotificationsResponse) {
    option (google.api.http) = {
      post: "/api/v1/users/me/notifications/read"
      body: "*"
    };
  }

  rpc UnreadNotifications(UnreadNotificationsRequest) returns (UnreadNotificationsResponse) {
    option (google.api.http) = {
      post: "/api/v1/users/me/notifications/unread"
      body: "*"
    };
  }

  rpc GetNotificationSettings(GetNotificationSettingsRequest) returns (GetNotificationSettingsResponse) {
    option (google.api.http) = {get: "/api/v1/users/me/notification-settings"};
  }

  rpc UpdateNotificationSettings(UpdateNotificationSettingsRequest) returns (UpdateNotificationSettingsResponse) {
    option (google.api.http) = {
      put: "/api/v1/users/me/notification-settings"
      body: "*"
    };
  }

  rpc ResetPassword(ResetPasswordRequest) returns (ResetPasswordResponse) {
    option (google.api.http) = {
      post: "/api/v1/users/me/reset-password"
      body: "*"
    };
  }
}

message GetUserRequest {
  /* empty */
}

message OrganizationSummary {
  string id = 1;
  string name = 2;
  string role = 3;
  google.protobuf.Timestamp expiration_time = 4;
  bool is_free_trial = 5;
  bool is_sso_inferred = 6;
  string plan = 7;
}

message User {
  string id = 1;
  string email = 2;
  repeated OrganizationSummary organizations = 3;
  repeated OrganizationSummary invitations = 4;
  bool unrestricted = 5;
  UserInfo user_info = 6;
  google.protobuf.Struct ui_preferences = 7;
  bool initial_signup = 8;
}

message GetUserResponse {
  User user = 1;
}

message UserInfo {
  string given_name = 1;
  string family_name = 2;
}

message UpdateUserUIPreferencesRequest {
  google.protobuf.Struct ui_preferences = 1;
}

message UpdateUserUIPreferencesResponse {
  /* empty */
}

message DeleteUserRequest {
  /* empty */
}

message DeleteUserResponse {
  /* empty */
}

message ListNotificationsRequest {
  optional uint32 limit = 1;
  optional uint32 offset = 2;
}

message ListNotificationsResponse {
  repeated Notification notifications = 1;
  uint32 count = 2;
  uint32 unread_count = 3;
}

message WatchNotificationsRequest {
  /* empty */
}

message WatchNotificationsResponse {
  Notification item = 1;
  akuity.types.events.v1.EventType type = 2;
}

enum NotificationCategory {
  NOTIFICATION_CATEGORY_UNSPECIFIED = 0;
  NOTIFICATION_CATEGORY_BILLING = 1;
  NOTIFICATION_CATEGORY_MARKETING = 2;
  NOTIFICATION_CATEGORY_USAGE_ALERTS = 3;
  NOTIFICATION_CATEGORY_PRODUCT_UPDATES = 4;
}

message Notification {
  string id = 1;
  google.protobuf.Timestamp create_time = 2;
  string title = 3;
  NotificationCategory category = 4;
  string template = 5;
  bool is_read = 6;
  google.protobuf.Struct metadata = 7;
}

message ReadNotificationsRequest {
  repeated string notification_ids = 1;
}

message ReadNotificationsResponse {
  /* empty */
}

message UnreadNotificationsRequest {
  repeated string notification_ids = 1;
}

message UnreadNotificationsResponse {
  /* empty */
}

message GetNotificationSettingsRequest {
  /* empty */
}

message GetNotificationSettingsResponse {
  NotificationSettings settings = 1;
}

message UpdateNotificationSettingsRequest {
  NotificationSettings settings = 1;
}

message UpdateNotificationSettingsResponse {
  /* empty */
}

message NotificationSettings {
  WebNotificationConfig web = 1;
  EmailNotificationConfig email = 2;
}

message WebNotificationConfig {
  bool disabled = 1;
  repeated NotificationCategory disabled_categories = 2;
}

message EmailNotificationConfig {
  bool disabled = 1;
  repeated NotificationCategory disabled_categories = 2;
}

message ResetPasswordRequest {
  /* Explicitly empty */
}

message ResetPasswordResponse {
  /* Explicitly empty */
}
