//go:build !ignore_autogenerated

// Code generated by controller-gen. DO NOT EDIT.

package v1alpha1

import (
	"k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AIConfig) DeepCopyInto(out *AIConfig) {
	*out = *in
	if in.Runbooks != nil {
		in, out := &in.Runbooks, &out.Runbooks
		*out = make([]*Runbook, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(Runbook)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.Incidents != nil {
		in, out := &in.Incidents, &out.Incidents
		*out = new(IncidentsConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.ArgocdSlackService != nil {
		in, out := &in.ArgocdSlackService, &out.ArgocdSlackService
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AIConfig.
func (in *AIConfig) DeepCopy() *AIConfig {
	if in == nil {
		return nil
	}
	out := new(AIConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AgentPermissionsRule) DeepCopyInto(out *AgentPermissionsRule) {
	*out = *in
	if in.ApiGroups != nil {
		in, out := &in.ApiGroups, &out.ApiGroups
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Resources != nil {
		in, out := &in.Resources, &out.Resources
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Verbs != nil {
		in, out := &in.Verbs, &out.Verbs
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AgentPermissionsRule.
func (in *AgentPermissionsRule) DeepCopy() *AgentPermissionsRule {
	if in == nil {
		return nil
	}
	out := new(AgentPermissionsRule)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AkuityIntelligence) DeepCopyInto(out *AkuityIntelligence) {
	*out = *in
	if in.AllowedUsernames != nil {
		in, out := &in.AllowedUsernames, &out.AllowedUsernames
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AllowedGroups != nil {
		in, out := &in.AllowedGroups, &out.AllowedGroups
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AkuityIntelligence.
func (in *AkuityIntelligence) DeepCopy() *AkuityIntelligence {
	if in == nil {
		return nil
	}
	out := new(AkuityIntelligence)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AkuityIntelligenceExtension) DeepCopyInto(out *AkuityIntelligenceExtension) {
	*out = *in
	if in.AllowedUsernames != nil {
		in, out := &in.AllowedUsernames, &out.AllowedUsernames
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AllowedGroups != nil {
		in, out := &in.AllowedGroups, &out.AllowedGroups
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AkuityIntelligenceExtension.
func (in *AkuityIntelligenceExtension) DeepCopy() *AkuityIntelligenceExtension {
	if in == nil {
		return nil
	}
	out := new(AkuityIntelligenceExtension)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AppControllerAutoScalingConfig) DeepCopyInto(out *AppControllerAutoScalingConfig) {
	*out = *in
	if in.ResourceMinimum != nil {
		in, out := &in.ResourceMinimum, &out.ResourceMinimum
		*out = new(Resources)
		**out = **in
	}
	if in.ResourceMaximum != nil {
		in, out := &in.ResourceMaximum, &out.ResourceMaximum
		*out = new(Resources)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AppControllerAutoScalingConfig.
func (in *AppControllerAutoScalingConfig) DeepCopy() *AppControllerAutoScalingConfig {
	if in == nil {
		return nil
	}
	out := new(AppControllerAutoScalingConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AppInAnyNamespaceConfig) DeepCopyInto(out *AppInAnyNamespaceConfig) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AppInAnyNamespaceConfig.
func (in *AppInAnyNamespaceConfig) DeepCopy() *AppInAnyNamespaceConfig {
	if in == nil {
		return nil
	}
	out := new(AppInAnyNamespaceConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AppReconciliationsRateLimiting) DeepCopyInto(out *AppReconciliationsRateLimiting) {
	*out = *in
	if in.BucketRateLimiting != nil {
		in, out := &in.BucketRateLimiting, &out.BucketRateLimiting
		*out = new(BucketRateLimiting)
		**out = **in
	}
	if in.ItemRateLimiting != nil {
		in, out := &in.ItemRateLimiting, &out.ItemRateLimiting
		*out = new(ItemRateLimiting)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AppReconciliationsRateLimiting.
func (in *AppReconciliationsRateLimiting) DeepCopy() *AppReconciliationsRateLimiting {
	if in == nil {
		return nil
	}
	out := new(AppReconciliationsRateLimiting)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AppSetDelegate) DeepCopyInto(out *AppSetDelegate) {
	*out = *in
	if in.ManagedCluster != nil {
		in, out := &in.ManagedCluster, &out.ManagedCluster
		*out = new(ManagedCluster)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AppSetDelegate.
func (in *AppSetDelegate) DeepCopy() *AppSetDelegate {
	if in == nil {
		return nil
	}
	out := new(AppSetDelegate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ApplicationSetExtension) DeepCopyInto(out *ApplicationSetExtension) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ApplicationSetExtension.
func (in *ApplicationSetExtension) DeepCopy() *ApplicationSetExtension {
	if in == nil {
		return nil
	}
	out := new(ApplicationSetExtension)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AppsetPlugins) DeepCopyInto(out *AppsetPlugins) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AppsetPlugins.
func (in *AppsetPlugins) DeepCopy() *AppsetPlugins {
	if in == nil {
		return nil
	}
	out := new(AppsetPlugins)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AppsetPolicy) DeepCopyInto(out *AppsetPolicy) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AppsetPolicy.
func (in *AppsetPolicy) DeepCopy() *AppsetPolicy {
	if in == nil {
		return nil
	}
	out := new(AppsetPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ArgoCD) DeepCopyInto(out *ArgoCD) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ArgoCD.
func (in *ArgoCD) DeepCopy() *ArgoCD {
	if in == nil {
		return nil
	}
	out := new(ArgoCD)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ArgoCD) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ArgoCDExtensionInstallEntry) DeepCopyInto(out *ArgoCDExtensionInstallEntry) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ArgoCDExtensionInstallEntry.
func (in *ArgoCDExtensionInstallEntry) DeepCopy() *ArgoCDExtensionInstallEntry {
	if in == nil {
		return nil
	}
	out := new(ArgoCDExtensionInstallEntry)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ArgoCDList) DeepCopyInto(out *ArgoCDList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ArgoCD, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ArgoCDList.
func (in *ArgoCDList) DeepCopy() *ArgoCDList {
	if in == nil {
		return nil
	}
	out := new(ArgoCDList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ArgoCDList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ArgoCDSpec) DeepCopyInto(out *ArgoCDSpec) {
	*out = *in
	in.InstanceSpec.DeepCopyInto(&out.InstanceSpec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ArgoCDSpec.
func (in *ArgoCDSpec) DeepCopy() *ArgoCDSpec {
	if in == nil {
		return nil
	}
	out := new(ArgoCDSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AutoScalerConfig) DeepCopyInto(out *AutoScalerConfig) {
	*out = *in
	if in.ApplicationController != nil {
		in, out := &in.ApplicationController, &out.ApplicationController
		*out = new(AppControllerAutoScalingConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.RepoServer != nil {
		in, out := &in.RepoServer, &out.RepoServer
		*out = new(RepoServerAutoScalingConfig)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AutoScalerConfig.
func (in *AutoScalerConfig) DeepCopy() *AutoScalerConfig {
	if in == nil {
		return nil
	}
	out := new(AutoScalerConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *BucketRateLimiting) DeepCopyInto(out *BucketRateLimiting) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new BucketRateLimiting.
func (in *BucketRateLimiting) DeepCopy() *BucketRateLimiting {
	if in == nil {
		return nil
	}
	out := new(BucketRateLimiting)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Cluster) DeepCopyInto(out *Cluster) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Cluster.
func (in *Cluster) DeepCopy() *Cluster {
	if in == nil {
		return nil
	}
	out := new(Cluster)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *Cluster) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterArgoCDNotificationsSettings) DeepCopyInto(out *ClusterArgoCDNotificationsSettings) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterArgoCDNotificationsSettings.
func (in *ClusterArgoCDNotificationsSettings) DeepCopy() *ClusterArgoCDNotificationsSettings {
	if in == nil {
		return nil
	}
	out := new(ClusterArgoCDNotificationsSettings)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterCompatibility) DeepCopyInto(out *ClusterCompatibility) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterCompatibility.
func (in *ClusterCompatibility) DeepCopy() *ClusterCompatibility {
	if in == nil {
		return nil
	}
	out := new(ClusterCompatibility)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterCustomization) DeepCopyInto(out *ClusterCustomization) {
	*out = *in
	in.Kustomization.DeepCopyInto(&out.Kustomization)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterCustomization.
func (in *ClusterCustomization) DeepCopy() *ClusterCustomization {
	if in == nil {
		return nil
	}
	out := new(ClusterCustomization)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterData) DeepCopyInto(out *ClusterData) {
	*out = *in
	if in.AutoUpgradeDisabled != nil {
		in, out := &in.AutoUpgradeDisabled, &out.AutoUpgradeDisabled
		*out = new(bool)
		**out = **in
	}
	in.Kustomization.DeepCopyInto(&out.Kustomization)
	if in.AppReplication != nil {
		in, out := &in.AppReplication, &out.AppReplication
		*out = new(bool)
		**out = **in
	}
	if in.RedisTunneling != nil {
		in, out := &in.RedisTunneling, &out.RedisTunneling
		*out = new(bool)
		**out = **in
	}
	if in.DirectClusterSpec != nil {
		in, out := &in.DirectClusterSpec, &out.DirectClusterSpec
		*out = new(DirectClusterSpec)
		(*in).DeepCopyInto(*out)
	}
	if in.DatadogAnnotationsEnabled != nil {
		in, out := &in.DatadogAnnotationsEnabled, &out.DatadogAnnotationsEnabled
		*out = new(bool)
		**out = **in
	}
	if in.EksAddonEnabled != nil {
		in, out := &in.EksAddonEnabled, &out.EksAddonEnabled
		*out = new(bool)
		**out = **in
	}
	if in.ManagedClusterConfig != nil {
		in, out := &in.ManagedClusterConfig, &out.ManagedClusterConfig
		*out = new(ManagedClusterConfig)
		**out = **in
	}
	if in.MaintenanceMode != nil {
		in, out := &in.MaintenanceMode, &out.MaintenanceMode
		*out = new(bool)
		**out = **in
	}
	if in.MultiClusterK8SDashboardEnabled != nil {
		in, out := &in.MultiClusterK8SDashboardEnabled, &out.MultiClusterK8SDashboardEnabled
		*out = new(bool)
		**out = **in
	}
	if in.AutoscalerConfig != nil {
		in, out := &in.AutoscalerConfig, &out.AutoscalerConfig
		*out = new(AutoScalerConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.Compatibility != nil {
		in, out := &in.Compatibility, &out.Compatibility
		*out = new(ClusterCompatibility)
		**out = **in
	}
	if in.ArgocdNotificationsSettings != nil {
		in, out := &in.ArgocdNotificationsSettings, &out.ArgocdNotificationsSettings
		*out = new(ClusterArgoCDNotificationsSettings)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterData.
func (in *ClusterData) DeepCopy() *ClusterData {
	if in == nil {
		return nil
	}
	out := new(ClusterData)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterList) DeepCopyInto(out *ClusterList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Cluster, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterList.
func (in *ClusterList) DeepCopy() *ClusterList {
	if in == nil {
		return nil
	}
	out := new(ClusterList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ClusterList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterSecretMapping) DeepCopyInto(out *ClusterSecretMapping) {
	*out = *in
	if in.Clusters != nil {
		in, out := &in.Clusters, &out.Clusters
		*out = new(ObjectSelector)
		(*in).DeepCopyInto(*out)
	}
	if in.Secrets != nil {
		in, out := &in.Secrets, &out.Secrets
		*out = new(ObjectSelector)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterSecretMapping.
func (in *ClusterSecretMapping) DeepCopy() *ClusterSecretMapping {
	if in == nil {
		return nil
	}
	out := new(ClusterSecretMapping)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterSpec) DeepCopyInto(out *ClusterSpec) {
	*out = *in
	in.Data.DeepCopyInto(&out.Data)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterSpec.
func (in *ClusterSpec) DeepCopy() *ClusterSpec {
	if in == nil {
		return nil
	}
	out := new(ClusterSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CrossplaneExtension) DeepCopyInto(out *CrossplaneExtension) {
	*out = *in
	if in.Resources != nil {
		in, out := &in.Resources, &out.Resources
		*out = make([]*CrossplaneExtensionResource, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(CrossplaneExtensionResource)
				**out = **in
			}
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CrossplaneExtension.
func (in *CrossplaneExtension) DeepCopy() *CrossplaneExtension {
	if in == nil {
		return nil
	}
	out := new(CrossplaneExtension)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CrossplaneExtensionResource) DeepCopyInto(out *CrossplaneExtensionResource) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CrossplaneExtensionResource.
func (in *CrossplaneExtensionResource) DeepCopy() *CrossplaneExtensionResource {
	if in == nil {
		return nil
	}
	out := new(CrossplaneExtensionResource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CustomDeprecatedAPI) DeepCopyInto(out *CustomDeprecatedAPI) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CustomDeprecatedAPI.
func (in *CustomDeprecatedAPI) DeepCopy() *CustomDeprecatedAPI {
	if in == nil {
		return nil
	}
	out := new(CustomDeprecatedAPI)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CveScanConfig) DeepCopyInto(out *CveScanConfig) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CveScanConfig.
func (in *CveScanConfig) DeepCopy() *CveScanConfig {
	if in == nil {
		return nil
	}
	out := new(CveScanConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DirectClusterSpec) DeepCopyInto(out *DirectClusterSpec) {
	*out = *in
	if in.KargoInstanceId != nil {
		in, out := &in.KargoInstanceId, &out.KargoInstanceId
		*out = new(string)
		**out = **in
	}
	if in.Server != nil {
		in, out := &in.Server, &out.Server
		*out = new(string)
		**out = **in
	}
	if in.Organization != nil {
		in, out := &in.Organization, &out.Organization
		*out = new(string)
		**out = **in
	}
	if in.Token != nil {
		in, out := &in.Token, &out.Token
		*out = new(string)
		**out = **in
	}
	if in.CaData != nil {
		in, out := &in.CaData, &out.CaData
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DirectClusterSpec.
func (in *DirectClusterSpec) DeepCopy() *DirectClusterSpec {
	if in == nil {
		return nil
	}
	out := new(DirectClusterSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *GarbageCollectorConfig) DeepCopyInto(out *GarbageCollectorConfig) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new GarbageCollectorConfig.
func (in *GarbageCollectorConfig) DeepCopy() *GarbageCollectorConfig {
	if in == nil {
		return nil
	}
	out := new(GarbageCollectorConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HostAliases) DeepCopyInto(out *HostAliases) {
	*out = *in
	if in.Hostnames != nil {
		in, out := &in.Hostnames, &out.Hostnames
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HostAliases.
func (in *HostAliases) DeepCopy() *HostAliases {
	if in == nil {
		return nil
	}
	out := new(HostAliases)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *IPAllowListEntry) DeepCopyInto(out *IPAllowListEntry) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new IPAllowListEntry.
func (in *IPAllowListEntry) DeepCopy() *IPAllowListEntry {
	if in == nil {
		return nil
	}
	out := new(IPAllowListEntry)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ImageUpdaterDelegate) DeepCopyInto(out *ImageUpdaterDelegate) {
	*out = *in
	if in.ManagedCluster != nil {
		in, out := &in.ManagedCluster, &out.ManagedCluster
		*out = new(ManagedCluster)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImageUpdaterDelegate.
func (in *ImageUpdaterDelegate) DeepCopy() *ImageUpdaterDelegate {
	if in == nil {
		return nil
	}
	out := new(ImageUpdaterDelegate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *IncidentWebhookConfig) DeepCopyInto(out *IncidentWebhookConfig) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new IncidentWebhookConfig.
func (in *IncidentWebhookConfig) DeepCopy() *IncidentWebhookConfig {
	if in == nil {
		return nil
	}
	out := new(IncidentWebhookConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *IncidentsConfig) DeepCopyInto(out *IncidentsConfig) {
	*out = *in
	if in.Triggers != nil {
		in, out := &in.Triggers, &out.Triggers
		*out = make([]*TargetSelector, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(TargetSelector)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.Webhooks != nil {
		in, out := &in.Webhooks, &out.Webhooks
		*out = make([]*IncidentWebhookConfig, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(IncidentWebhookConfig)
				**out = **in
			}
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new IncidentsConfig.
func (in *IncidentsConfig) DeepCopy() *IncidentsConfig {
	if in == nil {
		return nil
	}
	out := new(IncidentsConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *InstanceSpec) DeepCopyInto(out *InstanceSpec) {
	*out = *in
	if in.IpAllowList != nil {
		in, out := &in.IpAllowList, &out.IpAllowList
		*out = make([]*IPAllowListEntry, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(IPAllowListEntry)
				**out = **in
			}
		}
	}
	if in.Extensions != nil {
		in, out := &in.Extensions, &out.Extensions
		*out = make([]*ArgoCDExtensionInstallEntry, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(ArgoCDExtensionInstallEntry)
				**out = **in
			}
		}
	}
	if in.ClusterCustomizationDefaults != nil {
		in, out := &in.ClusterCustomizationDefaults, &out.ClusterCustomizationDefaults
		*out = new(ClusterCustomization)
		(*in).DeepCopyInto(*out)
	}
	if in.RepoServerDelegate != nil {
		in, out := &in.RepoServerDelegate, &out.RepoServerDelegate
		*out = new(RepoServerDelegate)
		(*in).DeepCopyInto(*out)
	}
	if in.CrossplaneExtension != nil {
		in, out := &in.CrossplaneExtension, &out.CrossplaneExtension
		*out = new(CrossplaneExtension)
		(*in).DeepCopyInto(*out)
	}
	if in.ImageUpdaterDelegate != nil {
		in, out := &in.ImageUpdaterDelegate, &out.ImageUpdaterDelegate
		*out = new(ImageUpdaterDelegate)
		(*in).DeepCopyInto(*out)
	}
	if in.AppSetDelegate != nil {
		in, out := &in.AppSetDelegate, &out.AppSetDelegate
		*out = new(AppSetDelegate)
		(*in).DeepCopyInto(*out)
	}
	if in.AppsetPolicy != nil {
		in, out := &in.AppsetPolicy, &out.AppsetPolicy
		*out = new(AppsetPolicy)
		**out = **in
	}
	if in.HostAliases != nil {
		in, out := &in.HostAliases, &out.HostAliases
		*out = make([]*HostAliases, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(HostAliases)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.AgentPermissionsRules != nil {
		in, out := &in.AgentPermissionsRules, &out.AgentPermissionsRules
		*out = make([]*AgentPermissionsRule, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(AgentPermissionsRule)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.AkuityIntelligenceExtension != nil {
		in, out := &in.AkuityIntelligenceExtension, &out.AkuityIntelligenceExtension
		*out = new(AkuityIntelligenceExtension)
		(*in).DeepCopyInto(*out)
	}
	if in.CustomDeprecatedApis != nil {
		in, out := &in.CustomDeprecatedApis, &out.CustomDeprecatedApis
		*out = make([]*CustomDeprecatedAPI, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(CustomDeprecatedAPI)
				**out = **in
			}
		}
	}
	if in.KubeVisionConfig != nil {
		in, out := &in.KubeVisionConfig, &out.KubeVisionConfig
		*out = new(KubeVisionConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.AppInAnyNamespaceConfig != nil {
		in, out := &in.AppInAnyNamespaceConfig, &out.AppInAnyNamespaceConfig
		*out = new(AppInAnyNamespaceConfig)
		**out = **in
	}
	if in.Secrets != nil {
		in, out := &in.Secrets, &out.Secrets
		*out = new(SecretsManagementConfig)
		(*in).DeepCopyInto(*out)
	}
	if in.AppsetPlugins != nil {
		in, out := &in.AppsetPlugins, &out.AppsetPlugins
		*out = make([]*AppsetPlugins, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(AppsetPlugins)
				**out = **in
			}
		}
	}
	if in.ApplicationSetExtension != nil {
		in, out := &in.ApplicationSetExtension, &out.ApplicationSetExtension
		*out = new(ApplicationSetExtension)
		**out = **in
	}
	if in.AppReconciliationsRateLimiting != nil {
		in, out := &in.AppReconciliationsRateLimiting, &out.AppReconciliationsRateLimiting
		*out = new(AppReconciliationsRateLimiting)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new InstanceSpec.
func (in *InstanceSpec) DeepCopy() *InstanceSpec {
	if in == nil {
		return nil
	}
	out := new(InstanceSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ItemRateLimiting) DeepCopyInto(out *ItemRateLimiting) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ItemRateLimiting.
func (in *ItemRateLimiting) DeepCopy() *ItemRateLimiting {
	if in == nil {
		return nil
	}
	out := new(ItemRateLimiting)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Kargo) DeepCopyInto(out *Kargo) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Kargo.
func (in *Kargo) DeepCopy() *Kargo {
	if in == nil {
		return nil
	}
	out := new(Kargo)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *Kargo) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KargoAgent) DeepCopyInto(out *KargoAgent) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KargoAgent.
func (in *KargoAgent) DeepCopy() *KargoAgent {
	if in == nil {
		return nil
	}
	out := new(KargoAgent)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KargoAgent) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KargoAgentCustomization) DeepCopyInto(out *KargoAgentCustomization) {
	*out = *in
	in.Kustomization.DeepCopyInto(&out.Kustomization)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KargoAgentCustomization.
func (in *KargoAgentCustomization) DeepCopy() *KargoAgentCustomization {
	if in == nil {
		return nil
	}
	out := new(KargoAgentCustomization)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KargoAgentData) DeepCopyInto(out *KargoAgentData) {
	*out = *in
	if in.AutoUpgradeDisabled != nil {
		in, out := &in.AutoUpgradeDisabled, &out.AutoUpgradeDisabled
		*out = new(bool)
		**out = **in
	}
	in.Kustomization.DeepCopyInto(&out.Kustomization)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KargoAgentData.
func (in *KargoAgentData) DeepCopy() *KargoAgentData {
	if in == nil {
		return nil
	}
	out := new(KargoAgentData)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KargoAgentList) DeepCopyInto(out *KargoAgentList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]KargoAgent, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KargoAgentList.
func (in *KargoAgentList) DeepCopy() *KargoAgentList {
	if in == nil {
		return nil
	}
	out := new(KargoAgentList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KargoAgentList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KargoAgentSpec) DeepCopyInto(out *KargoAgentSpec) {
	*out = *in
	in.Data.DeepCopyInto(&out.Data)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KargoAgentSpec.
func (in *KargoAgentSpec) DeepCopy() *KargoAgentSpec {
	if in == nil {
		return nil
	}
	out := new(KargoAgentSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KargoIPAllowListEntry) DeepCopyInto(out *KargoIPAllowListEntry) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KargoIPAllowListEntry.
func (in *KargoIPAllowListEntry) DeepCopy() *KargoIPAllowListEntry {
	if in == nil {
		return nil
	}
	out := new(KargoIPAllowListEntry)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KargoInstanceSpec) DeepCopyInto(out *KargoInstanceSpec) {
	*out = *in
	if in.IpAllowList != nil {
		in, out := &in.IpAllowList, &out.IpAllowList
		*out = make([]*KargoIPAllowListEntry, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(KargoIPAllowListEntry)
				**out = **in
			}
		}
	}
	if in.AgentCustomizationDefaults != nil {
		in, out := &in.AgentCustomizationDefaults, &out.AgentCustomizationDefaults
		*out = new(KargoAgentCustomization)
		(*in).DeepCopyInto(*out)
	}
	if in.GlobalCredentialsNs != nil {
		in, out := &in.GlobalCredentialsNs, &out.GlobalCredentialsNs
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.GlobalServiceAccountNs != nil {
		in, out := &in.GlobalServiceAccountNs, &out.GlobalServiceAccountNs
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.AkuityIntelligence != nil {
		in, out := &in.AkuityIntelligence, &out.AkuityIntelligence
		*out = new(AkuityIntelligence)
		(*in).DeepCopyInto(*out)
	}
	if in.GcConfig != nil {
		in, out := &in.GcConfig, &out.GcConfig
		*out = new(GarbageCollectorConfig)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KargoInstanceSpec.
func (in *KargoInstanceSpec) DeepCopy() *KargoInstanceSpec {
	if in == nil {
		return nil
	}
	out := new(KargoInstanceSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KargoList) DeepCopyInto(out *KargoList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Kargo, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KargoList.
func (in *KargoList) DeepCopy() *KargoList {
	if in == nil {
		return nil
	}
	out := new(KargoList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *KargoList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KargoOidcConfig) DeepCopyInto(out *KargoOidcConfig) {
	*out = *in
	if in.DexConfigSecret != nil {
		in, out := &in.DexConfigSecret, &out.DexConfigSecret
		*out = make(map[string]Value, len(*in))
		for key, val := range *in {
			(*out)[key] = *val.DeepCopy()
		}
	}
	in.AdminAccount.DeepCopyInto(&out.AdminAccount)
	in.ViewerAccount.DeepCopyInto(&out.ViewerAccount)
	if in.AdditionalScopes != nil {
		in, out := &in.AdditionalScopes, &out.AdditionalScopes
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	in.UserAccount.DeepCopyInto(&out.UserAccount)
	in.ProjectCreatorAccount.DeepCopyInto(&out.ProjectCreatorAccount)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KargoOidcConfig.
func (in *KargoOidcConfig) DeepCopy() *KargoOidcConfig {
	if in == nil {
		return nil
	}
	out := new(KargoOidcConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KargoPredefinedAccountClaimValue) DeepCopyInto(out *KargoPredefinedAccountClaimValue) {
	*out = *in
	if in.Values != nil {
		in, out := &in.Values, &out.Values
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KargoPredefinedAccountClaimValue.
func (in *KargoPredefinedAccountClaimValue) DeepCopy() *KargoPredefinedAccountClaimValue {
	if in == nil {
		return nil
	}
	out := new(KargoPredefinedAccountClaimValue)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KargoPredefinedAccountData) DeepCopyInto(out *KargoPredefinedAccountData) {
	*out = *in
	if in.Claims != nil {
		in, out := &in.Claims, &out.Claims
		*out = make(map[string]KargoPredefinedAccountClaimValue, len(*in))
		for key, val := range *in {
			(*out)[key] = *val.DeepCopy()
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KargoPredefinedAccountData.
func (in *KargoPredefinedAccountData) DeepCopy() *KargoPredefinedAccountData {
	if in == nil {
		return nil
	}
	out := new(KargoPredefinedAccountData)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KargoSpec) DeepCopyInto(out *KargoSpec) {
	*out = *in
	in.KargoInstanceSpec.DeepCopyInto(&out.KargoInstanceSpec)
	if in.OidcConfig != nil {
		in, out := &in.OidcConfig, &out.OidcConfig
		*out = new(KargoOidcConfig)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KargoSpec.
func (in *KargoSpec) DeepCopy() *KargoSpec {
	if in == nil {
		return nil
	}
	out := new(KargoSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KubeVisionConfig) DeepCopyInto(out *KubeVisionConfig) {
	*out = *in
	if in.CveScanConfig != nil {
		in, out := &in.CveScanConfig, &out.CveScanConfig
		*out = new(CveScanConfig)
		**out = **in
	}
	if in.AiConfig != nil {
		in, out := &in.AiConfig, &out.AiConfig
		*out = new(AIConfig)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KubeVisionConfig.
func (in *KubeVisionConfig) DeepCopy() *KubeVisionConfig {
	if in == nil {
		return nil
	}
	out := new(KubeVisionConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LabelSelectorRequirement) DeepCopyInto(out *LabelSelectorRequirement) {
	*out = *in
	if in.Key != nil {
		in, out := &in.Key, &out.Key
		*out = new(string)
		**out = **in
	}
	if in.Operator != nil {
		in, out := &in.Operator, &out.Operator
		*out = new(string)
		**out = **in
	}
	if in.Values != nil {
		in, out := &in.Values, &out.Values
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LabelSelectorRequirement.
func (in *LabelSelectorRequirement) DeepCopy() *LabelSelectorRequirement {
	if in == nil {
		return nil
	}
	out := new(LabelSelectorRequirement)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ManagedCluster) DeepCopyInto(out *ManagedCluster) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ManagedCluster.
func (in *ManagedCluster) DeepCopy() *ManagedCluster {
	if in == nil {
		return nil
	}
	out := new(ManagedCluster)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ManagedClusterConfig) DeepCopyInto(out *ManagedClusterConfig) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ManagedClusterConfig.
func (in *ManagedClusterConfig) DeepCopy() *ManagedClusterConfig {
	if in == nil {
		return nil
	}
	out := new(ManagedClusterConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ObjectSelector) DeepCopyInto(out *ObjectSelector) {
	*out = *in
	if in.MatchLabels != nil {
		in, out := &in.MatchLabels, &out.MatchLabels
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	if in.MatchExpressions != nil {
		in, out := &in.MatchExpressions, &out.MatchExpressions
		*out = make([]*LabelSelectorRequirement, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(LabelSelectorRequirement)
				(*in).DeepCopyInto(*out)
			}
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ObjectSelector.
func (in *ObjectSelector) DeepCopy() *ObjectSelector {
	if in == nil {
		return nil
	}
	out := new(ObjectSelector)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RepoServerAutoScalingConfig) DeepCopyInto(out *RepoServerAutoScalingConfig) {
	*out = *in
	if in.ResourceMinimum != nil {
		in, out := &in.ResourceMinimum, &out.ResourceMinimum
		*out = new(Resources)
		**out = **in
	}
	if in.ResourceMaximum != nil {
		in, out := &in.ResourceMaximum, &out.ResourceMaximum
		*out = new(Resources)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RepoServerAutoScalingConfig.
func (in *RepoServerAutoScalingConfig) DeepCopy() *RepoServerAutoScalingConfig {
	if in == nil {
		return nil
	}
	out := new(RepoServerAutoScalingConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RepoServerDelegate) DeepCopyInto(out *RepoServerDelegate) {
	*out = *in
	if in.ManagedCluster != nil {
		in, out := &in.ManagedCluster, &out.ManagedCluster
		*out = new(ManagedCluster)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RepoServerDelegate.
func (in *RepoServerDelegate) DeepCopy() *RepoServerDelegate {
	if in == nil {
		return nil
	}
	out := new(RepoServerDelegate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Resources) DeepCopyInto(out *Resources) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Resources.
func (in *Resources) DeepCopy() *Resources {
	if in == nil {
		return nil
	}
	out := new(Resources)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Runbook) DeepCopyInto(out *Runbook) {
	*out = *in
	if in.AppliedTo != nil {
		in, out := &in.AppliedTo, &out.AppliedTo
		*out = new(TargetSelector)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Runbook.
func (in *Runbook) DeepCopy() *Runbook {
	if in == nil {
		return nil
	}
	out := new(Runbook)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SecretsManagementConfig) DeepCopyInto(out *SecretsManagementConfig) {
	*out = *in
	if in.Sources != nil {
		in, out := &in.Sources, &out.Sources
		*out = make([]*ClusterSecretMapping, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(ClusterSecretMapping)
				(*in).DeepCopyInto(*out)
			}
		}
	}
	if in.Destinations != nil {
		in, out := &in.Destinations, &out.Destinations
		*out = make([]*ClusterSecretMapping, len(*in))
		for i := range *in {
			if (*in)[i] != nil {
				in, out := &(*in)[i], &(*out)[i]
				*out = new(ClusterSecretMapping)
				(*in).DeepCopyInto(*out)
			}
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SecretsManagementConfig.
func (in *SecretsManagementConfig) DeepCopy() *SecretsManagementConfig {
	if in == nil {
		return nil
	}
	out := new(SecretsManagementConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *TargetSelector) DeepCopyInto(out *TargetSelector) {
	*out = *in
	if in.ArgocdApplications != nil {
		in, out := &in.ArgocdApplications, &out.ArgocdApplications
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.K8SNamespaces != nil {
		in, out := &in.K8SNamespaces, &out.K8SNamespaces
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Clusters != nil {
		in, out := &in.Clusters, &out.Clusters
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.DegradedFor != nil {
		in, out := &in.DegradedFor, &out.DegradedFor
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new TargetSelector.
func (in *TargetSelector) DeepCopy() *TargetSelector {
	if in == nil {
		return nil
	}
	out := new(TargetSelector)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Value) DeepCopyInto(out *Value) {
	*out = *in
	if in.Value != nil {
		in, out := &in.Value, &out.Value
		*out = new(string)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Value.
func (in *Value) DeepCopy() *Value {
	if in == nil {
		return nil
	}
	out := new(Value)
	in.DeepCopyInto(out)
	return out
}
