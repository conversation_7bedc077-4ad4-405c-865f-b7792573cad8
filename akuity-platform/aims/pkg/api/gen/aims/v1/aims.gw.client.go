// Code generated by protoc-gen-grpc-gateway-client. DO NOT EDIT.
// source: aims/v1/aims.proto

package aimsv1

import (
	context "context"
	fmt "fmt"
	gateway "github.com/akuity/grpc-gateway-client/pkg/grpc/gateway"
	v1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	httpbody "google.golang.org/genproto/googleapis/api/httpbody"
	url "net/url"
)

// AimsServiceGatewayClient is the interface for AimsService service client.
type AimsServiceGatewayClient interface {
	GetInstanceById(context.Context, *GetInstanceByIdRequest) (*GetInstanceByIdResponse, error)
	GetKargoInstanceById(context.Context, *GetKargoInstanceByIdRequest) (*GetKargoInstanceByIdResponse, error)
	GetInternalAuditLogs(context.Context, *GetInternalAuditLogsRequest) (*GetInternalAuditLogsResponse, error)
	ListArgoInstances(context.Context, *ListArgoInstancesRequest) (*ListArgoInstancesResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	DeleteUnpaidInstance(context.Context, *DeleteUnpaidInstanceRequest) (*DeleteUnpaidInstanceResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	// buf:lint:ignore RPC_REQUEST_STANDARD_NAME
	DeleteUnpaidKargoInstance(context.Context, *DeleteUnpaidInstanceRequest) (*DeleteUnpaidInstanceResponse, error)
	ListKargoInstances(context.Context, *ListKargoInstancesRequest) (*ListKargoInstancesResponse, error)
	OnboardManualCustomer(context.Context, *OnboardManualCustomerRequest) (*OnboardManualCustomerResponse, error)
	GetOrganization(context.Context, *GetOrganizationRequest) (*GetOrganizationResponse, error)
	GetFeatureGates(context.Context, *GetFeatureGatesRequest) (*GetFeatureGatesResponse, error)
	PatchFeatureGates(context.Context, *PatchFeatureGatesRequest) (*PatchFeatureGatesResponse, error)
	InstanceClusterMaintenance(context.Context, *InstanceClusterMaintenanceRequest) (*InstanceClusterMaintenanceResponse, error)
	UpdateQuotas(context.Context, *UpdateQuotasRequest) (*UpdateQuotasResponse, error)
	ListUnbilledOrganizations(context.Context, *ListUnbilledOrganizationsRequest) (*ListUnbilledOrganizationsResponse, error)
	ListAllOrganizations(context.Context, *ListAllOrganizationsRequest) (*ListAllOrganizationsResponse, error)
	ListOrganizationMembers(context.Context, *ListOrganizationMembersRequest) (*ListOrganizationMembersResponse, error)
	UpdateOrganizationTrialExpiration(context.Context, *UpdateOrganizationTrialExpirationRequest) (*UpdateOrganizationTrialExpirationResponse, error)
	DecrementInstanceGeneration(context.Context, *DecrementInstanceGenerationRequest) (*DecrementInstanceGenerationResponse, error)
	ListClustersForInstance(context.Context, *ListClustersForInstanceRequest) (*ListClustersForInstanceResponse, error)
	ListAgentsForKargoInstance(context.Context, *ListAgentsForKargoInstanceRequest) (*ListAgentsForKargoInstanceResponse, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	GetClusterManifests(context.Context, *GetClusterManifestsRequest) (<-chan *httpbody.HttpBody, <-chan error, error)
	// buf:lint:ignore RPC_REQUEST_RESPONSE_UNIQUE
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	GetKargoAgentManifests(context.Context, *GetKargoAgentManifestsRequest) (<-chan *httpbody.HttpBody, <-chan error, error)
	SetManuallyVerified(context.Context, *SetManuallyVerifiedRequest) (*SetManuallyVerifiedResponse, error)
	SetDisabledInstanceCreation(context.Context, *SetDisabledInstanceCreationRequest) (*SetDisabledInstanceCreationResponse, error)
	DeleteOrganization(context.Context, *DeleteOrganizationRequest) (*DeleteOrganizationResponse, error)
	GetInternalConfig(context.Context, *GetInternalConfigRequest) (*GetInternalConfigResponse, error)
	SendNotification(context.Context, *SendNotificationRequest) (*SendNotificationResponse, error)
	ListAuditLogs(context.Context, *ListAuditLogsRequest) (*ListAuditLogsResponse, error)
	ListAvailablePlans(context.Context, *ListAvailablePlansRequest) (*ListAvailablePlansResponse, error)
	UpdateOrganizationBillingPlan(context.Context, *UpdateOrganizationBillingPlanRequest) (*UpdateOrganizationBillingPlanResponse, error)
	GetKubeVisionUsage(context.Context, *GetKubeVisionUsageRequest) (*GetKubeVisionUsageResponse, error)
	ListOrganizationDomains(context.Context, *ListOrganizationDomainsRequest) (*ListOrganizationDomainsResponse, error)
	UpdateOrganizationDomains(context.Context, *UpdateOrganizationDomainsRequest) (*UpdateOrganizationDomainsResponse, error)
	ResetMFA(context.Context, *ResetMFARequest) (*ResetMFAResponse, error)
	ListTeams(context.Context, *ListTeamsRequest) (*ListTeamsResponse, error)
	ListTeamMembers(context.Context, *ListTeamMembersRequest) (*ListTeamMembersResponse, error)
	ListWorkspaces(context.Context, *ListWorkspacesRequest) (*ListWorkspacesResponse, error)
	GetWorkspace(context.Context, *GetWorkspaceRequest) (*GetWorkspaceResponse, error)
	ListWorkspaceMembers(context.Context, *ListWorkspaceMembersRequest) (*ListWorkspaceMembersResponse, error)
	ListWorkspaceCustomRoles(context.Context, *ListWorkspaceCustomRolesRequest) (*ListWorkspaceCustomRolesResponse, error)
	ListOrganizationCustomRoles(context.Context, *ListOrganizationCustomRolesRequest) (*ListOrganizationCustomRolesResponse, error)
	ListOrganizationUsers(context.Context, *ListOrganizationUsersRequest) (*ListOrganizationUsersResponse, error)
	ListAIConversations(context.Context, *v1.ListAIConversationsRequest) (*v1.ListAIConversationsResponse, error)
	GetAIConversation(context.Context, *v1.GetAIConversationRequest) (*v1.GetAIConversationResponse, error)
	UpdateAnnouncementBanner(context.Context, *UpdateAnnouncementBannerRequest) (*UpdateAnnouncementBannerResponse, error)
	GetAnnouncementBanner(context.Context, *GetAnnouncementBannerRequest) (*GetAnnouncementBannerResponse, error)
}

func NewAimsServiceGatewayClient(c gateway.Client) AimsServiceGatewayClient {
	return &aimsServiceGatewayClient{
		gwc: c,
	}
}

type aimsServiceGatewayClient struct {
	gwc gateway.Client
}

func (c *aimsServiceGatewayClient) GetInstanceById(ctx context.Context, req *GetInstanceByIdRequest) (*GetInstanceByIdResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/instances/{instance_id}")
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	return gateway.DoRequest[GetInstanceByIdResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) GetKargoInstanceById(ctx context.Context, req *GetKargoInstanceByIdRequest) (*GetKargoInstanceByIdResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/kargo/instances/{instance_id}")
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	return gateway.DoRequest[GetKargoInstanceByIdResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) GetInternalAuditLogs(ctx context.Context, req *GetInternalAuditLogsRequest) (*GetInternalAuditLogsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/internal-audit-logs")
	q := url.Values{}
	for _, v := range req.Filters.ActorId {
		q.Add("filters.actorId", fmt.Sprintf("%v", v))
	}
	if req.Filters.ObjectType != nil {
		q.Add("filters.objectType", fmt.Sprintf("%v", *req.Filters.ObjectType))
	}
	for _, v := range req.Filters.Action {
		q.Add("filters.action", fmt.Sprintf("%v", v))
	}
	if req.Filters.StartTime != nil {
		q.Add("filters.startTime", fmt.Sprintf("%v", *req.Filters.StartTime))
	}
	if req.Filters.EndTime != nil {
		q.Add("filters.endTime", fmt.Sprintf("%v", *req.Filters.EndTime))
	}
	if req.Filters.Limit != nil {
		q.Add("filters.limit", fmt.Sprintf("%v", *req.Filters.Limit))
	}
	if req.Filters.Offset != nil {
		q.Add("filters.offset", fmt.Sprintf("%v", *req.Filters.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetInternalAuditLogsResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListArgoInstances(ctx context.Context, req *ListArgoInstancesRequest) (*ListArgoInstancesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/argo/instances")
	q := url.Values{}
	if req.Filter != nil {
		if req.Filter.Paid != nil {
			q.Add("filter.paid", fmt.Sprintf("%v", *req.Filter.Paid))
		}
		if req.Filter.Unpaid != nil {
			q.Add("filter.unpaid", fmt.Sprintf("%v", *req.Filter.Unpaid))
		}
		q.Add("filter.fuzz", fmt.Sprintf("%v", req.Filter.Fuzz))
		if req.Filter.TimeFrom != nil {
			q.Add("filter.timeFrom", fmt.Sprintf("%v", *req.Filter.TimeFrom))
		}
		if req.Filter.OrganizationId != nil {
			q.Add("filter.organizationId", fmt.Sprintf("%v", *req.Filter.OrganizationId))
		}
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListArgoInstancesResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) DeleteUnpaidInstance(ctx context.Context, req *DeleteUnpaidInstanceRequest) (*DeleteUnpaidInstanceResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/aims/instances/{instance_id}")
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteUnpaidInstanceResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) DeleteUnpaidKargoInstance(ctx context.Context, req *DeleteUnpaidInstanceRequest) (*DeleteUnpaidInstanceResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/aims/kargo/instances/{instance_id}")
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteUnpaidInstanceResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListKargoInstances(ctx context.Context, req *ListKargoInstancesRequest) (*ListKargoInstancesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/kargo/instances")
	q := url.Values{}
	if req.Filter != nil {
		if req.Filter.Paid != nil {
			q.Add("filter.paid", fmt.Sprintf("%v", *req.Filter.Paid))
		}
		if req.Filter.Unpaid != nil {
			q.Add("filter.unpaid", fmt.Sprintf("%v", *req.Filter.Unpaid))
		}
		q.Add("filter.fuzz", fmt.Sprintf("%v", req.Filter.Fuzz))
		if req.Filter.TimeFrom != nil {
			q.Add("filter.timeFrom", fmt.Sprintf("%v", *req.Filter.TimeFrom))
		}
		if req.Filter.OrganizationId != nil {
			q.Add("filter.organizationId", fmt.Sprintf("%v", *req.Filter.OrganizationId))
		}
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListKargoInstancesResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) OnboardManualCustomer(ctx context.Context, req *OnboardManualCustomerRequest) (*OnboardManualCustomerResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/aims/onboard")
	gwReq.SetBody(req)
	return gateway.DoRequest[OnboardManualCustomerResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) GetOrganization(ctx context.Context, req *GetOrganizationRequest) (*GetOrganizationResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations/{organization_id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	return gateway.DoRequest[GetOrganizationResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) GetFeatureGates(ctx context.Context, req *GetFeatureGatesRequest) (*GetFeatureGatesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations/{id}/feature-gates")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	return gateway.DoRequest[GetFeatureGatesResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) PatchFeatureGates(ctx context.Context, req *PatchFeatureGatesRequest) (*PatchFeatureGatesResponse, error) {
	gwReq := c.gwc.NewRequest("PATCH", "/api/v1/aims/organizations/{id}/feature-gates")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[PatchFeatureGatesResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) InstanceClusterMaintenance(ctx context.Context, req *InstanceClusterMaintenanceRequest) (*InstanceClusterMaintenanceResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/aims/instances/{instance_id}/clusters/{cluster_id}")
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("cluster_id", fmt.Sprintf("%v", req.ClusterId))
	gwReq.SetBody(req)
	return gateway.DoRequest[InstanceClusterMaintenanceResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) UpdateQuotas(ctx context.Context, req *UpdateQuotasRequest) (*UpdateQuotasResponse, error) {
	gwReq := c.gwc.NewRequest("PUT", "/api/v1/aims/organizations/{id}/quotas")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateQuotasResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListUnbilledOrganizations(ctx context.Context, req *ListUnbilledOrganizationsRequest) (*ListUnbilledOrganizationsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations/unbilled")
	return gateway.DoRequest[ListUnbilledOrganizationsResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListAllOrganizations(ctx context.Context, req *ListAllOrganizationsRequest) (*ListAllOrganizationsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations")
	q := url.Values{}
	if req.Filters != nil {
		if req.Filters.Limit != nil {
			q.Add("filters.limit", fmt.Sprintf("%v", *req.Filters.Limit))
		}
		if req.Filters.Offset != nil {
			q.Add("filters.offset", fmt.Sprintf("%v", *req.Filters.Offset))
		}
		if req.Filters.Fuzz != nil {
			q.Add("filters.fuzz", fmt.Sprintf("%v", *req.Filters.Fuzz))
		}
		if req.Filters.SortByCreation != nil {
			q.Add("filters.sortByCreation", req.Filters.SortByCreation.String())
		}
		if req.Filters.Billed != nil {
			q.Add("filters.billed", fmt.Sprintf("%v", *req.Filters.Billed))
		}
		if req.Filters.ManuallyVerified != nil {
			q.Add("filters.manuallyVerified", fmt.Sprintf("%v", *req.Filters.ManuallyVerified))
		}
		for _, v := range req.Filters.Plans {
			q.Add("filters.plans", fmt.Sprintf("%v", v))
		}
		if req.Filters.StartTime != nil {
			q.Add("filters.startTime", fmt.Sprintf("%v", *req.Filters.StartTime))
		}
		if req.Filters.EndTime != nil {
			q.Add("filters.endTime", fmt.Sprintf("%v", *req.Filters.EndTime))
		}
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListAllOrganizationsResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListOrganizationMembers(ctx context.Context, req *ListOrganizationMembersRequest) (*ListOrganizationMembersResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations/members")
	q := url.Values{}
	for _, v := range req.OrganizationId {
		q.Add("organizationId", fmt.Sprintf("%v", v))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListOrganizationMembersResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) UpdateOrganizationTrialExpiration(ctx context.Context, req *UpdateOrganizationTrialExpirationRequest) (*UpdateOrganizationTrialExpirationResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/aims/organizations/{organization_id}/trial")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateOrganizationTrialExpirationResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) DecrementInstanceGeneration(ctx context.Context, req *DecrementInstanceGenerationRequest) (*DecrementInstanceGenerationResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/aims/instances/{instance_id}/generation/decrement")
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetBody(req)
	return gateway.DoRequest[DecrementInstanceGenerationResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListClustersForInstance(ctx context.Context, req *ListClustersForInstanceRequest) (*ListClustersForInstanceResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/instances/{instance_id}/clusters")
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	q := url.Values{}
	q.Add("filter.fuzz", fmt.Sprintf("%v", req.Filter.Fuzz))
	if req.Filter.TimeFrom != nil {
		q.Add("filter.timeFrom", fmt.Sprintf("%v", *req.Filter.TimeFrom))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListClustersForInstanceResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListAgentsForKargoInstance(ctx context.Context, req *ListAgentsForKargoInstanceRequest) (*ListAgentsForKargoInstanceResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/kargo/instances/{instance_id}/agents")
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	return gateway.DoRequest[ListAgentsForKargoInstanceResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) GetClusterManifests(ctx context.Context, req *GetClusterManifestsRequest) (<-chan *httpbody.HttpBody, <-chan error, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/instances/{instance_id}/clusters/{cluster_id}/manifests")
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("cluster_id", fmt.Sprintf("%v", req.ClusterId))
	q := url.Values{}
	q.Add("audit.actor", fmt.Sprintf("%v", req.Audit.Actor))
	q.Add("audit.reason", fmt.Sprintf("%v", req.Audit.Reason))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoStreamingRequest[httpbody.HttpBody](ctx, c.gwc, gwReq)
}

func (c *aimsServiceGatewayClient) GetKargoAgentManifests(ctx context.Context, req *GetKargoAgentManifestsRequest) (<-chan *httpbody.HttpBody, <-chan error, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/kargo/instances/{instance_id}/agents/{agent_id}/manifests")
	gwReq.SetPathParam("instance_id", fmt.Sprintf("%v", req.InstanceId))
	gwReq.SetPathParam("agent_id", fmt.Sprintf("%v", req.AgentId))
	q := url.Values{}
	q.Add("audit.actor", fmt.Sprintf("%v", req.Audit.Actor))
	q.Add("audit.reason", fmt.Sprintf("%v", req.Audit.Reason))
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoStreamingRequest[httpbody.HttpBody](ctx, c.gwc, gwReq)
}

func (c *aimsServiceGatewayClient) SetManuallyVerified(ctx context.Context, req *SetManuallyVerifiedRequest) (*SetManuallyVerifiedResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/aims/organizations/{organization_id}/verified")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[SetManuallyVerifiedResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) SetDisabledInstanceCreation(ctx context.Context, req *SetDisabledInstanceCreationRequest) (*SetDisabledInstanceCreationResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/aims/organizations/instance_creation")
	gwReq.SetBody(req)
	return gateway.DoRequest[SetDisabledInstanceCreationResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) DeleteOrganization(ctx context.Context, req *DeleteOrganizationRequest) (*DeleteOrganizationResponse, error) {
	gwReq := c.gwc.NewRequest("DELETE", "/api/v1/aims/organizations/{organization_id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[DeleteOrganizationResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) GetInternalConfig(ctx context.Context, req *GetInternalConfigRequest) (*GetInternalConfigResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/internal/config")
	return gateway.DoRequest[GetInternalConfigResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) SendNotification(ctx context.Context, req *SendNotificationRequest) (*SendNotificationResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/aims/notification")
	gwReq.SetBody(req)
	return gateway.DoRequest[SendNotificationResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListAuditLogs(ctx context.Context, req *ListAuditLogsRequest) (*ListAuditLogsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations/{organization_id}/audit-logs")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.Filters != nil {
		for _, v := range req.Filters.ActorId {
			q.Add("filters.actorId", fmt.Sprintf("%v", v))
		}
		if req.Filters.K8SResource != nil {
			for _, v := range req.Filters.K8SResource.ObjectName {
				q.Add("filters.k8sResource.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.K8SResource.ObjectKind {
				q.Add("filters.k8sResource.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.K8SResource.ObjectGroup {
				q.Add("filters.k8sResource.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.K8SResource.ObjectParentName {
				q.Add("filters.k8sResource.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.K8SResource.ObjectParentParentName {
				q.Add("filters.k8sResource.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.K8SResource.ObjectParentApplicationName {
				q.Add("filters.k8sResource.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.K8SResource.Enabled != nil {
				q.Add("filters.k8sResource.enabled", fmt.Sprintf("%v", *req.Filters.K8SResource.Enabled))
			}
			for _, v := range req.Filters.K8SResource.ObjectParentKargoProjectName {
				q.Add("filters.k8sResource.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		if req.Filters.ArgocdApplication != nil {
			for _, v := range req.Filters.ArgocdApplication.ObjectName {
				q.Add("filters.argocdApplication.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdApplication.ObjectKind {
				q.Add("filters.argocdApplication.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdApplication.ObjectGroup {
				q.Add("filters.argocdApplication.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdApplication.ObjectParentName {
				q.Add("filters.argocdApplication.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdApplication.ObjectParentParentName {
				q.Add("filters.argocdApplication.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdApplication.ObjectParentApplicationName {
				q.Add("filters.argocdApplication.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.ArgocdApplication.Enabled != nil {
				q.Add("filters.argocdApplication.enabled", fmt.Sprintf("%v", *req.Filters.ArgocdApplication.Enabled))
			}
			for _, v := range req.Filters.ArgocdApplication.ObjectParentKargoProjectName {
				q.Add("filters.argocdApplication.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		if req.Filters.ArgocdCluster != nil {
			for _, v := range req.Filters.ArgocdCluster.ObjectName {
				q.Add("filters.argocdCluster.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdCluster.ObjectKind {
				q.Add("filters.argocdCluster.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdCluster.ObjectGroup {
				q.Add("filters.argocdCluster.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdCluster.ObjectParentName {
				q.Add("filters.argocdCluster.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdCluster.ObjectParentParentName {
				q.Add("filters.argocdCluster.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdCluster.ObjectParentApplicationName {
				q.Add("filters.argocdCluster.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.ArgocdCluster.Enabled != nil {
				q.Add("filters.argocdCluster.enabled", fmt.Sprintf("%v", *req.Filters.ArgocdCluster.Enabled))
			}
			for _, v := range req.Filters.ArgocdCluster.ObjectParentKargoProjectName {
				q.Add("filters.argocdCluster.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		if req.Filters.ArgocdInstance != nil {
			for _, v := range req.Filters.ArgocdInstance.ObjectName {
				q.Add("filters.argocdInstance.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdInstance.ObjectKind {
				q.Add("filters.argocdInstance.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdInstance.ObjectGroup {
				q.Add("filters.argocdInstance.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdInstance.ObjectParentName {
				q.Add("filters.argocdInstance.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdInstance.ObjectParentParentName {
				q.Add("filters.argocdInstance.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdInstance.ObjectParentApplicationName {
				q.Add("filters.argocdInstance.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.ArgocdInstance.Enabled != nil {
				q.Add("filters.argocdInstance.enabled", fmt.Sprintf("%v", *req.Filters.ArgocdInstance.Enabled))
			}
			for _, v := range req.Filters.ArgocdInstance.ObjectParentKargoProjectName {
				q.Add("filters.argocdInstance.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		if req.Filters.ArgocdProject != nil {
			for _, v := range req.Filters.ArgocdProject.ObjectName {
				q.Add("filters.argocdProject.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdProject.ObjectKind {
				q.Add("filters.argocdProject.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdProject.ObjectGroup {
				q.Add("filters.argocdProject.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdProject.ObjectParentName {
				q.Add("filters.argocdProject.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdProject.ObjectParentParentName {
				q.Add("filters.argocdProject.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ArgocdProject.ObjectParentApplicationName {
				q.Add("filters.argocdProject.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.ArgocdProject.Enabled != nil {
				q.Add("filters.argocdProject.enabled", fmt.Sprintf("%v", *req.Filters.ArgocdProject.Enabled))
			}
			for _, v := range req.Filters.ArgocdProject.ObjectParentKargoProjectName {
				q.Add("filters.argocdProject.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		if req.Filters.Member != nil {
			for _, v := range req.Filters.Member.ObjectName {
				q.Add("filters.member.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.Member.ObjectKind {
				q.Add("filters.member.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.Member.ObjectGroup {
				q.Add("filters.member.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.Member.ObjectParentName {
				q.Add("filters.member.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.Member.ObjectParentParentName {
				q.Add("filters.member.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.Member.ObjectParentApplicationName {
				q.Add("filters.member.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.Member.Enabled != nil {
				q.Add("filters.member.enabled", fmt.Sprintf("%v", *req.Filters.Member.Enabled))
			}
			for _, v := range req.Filters.Member.ObjectParentKargoProjectName {
				q.Add("filters.member.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		if req.Filters.OrganizationInvite != nil {
			for _, v := range req.Filters.OrganizationInvite.ObjectName {
				q.Add("filters.organizationInvite.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.OrganizationInvite.ObjectKind {
				q.Add("filters.organizationInvite.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.OrganizationInvite.ObjectGroup {
				q.Add("filters.organizationInvite.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.OrganizationInvite.ObjectParentName {
				q.Add("filters.organizationInvite.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.OrganizationInvite.ObjectParentParentName {
				q.Add("filters.organizationInvite.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.OrganizationInvite.ObjectParentApplicationName {
				q.Add("filters.organizationInvite.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.OrganizationInvite.Enabled != nil {
				q.Add("filters.organizationInvite.enabled", fmt.Sprintf("%v", *req.Filters.OrganizationInvite.Enabled))
			}
			for _, v := range req.Filters.OrganizationInvite.ObjectParentKargoProjectName {
				q.Add("filters.organizationInvite.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		for _, v := range req.Filters.Action {
			q.Add("filters.action", fmt.Sprintf("%v", v))
		}
		for _, v := range req.Filters.ActorType {
			q.Add("filters.actorType", fmt.Sprintf("%v", v))
		}
		if req.Filters.StartTime != nil {
			q.Add("filters.startTime", fmt.Sprintf("%v", *req.Filters.StartTime))
		}
		if req.Filters.EndTime != nil {
			q.Add("filters.endTime", fmt.Sprintf("%v", *req.Filters.EndTime))
		}
		if req.Filters.Limit != nil {
			q.Add("filters.limit", fmt.Sprintf("%v", *req.Filters.Limit))
		}
		if req.Filters.Offset != nil {
			q.Add("filters.offset", fmt.Sprintf("%v", *req.Filters.Offset))
		}
		if req.Filters.KargoInstance != nil {
			for _, v := range req.Filters.KargoInstance.ObjectName {
				q.Add("filters.kargoInstance.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoInstance.ObjectKind {
				q.Add("filters.kargoInstance.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoInstance.ObjectGroup {
				q.Add("filters.kargoInstance.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoInstance.ObjectParentName {
				q.Add("filters.kargoInstance.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoInstance.ObjectParentParentName {
				q.Add("filters.kargoInstance.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoInstance.ObjectParentApplicationName {
				q.Add("filters.kargoInstance.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.KargoInstance.Enabled != nil {
				q.Add("filters.kargoInstance.enabled", fmt.Sprintf("%v", *req.Filters.KargoInstance.Enabled))
			}
			for _, v := range req.Filters.KargoInstance.ObjectParentKargoProjectName {
				q.Add("filters.kargoInstance.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		if req.Filters.KargoAgent != nil {
			for _, v := range req.Filters.KargoAgent.ObjectName {
				q.Add("filters.kargoAgent.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoAgent.ObjectKind {
				q.Add("filters.kargoAgent.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoAgent.ObjectGroup {
				q.Add("filters.kargoAgent.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoAgent.ObjectParentName {
				q.Add("filters.kargoAgent.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoAgent.ObjectParentParentName {
				q.Add("filters.kargoAgent.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoAgent.ObjectParentApplicationName {
				q.Add("filters.kargoAgent.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.KargoAgent.Enabled != nil {
				q.Add("filters.kargoAgent.enabled", fmt.Sprintf("%v", *req.Filters.KargoAgent.Enabled))
			}
			for _, v := range req.Filters.KargoAgent.ObjectParentKargoProjectName {
				q.Add("filters.kargoAgent.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		if req.Filters.KargoPromotion != nil {
			for _, v := range req.Filters.KargoPromotion.ObjectName {
				q.Add("filters.kargoPromotion.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoPromotion.ObjectKind {
				q.Add("filters.kargoPromotion.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoPromotion.ObjectGroup {
				q.Add("filters.kargoPromotion.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoPromotion.ObjectParentName {
				q.Add("filters.kargoPromotion.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoPromotion.ObjectParentParentName {
				q.Add("filters.kargoPromotion.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoPromotion.ObjectParentApplicationName {
				q.Add("filters.kargoPromotion.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.KargoPromotion.Enabled != nil {
				q.Add("filters.kargoPromotion.enabled", fmt.Sprintf("%v", *req.Filters.KargoPromotion.Enabled))
			}
			for _, v := range req.Filters.KargoPromotion.ObjectParentKargoProjectName {
				q.Add("filters.kargoPromotion.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		if req.Filters.KargoFreight != nil {
			for _, v := range req.Filters.KargoFreight.ObjectName {
				q.Add("filters.kargoFreight.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoFreight.ObjectKind {
				q.Add("filters.kargoFreight.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoFreight.ObjectGroup {
				q.Add("filters.kargoFreight.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoFreight.ObjectParentName {
				q.Add("filters.kargoFreight.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoFreight.ObjectParentParentName {
				q.Add("filters.kargoFreight.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.KargoFreight.ObjectParentApplicationName {
				q.Add("filters.kargoFreight.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.KargoFreight.Enabled != nil {
				q.Add("filters.kargoFreight.enabled", fmt.Sprintf("%v", *req.Filters.KargoFreight.Enabled))
			}
			for _, v := range req.Filters.KargoFreight.ObjectParentKargoProjectName {
				q.Add("filters.kargoFreight.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		if req.Filters.CustomRoles != nil {
			for _, v := range req.Filters.CustomRoles.ObjectName {
				q.Add("filters.customRoles.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.CustomRoles.ObjectKind {
				q.Add("filters.customRoles.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.CustomRoles.ObjectGroup {
				q.Add("filters.customRoles.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.CustomRoles.ObjectParentName {
				q.Add("filters.customRoles.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.CustomRoles.ObjectParentParentName {
				q.Add("filters.customRoles.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.CustomRoles.ObjectParentApplicationName {
				q.Add("filters.customRoles.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.CustomRoles.Enabled != nil {
				q.Add("filters.customRoles.enabled", fmt.Sprintf("%v", *req.Filters.CustomRoles.Enabled))
			}
			for _, v := range req.Filters.CustomRoles.ObjectParentKargoProjectName {
				q.Add("filters.customRoles.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		if req.Filters.NotificationCfg != nil {
			for _, v := range req.Filters.NotificationCfg.ObjectName {
				q.Add("filters.notificationCfg.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.NotificationCfg.ObjectKind {
				q.Add("filters.notificationCfg.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.NotificationCfg.ObjectGroup {
				q.Add("filters.notificationCfg.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.NotificationCfg.ObjectParentName {
				q.Add("filters.notificationCfg.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.NotificationCfg.ObjectParentParentName {
				q.Add("filters.notificationCfg.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.NotificationCfg.ObjectParentApplicationName {
				q.Add("filters.notificationCfg.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.NotificationCfg.Enabled != nil {
				q.Add("filters.notificationCfg.enabled", fmt.Sprintf("%v", *req.Filters.NotificationCfg.Enabled))
			}
			for _, v := range req.Filters.NotificationCfg.ObjectParentKargoProjectName {
				q.Add("filters.notificationCfg.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		if req.Filters.ApiKeys != nil {
			for _, v := range req.Filters.ApiKeys.ObjectName {
				q.Add("filters.apiKeys.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ApiKeys.ObjectKind {
				q.Add("filters.apiKeys.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ApiKeys.ObjectGroup {
				q.Add("filters.apiKeys.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ApiKeys.ObjectParentName {
				q.Add("filters.apiKeys.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ApiKeys.ObjectParentParentName {
				q.Add("filters.apiKeys.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.ApiKeys.ObjectParentApplicationName {
				q.Add("filters.apiKeys.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.ApiKeys.Enabled != nil {
				q.Add("filters.apiKeys.enabled", fmt.Sprintf("%v", *req.Filters.ApiKeys.Enabled))
			}
			for _, v := range req.Filters.ApiKeys.ObjectParentKargoProjectName {
				q.Add("filters.apiKeys.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		if req.Filters.Addons != nil {
			for _, v := range req.Filters.Addons.ObjectName {
				q.Add("filters.addons.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.Addons.ObjectKind {
				q.Add("filters.addons.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.Addons.ObjectGroup {
				q.Add("filters.addons.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.Addons.ObjectParentName {
				q.Add("filters.addons.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.Addons.ObjectParentParentName {
				q.Add("filters.addons.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.Addons.ObjectParentApplicationName {
				q.Add("filters.addons.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.Addons.Enabled != nil {
				q.Add("filters.addons.enabled", fmt.Sprintf("%v", *req.Filters.Addons.Enabled))
			}
			for _, v := range req.Filters.Addons.ObjectParentKargoProjectName {
				q.Add("filters.addons.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		if req.Filters.AddonRepos != nil {
			for _, v := range req.Filters.AddonRepos.ObjectName {
				q.Add("filters.addonRepos.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.AddonRepos.ObjectKind {
				q.Add("filters.addonRepos.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.AddonRepos.ObjectGroup {
				q.Add("filters.addonRepos.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.AddonRepos.ObjectParentName {
				q.Add("filters.addonRepos.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.AddonRepos.ObjectParentParentName {
				q.Add("filters.addonRepos.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.AddonRepos.ObjectParentApplicationName {
				q.Add("filters.addonRepos.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.AddonRepos.Enabled != nil {
				q.Add("filters.addonRepos.enabled", fmt.Sprintf("%v", *req.Filters.AddonRepos.Enabled))
			}
			for _, v := range req.Filters.AddonRepos.ObjectParentKargoProjectName {
				q.Add("filters.addonRepos.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		if req.Filters.AddonMarketplaceInstall != nil {
			for _, v := range req.Filters.AddonMarketplaceInstall.ObjectName {
				q.Add("filters.addonMarketplaceInstall.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.AddonMarketplaceInstall.ObjectKind {
				q.Add("filters.addonMarketplaceInstall.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.AddonMarketplaceInstall.ObjectGroup {
				q.Add("filters.addonMarketplaceInstall.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.AddonMarketplaceInstall.ObjectParentName {
				q.Add("filters.addonMarketplaceInstall.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.AddonMarketplaceInstall.ObjectParentParentName {
				q.Add("filters.addonMarketplaceInstall.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.AddonMarketplaceInstall.ObjectParentApplicationName {
				q.Add("filters.addonMarketplaceInstall.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.AddonMarketplaceInstall.Enabled != nil {
				q.Add("filters.addonMarketplaceInstall.enabled", fmt.Sprintf("%v", *req.Filters.AddonMarketplaceInstall.Enabled))
			}
			for _, v := range req.Filters.AddonMarketplaceInstall.ObjectParentKargoProjectName {
				q.Add("filters.addonMarketplaceInstall.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		if req.Filters.Workspace != nil {
			for _, v := range req.Filters.Workspace.ObjectName {
				q.Add("filters.workspace.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.Workspace.ObjectKind {
				q.Add("filters.workspace.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.Workspace.ObjectGroup {
				q.Add("filters.workspace.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.Workspace.ObjectParentName {
				q.Add("filters.workspace.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.Workspace.ObjectParentParentName {
				q.Add("filters.workspace.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.Workspace.ObjectParentApplicationName {
				q.Add("filters.workspace.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.Workspace.Enabled != nil {
				q.Add("filters.workspace.enabled", fmt.Sprintf("%v", *req.Filters.Workspace.Enabled))
			}
			for _, v := range req.Filters.Workspace.ObjectParentKargoProjectName {
				q.Add("filters.workspace.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
		if req.Filters.WorkspaceMember != nil {
			for _, v := range req.Filters.WorkspaceMember.ObjectName {
				q.Add("filters.workspaceMember.objectName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.WorkspaceMember.ObjectKind {
				q.Add("filters.workspaceMember.objectKind", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.WorkspaceMember.ObjectGroup {
				q.Add("filters.workspaceMember.objectGroup", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.WorkspaceMember.ObjectParentName {
				q.Add("filters.workspaceMember.objectParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.WorkspaceMember.ObjectParentParentName {
				q.Add("filters.workspaceMember.objectParentParentName", fmt.Sprintf("%v", v))
			}
			for _, v := range req.Filters.WorkspaceMember.ObjectParentApplicationName {
				q.Add("filters.workspaceMember.objectParentApplicationName", fmt.Sprintf("%v", v))
			}
			if req.Filters.WorkspaceMember.Enabled != nil {
				q.Add("filters.workspaceMember.enabled", fmt.Sprintf("%v", *req.Filters.WorkspaceMember.Enabled))
			}
			for _, v := range req.Filters.WorkspaceMember.ObjectParentKargoProjectName {
				q.Add("filters.workspaceMember.objectParentKargoProjectName", fmt.Sprintf("%v", v))
			}
		}
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListAuditLogsResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListAvailablePlans(ctx context.Context, req *ListAvailablePlansRequest) (*ListAvailablePlansResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/plans")
	return gateway.DoRequest[ListAvailablePlansResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) UpdateOrganizationBillingPlan(ctx context.Context, req *UpdateOrganizationBillingPlanRequest) (*UpdateOrganizationBillingPlanResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/aims/organizations/{organization_id}/plan")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateOrganizationBillingPlanResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) GetKubeVisionUsage(ctx context.Context, req *GetKubeVisionUsageRequest) (*GetKubeVisionUsageResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations/{organization_id}/k8s/usage")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.StartTime != nil {
		q.Add("startTime.seconds", fmt.Sprintf("%v", req.StartTime.Seconds))
		q.Add("startTime.nanos", fmt.Sprintf("%v", req.StartTime.Nanos))
	}
	if req.EndTime != nil {
		q.Add("endTime.seconds", fmt.Sprintf("%v", req.EndTime.Seconds))
		q.Add("endTime.nanos", fmt.Sprintf("%v", req.EndTime.Nanos))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[GetKubeVisionUsageResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListOrganizationDomains(ctx context.Context, req *ListOrganizationDomainsRequest) (*ListOrganizationDomainsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations/{organization_id}/domains")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	return gateway.DoRequest[ListOrganizationDomainsResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) UpdateOrganizationDomains(ctx context.Context, req *UpdateOrganizationDomainsRequest) (*UpdateOrganizationDomainsResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/aims/organizations/{organization_id}/domains")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateOrganizationDomainsResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ResetMFA(ctx context.Context, req *ResetMFARequest) (*ResetMFAResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/aims/mfa/reset")
	gwReq.SetBody(req)
	return gateway.DoRequest[ResetMFAResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListTeams(ctx context.Context, req *ListTeamsRequest) (*ListTeamsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations/{organization_id}/teams")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListTeamsResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListTeamMembers(ctx context.Context, req *ListTeamMembersRequest) (*ListTeamMembersResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations/{organization_id}/teams/{team_name}/members")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("team_name", fmt.Sprintf("%v", req.TeamName))
	q := url.Values{}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListTeamMembersResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListWorkspaces(ctx context.Context, req *ListWorkspacesRequest) (*ListWorkspacesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations/{organization_id}/workspaces")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListWorkspacesResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) GetWorkspace(ctx context.Context, req *GetWorkspaceRequest) (*GetWorkspaceResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations/{organization_id}/workspaces/{workspace_id}")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	return gateway.DoRequest[GetWorkspaceResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListWorkspaceMembers(ctx context.Context, req *ListWorkspaceMembersRequest) (*ListWorkspaceMembersResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations/{organization_id}/workspaces/{workspace_id}/members")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	q := url.Values{}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListWorkspaceMembersResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListWorkspaceCustomRoles(ctx context.Context, req *ListWorkspaceCustomRolesRequest) (*ListWorkspaceCustomRolesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations/{organization_id}/workspaces/{workspace_id}/custom-roles")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	gwReq.SetPathParam("workspace_id", fmt.Sprintf("%v", req.WorkspaceId))
	q := url.Values{}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListWorkspaceCustomRolesResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListOrganizationCustomRoles(ctx context.Context, req *ListOrganizationCustomRolesRequest) (*ListOrganizationCustomRolesResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations/{organization_id}/custom-roles")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[ListOrganizationCustomRolesResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListOrganizationUsers(ctx context.Context, req *ListOrganizationUsersRequest) (*ListOrganizationUsersResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations/{organization_id}/users")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	return gateway.DoRequest[ListOrganizationUsersResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) ListAIConversations(ctx context.Context, req *v1.ListAIConversationsRequest) (*v1.ListAIConversationsResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations/{organization_id}/ai/conversations")
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	if req.IncidentOnly != nil {
		q.Add("incidentOnly", fmt.Sprintf("%v", *req.IncidentOnly))
	}
	if req.IncidentStatus != nil {
		q.Add("incidentStatus", req.IncidentStatus.String())
	}
	if req.Application != nil {
		q.Add("application", fmt.Sprintf("%v", *req.Application))
	}
	if req.Namespace != nil {
		q.Add("namespace", fmt.Sprintf("%v", *req.Namespace))
	}
	if req.TitleContains != nil {
		q.Add("titleContains", fmt.Sprintf("%v", *req.TitleContains))
	}
	if req.Offset != nil {
		q.Add("offset", fmt.Sprintf("%v", *req.Offset))
	}
	if req.Limit != nil {
		q.Add("limit", fmt.Sprintf("%v", *req.Limit))
	}
	if req.ClusterId != nil {
		q.Add("clusterId", fmt.Sprintf("%v", *req.ClusterId))
	}
	if req.KargoProject != nil {
		q.Add("kargoProject", fmt.Sprintf("%v", *req.KargoProject))
	}
	if req.KargoInstanceId != nil {
		q.Add("kargoInstanceId", fmt.Sprintf("%v", *req.KargoInstanceId))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[v1.ListAIConversationsResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) GetAIConversation(ctx context.Context, req *v1.GetAIConversationRequest) (*v1.GetAIConversationResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/organizations/{organization_id}/ai/conversations/{id}")
	gwReq.SetPathParam("id", fmt.Sprintf("%v", req.Id))
	gwReq.SetPathParam("organization_id", fmt.Sprintf("%v", req.OrganizationId))
	q := url.Values{}
	if req.InstanceId != nil {
		q.Add("instanceId", fmt.Sprintf("%v", *req.InstanceId))
	}
	if req.KargoInstanceId != nil {
		q.Add("kargoInstanceId", fmt.Sprintf("%v", *req.KargoInstanceId))
	}
	gwReq.SetQueryParamsFromValues(q)
	return gateway.DoRequest[v1.GetAIConversationResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) UpdateAnnouncementBanner(ctx context.Context, req *UpdateAnnouncementBannerRequest) (*UpdateAnnouncementBannerResponse, error) {
	gwReq := c.gwc.NewRequest("POST", "/api/v1/aims/banner")
	gwReq.SetBody(req)
	return gateway.DoRequest[UpdateAnnouncementBannerResponse](ctx, gwReq)
}

func (c *aimsServiceGatewayClient) GetAnnouncementBanner(ctx context.Context, req *GetAnnouncementBannerRequest) (*GetAnnouncementBannerResponse, error) {
	gwReq := c.gwc.NewRequest("GET", "/api/v1/aims/announcement")
	return gateway.DoRequest[GetAnnouncementBannerResponse](ctx, gwReq)
}
