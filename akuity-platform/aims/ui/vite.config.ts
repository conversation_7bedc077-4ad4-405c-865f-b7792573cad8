import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';
import tsConfigPaths from 'vite-tsconfig-paths';

export const UI_VERSION = process.env.VERSION || 'development';
export const HUBSPOT_HUB_ID = process.env.HUBSPOT_HUB_ID || 'not-set';

// https://vitejs.dev/config/
export default defineConfig({
  build: {
    outDir: 'build',
    sourcemap: false
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true
      }
    }
  },
  plugins: [tsConfigPaths(), react()],
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:9095/api',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    },
    port: 3005
  },
  define: {
    __UI_VERSION__: JSON.stringify(UI_VERSION),
    __HUBSPOT_HUB_ID__: JSON.stringify(HUBSPOT_HUB_ID)
  }
});
