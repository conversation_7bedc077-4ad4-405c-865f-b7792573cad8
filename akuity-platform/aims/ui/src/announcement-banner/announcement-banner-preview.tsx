import { faChevronRight } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Alert, Card, Typography } from 'antd';
import React from 'react';

import { BannerLink, BannerType } from './type';

interface BannerPreviewProps {
  title?: string;
  message?: string;
  type: BannerType;
  closable: boolean;
  paidCustomersOnly: boolean;
  links: BannerLink[];
}

export const AnnouncementBannerPreview: React.FC<BannerPreviewProps> = ({
  title,
  message,
  type,
  closable,
  paidCustomersOnly,
  links
}) => {
  const hasBannerContent = title && message;

  return (
    <Card title='Banner Preview' className='h-fit'>
      <div className='space-y-6'>
        <div className='flex items-center justify-between'>
          <p className='text-sm text-gray-600'>This is how the banner will appear to users:</p>
          <div className='flex items-center space-x-2'>
            <span className='text-xs text-gray-500'>Live Preview</span>
            <div className='w-2 h-2 bg-green-500 rounded-full animate-pulse'></div>
          </div>
        </div>

        {hasBannerContent ? (
          <div className='space-y-4'>
            <Alert
              message={
                <div className='w-fit ml-2'>
                  <div>
                    {!!title && <Typography.Text className='font-bold'>{title} :</Typography.Text>}{' '}
                    <Typography.Text>{message}</Typography.Text>
                  </div>
                  {links && links.length > 0 && (
                    <div className='text-xs my-1'>
                      <FontAwesomeIcon icon={faChevronRight} className='mr-2 text-gray-500' />
                      {links.map((link, idx) => (
                        <a className='text-blue-700' href={link.url} target='_blank' key={idx}>
                          {link.name}
                          {idx < links!.length - 1 && ' - '}
                        </a>
                      ))}
                    </div>
                  )}
                </div>
              }
              banner
              closable={closable}
              showIcon={false}
              type={type}
            />

            <div className='bg-gray-50 rounded-lg p-4 space-y-3'>
              <h5 className='text-sm font-medium text-gray-900'>Banner Configuration</h5>
              <div className='grid grid-cols-2 gap-4 text-sm'>
                <div>
                  <span className='text-gray-500'>Type:</span>
                  <span className={`ml-2 px-2 py-1 rounded text-xs font-medium capitalize `}>
                    {type}
                  </span>
                </div>
                <div>
                  <span className='text-gray-500'>Closable:</span>
                  <span
                    className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
                      closable ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {closable ? 'Yes' : 'No'}
                  </span>
                </div>
                <div>
                  <span className='text-gray-500'>Paid Customers Only:</span>
                  <span
                    className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
                      paidCustomersOnly ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {paidCustomersOnly ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className='col-span-2'>
                  <span className='text-gray-500'>Title Length:</span>
                  <span className='ml-2 text-gray-900'>{title.length}/100</span>
                </div>
                <div className='col-span-2'>
                  <span className='text-gray-500'>Message Length:</span>
                  <span className='ml-2 text-gray-900'>{message.length}/500</span>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className='p-12 text-center text-gray-500 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50'>
            <svg
              className='w-16 h-16 mx-auto mb-4 text-gray-300'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={1}
                d='M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v12a2 2 0 01-2 2h-3l-4 4z'
              />
            </svg>
            <p className='text-lg font-medium mb-2'>No Banner Configured</p>
            <p className='text-sm'>Fill in the title and message above to see a live preview</p>
          </div>
        )}
      </div>
    </Card>
  );
};
