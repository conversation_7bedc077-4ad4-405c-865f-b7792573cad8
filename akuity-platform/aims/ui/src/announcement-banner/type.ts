import { z } from 'zod';

import { zodAuditSchema } from '@/utils';

export const BANNER_TYPES = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
  SUCCESS: 'success'
} as const;

export type BannerType = (typeof BANNER_TYPES)[keyof typeof BANNER_TYPES];

export const bannerTypeOptions = Object.values(BANNER_TYPES).map((value) => ({
  label: value.charAt(0).toUpperCase() + value.slice(1),
  value
}));

export type BannerLink = {
  name?: string;
  url?: string;
};

export const bannerSchema = z.object({
  type: z.enum(Object.values(BANNER_TYPES) as [string, ...string[]], {
    required_error: 'Please select a banner type'
  }),
  closable: z.boolean(),
  paidCustomersOnly: z.boolean().default(false),
  title: z.string().min(1, 'Title is required').max(100, 'Title must be less than 100 characters'),
  message: z
    .string()
    .min(1, 'Message is required')
    .max(500, 'Message must be less than 500 characters'),
  links: z
    .array(
      z.object({
        name: z.string(),
        url: z.string()
      })
    )
    .default([]),
  audit: zodAuditSchema
});

export const initialBannerData: BannerForm = {
  title: '',
  message: '',
  closable: false,
  type: BANNER_TYPES.INFO,
  paidCustomersOnly: false,
  links: [],
  audit: { actor: '', reason: '' }
};

export type BannerForm = z.infer<typeof bannerSchema>;

export type AnnouncementBannerPageProps = {
  initialBanner: BannerForm;
};
