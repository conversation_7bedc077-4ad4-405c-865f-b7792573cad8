import { useGetAnnouncementBanner } from '../hook/api/banner-queries';

import { AnnouncementBannerPage } from './announcement-banner-page';
import { BannerType, initialBannerData } from './type';

export const AnnouncementBannerWrapper = () => {
  const { data: currentBanner, isLoading } = useGetAnnouncementBanner();

  if (isLoading) {
    return (
      <div className='p-6 w-full'>
        <div className='flex items-center justify-center h-64'>
          <div className='text-gray-500'>Loading current banner...</div>
        </div>
      </div>
    );
  }

  const bannerData = currentBanner?.banner
    ? {
        ...currentBanner.banner,
        type: currentBanner.banner.type as BannerType,
        audit: { actor: '', reason: '' }
      }
    : initialBannerData;

  return <AnnouncementBannerPage initialBanner={bannerData} />;
};
