import { PlainMessage } from '@bufbuild/protobuf';
import { faPlus, faTimes } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Card, Divider, Input, notification, Radio, Switch, Modal } from 'antd';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import {
  useUpdateAnnouncementBanner,
  useDeleteAnnouncementBanner
} from '../hook/api/banner-queries';
import { Audit } from '../lib/apiclient/aims/v1/aims_pb';
import { AuditForm } from '../lib/components/shared/audit-form';
import { FieldContainer } from '../lib/components/shared/forms';
import { zodAuditSchema } from '../utils';

import { AnnouncementBannerPreview } from './announcement-banner-preview';
import {
  AnnouncementBannerPageProps,
  BannerForm,
  BannerLink,
  bannerSchema,
  BannerType,
  bannerTypeOptions,
  initialBannerData
} from './type';

export const AnnouncementBannerPage = ({ initialBanner }: AnnouncementBannerPageProps) => {
  const [links, setLinks] = useState<BannerLink[]>(initialBanner?.links);

  const { control, handleSubmit, watch, reset, setError, clearErrors } = useForm<BannerForm>({
    resolver: zodResolver(bannerSchema),
    defaultValues: initialBanner
  });

  const updateBannerMutation = useUpdateAnnouncementBanner();
  const deleteBannerMutation = useDeleteAnnouncementBanner();

  const watchedValues = watch();

  const onSubmit = handleSubmit((data) => {
    updateBannerMutation.mutate(
      {
        banner: {
          title: data.title || undefined,
          message: data.message,
          closable: data.closable,
          type: data.type,
          paidCustomersOnly: data.paidCustomersOnly,
          links: links.map((link) => ({
            name: link.name || '',
            url: link.url || ''
          }))
        },
        audit: data.audit as PlainMessage<Audit>
      },
      {
        onSuccess: () => {
          notification.success({
            message: 'Banner Updated Successfully',
            description:
              'The announcement banner has been updated and will be visible to all users.'
          });
        }
      }
    );
  });

  const handleDeleteBanner = () => {
    clearErrors(['audit.actor', 'audit.reason']);
    const auditData = watchedValues.audit;
    const validationResult = zodAuditSchema.safeParse(auditData);

    if (!validationResult.success) {
      validationResult.error.errors.forEach((error) => {
        const field = error.path[0] as string;
        setError(`audit.${field}` as 'audit.actor' | 'audit.reason', {
          type: 'validation',
          message: error.message
        });
      });
      return;
    }

    Modal.confirm({
      title: 'Delete Announcement Banner',
      content:
        'Are you sure you want to delete the current announcement banner? This action cannot be undone.',
      okText: 'Delete Banner',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: () => {
        deleteBannerMutation.mutate(
          {
            delete: true,
            audit: {
              actor: auditData.actor.trim(),
              reason: auditData.reason.trim()
            } as PlainMessage<Audit>
          },
          {
            onSuccess: () => {
              notification.success({
                message: 'Banner Deleted Successfully',
                description: 'The announcement banner has been removed.'
              });
              reset(initialBannerData);
              setLinks([]);
            }
          }
        );
      }
    });
  };

  const addLink = () => setLinks((prev) => [...prev, { name: '', url: '' }]);
  const removeLink = (index: number) => setLinks((prev) => prev.filter((_, i) => i !== index));
  const updateLink = (index: number, field: 'name' | 'url', value: string) =>
    setLinks((prev) => prev.map((link, i) => (i === index ? { ...link, [field]: value } : link)));

  const isAuditComplete = watchedValues.audit.actor && watchedValues.audit.reason;

  return (
    <div className='p-6 w-full'>
      <div className='mb-8'>
        <h1 className='text-3xl font-bold text-gray-900 mb-3'>Announcement Banner Management</h1>
        <p className='text-gray-600 text-lg'>
          Manage the announcement banner that appears at the top of the Akuity Platform for all
          users.
        </p>
      </div>

      <div className='grid grid-cols-1 xl:grid-cols-2 gap-8'>
        <Card title='Banner Configuration' className='h-fit'>
          <form onSubmit={onSubmit} className='space-y-4'>
            <FieldContainer control={control} label='Banner Type' name='type'>
              {({ field }) => <Radio.Group {...field} options={bannerTypeOptions} />}
            </FieldContainer>

            <FieldContainer control={control} label='User Can Close Banner' name='closable'>
              {({ field: { value, onChange, ...field } }) => (
                <Switch checked={value} onChange={onChange} {...field} />
              )}
            </FieldContainer>

            <FieldContainer
              control={control}
              label='Show Only to Paid Customers'
              name='paidCustomersOnly'
            >
              {({ field: { value, onChange, ...field } }) => (
                <Switch checked={value} onChange={onChange} {...field} />
              )}
            </FieldContainer>

            <FieldContainer control={control} label='Banner Title' name='title'>
              {({ field }) => (
                <Input
                  {...field}
                  placeholder='e.g., System Maintenance'
                  maxLength={100}
                  showCount
                />
              )}
            </FieldContainer>

            <FieldContainer control={control} label='Banner Message' name='message'>
              {({ field }) => (
                <Input.TextArea
                  {...field}
                  placeholder='e.g., Scheduled maintenance will occur on...'
                  maxLength={500}
                  rows={4}
                  showCount
                />
              )}
            </FieldContainer>

            <div className='space-y-3 mt-6'>
              <div className='flex items-center justify-between'>
                <label className='text-sm font-medium text-gray-700'>Banner Links</label>
                <Button
                  className='mt-6'
                  type='dashed'
                  onClick={addLink}
                  icon={<FontAwesomeIcon icon={faPlus} />}
                  size='small'
                >
                  Add Link
                </Button>
              </div>

              {links.map((link, index) => (
                <div
                  key={index}
                  className='flex items-center space-x-2 p-3 border border-gray-200 rounded-lg'
                >
                  <div className='flex-1 space-y-2'>
                    <Input
                      placeholder='Link name'
                      value={link.name}
                      onChange={(e) => updateLink(index, 'name', e.target.value)}
                      maxLength={50}
                    />
                    <Input
                      placeholder='https://example.com'
                      value={link.url}
                      onChange={(e) => updateLink(index, 'url', e.target.value)}
                    />
                  </div>
                  <Button
                    type='text'
                    danger
                    icon={<FontAwesomeIcon icon={faTimes} />}
                    onClick={() => removeLink(index)}
                  />
                </div>
              ))}

              {links.length === 0 && (
                <div className='text-sm text-gray-500 text-center py-4 border-2 border-dashed border-gray-200 rounded-lg'>
                  No links added. Click "Add Link" to include helpful resources.
                </div>
              )}
            </div>

            <Divider />
            <AuditForm control={control} />

            <div className='flex gap-3 pt-4'>
              <Button
                type='primary'
                htmlType='submit'
                loading={updateBannerMutation.isPending}
                disabled={!watchedValues.title || !watchedValues.message}
              >
                Update Banner
              </Button>
              <Button
                danger
                onClick={handleDeleteBanner}
                loading={deleteBannerMutation.isPending}
                disabled={!isAuditComplete}
              >
                Delete Banner
              </Button>
            </div>
          </form>
        </Card>

        <AnnouncementBannerPreview
          title={watchedValues.title}
          message={watchedValues.message}
          type={watchedValues.type as BannerType}
          closable={watchedValues.closable}
          paidCustomersOnly={watchedValues.paidCustomersOnly}
          links={links}
        />
      </div>
    </div>
  );
};
