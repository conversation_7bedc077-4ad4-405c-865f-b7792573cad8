import { createContext, useContext } from 'react';
import { Outlet, useParams } from 'react-router-dom';

import { useGetOrganization } from '@/hook/api';
import { BasicOrganization } from '@/lib/apiclient/aims/v1/aims_pb';

type OrganizationContextType = {
  organization: BasicOrganization | null;
  refetch: () => void;
  isFetching: boolean;
  error: Error;
};

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

export const OrganizationProvider = ({ children }: { children: React.ReactNode }) => {
  const { orgId } = useParams();

  const { data: organization, refetch, isFetching, error } = useGetOrganization(orgId);

  return (
    <OrganizationContext.Provider
      value={{ organization: organization || null, refetch, isFetching, error }}
    >
      {children}
    </OrganizationContext.Provider>
  );
};

export const useOrganization = () => {
  const context = useContext(OrganizationContext);
  if (!context) {
    throw new Error('useOrganization must be used within an OrganizationProvider');
  }
  return context;
};

export const OrganizationProviderWrapper = () => (
  <OrganizationProvider>
    <Outlet />
  </OrganizationProvider>
);
