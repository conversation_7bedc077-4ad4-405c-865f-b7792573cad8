import { Button, Result } from 'antd';
import { useNavigate } from 'react-router-dom';

export const NotFoundPage = () => {
  const navigate = useNavigate();

  return (
    <div className='w-full flex items-center justify-center min-h-full'>
      <Result
        status='404'
        title='404'
        subTitle='Page not Found.'
        extra={
          <Button type='primary' onClick={() => navigate('/organizations')}>
            Back Home
          </Button>
        }
      />
    </div>
  );
};
