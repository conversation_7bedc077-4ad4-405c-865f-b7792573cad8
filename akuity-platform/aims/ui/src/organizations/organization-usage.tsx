import { faExclamationCircle } from '@fortawesome/free-solid-svg-icons';
import moment from 'moment';

import { useGetFeatureGates } from '@/hook/api';
import { BasicOrganization } from '@/lib/apiclient/aims/v1/aims_pb';
import IconLabel from '@/lib/components/shared/icon-label';

import { OrganizationQuota } from './organization-quota';
import { UsageGraph } from './usage-graph';
import { customerPlanStatus, getPlanDisplayName, getPlanTier } from './utils';
import { datesUtil } from './utils';

type OrganizationUsageProps = {
  organization: BasicOrganization;
};

export const OrganizationUsage = ({ organization }: OrganizationUsageProps) => {
  const { data: featureGates } = useGetFeatureGates(organization.id);

  const expiration = organization?.status?.expiry
    ? datesUtil(Number(organization.status.expiry))
    : null;

  const subscription = organization?.stripeData?.subscriptionEndTime
    ? datesUtil(organization.stripeData.subscriptionEndTime as unknown as string)
    : null;

  const plan = getPlanTier(
    // @ts-expect-error it has what is required
    organization,
    organization.billingDetails
  );

  const expired = moment.unix(Number(organization?.status?.expiry)).isBefore(moment());

  const subscriptionOverdue =
    expiration &&
    moment(new Date(organization?.stripeData?.subscriptionEndTime as unknown as string)).isBefore(
      moment()
    );

  return (
    <>
      <div className='flex border-b border-gray-200 pb-2'>
        <div className='mr-16'>
          <div className='uppercase text-xs text-gray-500 mb-1'>Current Plan</div>
          <div className='text-2xl mb-2 font-semibold'>
            <div>{getPlanDisplayName(plan)}</div>
          </div>
          {expiration && expiration.formattedDate != 'Invalid date' && (
            <div className='mb-3 w-64'>
              {/* Expiration Date field is date of renewal for paid users, and date of trial expiation for trial users */}
              <div className='mb-2'>
                {customerPlanStatus(
                  // @ts-expect-error it has what is required
                  organization,
                  organization.billingDetails,
                  expired
                )}
              </div>
              <div className='mb-2'>
                <div className='flex items-center'>
                  <IconLabel
                    className={`font-bold py-1 px-2 rounded text-white mr-2 text-center inline-block ${
                      expired ? 'bg-red-600' : 'bg-gray-500'
                    }`}
                    icon={expired ? faExclamationCircle : null}
                  >
                    {expiration.formattedDate}
                  </IconLabel>
                  <span>({expiration.humanFriendly})</span>
                </div>
              </div>
            </div>
          )}
          {subscription && subscription.formattedDate != 'Invalid date' && (
            <div className='mb-3 w-64'>
              <span className='mb-2 block'>Next invoice</span>
              <div className='flex items-center'>
                <IconLabel
                  className={`font-bold py-1 px-2 rounded text-white mr-2 text-center inline-block ${
                    subscriptionOverdue ? 'bg-red-600' : 'bg-gray-500'
                  }`}
                  icon={subscriptionOverdue ? faExclamationCircle : null}
                >
                  {subscription.formattedDate}
                </IconLabel>
                <span>({subscription.humanFriendly})</span>
              </div>
            </div>
          )}
        </div>
        <div className='mt-4'>
          <UsageGraph
            loading={organization.status.billingUpdating}
            usage={organization.usage}
            organization={organization}
            featureGates={featureGates?.featureGates}
          />
        </div>
      </div>
      <div className='mt-5'>
        <OrganizationQuota />
      </div>
    </>
  );
};
