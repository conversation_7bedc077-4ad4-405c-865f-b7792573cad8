import { Empty, Flex, List, Typography } from 'antd';
import { Link, generatePath } from 'react-router-dom';

type Props = {
  instances: { id: string; name: string }[];
  title: string;
  instancePath: string;
};

export const WorkspaceInstancesList = ({ instances, title, instancePath }: Props) => {
  return (
    <List
      className='w-full'
      header={
        <Flex justify='space-between'>
          <Typography.Title level={5} className='!mb-0'>
            {title}
          </Typography.Title>
        </Flex>
      }
      dataSource={instances}
      pagination={{ pageSize: 10, size: 'small', hideOnSinglePage: true }}
      locale={{
        emptyText: <Empty description='No Instances' image={Empty.PRESENTED_IMAGE_SIMPLE} />
      }}
      renderItem={(item) => (
        <List.Item>
          <Link to={generatePath(instancePath, { id: item.id })}>{item.name}</Link>
        </List.Item>
      )}
    />
  );
};
