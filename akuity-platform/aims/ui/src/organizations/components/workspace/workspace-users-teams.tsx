import { Table, Typography, Badge } from 'antd';
import { <PERSON> } from 'react-router-dom';

import { useRequiredParams } from '@/hook';
import { useGetWorkspaceMembers } from '@/hook/api';
import { ListWorkspaceMembersResponse } from '@/lib/apiclient/aims/v1/aims_pb';
import { WorkspaceMemberRole } from '@/lib/apiclient/organization/v1/organization_pb';
import { pluralize } from '@/utils';

export const workspaceMemberRolesOptions = [
  {
    value: WorkspaceMemberRole.MEMBER,
    label: 'Member',
    color: 'blue'
  },
  {
    value: WorkspaceMemberRole.ADMIN,
    label: 'Admin',
    color: 'red'
  }
];

const getRoleInfo = (role: number) => {
  return (
    workspaceMemberRolesOptions.find((r) => r.value === role) || {
      label: 'Unknown',
      color: 'default'
    }
  );
};

export const WorkspaceUsersTeams = () => {
  const { workspaceId, orgId } = useRequiredParams(['orgId', 'workspaceId']);
  const { data: members, isLoading } = useGetWorkspaceMembers(orgId, workspaceId, {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    select: (data) => ListWorkspaceMembersResponse.fromJson(data as any)
  });
  const users = members?.workspaceMembers.filter((i) => i.member?.case === 'user');
  const teams = members?.workspaceMembers.filter((i) => i.member?.case === 'team');

  const teamColumns = [
    {
      title: 'Team Name',
      dataIndex: 'name',
      key: 'name',
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      render: (text: string, record: any) => {
        return (
          <div>
            <Link to={`/organizations/${orgId}/teams/${text}`}>
              {text}
              <Typography.Text type='secondary' className='ml-2'>
                ({record.memberCount} {pluralize(record.memberCount, 'member')})
              </Typography.Text>
            </Link>
          </div>
        );
      },
      width: 250
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      render: (role: number) => {
        const roleInfo = getRoleInfo(role);
        return <Badge color={roleInfo.color} text={roleInfo.label} />;
      },
      width: 150
    }
  ];

  const userColumns = [
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 250
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      render: (role: number) => {
        const roleInfo = getRoleInfo(role);
        return <Badge color={roleInfo.color} text={roleInfo.label} />;
      },
      width: 150
    }
  ];

  return (
    <div style={{ marginTop: '16px' }}>
      <Typography.Title level={5}>Teams</Typography.Title>
      <Table
        columns={teamColumns}
        dataSource={teams
          ?.map((t) => {
            if (t.member.case === 'team') {
              const team = t.member.value;
              return {
                key: team.id,
                name: team.name,
                memberCount: Number(team.memberCount),
                role: t.role
              };
            }
            return null;
          })
          .filter(Boolean)}
        loading={isLoading}
        pagination={false}
      />

      <Typography.Title level={5} style={{ marginTop: '24px' }}>
        Other Members
      </Typography.Title>
      <Table
        columns={userColumns}
        dataSource={users
          ?.map((u) => {
            if (u.member.case === 'user') {
              const user = u.member.value;
              return {
                key: user.id,
                email: user.email,
                role: u.role
              };
            }
            return null;
          })
          .filter(Boolean)}
        loading={isLoading}
        pagination={false}
      />
    </div>
  );
};
