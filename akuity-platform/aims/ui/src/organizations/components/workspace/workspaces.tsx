import { Pagination } from 'antd';

import { usePagination } from '@/hook';
import { useListWorkspaces } from '@/hook/api';
import { BasicOrganization } from '@/lib/apiclient/aims/v1/aims_pb';
import { Loading } from '@/lib/components/loading';

import { WorkspaceTile } from './workspace-tile';

type WorkSpaceProps = {
  organization: BasicOrganization;
};

export const Workspaces = ({ organization }: WorkSpaceProps) => {
  const pagination = usePagination({ defaultLimit: 9 });
  const { data, isFetching } = useListWorkspaces(organization.id, pagination);
  return (
    <>
      {isFetching && <Loading />}
      <div className='mt-8 grid grid-cols-[repeat(auto-fill,_minmax(300px,_1fr))] auto-rows-[minmax(0,_2fr)] gap-6 px-0.5'>
        {data?.workspaces?.map((workspace) => (
          <WorkspaceTile key={workspace.id} workspace={workspace} orgId={organization.id} />
        ))}
      </div>

      {!isFetching && Math.ceil(Number(data?.count) / pagination.limit) > 1 && (
        <div className='mt-4 w-full flex justify-center'>
          <Pagination
            pageSize={pagination.limit}
            current={pagination.offset / pagination.limit + 1}
            onChange={pagination.setPage}
            onShowSizeChange={(_, size) => pagination.onPageSizeChange(size)}
            total={Number(data?.count)}
          />
        </div>
      )}
    </>
  );
};
