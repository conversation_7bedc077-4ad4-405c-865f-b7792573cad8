import { faUsers, faBriefcase, faUserShield, faCrown } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Table } from 'antd';
import React from 'react';
import { Link } from 'react-router-dom';

import { useRequiredParams } from '@/hook';
import { useGetOrganizationUsers } from '@/hook/api';
import { OrganizationUser, TeamInfo, WorkspaceInfo } from '@/lib/apiclient/aims/v1/aims_pb';

const roleIcons: Record<string, React.ReactElement> = {
  owner: <FontAwesomeIcon icon={faCrown} className='mr-2 text-yellow-500' />,
  member: <FontAwesomeIcon icon={faUserShield} className='mr-2 text-blue-500' />
};

const UserTable = () => {
  const { orgId } = useRequiredParams(['orgId']);
  const { data, isLoading } = useGetOrganizationUsers(orgId);

  return (
    <Table<OrganizationUser>
      dataSource={data?.users || []}
      rowKey='id'
      loading={isLoading}
      pagination={false}
    >
      <Table.Column<OrganizationUser>
        title='Email'
        dataIndex='email'
        sorter={(a, b) => a.email.localeCompare(b.email)}
        render={(e) => (
          <a
            href={`https://app.hubspot.com/contacts/${__HUBSPOT_HUB_ID__}/contact/email/${e}`}
            target='_blank'
            className='block text-blue-500'
          >
            {e}
          </a>
        )}
      />
      <Table.Column<OrganizationUser>
        title='Role'
        dataIndex='role'
        sorter={(a, b) => a.role.localeCompare(b.role)}
        render={(role: string) => (
          <span>
            {roleIcons[role.toLowerCase()] || roleIcons.member}
            {role.charAt(0).toUpperCase() + role.slice(1)}
          </span>
        )}
      />
      <Table.Column<OrganizationUser>
        title='Workspaces'
        dataIndex='workspaces'
        render={(workspaces: WorkspaceInfo[]) => (
          <span>
            <FontAwesomeIcon icon={faBriefcase} className='mr-2 text-green-500' />
            {workspaces.map((ws, index) => (
              <span key={ws.id}>
                <Link to={`/organizations/${orgId}/workspaces/${ws.id}`}>{ws.name}</Link>
                {index !== workspaces.length - 1 ? ', ' : ''}
              </span>
            ))}
          </span>
        )}
      />
      <Table.Column<OrganizationUser>
        title='Teams'
        dataIndex='teams'
        render={(teams: TeamInfo[]) => (
          <span>
            {teams.length > 0 ? (
              <>
                <FontAwesomeIcon icon={faUsers} className='mr-2 text-purple-500' />
                {teams.map((team, index) => (
                  <span key={team.id}>
                    <Link to={`/organizations/${orgId}/teams/${team.name}`}>{team.name}</Link>
                    {index !== teams.length - 1 ? ', ' : ''}
                  </span>
                ))}
              </>
            ) : (
              'No Team'
            )}
          </span>
        )}
      />
    </Table>
  );
};

export default UserTable;
