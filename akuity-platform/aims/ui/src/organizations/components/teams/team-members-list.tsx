import { faMagnifyingGlass } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Empty, Input, List, Pagination } from 'antd';
import React from 'react';

import { usePagination } from '@/hook';
import { useListTeamMembers } from '@/hook/api';

type Props = {
  teamName: string;
  isActiveTab: boolean;
  id: string;
};

export const TeamMembersList = ({ teamName, isActiveTab, id }: Props) => {
  const pagination = usePagination({ defaultLimit: 10 });

  const [search, setSearch] = React.useState('');
  const { data, isLoading: isMembersLoading } = useListTeamMembers(id, teamName, pagination);
  const teamMembers = React.useMemo(
    () => data?.teamMembers?.filter(({ email }) => !search || email.includes(search)),
    [data, search]
  );

  React.useEffect(() => setSearch(''), [isActiveTab]);
  return (
    <>
      <Input
        className='mb-2'
        prefix={<FontAwesomeIcon icon={faMagnifyingGlass} />}
        placeholder='Search...'
        value={search}
        onChange={(e) => setSearch(e.target.value)}
      />
      <List
        dataSource={teamMembers}
        loading={isMembersLoading}
        locale={{ emptyText: <Empty description='No members found' /> }}
        pagination={{ pageSize: 20, size: 'small', hideOnSinglePage: true }}
        renderItem={(member) => (
          <List.Item key={member.id}>
            <List.Item.Meta title={member.email} />
          </List.Item>
        )}
      />
      {!isMembersLoading && Math.ceil(Number(data?.count) / pagination.limit) > 1 && (
        <Pagination
          pageSize={pagination.limit}
          current={pagination.offset / pagination.limit + 1}
          onChange={pagination.setPage}
          onShowSizeChange={(_, size) => pagination.onPageSizeChange(size)}
          total={Number(data?.count)}
        />
      )}
    </>
  );
};
