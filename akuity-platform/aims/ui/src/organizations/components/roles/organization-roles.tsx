import { Collapse, Empty, Pagination } from 'antd';

import { usePagination } from '@/hook';
import { useGetOrganizationCustomRoles } from '@/hook/api';
import { BasicOrganization } from '@/lib/apiclient/aims/v1/aims_pb';
import { Loading } from '@/lib/components/loading';

type ManageCustomRolesProps = {
  organization: BasicOrganization;
};

const { Panel } = Collapse;
export const CustomRoles = ({ organization }: ManageCustomRolesProps) => {
  const pagination = usePagination({ defaultLimit: 10 });

  const { data, isFetching } = useGetOrganizationCustomRoles(organization.id, pagination);

  return (
    <div className='w-full mt-3 gap-5'>
      {isFetching && <Loading />}
      {!isFetching &&
        data?.customRoles.map((role) => (
          <Collapse key={role.name} className='mb-3'>
            <Panel
              key={role.name}
              header={
                <>
                  <p className='text-sm font-semibold'>{role.name}</p>
                  {role.description && <p className='text-xs text-gray-500'>{role.description}</p>}
                </>
              }
            >
              <pre className='bg-gray-100 p-3 rounded text-xs overflow-auto'>
                {role.policy.split('\n').map((line, index) => (
                  <div key={index}>{line}</div>
                ))}
              </pre>
            </Panel>
          </Collapse>
        ))}
      {!isFetching && Math.ceil(Number(data?.totalCount || 0) / pagination.limit) > 1 && (
        <div className='mt-4 w-full flex justify-center'>
          <Pagination
            pageSize={pagination.limit}
            current={pagination.offset / pagination.limit + 1}
            onChange={pagination.setPage}
            onShowSizeChange={(_, size) => pagination.onPageSizeChange(size)}
            total={Number(data?.totalCount || 0)}
          />
        </div>
      )}
      {!isFetching && data?.customRoles?.length === 0 && (
        <Empty description='No custom roles' className='h-full flex justify-center flex-col my-4' />
      )}
    </div>
  );
};
