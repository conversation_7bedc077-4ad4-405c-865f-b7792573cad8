import { PlainMessage } from '@bufbuild/protobuf';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Modal, notification } from 'antd';
import { useForm } from 'react-hook-form';
import { NavigateFunction } from 'react-router-dom';
import z from 'zod';

import { useDeleteUnpaidOrganization } from '@/hook/api';
import { Audit, BasicOrganization } from '@/lib/apiclient/aims/v1/aims_pb';
import { AuditForm } from '@/lib/components/shared/audit-form';
import { errorToString, zodAuditSchema } from '@/utils';

export const ConfirmFooter = ({
  onClose,
  onOk,
  danger
}: {
  onClose: (confirmed: boolean, e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
  onOk: () => void;
  danger: boolean;
}) => [
  <Button key='back' onClick={(e) => onClose(false, e)}>
    Cancel
  </Button>,
  <Button
    key='submit'
    type='primary'
    onClick={() => {
      onOk();
    }}
    danger={danger}
  >
    Confirm
  </Button>
];

const schema = z.object({ audit: zodAuditSchema });

export const DeleteOrganizationModal = ({
  hideDeleteModal,
  organization,
  visible,
  navigate
}: {
  hideDeleteModal?(): void;
  organization: BasicOrganization;
  visible?: boolean;
  navigate: NavigateFunction;
}) => {
  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      audit: {
        actor: '',
        reason: ''
      }
    }
  });
  const { mutate } = useDeleteUnpaidOrganization();
  const deleteOrganization = form.handleSubmit(({ audit }) => {
    mutate(
      { id: organization?.id, audit: audit as PlainMessage<Audit> },
      {
        onSuccess: () => {
          hideDeleteModal();
          navigate('/organizations');
          notification.success({
            message: `Successfully delete organization ${organization.name}`
          });
        },
        onError: (err) => {
          notification.error({ message: errorToString(err), placement: 'bottomRight' });
        }
      }
    );
  });

  return (
    <Modal
      open={visible}
      title={`Confirm Organization Delete`}
      onCancel={() => hideDeleteModal()}
      footer={ConfirmFooter({
        onClose: hideDeleteModal,
        onOk: deleteOrganization,
        danger: true
      })}
    >
      <div className='text-lg font-bold my-4'>Deleting organization {organization?.name}</div>
      Are you sure you want to delete this organization?
      <br />
      <b>This will delete all data associated with this organization.</b>
      <br />
      {!!organization?.billingDetails?.customerId && (
        <>
          This customer is observed in stripe. Make sure that this customer has no subscriptions
          active.
        </>
      )}
      <AuditForm control={form.control} />
    </Modal>
  );
};
