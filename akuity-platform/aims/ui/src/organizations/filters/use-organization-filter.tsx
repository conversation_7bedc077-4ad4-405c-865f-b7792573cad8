import { z } from 'zod';

import { Sort } from '@/lib/apiclient/aims/v1/aims_pb';
import type { OrganizationFilter } from '@/lib/apiclient/aims/v1/aims_pb';
import { withDefaults, useSearchParamsState } from '@/lib/hooks/use-search-params-state';
import { booleanFromString, ZodShapeFor } from '@/utils';

export const DEFAULT_PAGE_SIZE = 50;

export const organizationFiltersZodSchema = z
  .object({
    fuzz: z.string().optional(),
    billed: booleanFromString(),
    manuallyVerified: booleanFromString(),
    sortByCreation: z.coerce.number().pipe(z.nativeEnum(Sort)).optional(),
    limit: withDefaults(z.coerce.number().int().positive(), DEFAULT_PAGE_SIZE),
    offset: withDefaults(z.coerce.number().int().nonnegative(), 0),
    plans: z.array(z.string()).optional(),
    startTime: z.string().optional(),
    endTime: z.string().optional()
  } satisfies ZodShapeFor<OrganizationFilter>)
  .strict();

export type OrganizationFilters = z.infer<typeof organizationFiltersZodSchema>;

export const useOrganizationFilters = () => {
  const {
    state: filters,
    setSearchState: setFilters,
    removeKeysFromSearch: removeFilters,
    ...other
  } = useSearchParamsState(organizationFiltersZodSchema);

  return {
    filters,
    setFilters,
    removeFilters,
    ...other
  };
};
