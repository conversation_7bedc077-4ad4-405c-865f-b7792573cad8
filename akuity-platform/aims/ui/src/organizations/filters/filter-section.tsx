import { faCalendarDays } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Col, Input, Popover, Row, Select } from 'antd';
import { useState } from 'react';
import { useDebouncedCallback } from 'use-debounce';

import { useListAvailablePlans } from '@/hook/api';
import { TimeRangeFilterContent } from '@/lib/components/shared/time-range-context';

import { useOrganizationFilters } from './use-organization-filter';

const { Option } = Select;

export const PLAN_COLORS: { [k in string]: { bg: string; text: string } } = {
  enterprise: { bg: 'bg-purple-100', text: 'text-purple-800' },
  professional: { bg: 'bg-blue-100', text: 'text-blue-800' },
  professional_trial: { bg: 'bg-cyan-100', text: 'text-cyan-800' },
  professional_plus_trial: { bg: 'bg-indigo-100', text: 'text-indigo-800' },
  starter: { bg: 'bg-green-100', text: 'text-green-800' },
  starter_v2: { bg: 'bg-emerald-100', text: 'text-emerald-800' },
  default: { bg: 'bg-gray-100', text: 'text-gray-800' }
};

export const getPlanColor = (plan: string) => {
  const planKey = plan?.toLowerCase();
  return PLAN_COLORS[planKey] || PLAN_COLORS.default;
};

function FilterSection() {
  const { filters, setFilters, removeFilters } = useOrganizationFilters();
  const { data } = useListAvailablePlans();

  const searchOrgByFuzz = useDebouncedCallback(
    (fuzz: string) => setFilters({ ...filters, fuzz }),
    1000
  );

  const handlePlanFilterChange = (values: string[]) => {
    setFilters({ ...filters, plans: values.length > 0 ? values : undefined });
  };

  const [open, setOpen] = useState(false);

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
  };

  return (
    <div className='space-y-4 mb-4'>
      <Row gutter={[16, 16]} align='middle'>
        <Col xs={24} sm={18} md={18}>
          <Input
            onChange={(e) => {
              searchOrgByFuzz(e.target.value);
            }}
            placeholder='Search by Name, ID, or Email'
            defaultValue={filters?.fuzz}
            allowClear
          />
        </Col>
        <Col xs={24} sm={6} md={6} style={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Popover
            onOpenChange={handleOpenChange}
            open={open}
            content={
              <TimeRangeFilterContent
                startTime={filters.startTime}
                endTime={filters.endTime}
                onApply={(time) => {
                  setFilters(time);
                  setOpen(false);
                }}
                onClear={() => {
                  removeFilters(['startTime', 'endTime']);
                  setOpen(false);
                }}
              />
            }
            title='Filter by Creation Date'
            trigger='click'
            placement='bottomRight'
          >
            <Button
              className='w-full'
              icon={<FontAwesomeIcon icon={faCalendarDays} />}
              type={filters.startTime || filters.endTime ? 'primary' : 'default'}
            >
              Date Range
            </Button>
          </Popover>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col xs={24} sm={8} md={8}>
          <Select
            allowClear
            placeholder='Payment Status'
            style={{ width: '100%' }}
            value={filters.billed}
            onClear={() => removeFilters(['billed'])}
            onChange={(value) => setFilters({ ...filters, billed: value })}
          >
            <Option value={true}>Paid Customers</Option>
            <Option value={false}>Unpaid Organizations</Option>
          </Select>
        </Col>
        <Col xs={24} sm={8} md={8}>
          <Select
            allowClear
            placeholder='Verification Status'
            style={{ width: '100%' }}
            value={filters.manuallyVerified}
            onChange={(value) => setFilters({ ...filters, manuallyVerified: value })}
            onClear={() => removeFilters(['manuallyVerified'])}
          >
            <Option value={true}>Manually Verified</Option>
            <Option value={false}>Not Verified</Option>
          </Select>
        </Col>
        <Col xs={24} sm={8} md={8}>
          <Select
            mode='multiple'
            allowClear
            placeholder='Filter by Plans'
            style={{ width: '100%' }}
            value={filters.plans}
            onChange={handlePlanFilterChange}
            showSearch
          >
            {data?.map((option) => (
              <Option key={option.name} value={option.name}>
                {option.name}
              </Option>
            ))}
          </Select>
        </Col>
      </Row>
    </div>
  );
}

export default FilterSection;
