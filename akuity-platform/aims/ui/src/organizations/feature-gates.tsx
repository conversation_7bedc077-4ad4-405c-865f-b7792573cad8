import { PlainMessage } from '@bufbuild/protobuf';
import { faForward } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { zodResolver } from '@hookform/resolvers/zod';
import { notification, Modal, Button, List, Switch } from 'antd';
import { useMemo } from 'react';
import { FormProvider, useForm, useFormContext } from 'react-hook-form';
import { z } from 'zod';

import { queryClient } from '@/app';
import { queryKeys } from '@/constants/query-keys';
import { useDisclosure } from '@/hook';
import { useGetFeatureGates, usePatchFeatureGates } from '@/hook/api';
import { Audit, BasicOrganization, GetFeatureGatesResponse } from '@/lib/apiclient/aims/v1/aims_pb';
import { OrganizationFeatureGates } from '@/lib/apiclient/types/features/v1/features_pb';
import { Enabled } from '@/lib/components/enabled';
import { Loading } from '@/lib/components/loading';
import { AuditForm } from '@/lib/components/shared/audit-form';
import { zodAuditSchema } from '@/utils';

import { useOrganization } from './context/organization-context';
import {
  Changes as FeatureGatesChanges,
  featureGatesChanges,
  onlyRequireFeatureGates
} from './utils';

const schema = z.object({
  featureGates: z.record(z.string(), z.union([z.boolean(), z.array(z.string())])),
  audit: zodAuditSchema
});

type PatchFeatureGatesForm = z.infer<typeof schema>;

type FeatureGatesProps = {
  organization: BasicOrganization;
};

export const FeatureGates = ({ organization }: FeatureGatesProps) => {
  const { data: featureGates, isLoading: featureGatesLoading } = useGetFeatureGates(
    organization.id
  );

  if (featureGatesLoading) {
    return <Loading skeleton />;
  }

  return <Form featureGates={featureGates} />;
};

// form is separate so we only initialize the feature gates in form only when values are available
const Form = (props: { featureGates: PlainMessage<GetFeatureGatesResponse> }) => {
  const toggleFeatureGates = useMemo(
    () =>
      onlyRequireFeatureGates(
        props?.featureGates?.featureGates,
        props?.featureGates?.systemFeatureGates
      ),
    [props.featureGates]
  );

  const methods = useForm({
    defaultValues: {
      featureGates: toggleFeatureGates,
      audit: {
        actor: '',
        reason: ''
      }
    },
    resolver: zodResolver(schema)
  });

  const { watch, setValue } = methods;

  const features = watch('featureGates');

  const changes = featureGatesChanges(toggleFeatureGates, features);

  return (
    <FormProvider {...methods}>
      <div>
        <Submit changes={changes} />
        <div className='w-full flex gap-5 flex-wrap'>
          {Object.entries(
            onlyRequireFeatureGates(
              features as PlainMessage<OrganizationFeatureGates>,
              props?.featureGates?.systemFeatureGates
            )
          ).map(([feature, enabled]) => (
            <div
              key={feature}
              className='flex flex-col gap-5 w-2/12 shadow-sm p-5 my-1 border-gray-100 border-2'
            >
              <span className='text-xs font-semibold'>{feature}</span>
              <div>
                <Switch
                  checked={enabled as boolean}
                  onChange={(didTheyEnable) =>
                    setValue(
                      `featureGates.${feature as keyof PlainMessage<OrganizationFeatureGates>}`,
                      didTheyEnable
                    )
                  }
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </FormProvider>
  );
};

const Submit = (props: { changes: FeatureGatesChanges; className?: string }) => {
  const { organization } = useOrganization();

  const { isOpen, onClose, onOpen } = useDisclosure();

  const { handleSubmit, control, reset } = useFormContext<PatchFeatureGatesForm>();
  const { mutate: update, isPending } = usePatchFeatureGates();

  const fetchingData = !!queryClient.isFetching({
    queryKey: queryKeys.organizations.featureGates(organization.id).queryKey
  });

  const onSubmit = handleSubmit((data) => {
    const patch: Record<string, boolean> = {};

    for (const change of props.changes) {
      patch[change.key] = change.newValue === 'true';
    }

    update(
      {
        id: organization.id,
        featureGates: patch,
        audit: data.audit as PlainMessage<Audit>
      },
      {
        onSuccess: () => {
          queryClient.refetchQueries({
            queryKey: queryKeys.organizations.featureGates(organization.id).queryKey
          });
          onClose();
          notification.success({
            message: 'Successfully updated feature gates.',
            placement: 'bottomRight'
          });
        }
      }
    );
  });

  return (
    <>
      <Button
        icon={<FontAwesomeIcon icon={faForward} />}
        className={'mb-5 ' + props.className}
        type='primary'
        disabled={props.changes.length == 0 || fetchingData}
        onClick={onOpen}
      >
        Review Update
      </Button>

      <Button disabled={props?.changes?.length === 0} onClick={() => reset()} className='ml-5'>
        Reset Changes
      </Button>

      <Modal
        title='Review Updates'
        okText='Update'
        confirmLoading={isPending}
        onOk={onSubmit}
        open={isOpen}
        onCancel={onClose}
        classNames={{
          body: 'my-5'
        }}
      >
        <List
          header='Changes'
          bordered
          dataSource={props.changes}
          renderItem={(item) => (
            <List.Item>
              <Enabled value={item.newValue}>{item.key}</Enabled>
            </List.Item>
          )}
          className='mb-5'
        />
        <AuditForm control={control} />
      </Modal>
    </>
  );
};
