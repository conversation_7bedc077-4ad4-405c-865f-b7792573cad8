import { useListKargoInstances } from '@/hook/api';
import { ListKargo } from '@/kargo/components/table-list';
import { BasicOrganization, ListKargoInstancesRequest } from '@/lib/apiclient/aims/v1/aims_pb';

type OrganizationUsageProps = {
  organization: BasicOrganization;
};

export const OrganizationKargoList = ({ organization }: OrganizationUsageProps) => {
  const filters = new ListKargoInstancesRequest({
    filter: {
      organizationId: organization.id
    }
  });

  const { data: kargoInstances, isFetching } = useListKargoInstances(filters);

  return (
    <div className='w-full'>
      <ListKargo instances={kargoInstances} isOrganizationListing={true} isFetching={isFetching} />
    </div>
  );
};
