import { faCircleCheck, faCircleNotch } from '@fortawesome/free-solid-svg-icons';
import { Flex, Typography } from 'antd';

import { BasicOrganization } from '@/lib/apiclient/aims/v1/aims_pb';
import { OrganizationFeatureGates } from '@/lib/apiclient/types/features/v1/features_pb';
import IconLabel from '@/lib/components/shared/icon-label';
import { UsageBar } from '@/lib/components/shared/usage-bar';

export const UsageGraph = ({
  organization,
  loading,
  refreshButton,
  usage,
  featureGates
}: {
  organization: BasicOrganization;
  loading?: boolean;
  refreshButton?: React.ReactNode;
  usage: BasicOrganization['usage'];
  featureGates?: OrganizationFeatureGates;
}) => {
  return (
    <div className='mb-8'>
      <IconLabel
        className='font-semibold text-xl mb-3'
        icon={loading ? faCircleNotch : faCircleCheck}
        spin={loading}
        iconClass={loading ? 'text-blue-500' : 'text-green-500'}
      >
        Account Limits
        {refreshButton}
      </IconLabel>
      <UsageBar
        className='w-96'
        label='Organization Members'
        value={Number(usage.currentOrgMembers)}
        max={Number(organization?.quota?.maxOrgMembers)}
      />
      <UsageBar
        className='w-96'
        label='AI Cost (USD)'
        value={Number(usage.currentAiCostPerMonth)}
        max={Number(organization?.quota?.maxAiCostPerMonth)}
      />
      <Flex gap={24}>
        <Flex vertical flex={1}>
          <Typography.Title level={5} className='!mb-0 mt-2'>
            Argo CD
          </Typography.Title>
          <UsageBar
            label='Control Planes'
            value={Number(organization?.usage?.currentInstances)}
            max={Number(organization?.quota?.maxInstances)}
          />
          <UsageBar
            label='Clusters'
            value={Number(organization?.usage?.currentClusters)}
            max={Number(organization?.quota?.maxClusters)}
          />
          <UsageBar
            label='Applications'
            value={Number(organization?.usage?.currentApplications)}
            max={Number(organization?.quota?.maxApplications)}
          />
        </Flex>

        {!!featureGates?.kargo && (
          <Flex vertical flex={1}>
            <Typography.Title level={5} className='!mb-0 mt-2'>
              Kargo
            </Typography.Title>
            <UsageBar
              label='Control Planes'
              value={Number(organization?.usage?.currentKargoInstances)}
              max={Number(organization?.quota?.maxKargoInstances)}
            />
            <UsageBar
              label='Agents'
              value={Number(organization?.usage?.currentKargoAgents)}
              max={Number(organization?.quota?.maxKargoAgents)}
            />
            <UsageBar
              label='Stages'
              value={Number(organization?.usage?.currentKargoStages)}
              max={Number(organization?.quota?.maxKargoStages)}
            />
          </Flex>
        )}
      </Flex>
    </div>
  );
};
