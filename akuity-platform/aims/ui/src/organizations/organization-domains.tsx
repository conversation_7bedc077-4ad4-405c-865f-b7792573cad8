import { faTrash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { zodResolver } from '@hookform/resolvers/zod';
import { Table, Switch, Button, Input, notification, Select } from 'antd';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { useGetOrganizationDomains, useUpdateOrganizationDomains } from '@/hook/api';
import { DomainVerification } from '@/lib/apiclient/organization/v1/organization_pb';
import { Loading } from '@/lib/components/loading';
import { FieldContainer } from '@/lib/components/shared/forms';
import { zodAuditSchema } from '@/utils';

type OrganizationDomainsProps = {
  orgId: string;
};

const domainSchema = z
  .object({
    domainAliases: z.array(z.string().min(1, 'Domain alias cannot be empty')).optional(),
    domains: z.array(
      z.object({
        domain: z.string(),
        verified: z.boolean()
      })
    )
  })
  .merge(zodAuditSchema);

const defaultValues: z.infer<typeof domainSchema> = {
  domains: [],
  actor: '',
  domainAliases: [],
  reason: ''
};

const OrganizationDomains = ({ orgId }: OrganizationDomainsProps) => {
  const {
    data: organization,
    isFetching: isOrganizationFetching,
    isSuccess
  } = useGetOrganizationDomains(orgId);

  const { control, handleSubmit, formState, reset, setValue, watch } = useForm<
    z.infer<typeof domainSchema>
  >({
    defaultValues,
    resolver: zodResolver(domainSchema)
  });

  const [isModified, setIsModified] = useState(false);

  const domains = watch('domains') as DomainVerification[];
  const updateDomainsMutation = useUpdateOrganizationDomains();

  const handleToggleVerified = (index: number) => {
    const updatedDomains = domains.map((domain, idx) =>
      idx === index ? { ...domain, verified: !domain.verified } : domain
    );
    setValue('domains', updatedDomains);
    setIsModified(true);
  };

  const handleFormSubmit = handleSubmit((formData) => {
    const newDomains =
      formData.domainAliases?.map((alias) => ({
        domain: alias,
        verified: true
      })) || [];

    const combinedDomains = [...domains, ...newDomains] as DomainVerification[];
    updateDomainsMutation.mutate(
      {
        id: orgId,
        audit: { actor: formData.actor, reason: formData.reason },
        domains: combinedDomains
      },
      {
        onSuccess: (response) => {
          reset({
            domains: response.domains || [],
            actor: '',
            domainAliases: [],
            reason: ''
          });
          notification.success({ message: 'Successfully updated domains' });
          setIsModified(false);
        },
        onError: (err) => {
          notification.error({ message: 'Failed to update domains', description: err.message });
        }
      }
    );
  });

  const columns = [
    {
      title: 'Domain Aliases',
      dataIndex: 'domain',
      key: 'domain',
      width: '70%'
    },
    {
      title: 'Verified',
      key: 'verified',
      width: '15%',
      render: (_: DomainVerification, record: DomainVerification, index: number) => (
        <Switch checked={record.verified} onChange={() => handleToggleVerified(index)} />
      )
    },
    {
      title: 'Action',
      key: 'action',
      width: '15%',
      render: (_: DomainVerification, record: DomainVerification, index: number) => (
        <Button
          danger
          onClick={() => {
            const updatedDomains = domains.filter((_, idx) => idx !== index);
            setValue('domains', updatedDomains);
            setIsModified(true);
          }}
        >
          <FontAwesomeIcon icon={faTrash} />
        </Button>
      )
    }
  ];

  useEffect(() => {
    if (organization) {
      setValue('domains', organization.domains || []);
      setIsModified(false);
    }
  }, [isSuccess, organization, reset]);

  if (isOrganizationFetching || !organization) {
    return <Loading skeleton />;
  }

  return (
    <div className=' rounded-md mb-3'>
      <Button
        disabled={!isModified}
        loading={updateDomainsMutation.isPending}
        onClick={handleFormSubmit}
        type='primary'
        className='mb-4'
      >
        Save Changes
      </Button>
      <FieldContainer
        name='domainAliases'
        control={control}
        label='Domain Aliases'
        optional
        error={formState.errors.domainAliases?.[0]?.message}
      >
        {({ field }) => (
          <Select
            mode='tags'
            dropdownStyle={{ display: 'none' }}
            placeholder='Type your <domain-alias.com> and press enter'
            className='w-full'
            onChange={(value) => {
              field.onChange(value);
              setIsModified(true);
            }}
            value={field.value}
          />
        )}
      </FieldContainer>

      <h2 className='text-lg font-semibold mb-2'>Audit</h2>
      <FieldContainer control={control} label='Your Email' name='actor'>
        {({ field }) => <Input placeholder='Enter your email (e.g., <EMAIL>)' {...field} />}
      </FieldContainer>
      <FieldContainer control={control} label='Reason for this change' name='reason'>
        {({ field }) => (
          <Input.TextArea placeholder='Provide a reason for this update' {...field} />
        )}
      </FieldContainer>
      <Table<DomainVerification>
        dataSource={domains}
        columns={columns}
        rowKey='domain'
        pagination={false}
        scroll={{ y: 400 }}
      />
    </div>
  );
};

export default OrganizationDomains;
