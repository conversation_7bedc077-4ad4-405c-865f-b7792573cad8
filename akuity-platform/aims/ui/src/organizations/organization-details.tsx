import { PlainMessage } from '@bufbuild/protobuf';
import {
  faBriefcase,
  faBuilding,
  faCheck,
  faDatabase,
  faExclamationCircle,
  faKey,
  faLink,
  faList,
  faRobot,
  faServer,
  faShieldAlt,
  faTh,
  faTimes,
  faUsers,
  faUserShield
} from '@fortawesome/free-solid-svg-icons';
import { faTrash } from '@fortawesome/free-solid-svg-icons/faTrash';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { zodResolver } from '@hookform/resolvers/zod';
import { Alert, Button, Breadcrumb, Modal, Tabs } from 'antd';
import { useForm } from 'react-hook-form';
import { Link, useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { z } from 'zod';

import { AiSupportEngineerUsage } from '@/ai-support-engineer/ai-support-engineer-usage';
import { useModal } from '@/hook';
import { useSetManuallyVerified } from '@/hook/api';
import { Audit, BasicOrganization } from '@/lib/apiclient/aims/v1/aims_pb';
import { AuditLogTable } from '@/lib/components/audit-log/audit-log-table';
import { Loading } from '@/lib/components/loading';
import { AuditForm } from '@/lib/components/shared/audit-form';
import { StripeButton } from '@/lib/components/stripe-button';
import { zodAuditSchema } from '@/utils';

import { CustomRoles } from './components/roles/organization-roles';
import { Teams } from './components/teams/teams';
import Users from './components/users/organization-users';
import { Workspaces } from './components/workspace/workspaces';
import { useOrganization } from './context/organization-context';
import { DeleteOrganizationModal } from './delete-organization-modal';
import { FeatureGates } from './feature-gates';
import { NotFoundPage } from './not-found-page';
import { OrganizationArgoList } from './organization-argo';
import OrganizationDomains from './organization-domains';
import { OrganizationKargoList } from './organization-kargo';
import { OrganizationMfaReset } from './organization-mfa-reset';
import { OrganizationUsage } from './organization-usage';
import { getStripeCustomerDashboard } from './utils';

type OrganizationTabTypes = 'auditLogs' | 'usage' | 'instances' | 'members' | 'management';

const verifySchema = z.object({
  audit: zodAuditSchema
});

type VerifyForm = z.infer<typeof verifySchema>;

const VerifyOrganizationModal = ({
  visible,
  hide,
  organization,
  onSuccess
}: {
  visible: boolean;
  hide: () => void;
  organization: BasicOrganization;
  onSuccess: () => void;
}) => {
  const { mutate, isPending } = useSetManuallyVerified();
  const { control, handleSubmit } = useForm<VerifyForm>({
    resolver: zodResolver(verifySchema),
    defaultValues: {
      audit: {
        actor: '',
        reason: ''
      }
    }
  });

  const onSubmit = handleSubmit((data) => {
    mutate(
      {
        id: organization.id,
        verified: !organization.manuallyVerified,
        audit: data.audit as PlainMessage<Audit>
      },
      {
        onSuccess: () => {
          onSuccess();
          hide();
        }
      }
    );
  });

  return (
    <Modal
      title={
        organization.manuallyVerified ? 'Unverify Organization' : 'Manually Verify Organization'
      }
      visible={visible}
      onCancel={hide}
      footer={[
        <Button key='back' onClick={hide}>
          Cancel
        </Button>,
        <Button key='submit' type='primary' loading={isPending} onClick={onSubmit}>
          Confirm
        </Button>
      ]}
    >
      <p>
        Are you sure you want to {organization.manuallyVerified ? 'unverify' : 'manually verify'}{' '}
        the organization "{organization.name}"?
      </p>
      <AuditForm control={control} />
    </Modal>
  );
};

export const ConfirmFooter = ({
  onClose,
  onOk,
  danger
}: {
  onClose: (confirmed: boolean, e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
  onOk: () => void;
  danger: boolean;
}) => [
  <Button key='back' onClick={(e) => onClose(false, e)}>
    Cancel
  </Button>,
  <Button
    key='submit'
    type='primary'
    onClick={() => {
      onOk();
    }}
    danger={danger}
  >
    Confirm
  </Button>
];

export const OrganizationDetails = () => {
  useParams(); // consuming orgId from context

  const navigate = useNavigate();

  const [search, setSearch] = useSearchParams();

  const tab: OrganizationTabTypes = (search.get('tab') as OrganizationTabTypes) || 'auditLogs';
  const subtab = search.get('subtab') || '';

  const { organization, isFetching, error, refetch } = useOrganization();

  const { show: showDeleteModal } = useModal((p) => (
    <DeleteOrganizationModal
      visible={p.visible}
      hideDeleteModal={p.hide}
      organization={organization}
      navigate={navigate}
    />
  ));

  const { show: showVerifyModal } = useModal((p) => (
    <VerifyOrganizationModal {...p} organization={organization} onSuccess={() => refetch()} />
  ));

  if (isFetching) {
    return <Loading skeleton />;
  }

  if (!organization || error) {
    return <NotFoundPage />;
  }

  return (
    <div className='w-full'>
      <Breadcrumb className='mb-4 text-gray-500'>
        <Breadcrumb.Item>
          <Link to='/organizations'>Organizations</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{organization?.name}</Breadcrumb.Item>
      </Breadcrumb>
      <div className='flex items-center w-full mb-4'>
        <div className='text-2xl font-bold'>{organization?.name}</div>
        <div className='text-gray-600 ml-auto'>{organization?.id}</div>
      </div>

      {!organization?.billed && organization?.numInstances > 0 && (
        <Alert
          className='my-4'
          type='warning'
          message={`Organization cannot be deleted due to ${organization?.numInstances} associated instances. To delete this organization, delete all instances first.`}
        />
      )}

      <div className='flex items-center mb-4'>
        <Button
          className='mr-2'
          onClick={() => {
            showVerifyModal();
          }}
        >
          {organization?.manuallyVerified ? (
            <>
              <FontAwesomeIcon icon={faTimes} className='mr-2' />
              Unverify
            </>
          ) : (
            <>
              <FontAwesomeIcon icon={faCheck} className='mr-2' />
              Manually Verify
            </>
          )}
        </Button>

        {organization?.billingDetails?.customerId && (
          <div className='mr-2'>
            <StripeButton
              href={getStripeCustomerDashboard(organization.billingDetails.customerId)}
              target='_blank'
              icon={<FontAwesomeIcon icon={faLink} />}
            >
              Stripe Dashboard
            </StripeButton>
          </div>
        )}
        {organization?.misc?.eksClusters > 0 && (
          <div className='mr-2 px-4 py-2 border rounded-lg bg-gray-100 flex items-center'>
            <FontAwesomeIcon icon={faServer} className='mr-2 text-gray-600' />
            <span>AWS EKS: {organization.misc.eksClusters} Clusters</span>
          </div>
        )}
        {organization.canDelete && (
          <Button
            danger
            type='primary'
            onClick={() => showDeleteModal()}
            disabled={organization?.numInstances > 0}
          >
            <FontAwesomeIcon icon={faTrash} className='mr-2' /> Delete Organization
            <FontAwesomeIcon icon={faExclamationCircle} className='ml-2' />
          </Button>
        )}
      </div>

      <Tabs
        activeKey={tab}
        onChange={(newTab) => setSearch({ tab: newTab })}
        items={[
          {
            key: 'auditLogs',
            label: (
              <>
                <FontAwesomeIcon icon={faList} className='mr-2' />
                Audit Logs
              </>
            ),
            children: <AuditLogTable organization={organization} />
          },
          {
            key: 'members',
            label: (
              <>
                <FontAwesomeIcon icon={faUsers} className='mr-2' />
                Members
              </>
            ),
            children: <Users />
          },

          {
            key: 'usage',
            label: (
              <>
                <FontAwesomeIcon icon={faDatabase} className='mr-2' />
                Usage
              </>
            ),
            children: <OrganizationUsage organization={organization} />
          },
          {
            key: 'instances',
            label: (
              <>
                <FontAwesomeIcon icon={faServer} className='mr-2' />
                Instances
              </>
            ),
            children: (
              <Tabs
                tabPosition='left'
                activeKey={subtab || 'argo'}
                onChange={(newSubtab) => setSearch({ tab: 'instances', subtab: newSubtab })}
                items={[
                  {
                    key: 'argo',
                    label: (
                      <>
                        <FontAwesomeIcon icon={faTh} className='mr-2' />
                        Argo
                      </>
                    ),
                    children: <OrganizationArgoList organization={organization} />
                  },
                  {
                    key: 'kargo',
                    label: (
                      <>
                        <FontAwesomeIcon icon={faServer} className='mr-2' />
                        Kargo
                      </>
                    ),
                    children: <OrganizationKargoList organization={organization} />
                  }
                ]}
              />
            )
          },
          {
            key: 'management',
            label: (
              <>
                <FontAwesomeIcon icon={faBriefcase} className='mr-2' />
                Management
              </>
            ),
            children: (
              <Tabs
                tabPosition='left'
                activeKey={subtab || 'feature-gates'}
                onChange={(newSubtab) => setSearch({ tab: 'management', subtab: newSubtab })}
                items={[
                  {
                    key: 'feature-gates',
                    label: (
                      <>
                        <FontAwesomeIcon icon={faKey} className='mr-2' />
                        Feature Gates
                      </>
                    ),
                    children: <FeatureGates organization={organization} />
                  },
                  {
                    key: 'domains',
                    label: (
                      <>
                        <FontAwesomeIcon icon={faLink} className='mr-2' />
                        SSO Domain
                      </>
                    ),
                    children: <OrganizationDomains orgId={organization.id} />
                  },
                  {
                    key: 'mfa-reset',
                    label: (
                      <>
                        <FontAwesomeIcon icon={faShieldAlt} className='mr-2' />
                        MFA Reset
                      </>
                    ),
                    children: <OrganizationMfaReset />
                  },
                  {
                    key: 'workspaces',
                    label: (
                      <>
                        <FontAwesomeIcon icon={faBuilding} className='mr-2' />
                        Workspaces
                      </>
                    ),
                    children: <Workspaces organization={organization} />
                  },
                  {
                    key: 'teams',
                    label: (
                      <>
                        <FontAwesomeIcon icon={faUsers} className='mr-2' />
                        Teams
                      </>
                    ),
                    children: <Teams organization={organization} />
                  },
                  {
                    key: 'customRoles',
                    label: (
                      <>
                        <FontAwesomeIcon icon={faUserShield} className='mr-2' />
                        Custom Roles
                      </>
                    ),
                    children: <CustomRoles organization={organization} />
                  }
                ]}
              />
            )
          },
          {
            key: 'Intelligence',
            label: (
              <>
                <FontAwesomeIcon icon={faRobot} className='mr-2' />
                Intelligence
              </>
            ),
            children: <AiSupportEngineerUsage organization={organization} />
          }
        ]}
      />
    </div>
  );
};
