import { Flex } from 'antd';

import { AIMessageContext } from '@/lib/apiclient/organization/v1/organization_pb';

import { ResourceTag } from './resource-tag';

interface SuggestedContextsProps {
  suggestedContexts: AIMessageContext[];
}

export const SuggestedContexts = ({ suggestedContexts }: SuggestedContextsProps) => {
  if (!suggestedContexts || suggestedContexts.length === 0) {
    return null;
  }

  const resource = (context: AIMessageContext) => {
    if (context.kargoProject) {
      return {
        kind: 'Project',
        name: context.kargoProject?.name || ''
      };
    }

    if (context.argoCdApp) {
      return {
        kind: 'Application',
        name: context.argoCdApp?.name || ''
      };
    }

    return {
      kind: 'Namespace',
      name: context.k8sNamespace?.name || '',
      clusterId: context.k8sNamespace?.clusterId
    };
  };

  return (
    <>
      <strong className='block mt-4 text-sm'>Suggested Contexts:</strong>
      <span className='block mt-2 mb-2 text-sm'>
        Click to add the suggested context to your next message.
      </span>
      <Flex wrap gap='2px' className='mb-4'>
        {suggestedContexts.map((context, index) => {
          return (
            <div key={index} style={{ cursor: 'pointer' }} className='hover:opacity-80'>
              <ResourceTag resource={resource(context)} />
            </div>
          );
        })}
      </Flex>
    </>
  );
};
