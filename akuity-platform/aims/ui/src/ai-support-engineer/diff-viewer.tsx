import { faColumns, faSquareMinus } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, ConfigProvider, message } from 'antd';
import { useRef, useState } from 'react';
import ReactDiffViewer, { DiffMethod } from 'react-diff-viewer-continued';

import {
  AISuggestedChange,
  AIMessageContext
} from '@/lib/apiclient/organization/v1/organization_pb';

// Define the type for ReactDiffViewer component
type ReactDiffViewerType = {
  resetCodeBlocks?: () => void;
};

interface DiffViewerProps {
  suggestedChange: AISuggestedChange;
  messageId?: string;
  context?: AIMessageContext;
  messageTypeClass: string;
}

export const DiffViewer = ({ suggestedChange, messageTypeClass }: DiffViewerProps) => {
  const diffViewerRef = useRef<ReactDiffViewerType | null>(null);
  const [splitView, setSplitView] = useState<boolean>(false);

  const [, contextHolder] = message.useMessage();

  const customDiffStyles = {
    variables: {
      light: {
        diffViewerBackground: 'rgba(255,255,255,0.3)',
        diffViewerColor: '#64748b',
        gutterBackground: 'rgba(255,255,255,0.2)'
      },
      dark: {
        diffViewerBackground: '#1a1f2e'
      }
    },
    contentText: {
      fontSize: '0.85rem',
      lineHeight: '1.4'
    },
    gutter: {
      minWidth: '40px'
    },
    line: {
      padding: '2px 0'
    }
  };

  const toggleDiffCollapse = () => {
    // Use the resetCodeBlocks method if available
    diffViewerRef.current?.resetCodeBlocks?.();
  };

  const toggleSplitView = () => {
    setSplitView(!splitView);
    // Reset code blocks after changing view mode to ensure proper rendering
    if (diffViewerRef.current?.resetCodeBlocks) {
      setTimeout(() => {
        diffViewerRef.current.resetCodeBlocks?.();
      }, 0);
    }
  };

  return (
    <div className={`diff-container ${messageTypeClass}`}>
      {contextHolder}
      <div className='diff-header'>
        <Button
          icon={<FontAwesomeIcon icon={faSquareMinus} />}
          type={'default'}
          size='small'
          onClick={toggleDiffCollapse}
          className='diff-collapse-button'
        >
          Collapse
        </Button>
        <ConfigProvider theme={{ components: { Button: { defaultColor: '#64748b' } } }}>
          <Button
            icon={<FontAwesomeIcon icon={faColumns} />}
            type={splitView ? 'primary' : 'default'}
            size='small'
            onClick={toggleSplitView}
            className='view-button'
          >
            Layout
          </Button>
        </ConfigProvider>
      </div>
      <div className='react-diff-viewer-container'>
        <ReactDiffViewer
          ref={(ref) => {
            if (ref) diffViewerRef.current = ref;
          }}
          compareMethod={DiffMethod.WORDS}
          oldValue={suggestedChange.old}
          newValue={suggestedChange.new}
          splitView={splitView}
          styles={customDiffStyles}
        />
      </div>
    </div>
  );
};
