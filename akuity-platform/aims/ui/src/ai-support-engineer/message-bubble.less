.message-bubble {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1),
                0 8px 24px rgba(0, 0, 0, 0.05);
  }

  .markdown-content {
    ol {
      list-style-type: decimal;
      padding-left: 1.5em;
      margin: 0.5em 0;
    }

    ul {
      list-style-type: disc;
      padding-left: 1.5em;
      margin: 0.5em 0;
    }

    pre {
      background: rgba(0, 0, 0, 0.04);
      border-radius: 6px;
      padding: 12px;
      margin: 0.5em 0;
      overflow-x: auto;
      font-family: '<PERSON><PERSON>', 'Monaco', 'Courier New', monospace;
      font-size: 0.9em;
      line-height: 1.4;

      code {
        background: none;
        padding: 0;
        border-radius: 0;
        color: inherit;
      }
    }

    code {
      background: rgba(0, 0, 0, 0.04);
      border-radius: 4px;
      padding: 2px 6px;
      font-family: '<PERSON><PERSON>', 'Monaco', 'Courier New', monospace;
      font-size: 0.9em;
    }

    p {
      margin: 0.5em 0;
      line-height: 1.5;

      &:first-child {
        margin-top: 0;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  // Styles for ReactDiffViewer
  .diff-container {
    margin: 12px 0;
    width: 100%;
    border-radius: 6px;
    overflow: hidden;
    font-size: 0.9em;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    
    &:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
    }
    
    .diff-header {
      display: flex;
      justify-content: flex-end;
      width: 100%;
      padding: 4px 8px;
      background: rgba(0, 0, 0, 0.03);
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      position: sticky;
      top: 0;
      z-index: 10;
      
      .diff-collapse-button {
        font-size: 0.8em;
        padding: 0 8px;
        height: 24px;
        color: #64748b;
      }

      .view-button {
        margin-left: 4px;
        font-size: 0.8em;
        padding: 0 8px;
        height: 24px;
      }
    }
    
    // Apply Changes button container
    .diff-footer {
      margin: 16px 8px 8px;
      display: flex;
      justify-content: flex-end;
      position: sticky;
      bottom: 0;
      z-index: 10;
      background: inherit;
      
      .ant-btn-primary, .ant-btn-default {
        margin: 0;
        padding: 0 12px;
        height: 32px;
        border: none;
        transition: all 0.5s ease-in-out;
        background: linear-gradient(90deg, #60a5fa 0%, #93c5fd 100%);
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        
        &:hover {
          box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
          background: linear-gradient(90deg, #93c5fd 0%, #93c5fd 100%);
        }
      }

      .ant-btn-default {
        background: linear-gradient(90deg, #fa6060 0%, #fd9393 100%);
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
        &:hover {
          box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
          background: linear-gradient(90deg, #fd9393 0%, #fd9393 100%);
        }
        color: #fff;
      }
    }
    
    // Add a wrapper for ReactDiffViewer to enable horizontal scrolling
    // while keeping header and footer fixed
    .react-diff-viewer-container {
      overflow-x: auto;
      width: 100%;
    }
    
    // ReactDiffViewer specific classes
    .title-header {
      padding: 8px 12px;
      font-size: 0.85em;
      font-weight: 500;
      background: rgba(0, 0, 0, 0.04);
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }
    
    .gutter {
      background-color: rgba(0, 0, 0, 0.02);
      color: rgba(0, 0, 0, 0.4);
      text-align: right;
    }
    
    .code {
      padding: 4px 0;
    }
    
    pre {
      font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
      font-size: 0.9em;
      line-height: 1.4;
      overflow-x: auto;
    }
    
    // Adjust the table layout
    table {
      border-collapse: collapse;
      width: 100%;
      overflow-x: auto;
    }
    
    td {
      padding: 0 8px;
    }
    
    // Highlight colors for light theme
    .token.inserted {
      background-color: rgba(34, 197, 94, 0.15);
      color: rgba(0, 100, 0, 0.9);
    }
    
    .token.deleted {
      background-color: rgba(239, 68, 68, 0.15);
      color: rgba(150, 0, 0, 0.9);
    }
    
    // Add a subtle border
    border: 1px solid rgba(0, 0, 0, 0.05);
    
    // Style the diff container based on message type
    &.bot-message {
      background-color: rgba(240, 247, 255, 0.7);
    }
    
    &.user-message {
      background-color: rgba(248, 250, 252, 0.7);
    }
  }
}

// add animation for message bubble
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

// message bubble gradient background
.gradient-bg-blue {
  background: transparent;
  border-bottom-left-radius: 4px;
  color: #4b6fa8;
  position: relative;
}

.gradient-bg-gray {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1),
                0 8px 24px rgba(0, 0, 0, 0.05);
  background: linear-gradient(90deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom-right-radius: 4px;
  color: #6b7280;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    right: -10px;
    bottom: 0;
    width: 20px;
    height: 20px;
    background: #e2e8f0;
    border-right: 1px solid rgba(71, 85, 105, 0);
    border-bottom: 1px solid rgba(71, 85, 105, 0);
    clip-path: polygon(0 0, 0 100%, 100% 100%);
    transform: rotate(45deg) translateX(6px) translateY(6px);
  }
}

// Dark theme styles
.dark {
  .message-bubble {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2),
    0 8px 30px rgba(0, 0, 0, 0.15);

    .markdown-content {
      pre {
        background: rgba(0, 0, 0, 0.3);
        color: #e2e8f0;
      }

      code {
        background: rgba(0, 0, 0, 0.3);
        color: #e2e8f0;
      }
    }
    
    .diff-container {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.05);
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
        transform: translateY(-1px);
      }
      
      // Apply Changes button in dark mode
      .diff-footer {
        .ant-btn-primary {
          background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
          box-shadow: 0 2px 4px rgba(59, 130, 246, 0.4);
          
          &:hover {
            background: linear-gradient(90deg, #60a5fa 0%, #3b82f6 100%);
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.5);
          }
        }
      }
      
      // Add dark theme styles for diff header
      .diff-header {
        background: rgba(0, 0, 0, 0.2);
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        
        .diff-collapse-button {
          color: #94a3b8;
        }

        .view-button {
          color: #94a3b8;
        }
      }
      
      .title-header {
        background: rgba(0, 0, 0, 0.3);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        color: #e2e8f0;
      }
      
      .gutter {
        background-color: rgba(0, 0, 0, 0.3);
        color: rgba(255, 255, 255, 0.4);
      }
      
      // Highlight colors for dark theme
      .token.inserted {
        background-color: rgba(34, 197, 94, 0.3);
        color: rgba(100, 255, 100, 0.9);
      }
      
      .token.deleted {
        background-color: rgba(239, 68, 68, 0.3);
        color: rgba(255, 100, 100, 0.9);
      }
      
      // Style the diff container based on message type in dark mode
      &.bot-message {
        background-color: rgba(30, 36, 51, 0.7);
      }
      
      &.user-message {
        background-color: rgba(45, 55, 72, 0.7);
      }
    }
  }
  
  .gradient-bg-blue {
    background: linear-gradient(90deg, #1e2433 0%, #2d3748 100%);
    color: #90cdf4;

    &::before {
      background: #1e2433;
    }
  }

  .gradient-bg-gray {
    background: linear-gradient(90deg, #2d3748 0%, #1e2433 100%);
    color: #a0aec0;

    &::before {
      background: #1e2433;
    }
  }
} 

.message-container {
  position: relative;
  width: 100%;
  display: flex;

  // For user messages
  &.pl-16 { // This class is added to user message containers in TSX
    justify-content: flex-end;
    .message-wrapper {
      // Styles for user message wrapper to maintain original appearance
      max-width: calc(100% - 32px); // Original max-width
      min-width: 250px;             // Original min-width
      width: auto;                  // Let content define width up to max-width
    }
    .message-bubble.user-message {
      min-width: 250px; // Original min-width
    }
  }

  // For bot messages (container does not have .pl-16 after TSX change)
  &:not(.pl-16) {
    justify-content: flex-start;
    .message-wrapper {
      width: 100%;    // Bot message wrapper takes full width
      max-width: 100%;
      min-width: 0;   // No minimum width constraint
    }
  }

  // Common styles for all message wrappers
  .message-wrapper {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    position: relative;
    // width, max-width, min-width are now set conditionally above
  }

  // Common styles for all message bubbles
  .message-bubble {
    overflow-wrap: break-word;
    flex: 1; 
    position: relative;

    &.bot-message {
        min-width: 0; // Override any previous general min-width like 250px
    }

    // Feedback buttons styling (now inside .message-bubble)
    .feedback-buttons {
      display: flex;
      flex-direction: row;
      gap: 8px;
      position: absolute;
      right: 16px;
      bottom: 20px;
      pointer-events: none;
      z-index: 1;

      .feedback-button {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: 1px solid rgba(59, 130, 246, 0.2);
        background: white;
        color: #64748b;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

        &:active, &.active {
          background: #d1e7ff;
          color: #1d4ed8;
          border-color: #3b82f6;
          transform: translateY(0);
          box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
        }
      }
    }
  }
}

.thinking-process-collapse {
  margin-bottom: 12px;
  background-color: rgb(230, 243, 255);
  border: 1.5px solid rgba(59, 130, 246, 0.1);
  border-radius: 4px;
  overflow: hidden;

  .ant-collapse-header {
    padding: 0 !important;
    display: flex;
    align-items: center;
    
    .ant-collapse-expand-icon {
      padding-right: 8px !important;
      margin-right: 4px;
      font-size: 16px;
      color: rgb(93, 106, 121);
    }
    
    .ant-collapse-header-text {
      font-weight: 500;
    }
  }
  
  .thinking-collapse-icon {
    transition: transform 0.3s;
  }
  
  .ant-collapse-content-box {
    padding: 0 !important;
  }
  
  &:not(.ant-collapse-item-active) {
    .ant-collapse-header {
      background-color: rgba(230, 243, 255, 1);
      padding: 8px !important;
      border-radius: 4px;
    }
  }
}

.thinking-process-title {
  color: #4b6fa8;
}

.thinking-process-container {
  margin: 12px 0;
  border-left: 3px solid rgba(59, 130, 246, 0.2);
  padding-left: 8px;
  overflow: hidden;
  width: 100%;
}

.thinking-process-content {
  white-space: pre-wrap;
  color: rgba(75, 111, 168, 0.5);
  background-color: rgba(230, 243, 255, 0.8);
  border-radius: 4px;
  transition: all 0.5s ease-in-out;
  overflow-x: auto;
  width: 100%;

  ol {
    list-style-type: decimal;
    padding-left: 1.5em;
    margin: 0;
  }

  ul {
    list-style-type: disc;
    padding-left: 1.5em;
    margin: 0;
  }

  ol li, ul li {
    margin: 0;
    line-height: 1;
  }

  pre {
    background: rgba(0, 0, 0, 0.04);
    border-radius: 6px;
    padding: 12px;
    margin: 0.5em 0;
    overflow-x: auto;
    font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
    line-height: 1.4;

    code {
      background: none;
      padding: 0;
      border-radius: 0;
      color: inherit;
    }
  }

  code {
    background: rgba(0, 0, 0, 0.04);
    border-radius: 4px;
    padding: 2px 6px;
    font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
    font-size: 0.9em;
  }

  p {
    margin: 0.5em 0;
    line-height: 1.5;

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.dark .thinking-process-collapse {
  background-color: rgba(30, 41, 59, 0.3);
  .ant-collapse-expand-icon {
    color: rgba(209, 213, 219, 0.9);
  }
  
  &:not(.ant-collapse-item-active) {
    .ant-collapse-header {
      background-color: rgba(30, 41, 59, 0.3);
      border: 1px solid rgba(59, 130, 246, 0.2);
    }
  }
}

.dark .thinking-process-title {
  color: rgba(209, 213, 219, 0.9);
}

.dark .thinking-process-content {
  color: rgba(209, 213, 219, 0.9);
  background-color: rgba(30, 41, 59, 0.8);
  border-left-color: rgba(59, 130, 246, 0.3);

  pre {
    background: rgba(0, 0, 0, 0.3);
    color: #e2e8f0;
  }

  code {
    background: rgba(0, 0, 0, 0.3);
    color: #e2e8f0;
  }

  ol li, ul li {
    color: rgba(209, 213, 219, 0.9);
  }
} 

.dark {
  .message-container {
    .feedback-button {
      background: #2d3748;
      border-color: rgba(59, 130, 246, 0.3);
      color: #94a3b8;

      &:hover {
        background: #3a4e6a;
        color: #7cbaff;
        border-color: #60a5fa;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
      }

      &:active, &.active {
        background: #60a5fa;
        color: white;
        border-color: #60a5fa;
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.4);
      }
    }
  }
} 

.dark {
  .resource-tag {
    .ant-tooltip {
      .ant-tooltip-inner {
        background-color: #1e2433 !important;
        
        strong, span {
          color: #94a3b8 !important;
        }
      }
    }

    .ant-tag {
      color: #94a3b8 !important;
      border-color: rgba(74, 85, 104, 0.5) !important;
      background-color: rgba(45, 55, 72, 0.5) !important;

      &:hover {
        background-color: rgba(55, 65, 82, 0.7) !important;
      }

      .anticon-close {
        color: #94a3b8;
        
        &:hover {
          color: #e2e8f0;
        }
      }
    }
  }
} 

.ai-message-steps {
  .ant-steps-item-description {
    .step-content-collapse {
      width: 100%;
      &.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-header {
        padding-top: 2px !important;
        padding-bottom: 2px !important;
        padding-left: 0 !important;
      }
      &.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box {
        padding-top: 4px !important;
        padding-bottom: 2px !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
      }

      .markdown-content {
        p,
        ul,
        ol {
          margin-top: 0.25em;
          margin-bottom: 0.25em;
        }
        pre {
          padding: 8px;
          margin: 0.25em 0;
        }
      }
    }
  }
} 

.ant-steps-item-title {
  width: 100%;
}

.dark .ai-message-steps {
  .ant-steps-item-title {
    color: #cbd5e1;
    strong {
      color: #90cdf4;
    }
  }
  .ant-steps-item-description {
    color: #a0aec0;

    .step-content-collapse {
      &.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-header {
        .ant-collapse-header-text span {
          color: #cbd5e1;
        }
      }
      .markdown-content {
        color: #a0aec0;
        p, ul, ol, li {
          color: #a0aec0;
        }
      }
    }
  }
  .ant-steps-item-icon {
    color: #90cdf4;
  }
} 

.step-content-outer-box {
  background-color: rgb(230, 243, 255);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  padding: 8px 12px;
  margin-top: 4px;
  margin-bottom: 8px;
  width: 100%;

  .step-description {
    &.markdown-content {
      color: #4b6fa8;
      p,
      ul,
      ol,
      li {
        color: #4b6fa8;
      }
      max-height: 200px;
      overflow-y: auto;
      background-color: transparent;
      padding: 0;
      pre {
        padding: 8px;
        margin: 0.25em 0;
      }
      p, ul, ol {
        margin-top: 0.25em;
        margin-bottom: 0.25em;
      }
    }
  }

  &.status-process {
    background-color: #f3f4f6;
    border-color: #e5e7eb;
    .step-description {
      &.markdown-content {
        color: #6b7280;
        p,
        ul,
        ol,
        li {
          color: #6b7280;
        }
      }
    }
  }

  &.status-finish {
    background-color: rgb(230, 243, 255);
    border-color: rgba(59, 130, 246, 0.3);
    .step-description {
      &.markdown-content {
        color: #4b6fa8;
        p,
        ul,
        ol,
        li {
          color: #4b6fa8;
        }
      }
    }
  }
}

.dark .step-content-outer-box {
  background-color: rgba(30, 41, 59, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.4);

  .step-description {
    &.markdown-content {
      color: #cbd5e1;
      p,
      ul,
      ol,
      li {
        color: #cbd5e1;
      }
    }
  }

  &.status-process {
    background-color: rgba(55, 65, 82, 0.8);
    border-color: rgba(71, 85, 105, 0.8);
    .step-description {
      &.markdown-content {
        color: #9ca3af;
        p,
        ul,
        ol,
        li {
          color: #9ca3af;
        }
      }
    }
  }

  &.status-finish {
    background-color: rgba(30, 41, 59, 0.8);
    border-color: rgba(59, 130, 246, 0.4);
    .step-description {
      &.markdown-content {
        color: #cbd5e1;
        p,
        ul,
        ol,
        li {
          color: #cbd5e1;
        }
      }
    }
  }
}

.step-description {
  &.markdown-content {
    max-height: 200px;
    border-radius: 8px;
    overflow-y: auto;
    p,
    ul,
    ol {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
    pre {
      padding: 8px;
      margin: 0.25em 0;
    }
  }
}

.dark .step-description {
  color: #a0aec0;
  p, ul, ol, li {
    color: #a0aec0;
  }
} 

// Styling for step status - REVISED
.ai-message-steps {
  .ant-steps-item-process .ant-steps-item-title strong {
    color: #9ca3af; 
  }
  .ant-steps-item-finish .ant-steps-item-title strong {
    color: #4b6fa8; 
  }
  .ant-steps-item-error .ant-steps-item-title strong {
    color: #ef4444; 
  }
}

.dark .ai-message-steps {
  .ant-steps-item-process .ant-steps-item-title strong {
    color: #94a3b8; 
  }
  .ant-steps-item-finish .ant-steps-item-title strong {
    color: #90cdf4; 
  }
  .ant-steps-item-error .ant-steps-item-title strong {
    color: #f87171; 
  }
}