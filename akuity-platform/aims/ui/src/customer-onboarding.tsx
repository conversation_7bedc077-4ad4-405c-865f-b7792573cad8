import { PlainMessage } from '@bufbuild/protobuf';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Input, Alert, AutoComplete } from 'antd';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { z } from 'zod';

import { useListUnbilledOrganizations, useOnboardManualCustomer } from '@/hook/api';
import { AuditForm } from '@/lib/components/shared/audit-form';
import { FieldContainer } from '@/lib/components/shared/forms';
import { errorToString, zodAuditSchema } from '@/utils';

import { Audit, BasicOrganization, Customer } from './lib/apiclient/aims/v1/aims_pb';
import { FormSizer } from './lib/components/form-sizer';
import { Header } from './lib/components/header';

const schema = z.object({
  organizationName: z.string().min(1, 'Organization name is required.'),
  stripeId: z.string().optional(),
  billingName: z.string().optional(),
  billingEmail: z.string().email('Invalid email address').or(z.literal('')).optional(),
  audit: zodAuditSchema
});

type OnboardingForm = z.infer<typeof schema>;

export const Onboarding = () => {
  const navigate = useNavigate();

  const { control, handleSubmit } = useForm<OnboardingForm>({
    defaultValues: {
      organizationName: '',
      stripeId: '',
      billingName: '',
      billingEmail: '',
      audit: {
        actor: '',
        reason: ''
      }
    },
    resolver: zodResolver(schema)
  });

  const { mutate, isPending, error, isError, isSuccess } = useOnboardManualCustomer();

  const { data: orgs = [] } = useListUnbilledOrganizations();

  const organizations: Record<string, BasicOrganization> = orgs.reduce(
    (acc, org) => {
      if (!org.billed) {
        acc[org.name] = org;
      }
      return acc;
    },
    {} as Record<string, BasicOrganization>
  );

  const onSubmit: SubmitHandler<OnboardingForm> = (vals) => {
    const { organizationName, audit, ...customerData } = vals;
    const customer = customerData as Customer;
    const id = organizations[organizationName as string]?.id;

    if (!id) {
      throw new Error('Organization not found or already has billing entry.');
    }
    customer.organizationId = id;
    mutate(
      { customer, audit: audit as PlainMessage<Audit> },
      {
        onSuccess: () => {
          setTimeout(() => {
            navigate('/billing');
          }, 4000);
        }
      }
    );
  };

  return (
    <div className='block'>
      <Header
        description={`Fill out the form below to manually associate a paying customer's Stripe account with their Akuity Platform account. Please note: customers who are onboarded here will be unable to self-manage their Billing details.`}
      >
        Customer Onboarding
      </Header>
      <FormSizer>
        <FieldContainer label='Organization Name' name='organizationName' control={control}>
          {({ field }) => (
            <AutoComplete
              {...field}
              placeholder='my-org'
              options={Object.values(organizations || [])
                .filter((o) => !o.billed)
                .map((o) => {
                  return { value: o.name };
                })}
              className='w-full'
            />
          )}
        </FieldContainer>
        <FieldContainer
          label='Stripe ID'
          name='stripeId'
          control={control}
          description='If Stripe ID is not provided, a new Stripe customer will be created.'
          optional
        >
          {({ field }) => <Input {...field} placeholder='cus_123456' />}
        </FieldContainer>
        <FieldContainer label='Billing Name' name='billingName' control={control} optional>
          {({ field }) => <Input {...field} placeholder='John Doe' />}
        </FieldContainer>
        <FieldContainer label='Billing Email' name='billingEmail' control={control} optional>
          {({ field }) => <Input {...field} placeholder='<EMAIL>' />}
        </FieldContainer>
        <AuditForm control={control} />
        <Button onClick={handleSubmit(onSubmit)} type='primary' loading={isPending}>
          Submit
        </Button>
        {isError && (
          <div className='mt-4'>
            <Alert type='error' className='mt-4' message={errorToString(error)} />
          </div>
        )}
        {isSuccess && (
          <Alert type='success' className='mt-4' message='Customer successfully created.' />
        )}
      </FormSizer>
    </div>
  );
};
