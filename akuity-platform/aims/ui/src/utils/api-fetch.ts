// lib/apiclient/api-fetch.ts
import { JsonValue } from '@bufbuild/protobuf';

import { RpcStatus } from '@/types';
import { refreshToken } from '@/utils';

import { UIVersionManager } from '../lib/components/shared/ui-version';

const versionManager = new UIVersionManager();

export interface StatusResponse {
  status: number;
  message: string;
}

export const injectAuthHeaders = (token?: string, opts?: RequestInit): RequestInit => {
  const headers = {
    ...opts?.headers,
    ...(token ? { Authorization: `Bearer ${token}` } : {})
  };
  return { ...opts, headers };
};

interface FetchOptions<T> extends RequestInit {
  responseReader?: (res: Response) => Promise<T>;
  ignoreJSONReadFromResponse?: boolean;
}

export async function apiFetch<T = JsonValue>(
  endpoint: string,
  options?: FetchOptions<T>
): Promise<T> {
  const { responseReader, ignoreJSONReadFromResponse = false, ...fetchOpts } = options || {};

  const api = `/api/${endpoint}`;
  let retry = false;

  const fetchWithRetry = async (): Promise<T> => {
    try {
      const res = await fetch(api, fetchOpts);

      // Check API version
      const latestVersion = res.headers.get('X-Version') || 'development';
      setTimeout(() => {
        versionManager.checkAndTriggerVersionUpdateNotification(latestVersion);
      });

      if (res.status >= 200 && res.status < 300) {
        const defaultReader = async (res: Response) => {
          try {
            return await res.json();
          } catch (err) {
            if (ignoreJSONReadFromResponse) {
              return { status: res.status, message: res.statusText } as unknown as T;
            }
            throw err;
          }
        };

        const finalReader = responseReader || defaultReader;
        return await finalReader(res);
      }

      try {
        const errorData = await res.json();

        throw errorData;
      } catch (jsonError) {
        if (jsonError instanceof SyntaxError) {
          throw { status: res.status, message: res.statusText };
        } else {
          throw jsonError;
        }
      }
    } catch (err) {
      const error = err as StatusResponse | RpcStatus;

      if ((error as StatusResponse)?.status === 401 || (error as RpcStatus)?.code === 16) {
        if (!retry) {
          await refreshToken();
          retry = true;
          return fetchWithRetry();
        } else {
          const returnURL = encodeURIComponent(window.location.pathname + window.location.search);
          window.location.href = `/api/auth/login?return_url=${returnURL}`;
          await new Promise((resolve) => setTimeout(() => resolve(null), 5000));
        }
      }

      throw error;
    }
  };

  return fetchWithRetry();
}

export function withJsonParser<Body, T>(convert: (body: Body) => T) {
  return async (res: Response) => {
    const data = (await res.json()) as Body;
    return convert(data);
  };
}
