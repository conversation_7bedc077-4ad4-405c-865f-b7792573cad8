import { PlainMessage, Timestamp } from '@bufbuild/protobuf';
import { marked } from 'marked';
import moment, { Moment } from 'moment';
import { generatePath } from 'react-router-dom';
import { z } from 'zod';

import { Incident } from '@/lib/apiclient/organization/v1/organization_pb';
import { TimeRangeOption } from '@/types';

const { isMoment } = moment;

export const delay = (delayInms: number) => {
  return new Promise((resolve) => setTimeout(resolve, delayInms));
};

export const errorToString = (err: unknown): string => {
  if (typeof err === 'string') return err;
  if ((err as Error)?.message) return (err as Error).message;
  return JSON.stringify(err);
};

export const zodAuditSchema = z
  .object({
    actor: z.string().email(),
    reason: z.string().min(1, 'Reason is required.')
  })
  .required();

export const capitalize = (str: string): string => {
  if (!str?.length) return '';
  return str?.charAt(0).toUpperCase() + str.slice(1)?.toLowerCase();
};
export const firstCharacterUppercase = (s: string) => `${s.charAt(0).toUpperCase()}${s.slice(1)}`;

export const pluralize = (count: number, label: string): string => {
  return `${label}${count === 1 ? '' : 's'}`;
};

let refreshTokenRequest: Promise<Response | void> | null = null;

export const refreshToken = async () => {
  // Avoid parallel requests
  if (refreshTokenRequest === null) {
    refreshTokenRequest = fetch('/api/v1/auth/refresh-token', { method: 'POST' })
      .then((res) => {
        if (!res.ok) {
          throw new Error('Failed to refresh token');
        }
      })
      .finally(() => {
        // Clean up the promise
        refreshTokenRequest = null;
      });
  }

  return refreshTokenRequest;
};

export const pick = <T extends object, Keys extends Array<keyof T>>(obj: T, keys: Keys) => {
  const _obj: unknown = {};
  for (const key of keys) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      _obj[key] = obj[key];
    }
  }
  return _obj as {
    [key in Keys[number]]: T[key];
  };
};

export const omit = <T extends object, Keys extends Array<keyof T>>(obj: T, excludeKeys: Keys) =>
  pick<T, Keys>(
    obj,
    Object.keys(obj).filter((k) => !excludeKeys.includes(k as keyof T)) as Keys
  ) as {
    [key in keyof T]: T[key];
  };

export const plural = (arr?: Array<unknown>) => (arr?.length ?? 0) > 1;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function debounce<F extends (...params: any[]) => void>(fn: F, delay: number) {
  let timeoutID: number = null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return function (this: any, ...args: any[]) {
    clearTimeout(timeoutID);
    timeoutID = window.setTimeout(() => fn.apply(this, args), delay);
  } as F;
}

export const constructBillingPath = (orgId: string) => {
  return `${generatePath('/organizations/:id', {
    id: orgId
  })}?tab=billing`;
};

export const smallObjectDeepCompare = (obj1: unknown, obj2: unknown): boolean => {
  if (obj1 === obj2) return true;
  if (!obj1 || !obj2) return false;
  if (typeof obj1 !== 'object' || typeof obj2 !== 'object') return false;

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  return keys1.every((key) => {
    const val1 = (obj1 as Record<string, unknown>)[key];
    const val2 = (obj2 as Record<string, unknown>)[key];

    if (typeof val1 === 'object' && typeof val2 === 'object') {
      return smallObjectDeepCompare(val1, val2);
    }

    return val1 === val2;
  });
};

export const booleanFromString = () =>
  z.preprocess((val) => {
    if (val === 'true') return true;
    if (val === 'false') return false;
    return val;
  }, z.boolean().optional());

export type ZodShapeFor<T> = Partial<{ [K in keyof T]: z.ZodTypeAny }>;

export const timestampToDate = (timestamp?: Timestamp): Date => {
  if (!timestamp) {
    return new Date();
  }

  try {
    if (
      timestamp.seconds === undefined ||
      timestamp.seconds === null ||
      Number.isNaN(Number(timestamp.seconds))
    ) {
      return new Date();
    }

    const date = new Date(Number(timestamp.seconds) * 1000 + (timestamp.nanos || 0) / 1000000);

    if (isNaN(date.getTime())) {
      return new Date();
    }
    return date;
  } catch {
    return new Date();
  }
};

export const renderMarkdown = (markdown: string) => {
  const result = marked.parse(markdown || '') as string;
  return result.trim();
};

export const getTimeRangeDates = (option: TimeRangeOption): [Moment, Moment] | null => {
  const end = moment().endOf('day');
  let start: Moment;

  switch (option) {
    case '7-days':
      start = moment().subtract(6, 'days').startOf('day');
      break;
    case '1-month':
      start = moment().subtract(1, 'month').startOf('day');
      break;
    case '90-days':
      start = moment().subtract(90, 'days').startOf('day');
      break;
    default:
      return null;
  }
  return [start, end];
};

export const determineTimeRange = (startTime?: string, endTime?: string): TimeRangeOption => {
  if (!startTime || !endTime) return '7-days';

  const startMoment = moment(startTime);
  const endMoment = moment(endTime);

  const predefinedRanges: TimeRangeOption[] = ['7-days', '1-month', '90-days'];

  for (const range of predefinedRanges) {
    const dates = getTimeRangeDates(range);
    if (dates && startMoment.isSame(dates[0], 'day') && endMoment.isSame(dates[1], 'day')) {
      return range;
    }
  }

  return 'custom';
};

export function formatTime(
  time: Moment | Timestamp | Date | string | number,
  opts?: { useUTCTime?: boolean; format?: string; fromNow?: boolean }
) {
  if (!time) {
    return 'N/A';
  }
  if (time instanceof Timestamp) {
    time = time.toDate();
  }
  const momentTime = isMoment(time) ? time : moment(time);
  const adjustedTime = opts?.useUTCTime ? momentTime.utc() : momentTime.local();
  return opts?.fromNow
    ? adjustedTime.fromNow()
    : adjustedTime.format(opts?.format ?? 'YYYY-MM-DD HH:mm:ss Z');
}

export const isValidJSON = (str: string) => {
  try {
    JSON.parse(str);
    return true;
  } catch {
    return false;
  }
};

export const hasValue = (value: unknown): boolean => {
  if (value === null || value === undefined) return false;
  if (typeof value === 'string' && value.trim() === '') return false;
  if (typeof value === 'object' && value !== null && Object.keys(value as object).length === 0)
    return false;
  return true;
};

export const filterEmptyValues = (obj: Record<string, unknown>): Record<string, unknown> => {
  const filtered: Record<string, unknown> = {};
  for (const [key, value] of Object.entries(obj)) {
    if (hasValue(value)) {
      filtered[key] = value;
    }
  }
  return filtered;
};

export const cleanForDisplay = (obj: unknown): object | unknown[] | string | boolean | number => {
  if (obj === null || obj === undefined) return {};

  if (typeof obj === 'string') {
    return obj.trim() === '' ? {} : obj;
  }

  if (typeof obj === 'boolean' || typeof obj === 'number') {
    return obj;
  }

  if (Array.isArray(obj)) {
    const cleaned = obj.map(cleanForDisplay).filter((item) => {
      if (typeof item === 'object' && item !== null) {
        return Object.keys(item).length > 0;
      }
      return item !== undefined && item !== '' && item !== null;
    });
    return cleaned.length > 0 ? cleaned : [];
  }

  if (typeof obj === 'object' && obj !== null) {
    const cleaned: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(obj as Record<string, unknown>)) {
      const cleanedValue = cleanForDisplay(value);
      if (typeof cleanedValue === 'object' && cleanedValue !== null) {
        if (Object.keys(cleanedValue).length > 0) {
          cleaned[key] = cleanedValue;
        }
      } else if (cleanedValue !== undefined && cleanedValue !== '' && cleanedValue !== null) {
        cleaned[key] = cleanedValue;
      }
    }
    return Object.keys(cleaned).length > 0 ? cleaned : {};
  }

  return {};
};

export const copyToClipboard = async (text: string): Promise<void> => {
  try {
    await navigator.clipboard.writeText(text);
  } catch {
    // ignore clipboard errors
  }
};

export const formatSnakeCaseToTitleCase = (key: string): string => {
  return key.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
};

/**
 * Formats the title for incidents to include the INC-xxx prefix
 * @param title - The original title
 * @param incident - The incident object containing the number
 * @returns Formatted title with INC-xxx prefix for incidents, or original title for non-incidents
 */
export const formatIncidentTitle = (title: string, incident?: PlainMessage<Incident>): string => {
  if (incident && incident.incidentNumber && incident.incidentNumber > 0) {
    return `INC-${incident.incidentNumber}: ${title}`;
  }
  return title;
};
