import { ObjectFilter } from '@/lib/apiclient/organization/v1/organization_pb';
export const ARGO = 'argo';

export const KARGO = 'kargo';

export type InstanceType = typeof ARGO | typeof KARGO;

export type PaginationPaylod = {
  limit?: number;
  offset?: number;
};

export type Features = 'ui-settings-v2';

export type AuditFilterAction =
  | 'created'
  | 'updated'
  | 'deleted'
  | 'action-ran'
  | 'sync-started'
  | 'rollback-started';

export type AuditFilterObjectType =
  | 'organization_invite'
  | 'member'
  | 'argocd_instance'
  | 'argocd_cluster'
  | 'argocd_project'
  | 'argocd_application'
  | 'k8s_resource'
  | 'kargo_instance'
  | 'kargo_agent'
  | 'kargo_promotion'
  | 'kargo_freight'
  | 'custom_roles'
  | 'notification_cfg'
  | 'api_keys'
  | 'addons'
  | 'addon_marketplace_install'
  | 'addon_repos';

export type RawAuditFilters = {
  // Pagination
  limit: number;
  offset: number;
  // Time
  start_time: string;
  end_time: string;
  // Object
  k8s_resource?: ObjectFilter | null;
  argocd_application?: ObjectFilter | null;
  argocd_cluster?: ObjectFilter | null;
  argocd_instance?: ObjectFilter | null;
  argocd_project?: ObjectFilter | null;
  member?: ObjectFilter | null;
  organization_invite?: ObjectFilter | null;
  kargo_instance?: ObjectFilter | null;
  kargo_agent?: ObjectFilter | null;
  kargo_promotion?: ObjectFilter | null;
  kargo_freight?: ObjectFilter | null;
  custom_roles?: ObjectFilter | null;
  notification_cfg?: ObjectFilter | null;
  api_keys?: ObjectFilter | null;
  addons?: ObjectFilter | null;
  addon_repos?: ObjectFilter | null;
  addon_marketplace_install?: ObjectFilter | null;
  // Actor
  actor_id: string[];
  actor_type: string[];
  // Action
  action: string[];
};

export type TFilterScopes = 'instance' | 'kargo_instance';

export type PlanTier =
  | 'starter_v2'
  | 'starter'
  | 'professional'
  | 'professional_plus'
  | 'professional_plus_trial'
  | 'enterprise'
  | 'unknown'
  | 'custom';

export type ResourceOperation = {
  can?: boolean;
  resources?: string[];
};

export enum Permissions {
  get = 'get',
  create = 'create',
  update = 'update',
  delete = 'delete'
}

export interface RpcStatus {
  /** @format int32 */
  code?: number;
  message?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  details?: any[];
}

export interface HealthV1Status {
  code?: HealthV1StatusCode;
  message?: string;
}

/** @default "STATUS_CODE_UNSPECIFIED" */
export enum HealthV1StatusCode {
  STATUS_CODE_UNSPECIFIED = 'STATUS_CODE_UNSPECIFIED',
  STATUS_CODE_HEALTHY = 'STATUS_CODE_HEALTHY',
  STATUS_CODE_PROGRESSING = 'STATUS_CODE_PROGRESSING',
  STATUS_CODE_DEGRADED = 'STATUS_CODE_DEGRADED',
  STATUS_CODE_UNKNOWN = 'STATUS_CODE_UNKNOWN'
}

export interface ReconciliationV1Status {
  code?: ReconciliationV1StatusCode;
  message?: string;
}

/** @default "STATUS_CODE_UNSPECIFIED" */
export enum ReconciliationV1StatusCode {
  STATUS_CODE_UNSPECIFIED = 'STATUS_CODE_UNSPECIFIED',
  STATUS_CODE_SUCCESSFUL = 'STATUS_CODE_SUCCESSFUL',
  STATUS_CODE_PROGRESSING = 'STATUS_CODE_PROGRESSING',
  STATUS_CODE_FAILED = 'STATUS_CODE_FAILED'
}

export type TimeRangeOption = '7-days' | '1-month' | '90-days' | 'custom';
