import { faForward } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Input } from 'antd';
import React, { Suspense } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

// https://github.com/zenoamaro/react-quill/issues/989
import 'react-quill-new/dist/quill.snow.css';
import { FieldContainer } from '@/lib/components/shared/forms';

const ReactQuill = React.lazy(() => import('react-quill-new'));

const cveForm = z.object({
  subject: z.string().min(1, 'Subject is required.'),
  title: z.string().min(1, 'Title is required.'),
  body: z.string().min(1, 'Body is required.')
});

// Define the form type from the schema
type CveFormType = z.infer<typeof cveForm>;

export const NotificationCustomInput = ({ onSubmit }: { onSubmit(state: CveFormType): void }) => {
  const form = useForm<CveFormType>({
    defaultValues: {
      subject: 'CVE Alert for ArgoCD',
      title: `⚠️ CVE`,
      body: ''
    },
    resolver: zodResolver(cveForm)
  });

  const handleSubmit = form.handleSubmit(onSubmit);

  return (
    <div className='rounded-2xl bg-gray-50 p-5 ring-1 ring-inset ring-gray-900/5'>
      <h2 className='font-semibold'>Send to AKP portal and Email: </h2>
      <FieldContainer control={form.control} name='subject' label='Subject' wrapperClassName='my-3'>
        {({ field }) => (
          <Input value={field.value} onChange={(e) => field.onChange(e.target.value)} />
        )}
      </FieldContainer>

      <FieldContainer control={form.control} name='title' label='Title' wrapperClassName='my-3'>
        {({ field }) => (
          <Input value={field.value} onChange={(e) => field.onChange(e.target.value)} />
        )}
      </FieldContainer>

      <FieldContainer control={form.control} name='body' label='Body' wrapperClassName='my-3'>
        {({ field }) => (
          <Suspense fallback={<div>Loading Editor...</div>}>
            <ReactQuill
              placeholder={`Today a new CVE for Argo CD will be released regarding an exploit of the admin account. We've noticed that your admin account is still active would ask that you either disable the admin account or upgrade to a patched version.

You can disable the Admin account in Argo CD by going to the Settings tab, then "System Accounts" and disabling the Admin Account toggle.

The patched versions are v2.10.4, v2.9.9, v2.8.13.`}
              formats={['header', 'bold', 'italic', 'underline', 'indent', 'link']}
              modules={{
                toolbar: {
                  container: [
                    [{ header: [1, 2, 3, 4] }],
                    ['bold', 'italic', 'underline', 'blockquote'],
                    [{ indent: '-1' }, { indent: '+1' }],
                    ['link']
                  ]
                }
              }}
              style={{ height: '156px' }}
              className='mb-5 w-full'
              value={field.value}
              onChange={field.onChange}
            />
          </Suspense>
        )}
      </FieldContainer>
      <Button
        type='primary'
        className='mt-5'
        icon={<FontAwesomeIcon icon={faForward} />}
        onClick={handleSubmit}
      >
        Test and Send
      </Button>
    </div>
  );
};
