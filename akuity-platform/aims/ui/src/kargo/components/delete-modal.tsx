import { PlainMessage } from '@bufbuild/protobuf';
import { faTrash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { zodResolver } from '@hookform/resolvers/zod';
import { Modal, Button, Input, Tag } from 'antd';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { queryClient } from '@/app';
import { queryKeys } from '@/constants/query-keys';
import { useDisclosure } from '@/hook';
import { useDeleteKargoInstance } from '@/hook/api';
import { Audit, ListKargoInstancesResponse } from '@/lib/apiclient/aims/v1/aims_pb';
import { AuditForm } from '@/lib/components/shared/audit-form';
import { zodAuditSchema } from '@/utils';

const schema = z.object({
  name: z.string(),
  audit: zodAuditSchema
});

type DeleteKargoForm = z.infer<typeof schema>;

type PlainKargoInstancesResponse = PlainMessage<ListKargoInstancesResponse>;

export const DeleteKargo = (props: { agent: PlainKargoInstancesResponse['instances'][0] }) => {
  const { isOpen, onOpen, onClose } = useDisclosure();

  const { mutate: deleteInstance, isPending: deleting } = useDeleteKargoInstance();

  const { control, handleSubmit, watch, setValue } = useForm<DeleteKargoForm>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: '',
      audit: {
        actor: '',
        reason: ''
      }
    }
  });
  const name = watch('name');
  const audit = watch('audit');

  const onSubmit = handleSubmit((data) => {
    deleteInstance(
      {
        id: props.agent?.instance?.id,
        audit: data.audit as PlainMessage<Audit>
      },
      {
        onSuccess: () => {
          onClose();
          queryClient.refetchQueries({
            queryKey: queryKeys.instances.kargoInstances().queryKey
          });
        }
      }
    );
  });

  return (
    <>
      <Button danger onClick={onOpen}>
        <FontAwesomeIcon icon={faTrash} />
      </Button>
      <Modal
        open={isOpen}
        onCancel={onClose}
        footer={
          <>
            <Button
              onClick={onSubmit}
              loading={deleting}
              disabled={name !== props.agent?.instance?.name || !audit.actor || !audit.reason}
              danger
              type='primary'
            >
              Yes, Delete
            </Button>
            <Button
              onClick={() => {
                onClose();
              }}
            >
              No
            </Button>
          </>
        }
      >
        <div>
          <div className='mb-6 font-semibold text-xl'>
            Are you sure you want to delete the Kargo instance{' '}
            <Tag className='text-xl font-mono'>{props.agent?.instance?.name}</Tag>?
          </div>
          <div>
            This will <b>permanently</b> delete the instance from the{' '}
            <Tag className='font-mono text-md'>{props.agent?.organization?.name}</Tag> organization.
            Please note: It may take a few moments for the platform controller to delete the
            instance once it is marked for deletion. If the instance still appears in the list, try
            waiting a few minutes and refreshing the page.
          </div>
          <div className='mt-4'>
            To confirm, please type the name of the instance below:
            <Input
              placeholder={props?.agent?.instance?.name || 'kargo'}
              onChange={(e) => setValue('name', e.target.value)}
            />
          </div>
        </div>
        <AuditForm control={control} />
      </Modal>
    </>
  );
};
