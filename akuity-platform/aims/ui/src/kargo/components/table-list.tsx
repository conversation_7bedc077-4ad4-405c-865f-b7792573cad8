import { PlainMessage } from '@bufbuild/protobuf';
import { faCircleNotch } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Table } from 'antd';
import moment from 'moment';
import { Link } from 'react-router-dom';

import { ListKargoInstancesResponse } from '@/lib/apiclient/aims/v1/aims_pb';
import { getAuditFilterString } from '@/lib/components/audit-log/filters/utils';
import { DisplayTime } from '@/lib/components/display-time';
import { HealthState } from '@/lib/components/shared/health-state';
import { KARGO } from '@/types';

import { DeleteKargo } from './delete-modal';

type PlainKargoInstancesResponse = PlainMessage<ListKargoInstancesResponse>;

export const ListKargo = ({
  instances,
  isOrganizationListing = false,
  isFetching
}: {
  instances: PlainMessage<ListKargoInstancesResponse>;
  isOrganizationListing?: boolean;
  isFetching?: boolean;
}) => (
  <Table
    loading={isFetching}
    dataSource={instances?.instances}
    columns={[
      {
        key: 'id',
        title: 'Name / ID',
        dataIndex: 'instance',
        render: (value: PlainKargoInstancesResponse['instances'][0]['instance']) => (
          <>
            <Link to={`/kargo/instances/${value?.id}`} className='font-bold text-blue-500'>
              {value?.name}
            </Link>
            <div className='text-xs text-gray-500 mt-1'>ID: {value?.id}</div>
          </>
        )
      },
      ...(!isOrganizationListing
        ? [
            {
              key: 'organization',
              title: 'Organization',
              dataIndex: 'organization',
              render: (record: PlainKargoInstancesResponse['instances'][0]['organization']) => (
                <div>
                  <Link
                    to={`/organizations/${record.id}?${getAuditFilterString(KARGO)}`}
                    className='font-bold text-blue-500'
                  >
                    {record.name}
                  </Link>
                  <div className='text-xs text-gray-500 mt-1'>ID: {record.id}</div>
                </div>
              )
            }
          ]
        : []),

      {
        key: 'status',
        title: 'Status',
        dataIndex: 'instance',
        render: (value: PlainKargoInstancesResponse['instances'][0]['instance']) => (
          <div>
            {/* @ts-expect-error had split types (for AIMS and UI) but from same proto */}
            <HealthState health={value.healthStatus} deletionTimestamp={value.deleteTime} />
          </div>
        )
      },

      {
        key: 'project',
        title: 'Projects',
        dataIndex: ['statusInfo', 'kargoStats'],
        render: (value) => <div>{value?.projectCount ?? 0}</div>,
        sorter: (a, b) => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const aCount = (a?.statusInfo as any)?.kargoStats?.projectCount ?? 0;
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const bCount = (b?.statusInfo as any)?.kargoStats?.projectCount ?? 0;
          return aCount - bCount;
        }
      },
      {
        key: 'stage',
        title: 'Stages',
        dataIndex: ['statusInfo', 'kargoStats'],
        render: (value) => <div>{value?.stageCount ?? 0}</div>,
        sorter: (a, b) => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const aCount = (a?.statusInfo as any)?.kargoStats?.stageCount ?? 0;
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const bCount = (b?.statusInfo as any)?.kargoStats?.stageCount ?? 0;
          return aCount - bCount;
        }
      },

      {
        key: 'version',
        title: 'Version',
        dataIndex: 'instance',
        render: (value: PlainKargoInstancesResponse['instances'][0]['instance']) => (
          <div>{value.version}</div>
        )
      },
      {
        key: 'creation',
        title: 'Creation Time',
        dataIndex: 'creationTimestamp',
        render: (value: string) => <DisplayTime time={new Date(value).toISOString()} />,
        sorter: {
          compare: (a, b) => {
            const aCreateTime = a?.creationTimestamp as unknown as string;
            const bCreateTime = b?.creationTimestamp as unknown as string;

            return moment(new Date(aCreateTime)).diff(new Date(bCreateTime));
          }
        }
      },
      {
        key: 'action',
        render: (_, agent) => {
          if (agent?.organization?.billed) {
            return null;
          }

          if (agent?.instance?.deleteTime) {
            return (
              <div className='flex items-center'>
                <FontAwesomeIcon icon={faCircleNotch} spin className='mr-2' />
                Deleting...
              </div>
            );
          }

          return <DeleteKargo agent={agent} />;
        }
      }
    ]}
  />
);
