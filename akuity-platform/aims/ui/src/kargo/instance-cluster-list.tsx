import { PlainMessage } from '@bufbuild/protobuf';
import { faDownload } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import JsonView from '@uiw/react-json-view';
import { Table, Button } from 'antd';
import { omit } from 'lodash';
import { useContext } from 'react';

import { useModal } from '@/hook';
import { useListKargoInstanceClusters } from '@/hook/api';
import { KargoAgent } from '@/lib/apiclient/kargo/v1/kargo_pb';
import { DownloadManifestModal } from '@/lib/components/download-manifest-modal';
import { Loading } from '@/lib/components/loading';
import { HealthState } from '@/lib/components/shared/health-state';

import { KargoInstanceContext } from './context/instance';

type TKargoAgent = PlainMessage<KargoAgent>;

const Clusters = () => {
  const data = useContext(KargoInstanceContext);
  const { show } = useModal();

  const kargoInstance = data?.instance?.instance;

  const { data: clustersData, isFetching } = useListKargoInstanceClusters(
    kargoInstance?.instance?.id
  );

  if (isFetching) {
    return <Loading />;
  }

  return (
    <Table
      dataSource={clustersData?.agents || []}
      columns={[
        {
          key: 'id',
          title: 'Id/Name',
          render: (value: TKargoAgent) => (
            <>
              <b>{value?.name}</b>
              <div className='text-gray-400'>{value?.id}</div>
            </>
          )
        },
        {
          title: 'Status',
          width: 120,
          render: (_, agent) => (
            <>
              <HealthState
                deletionTimestamp={agent.deleteTime}
                // @ts-expect-error split types but same proto
                health={agent.healthStatus}
                className='text-xs'
              />
            </>
          )
        },
        {
          title: 'Agent Version',
          dataIndex: ['agentState', 'version'],
          width: 130,
          render: (val) => val || '-'
        },
        {
          title: 'Kargo Version',
          dataIndex: ['agentState', 'kargoVersion'],
          width: 150,
          render: (val) => val || '-'
        },
        { title: 'Namespace', dataIndex: ['data', 'namespace'], width: 120 },
        {
          title: 'Remote Argo CD',
          width: 150,
          render: (_, agent) => {
            if (!agent?.data?.remoteArgocd) {
              return '-';
            }

            return (
              <a
                href={`/instances/settings/${agent?.data?.remoteArgocd}`}
                className='text-blue-400'
                target='_blank'
              >
                {agent?.data?.remoteArgocd}
              </a>
            );
          }
        },
        {
          title: 'Data',
          render: (_, agent) => (
            <JsonView collapsed value={omit(agent?.data, ['namespace', 'remoteArgocd'])} />
          )
        },
        {
          title: 'Manifest',
          width: 120,
          render: (_, agent: TKargoAgent) => (
            <Button
              type='link'
              size='small'
              icon={<FontAwesomeIcon icon={faDownload} />}
              onClick={() =>
                show((modalProps) => (
                  <DownloadManifestModal
                    {...modalProps}
                    instanceId={kargoInstance?.instance?.id}
                    clusterId={agent.id}
                    clusterName={agent.name}
                    type='kargo'
                  />
                ))
              }
            >
              Download
            </Button>
          )
        }
      ]}
    />
  );
};

export default Clusters;
