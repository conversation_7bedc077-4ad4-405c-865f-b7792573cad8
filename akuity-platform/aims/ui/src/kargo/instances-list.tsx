import { DatePicker, Select, Input } from 'antd';
import moment from 'moment';
import { useSearchParams } from 'react-router-dom';

import { generateKargoFilter, useListKargoInstances } from '@/hook/api';
import { ListKargoInstancesRequest } from '@/lib/apiclient/aims/v1/aims_pb';

import { ListKargo } from './components/table-list';

export const KargoInstancesList = ({
  paid = false,
  unpaid = false
}: {
  paid?: boolean;
  unpaid?: boolean;
}) => {
  const [search, setSearch] = useSearchParams();

  const filters = {
    get: () => {
      const filters = new ListKargoInstancesRequest({
        filter: {
          paid,
          unpaid
        }
      });

      if (paid) {
        filters.filter.paid = true;
      }

      if (unpaid) {
        filters.filter.unpaid = true;
      }

      const organizationId = search.get('filter.organizationId');
      if (organizationId) {
        filters.filter.organizationId = organizationId;
      }

      const fuzz = search.get('filter.fuzz');
      if (fuzz) {
        filters.filter.fuzz = fuzz;
      }

      const time = search.get('filter.timeFrom');
      if (time) {
        filters.filter.timeFrom = time;
      }

      return filters;
    },

    set: (next: ListKargoInstancesRequest) => {
      setSearch(generateKargoFilter(next));
    }
  };

  const appliedFilters = filters.get();

  const { data: kargoInstances, isFetching } = useListKargoInstances(filters.get());

  return (
    <div className='w-full'>
      <Input
        placeholder='Search by kargo instance name or id or its organization name'
        className='mb-4'
        value={filters.get()?.filter?.fuzz}
        onChange={(e) =>
          filters.set(
            new ListKargoInstancesRequest({
              filter: {
                ...filters.get(),
                fuzz: e.target.value
              }
            })
          )
        }
      />

      <div className='mb-4'>
        <label>Created</label>
        <Select
          value={appliedFilters.filter?.timeFrom}
          labelRender={(props) => moment().from(props.value)}
          className='w-2/12 ml-5'
          options={[
            {
              label: 'in 1 Hour',
              value: moment().subtract('1', 'hour').toISOString()
            },
            {
              label: 'in 1 Day',
              value: moment().subtract('1', 'day').toISOString()
            },
            {
              label: 'in 1 Week',
              value: moment().subtract('1', 'week').toISOString()
            },
            {
              label: 'in 1 Month',
              value: moment().subtract('1', 'month').toISOString()
            },
            {
              label: 'Clear',
              value: ''
            }
          ]}
          onChange={(value) =>
            filters.set(
              new ListKargoInstancesRequest({ filter: { ...appliedFilters, timeFrom: value } })
            )
          }
        />
        <DatePicker
          className='ml-2'
          onChange={(date) =>
            filters.set(new ListKargoInstancesRequest({ filter: { timeFrom: date.toISOString() } }))
          }
        />
      </div>
      <ListKargo instances={kargoInstances} isFetching={isFetching} />
    </div>
  );
};
