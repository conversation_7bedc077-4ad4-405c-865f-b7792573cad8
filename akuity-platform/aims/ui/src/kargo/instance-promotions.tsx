import { PlainMessage } from '@bufbuild/protobuf';
import { faCopy } from '@fortawesome/free-regular-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Table, Tooltip, Button } from 'antd';

import { InternalKargoInstance } from '@/lib/apiclient/aims/v1/aims_pb';

import { hasValue, copyToClipboard } from '../utils';

interface PromotionsTabProps {
  kargoInstance: PlainMessage<InternalKargoInstance>;
}

const PromotionsTab = ({ kargoInstance }: PromotionsTabProps) => {
  if (!hasValue(kargoInstance.promotions)) {
    return <div className='text-gray-500 text-center py-8'>No promotions found</div>;
  }

  return (
    <Table
      dataSource={kargoInstance.promotions || []}
      rowKey={(_, index) => index}
      pagination={false}
      size='small'
      scroll={{ x: 1200 }}
    >
      <Table.Column
        key='project-stage'
        title='Project / Stage'
        render={(promotion) => {
          const promo = promotion as Record<string, unknown>;
          return (
            <>
              <Tooltip title={(promo.projectName as string) || 'N/A'} placement='top'>
                <b className='truncate block'>{(promo.projectName as string) || 'N/A'}</b>
              </Tooltip>
              <Tooltip title={(promo.stageName as string) || 'N/A'} placement='top'>
                <div className='text-gray-400 truncate'>{(promo.stageName as string) || 'N/A'}</div>
              </Tooltip>
            </>
          );
        }}
        width={150}
      />
      <Table.Column
        title='Promotion Name'
        render={(promotion) => {
          const promo = promotion as Record<string, unknown>;
          const promotionName = (promo.promotionName as string) || 'N/A';
          return (
            <div className='flex items-center gap-2'>
              <Tooltip title={promotionName} placement='top'>
                <span className='truncate'>{promotionName}</span>
              </Tooltip>
              <Button
                type='text'
                size='small'
                icon={
                  <FontAwesomeIcon icon={faCopy} className='text-gray-400 hover:text-gray-600' />
                }
                onClick={() => copyToClipboard(promotionName)}
                className='flex-shrink-0'
              />
            </div>
          );
        }}
        width={250}
      />
      <Table.Column
        title='Result'
        render={(promotion) => {
          const promo = promotion as Record<string, unknown>;
          const phase = promo.resultPhase as string;
          return (
            <div>
              <span
                className={
                  phase === 'Succeeded'
                    ? 'text-green-600 font-semibold'
                    : phase === 'Failed'
                      ? 'text-red-600 font-semibold'
                      : 'text-yellow-600 font-semibold'
                }
              >
                {phase || 'N/A'}
              </span>
              <div className='text-xs text-gray-500 mt-1'>
                {(promo.resultMessage as string) || 'N/A'}
              </div>
            </div>
          );
        }}
        width={200}
      />
      <Table.Column
        title='Timing'
        render={(promotion) => {
          const promo = promotion as Record<string, unknown>;
          return (
            <div>
              <div className='text-xs'>
                <span className='text-gray-500'>Start:</span> {(promo.startTime as string) || 'N/A'}
              </div>
              <div className='text-xs'>
                <span className='text-gray-500'>End:</span> {(promo.endTime as string) || 'N/A'}
              </div>
            </div>
          );
        }}
        width={180}
      />
      <Table.Column
        title='Freight'
        render={(promotion) => {
          const promo = promotion as Record<string, unknown>;
          const details = (promo.details as Record<string, unknown>) || {};
          const freightDetails = (details.freightDetails as Record<string, unknown>) || {};
          return (
            <div>
              <Tooltip title={(freightDetails.freightAlias as string) || 'N/A'} placement='top'>
                <b className='truncate block'>{(freightDetails.freightAlias as string) || 'N/A'}</b>
              </Tooltip>
              <Tooltip title={(freightDetails.freightName as string) || 'N/A'} placement='top'>
                <div className='text-xs text-gray-400 font-mono truncate'>
                  {(freightDetails.freightName as string) || 'N/A'}
                </div>
              </Tooltip>
            </div>
          );
        }}
        width={150}
      />
      <Table.Column
        title='Initiated By'
        render={(promotion) => {
          const promo = promotion as Record<string, unknown>;
          const details = (promo.details as Record<string, unknown>) || {};
          const initiatedBy = (details.initiatedBy as Record<string, unknown>) || {};
          return (initiatedBy.username as string) || 'N/A';
        }}
        width={120}
      />
      <Table.Column
        title='ID'
        render={(promotion) => {
          const promo = promotion as Record<string, unknown>;
          return (
            <div className='text-xs font-mono text-gray-500'>{(promo.id as string) || 'N/A'}</div>
          );
        }}
        width={120}
        ellipsis
      />
    </Table>
  );
};

export default PromotionsTab;
