import { PlainMessage } from '@bufbuild/protobuf';
import JsonView from '@uiw/react-json-view';
import { Tabs } from 'antd';
import { useContext } from 'react';
import { Link, useParams } from 'react-router-dom';

import { useGetKargoInstanceById } from '@/hook/api';
import { InternalKargoInstance } from '@/lib/apiclient/aims/v1/aims_pb';
import { Header } from '@/lib/components/header';
import { InfoItem } from '@/lib/components/info-item';
import { Loading } from '@/lib/components/loading';
import { HealthState } from '@/lib/components/shared/health-state';
import JSONField from '@/lib/components/shared/json-field';
import { UnsupportedVersionBanner } from '@/lib/components/unsupported-version-banner';

import { filterEmptyValues, hasValue, cleanForDisplay } from '../utils';

import { KargoInstanceContext } from './context/instance';
import Clusters from './instance-cluster-list';

export const KargoInstanceDetails = () => {
  const { instanceId } = useParams();

  const { data, isFetching, error } = useGetKargoInstanceById(instanceId);

  if (isFetching) {
    return <Loading />;
  }

  if (error) {
    return JSON.stringify(error);
  }

  return (
    <KargoInstanceContext.Provider value={{ instance: data }}>
      <Details />
    </KargoInstanceContext.Provider>
  );
};

const Details = () => {
  const data = useContext(KargoInstanceContext);

  const kargoInstance = data?.instance?.instance;

  return (
    <div className='w-full'>
      <UnsupportedVersionBanner
        isUnsupported={kargoInstance.instance.unsupportedVersion}
        version={kargoInstance.instance.version}
      />
      <div className='flex items-center gap-5 mb-4'>
        <img src='/images/kargo.png' alt='Kargo' style={{ width: '48px' }} />
        <div>
          <Header>{kargoInstance?.instance?.name}</Header>
        </div>
        <div className='ml-auto'>
          <span className='ml-auto'>{kargoInstance?.instance?.id}</span>
          {kargoInstance?.instance?.fqdn && (
            <a
              href={`https://${kargoInstance?.instance?.fqdn}`}
              className='text-blue-500 block mt-2'
              target='_blank'
            >
              {kargoInstance?.instance?.fqdn}
            </a>
          )}
        </div>
      </div>
      <div className='mb-6'>
        <div className='flex items-start mb-6'>
          <InfoItem label='VERSION'>{kargoInstance?.instance?.version}</InfoItem>
          <InfoItem label='HEALTH'>
            {kargoInstance?.instance?.healthStatus && (
              <HealthState
                // @ts-expect-error had split types (for AIMS and UI) but from same proto
                health={kargoInstance?.instance?.healthStatus}
                deletionTimestamp={kargoInstance?.instance?.deleteTime}
                className='font-semibold'
              />
            )}
          </InfoItem>
          <InfoItem label='RECONCILIATION'>
            {kargoInstance?.instance?.reconciliationStatus && (
              <>
                <HealthState
                  // @ts-expect-error had split types (for AIMS and UI) but from same proto
                  health={kargoInstance?.instance?.reconciliationStatus}
                  className='font-semibold'
                />
                <span className='mt-2 block text-sm'>
                  {kargoInstance?.instance?.reconciliationStatus?.message}
                </span>
              </>
            )}
          </InfoItem>

          <InfoItem label='WORKSPACE'>
            {kargoInstance?.workspace ? (
              <Link
                to={`/organizations/${kargoInstance?.organization?.id}/workspaces/${kargoInstance?.workspace?.id}`}
                className='text-blue-500 hover:underline font-semibold'
              >
                {kargoInstance?.workspace?.name}
              </Link>
            ) : (
              'N/A'
            )}
          </InfoItem>
          <InfoItem label='PROJECT | STAGE'>
            {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
            {(kargoInstance?.statusInfo as any)?.kargoStats?.projectCount ?? 0} | {''}
            {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
            {(kargoInstance?.statusInfo as any)?.kargoStats?.stageCount ?? 0}
          </InfoItem>
        </div>

        <Tabs
          items={[
            {
              key: 'configuration',
              label: 'Configuration',
              children: <ConfigurationTab kargoInstance={kargoInstance} />
            },
            {
              key: 'clusters',
              label: 'Clusters',
              children: <Clusters />
            }
          ]}
        />
      </div>
    </div>
  );
};

const ConfigurationTab = ({
  kargoInstance
}: {
  kargoInstance: PlainMessage<InternalKargoInstance>;
}) => {
  const statusInfo = kargoInstance.statusInfo;

  return (
    <div className='flex flex-wrap'>
      <JSONField label='INSTANCE METADATA'>
        <JsonView
          value={filterEmptyValues({
            id: kargoInstance.instance.id,
            name: kargoInstance.instance.name,
            description: kargoInstance.instance.description,
            version: kargoInstance.instance.version,
            subdomain: kargoInstance.instance.subdomain,
            fqdn: kargoInstance.instance.fqdn,
            workspaceId: kargoInstance.instance.workspaceId,
            generation: kargoInstance.instance.generation,
            hostname: kargoInstance.instance.hostname,
            ownerOrganizationName: kargoInstance.instance.ownerOrganizationName,
            unsupportedVersion: kargoInstance.instance.unsupportedVersion
          })}
        />
      </JSONField>

      {hasValue(statusInfo) && (
        <JSONField label='STATUS & RUNTIME INFO'>
          <JsonView value={cleanForDisplay(statusInfo) as object} />
        </JSONField>
      )}

      {hasValue(kargoInstance.apiCm) && (
        <JSONField label='API CONFIG (ADMIN ACCOUNT)'>
          <JsonView value={cleanForDisplay(kargoInstance.apiCm) as object} />
        </JSONField>
      )}

      {hasValue(kargoInstance.oidcConfig) && (
        <JSONField label='OIDC CONFIG'>
          <JsonView value={cleanForDisplay(kargoInstance.oidcConfig) as object} />
        </JSONField>
      )}

      {hasValue(kargoInstance.controllerConfig) && (
        <JSONField label='CONTROLLER CONFIG'>
          <JsonView value={cleanForDisplay(kargoInstance.controllerConfig) as object} />
        </JSONField>
      )}

      {hasValue(kargoInstance.webhookConfig) && (
        <JSONField label='WEBHOOK CONFIG'>
          <JsonView value={cleanForDisplay(kargoInstance.webhookConfig) as object} />
        </JSONField>
      )}

      {hasValue(kargoInstance.apiSecret) && (
        <JSONField label='API SECRETS (CENSORED)'>
          <JsonView value={kargoInstance.apiSecret} />
        </JSONField>
      )}

      {hasValue(kargoInstance.miscellaneousSecrets) && (
        <JSONField label='MISCELLANEOUS SECRETS (CENSORED)'>
          <JsonView value={kargoInstance.miscellaneousSecrets} />
        </JSONField>
      )}

      {hasValue(kargoInstance.certificateStatus) && (
        <JSONField label='CERTIFICATE STATUS'>
          <JsonView value={kargoInstance.certificateStatus} />
        </JSONField>
      )}
    </div>
  );
};

export default ConfigurationTab;
