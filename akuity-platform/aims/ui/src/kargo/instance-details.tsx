import { Tabs } from 'antd';
import { useContext } from 'react';
import { Link, useParams } from 'react-router-dom';

import { useGetKargoInstanceById } from '@/hook/api';
import { Header } from '@/lib/components/header';
import { InfoItem } from '@/lib/components/info-item';
import { Loading } from '@/lib/components/loading';
import { HealthState } from '@/lib/components/shared/health-state';
import { UnsupportedVersionBanner } from '@/lib/components/unsupported-version-banner';

import { KargoInstanceContext } from './context/instance';
import Clusters from './instance-cluster-list';
import ConfigurationTab from './instance-config';
import ExtensionsTab from './instance-extensions';
import PromotionsTab from './instance-promotions';

export const KargoInstanceDetails = () => {
  const { instanceId } = useParams();

  const { data, isFetching, error } = useGetKargoInstanceById(instanceId);

  if (isFetching) {
    return <Loading />;
  }

  if (error) {
    return JSON.stringify(error);
  }

  return (
    <KargoInstanceContext.Provider value={{ instance: data }}>
      <Details />
    </KargoInstanceContext.Provider>
  );
};

const Details = () => {
  const data = useContext(KargoInstanceContext);

  const kargoInstance = data?.instance?.instance;

  return (
    <div className='w-full'>
      <UnsupportedVersionBanner
        isUnsupported={kargoInstance.instance.unsupportedVersion}
        version={kargoInstance.instance.version}
      />
      <div className='flex items-center gap-5 mb-4'>
        <img src='/images/kargo.png' alt='Kargo' style={{ width: '48px' }} />
        <div>
          <Header>{kargoInstance?.instance?.name}</Header>
        </div>
        <div className='ml-auto'>
          <span className='ml-auto'>{kargoInstance?.instance?.id}</span>
          {kargoInstance?.instance?.fqdn && (
            <a
              href={`https://${kargoInstance?.instance?.fqdn}`}
              className='text-blue-500 block mt-2'
              target='_blank'
            >
              {kargoInstance?.instance?.fqdn}
            </a>
          )}
        </div>
      </div>
      <div className='mb-6'>
        <div className='flex items-start mb-6'>
          <InfoItem label='VERSION'>{kargoInstance?.instance?.version}</InfoItem>
          <InfoItem label='HEALTH'>
            {kargoInstance?.instance?.healthStatus && (
              <HealthState
                // @ts-expect-error had split types (for AIMS and UI) but from same proto
                health={kargoInstance?.instance?.healthStatus}
                deletionTimestamp={kargoInstance?.instance?.deleteTime}
                className='font-semibold'
              />
            )}
          </InfoItem>
          <InfoItem label='RECONCILIATION'>
            {kargoInstance?.instance?.reconciliationStatus && (
              <>
                <HealthState
                  // @ts-expect-error had split types (for AIMS and UI) but from same proto
                  health={kargoInstance?.instance?.reconciliationStatus}
                  className='font-semibold'
                />
                <span className='mt-2 block text-sm'>
                  {kargoInstance?.instance?.reconciliationStatus?.message}
                </span>
              </>
            )}
          </InfoItem>

          <InfoItem label='WORKSPACE'>
            {kargoInstance?.workspace ? (
              <Link
                to={`/organizations/${kargoInstance?.organization?.id}/workspaces/${kargoInstance?.workspace?.id}`}
                className='text-blue-500 hover:underline font-semibold'
              >
                {kargoInstance?.workspace?.name}
              </Link>
            ) : (
              'N/A'
            )}
          </InfoItem>
          <InfoItem label='PROJECT | STAGE'>
            {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
            {(kargoInstance?.statusInfo as any)?.kargoStats?.projectCount ?? 0} | {''}
            {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
            {(kargoInstance?.statusInfo as any)?.kargoStats?.stageCount ?? 0}
          </InfoItem>
        </div>

        <Tabs
          items={[
            {
              key: 'configuration',
              label: 'Configuration',
              children: <ConfigurationTab kargoInstance={kargoInstance} />
            },
            {
              key: 'clusters',
              label: 'Clusters',
              children: <Clusters />
            },
            {
              key: 'promotions',
              label: 'Promotions',
              children: <PromotionsTab kargoInstance={kargoInstance} />
            },
            {
              key: 'extensions',
              label: 'Instance Specs & Extensions',
              children: <ExtensionsTab kargoInstance={kargoInstance} />
            }
          ]}
        />
      </div>
    </div>
  );
};
