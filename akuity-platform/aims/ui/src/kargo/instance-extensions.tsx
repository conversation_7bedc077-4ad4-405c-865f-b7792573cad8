import { PlainMessage } from '@bufbuild/protobuf';
import JsonView from '@uiw/react-json-view';
import { Table } from 'antd';

import { InternalKargoInstance } from '@/lib/apiclient/aims/v1/aims_pb';
import { formatSnakeCaseToTitleCase, cleanForDisplay } from '@/utils';

interface ExtensionsTabProps {
  kargoInstance: PlainMessage<InternalKargoInstance>;
}

const ExtensionsTab = ({ kargoInstance }: ExtensionsTabProps) => {
  if (!kargoInstance.instanceSpecs) {
    return (
      <div className='text-gray-500 text-center py-8'>No instance specifications configured</div>
    );
  }

  const extensions = kargoInstance.instanceSpecs as Record<string, unknown>;
  const extensionsList = Object.entries(extensions).map(([key, value]) => {
    return {
      key,
      name: formatSnakeCaseToTitleCase(key),
      configuration: value,
      hasEnabled: typeof value === 'object' && value !== null && 'enabled' in value
    };
  });

  const columns = [
    {
      title: 'Feature',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (name: string, record: { hasEnabled: boolean }) => (
        <div className='flex items-center gap-2'>
          <div className='font-medium'>{name}</div>
          {record.hasEnabled && (
            <span className='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800'>
              Enabled
            </span>
          )}
        </div>
      )
    },
    {
      title: 'Configuration',
      dataIndex: 'configuration',
      key: 'configuration',
      render: (config: unknown) => {
        if (
          typeof config === 'string' ||
          typeof config === 'boolean' ||
          typeof config === 'number'
        ) {
          return (
            <div className='max-w-md'>
              <span className='font-mono text-sm bg-gray-100 px-2 py-1 rounded'>
                {String(config) || '-'}
              </span>
            </div>
          );
        }
        // Only use JsonView for objects and arrays
        if (typeof config === 'object' && config !== null) {
          return (
            <div className='max-w-md'>
              <JsonView value={cleanForDisplay(config) as object} />
            </div>
          );
        }
        return (
          <div className='max-w-md'>
            <span className='font-mono text-sm bg-gray-100 px-2 py-1 rounded'>-</span>
          </div>
        );
      }
    }
  ];

  return (
    <div className='p-1'>
      <div className='mb-6'>
        <h3 className='text-lg font-semibold mb-2'>Instance Specifications & Features</h3>
        <p className='text-gray-600'>
          This table shows all configuration settings and features for this Kargo instance.
        </p>
      </div>

      <Table
        dataSource={extensionsList}
        columns={columns}
        pagination={false}
        size='small'
        rowKey='key'
        scroll={{ x: 800 }}
      />
    </div>
  );
};

export default ExtensionsTab;
