import { useListArgoInstances } from '@/hook/api';
import { ListArgoInstancesRequest } from '@/lib/apiclient/aims/v1/aims_pb';

import { Header } from '../lib/components/header';

import { InstancesList } from './instances-list';

export const UnpaidInstanceTable = () => {
  const filters = new ListArgoInstancesRequest({
    filter: {
      unpaid: true
    }
  });

  const { data: instances, refetch, isFetching } = useListArgoInstances(filters);

  return (
    <div className='w-full'>
      <Header>Unpaid Argo CD Instances</Header>
      <InstancesList instances={instances} refetch={refetch} paid={false} isFetching={isFetching} />
    </div>
  );
};
