import {
  faArrowsRotate,
  faCircleNotch,
  faInfoCircle,
  faTrash
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Alert, Button, Flex, Radio, Space, Table, Tooltip } from 'antd';
import moment, { Moment } from 'moment';
import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import { useModal } from '@/hook';
import { InternalInstance } from '@/lib/apiclient/aims/v1/aims_pb';
import { ListOrganizationMembersResponse } from '@/lib/apiclient/aims/v1/aims_pb';
import { Instance } from '@/lib/apiclient/argocd/v1/argocd_pb';
import { getAuditFilterString } from '@/lib/components/audit-log/filters/utils';
import { DeleteExpiredInstanceModal } from '@/lib/components/delete-expired-instance-modal';
import { DisplayTime } from '@/lib/components/display-time';
import { GenerationModal } from '@/lib/components/generation-modal';
import { HealthState } from '@/lib/components/shared/health-state';
import { ARGO } from '@/types';

import { InstanceDetailsModal } from '../instance-details-modal';

const NoneHighlight = ({ metric }: { metric: number }) => {
  return <div>{metric > 0 ? metric : <div className='text-red-500 font-semibold'>None</div>}</div>;
};

const ClustersMetric = ({ connected, total }: { connected: number; total: number }) => {
  return total == 0 ? (
    <NoneHighlight metric={total} />
  ) : (
    <Tooltip title={`${connected} connected / ${total} total`}>
      {connected} / {total}
    </Tooltip>
  );
};

const RadioFontReset = (props: { value: string; children: React.ReactNode }) => (
  <Radio value={props.value} className='font-normal'>
    {props.children}
  </Radio>
);

type DateRange = 'all' | 'last24' | 'last7' | 'last30';

const dateFrom = (filter: DateRange): Moment | null => {
  switch (filter) {
    case 'all':
      return null;
    case 'last24':
      return moment().subtract(24, 'hours');
    case 'last7':
      return moment().subtract(7, 'days');
    case 'last30':
      return moment().subtract(30, 'days');
  }
};

export const ListArgo = ({
  instances,
  isOrganizationListing = false,
  isFetching,
  members,
  membersFetching,
  paid,
  refetch
}: {
  instances: InternalInstance[];
  isOrganizationListing?: boolean;
  isFetching?: boolean;
  members?: ListOrganizationMembersResponse['members'];
  membersFetching?: boolean;
  paid?: boolean;
  refetch: () => Promise<unknown>;
}) => {
  const [selectedInstance, setSelectedInstance] = useState<InternalInstance | null>(null);
  const [instToDelete, setInstToDelete] = useState<Instance | null>(null);
  const [instToDecrement, setInstToDecrement] = useState<Instance | null>(null);

  useEffect(() => {
    if (instToDelete) {
      show();
    }
  }, [instToDelete]);

  useEffect(() => {
    if (instToDecrement) {
      showGenerationModal();
    }
  }, [instToDecrement]);

  const [err, setErr] = useState<boolean>(false);
  const [done, setDone] = useState<boolean>(false);

  const { show, hide } = useModal((p) => (
    <DeleteExpiredInstanceModal
      visible={p.visible}
      instToDelete={instToDelete}
      onClose={(confirmed, e) => {
        hide();
        setInstToDelete(null);
        refetch();

        if (confirmed) {
          if (e) {
            setErr(true);
            setTimeout(() => setErr(false), 5000);
          } else {
            setErr(false);
          }

          setDone(true);
          setTimeout(() => setDone(false), 5000);
        }
      }}
    />
  ));

  const { show: showGenerationModal, hide: hideGenerationModal } = useModal((p) => (
    <GenerationModal
      visible={p.visible}
      inst={instToDecrement}
      onClose={(confirmed, e) => {
        hideGenerationModal();
        setInstToDecrement(null);
        refetch();

        if (confirmed) {
          if (e) {
            setErr(true);
            setTimeout(() => setErr(false), 5000);
          } else {
            setErr(false);
          }

          setDone(true);
          setTimeout(() => setDone(false), 5000);
        }
      }}
    />
  ));
  return (
    <>
      {done && (
        <Alert
          type={err ? 'error' : 'success'}
          className='mb-4'
          message={err ? 'Error deleting instance' : 'Instance deleted successfully'}
        />
      )}
      <Table
        loading={isFetching}
        dataSource={instances}
        rowKey={(ii) => `${ii?.instance?.id}-${ii?.instance?.name}`}
        className='w-full'
        pagination={{ defaultPageSize: 50 }}
      >
        <Table.Column
          title='Name / ID'
          width={200}
          render={({ instance }) => (
            <>
              <Link to={`/instances/settings/${instance.id}`} className='font-bold text-blue-500'>
                {instance.name}
              </Link>
              <div className='text-xs text-gray-500 mt-1'>ID: {instance.id}</div>
            </>
          )}
        />

        {!isOrganizationListing && (
          <Table.Column
            title='Organization'
            key='organization'
            render={(record) => (
              <>
                <Link
                  to={`/organizations/${record.orgId}?${getAuditFilterString(ARGO)}`}
                  className='font-bold text-blue-500'
                >
                  {record.instance.ownerOrganizationName}
                </Link>
                <div className='text-xs text-gray-500 mt-1'>ID: {record.orgId}</div>
              </>
            )}
          />
        )}
        <Table.Column
          title='Health Status'
          key='healthStatus'
          render={(ii: InternalInstance) => (
            /* @ts-expect-error healthStatus is a PlainMessage */
            <HealthState health={ii.instance.healthStatus} />
          )}
        />

        <Table.Column
          title='# Apps'
          render={(ii: InternalInstance) => (
            <NoneHighlight metric={ii?.instance?.info?.applicationsStatus?.applicationCount} />
          )}
        />
        <Table.Column
          title='Clusters'
          render={(ii: InternalInstance) => (
            <ClustersMetric connected={ii?.connectedClusters} total={ii?.instance?.clusterCount} />
          )}
        />
        <Table.Column title='Version' render={({ instance }) => <div>{instance.version}</div>} />
        <Table.Column
          width='200px'
          title='Created At'
          render={(ii: InternalInstance) => (
            <DisplayTime time={ii.createTime.toDate().toISOString()} />
          )}
          sorter={{
            compare: (a: InternalInstance, b: InternalInstance) =>
              moment(a.createTime.toDate().toISOString()).diff(b.createTime.toDate().toISOString())
          }}
          onFilter={(value, record) => {
            const date = dateFrom(value as DateRange);
            if (!date) {
              return true;
            }
            return moment(record.createTime.toDate().toISOString()).isAfter(date);
          }}
          filterDropdown={({ confirm, setSelectedKeys }) => (
            <div className='p-4'>
              <Radio.Group
                onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                className='w-full mb-4'
                defaultValue={'all'}
              >
                <Space direction='vertical'>
                  <RadioFontReset value='all'>All</RadioFontReset>
                  <RadioFontReset value='last24'>Last 24 hours</RadioFontReset>
                  <RadioFontReset value='last7'>Last 7 days</RadioFontReset>
                  <RadioFontReset value='last30'>Last 30 days</RadioFontReset>
                </Space>
              </Radio.Group>
              <Button type='primary' onClick={() => confirm({ closeDropdown: true })}>
                Confirm
              </Button>
            </div>
          )}
        />
        <Table.Column
          title='Actions'
          render={(ii: InternalInstance) => {
            return (
              <Flex gap={4}>
                <Tooltip title='Details'>
                  <Button
                    type='default'
                    onClick={() => setSelectedInstance(ii)}
                    icon={<FontAwesomeIcon icon={faInfoCircle} />}
                  />
                </Tooltip>
                <div className='flex items-center'>
                  <Button type='primary' onClick={() => setInstToDecrement(ii.instance)}>
                    <FontAwesomeIcon icon={faArrowsRotate} />
                  </Button>
                </div>
                {!paid &&
                  (ii?.instance?.deleteTime ? (
                    <div className='flex items-center'>
                      <FontAwesomeIcon icon={faCircleNotch} spin className='mr-2' />
                      Deleting...
                    </div>
                  ) : (
                    <Button
                      danger
                      onClick={() => {
                        setInstToDelete(ii.instance);
                        show();
                      }}
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </Button>
                  ))}
              </Flex>
            );
          }}
        />
      </Table>
      <InstanceDetailsModal
        isOpen={!!selectedInstance}
        onClose={() => setSelectedInstance(null)}
        instance={selectedInstance}
        members={members}
        membersFetching={membersFetching}
      />
    </>
  );
};
