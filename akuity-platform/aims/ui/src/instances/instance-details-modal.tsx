import { faFileCode, faIdCard, faUsers } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import JsonView from '@uiw/react-json-view';
import { Descriptions, List, Modal, Typography, Collapse } from 'antd';

import { InternalInstance, ListOrganizationMembersResponse } from '@/lib/apiclient/aims/v1/aims_pb';
import { isValidJSON } from '@/utils';

interface InstanceDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  instance: InternalInstance | null;
  members: ListOrganizationMembersResponse['members'];
  membersFetching: boolean;
}

export const InstanceDetailsModal = ({
  isOpen,
  onClose,
  instance,
  members,
  membersFetching
}: InstanceDetailsModalProps) => {
  if (!instance) return null;

  const processedInfo = instance.statusProcessedInfo;
  const owners = members?.[instance.instance.ownerOrganizationName]?.email || [];

  return (
    <Modal title='Instance Details' open={isOpen} onCancel={onClose} footer={null} width={800}>
      <Descriptions bordered column={1} className='mb-4'>
        <Descriptions.Item
          label={
            <>
              <FontAwesomeIcon icon={faIdCard} className='mr-2' /> Name
            </>
          }
        >
          <strong>{instance.instance.name}</strong>
        </Descriptions.Item>
        <Descriptions.Item
          label={
            <>
              <FontAwesomeIcon icon={faFileCode} className='mr-2' /> Processed Info
            </>
          }
        >
          {processedInfo && isValidJSON(processedInfo) ? (
            <JsonView value={JSON.parse(processedInfo)} />
          ) : (
            <Typography.Text>{processedInfo || 'N/A'}</Typography.Text>
          )}
        </Descriptions.Item>
      </Descriptions>

      <Collapse className='mb-4'>
        <Collapse.Panel
          key='1'
          header={
            <>
              <FontAwesomeIcon icon={faUsers} className='mr-2' /> Owners ({owners.length})
            </>
          }
        >
          <List
            loading={membersFetching}
            dataSource={owners}
            renderItem={(email) => (
              <List.Item>
                <Typography.Text>
                  <a
                    href={`https://app.hubspot.com/contacts/${__HUBSPOT_HUB_ID__}/contact/email/${email}`}
                    target='_blank'
                    key={email}
                    className='block'
                    rel='noreferrer'
                  >
                    {email}
                  </a>
                </Typography.Text>
              </List.Item>
            )}
            locale={{ emptyText: 'No owners found' }}
          />
        </Collapse.Panel>
      </Collapse>
    </Modal>
  );
};
