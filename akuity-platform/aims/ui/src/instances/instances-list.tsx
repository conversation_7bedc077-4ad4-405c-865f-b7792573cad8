import { Input, Select } from 'antd';

import { useListOrganizationMembers } from '@/hook/api';
import { InternalInstance } from '@/lib/apiclient/aims/v1/aims_pb';
import { StatusCode } from '@/lib/apiclient/types/status/health/v1/health_pb';
import { humananifyStatusCode } from '@/lib/components/shared/health-state';

import { ListArgo } from './components/table-list';
import { instanceFilters, useInstanceFilters } from './utils';

export const InstancesList = (props: {
  instances: InternalInstance[];
  refetch: () => Promise<unknown>;
  paid: boolean;
  isFetching: boolean;
}) => {
  const { data: members, isFetching: membersFetching } = useListOrganizationMembers();

  const { filteredInstances, setSearch, filters } = useInstanceFilters(props.instances, members);

  return (
    <>
      <div className='w-full'>
        <Input
          placeholder='Search by instance name/id, organization name/member'
          className='mb-5'
          value={filters?.fuzz}
          onChange={(e) =>
            setSearch(instanceFilters.toSearch({ ...filters, fuzz: e.target.value }))
          }
        />
        <label>Health: </label>
        <Select
          className='w-2/12 mb-5 ml-2'
          placeholder='Health'
          options={[
            StatusCode.HEALTHY,
            StatusCode.PROGRESSING,
            StatusCode.DEGRADED,
            StatusCode.UNSPECIFIED
          ].map((value) => ({
            label: humananifyStatusCode(value),
            value
          }))}
          onChange={(value) => setSearch(instanceFilters.toSearch({ ...filters, health: value }))}
        />
        <label className='ml-5'>Apps greater than equal to: </label>
        <Input
          type='number'
          value={filters?.apps}
          onChange={(e) =>
            setSearch(instanceFilters.toSearch({ ...filters, apps: +e.target.value }))
          }
          min={0}
          className='w-2/12 ml-2'
        />
        <label className='ml-5'>Clusters greater than equal to: </label>
        <Input
          type='number'
          min={0}
          value={filters?.clusters}
          onChange={(e) =>
            setSearch(instanceFilters.toSearch({ ...filters, clusters: Number(e.target.value) }))
          }
          className='w-2/12 ml-2'
        />
        <ListArgo
          instances={filteredInstances}
          isOrganizationListing={false}
          isFetching={props.isFetching}
          members={members?.members}
          membersFetching={membersFetching}
          paid={props.paid}
          refetch={props.refetch}
        />
      </div>
    </>
  );
};
