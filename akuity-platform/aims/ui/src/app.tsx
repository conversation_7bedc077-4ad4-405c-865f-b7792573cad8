import '@ant-design/v5-patch-for-react-19';
import { QueryClientProvider, QueryClient, MutationCache } from '@tanstack/react-query';
import { notification } from 'antd';
import ReactDOM from 'react-dom/client';
import { BrowserRouter, Navigate, Route, Routes } from 'react-router-dom';
import './app.less';

import { AnnouncementBannerWrapper } from './announcement-banner/announcement-banner-wrapper';
import { BillingPage } from './billing/billing-page';
import { ModalContextProvider } from './context/modal-context';
import { Onboarding } from './customer-onboarding';
import { InstanceDetails } from './instances/instance-details';
import { PaidInstanceTable } from './instances/paid-instances-list';
import { UnpaidInstanceTable } from './instances/unpaid-instances-list';
import { InternalAuditLogs } from './internal-audit-logs/internal-audit-logs';
import { KargoInstanceDetails } from './kargo/instance-details';
import { KargoPaidInstance } from './kargo/paid-instances-list';
import { KargoUnpaidInstance } from './kargo/unpaid-instances-list';
import { Layout } from './lib/components/layout';
import { NotificationPage } from './notifications/notifications-page';
import { TeamDetails } from './organizations/components/teams/team-details';
import { WorkspaceDetails } from './organizations/components/workspace/workspace-details';
import { OrganizationProviderWrapper } from './organizations/context/organization-context';
import { OrganizationDetails } from './organizations/organization-details';
import { OrganizationsList } from './organizations/organizations-list';
import { UpdateTrial } from './update-trial-expiration';

// eslint-disable-next-line
console.info('AIMS UI version:', __UI_VERSION__);

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      refetchOnWindowFocus: false,
      gcTime: 0
    }
  },
  mutationCache: new MutationCache({
    onError: (err) => notification.error({ message: err?.message })
  })
});

ReactDOM.createRoot(document.getElementById('root')).render(
  <QueryClientProvider client={queryClient}>
    <ModalContextProvider>
      <BrowserRouter>
        <Layout>
          <Routes>
            <Route path='/instances/paid' element={<PaidInstanceTable />} />
            <Route path='/instances/unpaid' element={<UnpaidInstanceTable />} />
            <Route path='/billing' element={<BillingPage />} />
            <Route path='/onboarding' element={<Onboarding />} />
            <Route path='/trial' element={<UpdateTrial />} />
            <Route path='/instances/settings/:instanceId' element={<InstanceDetails />} />
            <Route path='/organizations' element={<OrganizationsList />} />

            <Route path='/organizations/:orgId' element={<OrganizationProviderWrapper />}>
              <Route index element={<OrganizationDetails />} />
              <Route path='teams/:teamName' element={<TeamDetails />} />
              <Route path='workspaces/:workspaceId' element={<WorkspaceDetails />} />
            </Route>

            <Route path='/kargo/instances/paid' element={<KargoPaidInstance />} />
            <Route path='/kargo/instances/unpaid' element={<KargoUnpaidInstance />} />
            <Route path='/kargo/instances/:instanceId' element={<KargoInstanceDetails />} />
            <Route path='/notifications' element={<NotificationPage />} />
            <Route path='/announcement-banner' element={<AnnouncementBannerWrapper />} />
            <Route path='/audit-logs' element={<InternalAuditLogs />} />
            <Route path='*' element={<Navigate to='/instances/paid' replace />} />
          </Routes>
        </Layout>
      </BrowserRouter>
    </ModalContextProvider>
  </QueryClientProvider>
);
