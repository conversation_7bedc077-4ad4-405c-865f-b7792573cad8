import { faInfoCircle } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Table, Tag, Space, Button, Tooltip, message } from 'antd';
import type { TablePaginationConfig } from 'antd/es/table';
import moment from 'moment';
import { useState } from 'react';
import { Link } from 'react-router-dom';

import { useListInternalAuditLogs } from '@/hook/api/audit-logs-queries';
import { AuditLog } from '@/lib/apiclient/aims/v1/aims_pb';
import { FilteredIcon } from '@/lib/components/shared/filter';
import { TimeRangeFilterContent } from '@/lib/components/shared/time-range-context';

import { AuditLogsDetailsModal } from './audit-logs-details-modal';
import {
  ActionFilter,
  ActorFilter,
  ResourceTypeFilter,
  AppliedAuditLogsFilters
} from './audit-logs-filters';
import {
  ObjectType,
  objectTypeLabels,
  useInternalAuditLogsFilters
} from './use-audit-logs-filters';

export const InternalAuditLogs = () => {
  const { filters, setFilters, removeFilters } = useInternalAuditLogsFilters();
  const [selectedAuditLog, setSelectedAuditLog] = useState<AuditLog | null>(null);

  const { data, isFetching } = useListInternalAuditLogs();

  const handleTableChange = (pagination: TablePaginationConfig) => {
    setFilters({
      limit: pagination.pageSize || 10,
      offset: ((pagination.current || 1) - 1) * (pagination.pageSize || 10)
    });
  };

  const columns = [
    {
      title: 'Time',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 180,
      fixed: 'left' as const,
      render: (text: string) => {
        const parsedTimestamp = moment(text);
        const readableTimestamp = parsedTimestamp.format('YYYY-MM-DD HH:mm:ss');

        return (
          <Tooltip title='Copy Timestamp'>
            <div
              className='font-mono text-sm cursor-pointer hover:text-blue-500'
              onClick={async () => {
                try {
                  await navigator.clipboard.writeText(readableTimestamp);
                  message.success('Copied to clipboard!');
                } catch {
                  message.error('Failed to copy');
                }
              }}
            >
              {moment().diff(parsedTimestamp, 'hours') > 24
                ? readableTimestamp
                : parsedTimestamp.fromNow()}
            </div>
          </Tooltip>
        );
      },
      filterDropdown: ({ close }: { close: () => void }) => (
        <TimeRangeFilterContent
          startTime={filters.startTime}
          endTime={filters.endTime}
          onApply={(times) => {
            setFilters(times);
            close();
          }}
          onClear={() => {
            removeFilters(['startTime', 'endTime']);
            close();
          }}
        />
      ),
      filterIcon: FilteredIcon,
      filtered: Boolean(filters.startTime || filters.endTime)
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      width: 100,
      render: (text: string) => <Tag color='blue'>{text}</Tag>,
      filterDropdown: ({ close }: { close?: () => void }) => <ActionFilter close={close} />,
      filterIcon: FilteredIcon,
      filtered: Boolean(filters.action?.length)
    },
    {
      title: 'Actor',
      dataIndex: ['actor', 'id'],
      key: 'actor',
      width: 180,
      render: (text: string, record: AuditLog) => (
        <Space>
          <span>{text}</span>
          {record.actor?.type && <Tag color='green'>{record.actor.type}</Tag>}
        </Space>
      ),
      filterDropdown: ({ close }: { close?: () => void }) => <ActorFilter close={close} />,
      filterIcon: FilteredIcon,
      filtered: Boolean(filters.actorId?.length)
    },
    {
      title: 'Resource Type',
      dataIndex: ['object', 'type'],
      key: 'resourceType',
      width: 130,
      render: (text: ObjectType) => <Tag color='purple'>{objectTypeLabels[text] || text}</Tag>,
      filterDropdown: ({ close }: { close?: () => void }) => <ResourceTypeFilter close={close} />,
      filterIcon: FilteredIcon,
      filtered: Boolean(filters.objectType?.length)
    },
    {
      title: 'Resource ID',
      dataIndex: ['object', 'id'],
      key: 'resourceId',
      width: 180,
      render: (text: string, record: AuditLog) =>
        record.object?.type === 'organization' ? (
          <Link className='text-blue-600' to={`/organizations/${text}`}>
            {text}
          </Link>
        ) : (
          <span>{text || 'N/A'}</span>
        )
    },
    {
      title: 'Message',
      dataIndex: ['details', 'message'],
      key: 'message',
      width: 200,
      render: (text: string) => (
        <Tooltip title={text}>
          <span className='truncate block'>
            {text?.length > 50 ? `${text.substring(0, 50)}...` : text}
          </span>
        </Tooltip>
      )
    },
    {
      title: 'Details',
      key: 'details',
      width: 80,
      render: (_: unknown, record: AuditLog) => (
        <Button
          icon={<FontAwesomeIcon icon={faInfoCircle} />}
          onClick={() => setSelectedAuditLog(record)}
          type='text'
          size='small'
          className='text-gray-500'
        />
      )
    }
  ];

  return (
    <div className='block w-full'>
      <div className='text-2xl font-bold mb-4'>Internal Audit Logs</div>
      <AppliedAuditLogsFilters />
      <Table
        loading={isFetching}
        className='w-full'
        columns={columns}
        dataSource={data?.items}
        rowKey='timestamp'
        pagination={{
          total: data?.totalCount,
          pageSize: filters.limit,
          current: filters.offset / filters.limit + 1
        }}
        onChange={handleTableChange}
        scroll={{ x: 1150 }}
      />
      <AuditLogsDetailsModal
        isOpen={!!selectedAuditLog}
        onClose={() => setSelectedAuditLog(null)}
        auditLog={selectedAuditLog}
      />
    </div>
  );
};
