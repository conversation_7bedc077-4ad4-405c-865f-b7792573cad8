import { Select } from 'antd';
import { useForm } from 'react-hook-form';

import { Filter } from '@/lib/components/shared/filter';
import { FieldContainer } from '@/lib/components/shared/forms';

import { useInternalAuditLogsFilters } from '../use-audit-logs-filters';

export const ActionFilter = ({ close }: { close?: () => void }) => {
  const { filters, setFilters, removeFilters } = useInternalAuditLogsFilters();

  const { control, handleSubmit } = useForm({
    values: filters
  });

  const onClear = () => {
    removeFilters(['action']);
    close?.();
  };

  const onApply = handleSubmit((filters) => {
    setFilters(filters);
    close?.();
  });

  return (
    <Filter onApplyFilter={onApply} onClearFilter={onClear} close={close}>
      <FieldContainer
        control={control}
        name='action'
        defaultValue={filters.action || []}
        label='Action'
      >
        {({ field }) => (
          <Select
            mode='multiple'
            style={{ width: '100%' }}
            placeholder='Select actions'
            value={field.value}
            onChange={field.onChange}
            options={[
              { label: 'Inserted', value: 'inserted' },
              { label: 'Updated', value: 'updated' },
              { label: 'Deleted', value: 'deleted' }
            ]}
          />
        )}
      </FieldContainer>
    </Filter>
  );
};
