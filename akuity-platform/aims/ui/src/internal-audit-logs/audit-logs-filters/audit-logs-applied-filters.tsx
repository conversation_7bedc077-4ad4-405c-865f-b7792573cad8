import { AppliedFilters } from '@/lib/components/shared/applied-filter';

import {
  internalAuditLogsFiltersZodSchema,
  useInternalAuditLogsFilters
} from '../use-audit-logs-filters';
export const AppliedAuditLogsFilters = () => {
  const { appliedFilters, removeFilters } = useInternalAuditLogsFilters();

  const LIST_OF_KEYS = Object.keys(internalAuditLogsFiltersZodSchema.shape) as Array<
    keyof typeof internalAuditLogsFiltersZodSchema.shape
  >;

  return (
    <AppliedFilters
      appliedFilters={appliedFilters}
      showMoreCount={true}
      clearAllButton={{
        text: 'Clear all',
        onClick: () => removeFilters(LIST_OF_KEYS)
      }}
      className='mb-4 mt-2'
    />
  );
};
