import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import {
  faListNumeric,
  faArrowTurnDown,
  faUser,
  faCube,
  faCalendar
} from '@fortawesome/free-solid-svg-icons';
import { z } from 'zod';

import { InternalAuditFilters } from '@/lib/apiclient/aims/v1/aims_pb';
import { withDefaults, useSearchParamsState } from '@/lib/hooks/use-search-params-state';
import { ZodShapeFor } from '@/utils';

export const DEFAULT_PAGE_SIZE = 50;

export const objectTypeEnum = z.enum([
  'organization',
  'argo_cd_cluster',
  'event',
  'billing',
  'argo_cd_instance',
  'kargo_instance'
]);

export type ObjectType = z.infer<typeof objectTypeEnum>;

export const objectTypeLabels: Record<ObjectType, string> = {
  organization: 'Organization',
  argo_cd_cluster: 'Argo CD Cluster',
  event: 'Event',
  billing: 'Billing',
  argo_cd_instance: 'Argo CD Instance',
  kargo_instance: 'Kargo Instance'
};

export const objectTypeOptions = objectTypeEnum.options.map((value) => ({
  value,
  label: objectTypeLabels[value]
}));

export const internalAuditLogsFiltersZodSchema = z
  .object({
    startTime: z.string().optional(),
    endTime: z.string().optional(),
    action: z.array(z.string()).default([]),
    actorId: z.array(z.string()).default([]),
    objectType: z.array(objectTypeEnum).default([]),
    limit: withDefaults(z.coerce.number().int().positive(), DEFAULT_PAGE_SIZE),
    offset: withDefaults(z.coerce.number().int().nonnegative(), 0)
  } satisfies ZodShapeFor<InternalAuditFilters>)
  .strict();

export type InternalAuditLogsFilters = z.infer<typeof internalAuditLogsFiltersZodSchema>;

export const FILTER_META: Record<
  keyof InternalAuditLogsFilters,
  { label: string; icon: IconDefinition }
> = {
  startTime: { label: 'Start Time', icon: faCalendar },
  endTime: { label: 'End Time', icon: faCalendar },
  action: { label: 'Action', icon: faCube },
  actorId: { label: 'Actor ID', icon: faUser },
  objectType: { label: 'Resource Type', icon: faCube },
  limit: { label: 'Limit', icon: faListNumeric },
  offset: { label: 'Offset', icon: faArrowTurnDown }
};

export const useInternalAuditLogsFilters = () => {
  const {
    state: filters,
    setSearchState: setFilters,
    removeKeysFromSearch: removeFilters,
    hasActiveState,
    buildURLSearchParams,
    appliedFilters: appliedFiltersFromSearchParams
  } = useSearchParamsState(internalAuditLogsFiltersZodSchema);

  const appliedFilters = appliedFiltersFromSearchParams.map((config) => ({
    ...config,
    label: FILTER_META[config.key].label,
    icon: FILTER_META[config.key].icon
  }));

  return {
    filters,
    setFilters,
    removeFilters,
    hasActiveState,
    appliedFilters,
    buildURLSearchParams
  };
};
