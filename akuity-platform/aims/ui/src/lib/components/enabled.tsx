export const Enabled = (props: { value: string | boolean; children: React.ReactNode }) => {
  const { value } = props;
  const enabled = <span className='bg-green-100 p-1'>Enabled</span>;
  const disabled = <span className='bg-red-100 p-1'>Disabled</span>;

  let label = value === 'true' ? enabled : disabled;
  if (typeof value === 'boolean') {
    label = value ? enabled : disabled;
  }

  return (
    <>
      {label} {props.children}
    </>
  );
};
