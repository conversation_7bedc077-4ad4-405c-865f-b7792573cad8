import classNames from 'classnames';

import { AuditLog } from '@/lib/apiclient/organization/v1/organization_pb';
import IconLabel from '@/lib/components/shared/icon-label';

import { getIconForAuditObject, shouldShowParentObjectDetails } from './utils';

export interface AuditLogParentObjectProps {
  auditLog: AuditLog;
  hide?: boolean;
}

export const AuditLogParentObject = ({ auditLog, hide }: AuditLogParentObjectProps) => {
  if (!shouldShowParentObjectDetails(auditLog)) {
    return null;
  }

  let parentResources: Array<{
    name: string;
    type: AuditLog['object']['type'];
    value: string;
  }> = [];

  if (auditLog.object?.type === 'argocd_application') {
    parentResources = [
      {
        name: 'instance',
        type: 'argocd_instance',
        value: auditLog.object?.parentId?.name
      }
    ];
  } else if (auditLog.object?.type === 'k8s_resource') {
    parentResources = [
      {
        name: 'instance',
        type: 'argocd_instance',
        value: auditLog.object?.parentId?.parentName
      },
      {
        name: 'cluster',
        type: 'argocd_cluster',
        value: auditLog.object?.parentId?.name
      }
    ];
  } else if (auditLog.object?.type === 'argocd_cluster') {
    parentResources = [
      {
        name: 'instance',
        type: 'argocd_instance',
        value: auditLog.object?.parentId?.name
      }
    ];
  } else if (auditLog.object?.type === 'team_member') {
    parentResources = [
      {
        name: 'team',
        type: 'team',
        value: auditLog.object?.parentId?.name
      }
    ];
  } else if (auditLog.object?.type === 'workspace_member') {
    parentResources = [
      {
        name: 'workspace',
        type: 'workspace',
        value: auditLog.object?.parentId?.name
      }
    ];
  }

  return (
    <div className={classNames('audit-log__parent-id-block', { hidden: hide })}>
      <h4 className='font-medium mb-2'>Parent</h4>

      {parentResources.map((parentResource) => (
        <div key={parentResource.type} className='audit-log__parent-id-block--item'>
          {parentResource.name}:
          <IconLabel
            icon={getIconForAuditObject({ type: parentResource.type })}
            className='ml-auto mt-1'
          >
            {parentResource.value}
          </IconLabel>
        </div>
      ))}
    </div>
  );
};
