import { faInfoCircle } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Flex } from 'antd';
import { Popover } from 'antd';

import { AuditLog, Organization } from '@/lib/apiclient/organization/v1/organization_pb';
import IconLabel from '@/lib/components/shared/icon-label';
import { capitalize } from '@/utils';

import { AuditLogParentObject } from './parent-object';
import {
  getIconForAuditObject,
  getIconForAuditParentObject,
  getLabelForAuditParentObject,
  shouldShowParentObjectDetails
} from './utils';

export const AuditLogObject = (props: {
  auditLog: AuditLog;
  organization: Organization;
  showParentObject?: boolean;
}) => (
  <Flex align='center'>
    <IconLabel
      className='font-medium'
      icon={getIconForAuditObject(props.auditLog?.object)}
      iconClass='text-akuity-500'
    >
      {props.auditLog?.object?.id?.name || props.organization?.name || 'Unknown'}
    </IconLabel>
    {props.showParentObject &&
      shouldShowParentObjectDetails({ object: props.auditLog?.object }) && (
        <>
          <span className='ml-2'>-</span>
          <IconLabel
            className='ml-2'
            icon={getIconForAuditParentObject(props.auditLog.object)}
            iconClass='text-akuity-300/50'
          >
            {props.auditLog?.object?.parentId?.name} (
            {getLabelForAuditParentObject(props.auditLog.object)})
          </IconLabel>
          {props.auditLog?.object?.type === 'k8s_resource' && (
            <Popover
              content={<AuditLogParentObject auditLog={props.auditLog} />}
              placement='right'
              overlayInnerStyle={{ width: '256px' }}
            >
              <FontAwesomeIcon
                className='ml-2 text-sm cursor-pointer text-blue-500'
                icon={faInfoCircle}
              />
            </Popover>
          )}
        </>
      )}
    <Flex align='center' className='ml-auto'>
      <IconLabel
        icon={getIconForAuditObject(props.auditLog?.object)}
        className={`text-sm rounded border border-gray-300 px-2 font-semibold text-akuity-400`}
      >
        {(props.auditLog?.object?.type || '')
          .replace('-', '_')
          .split('_')
          .map((word) => capitalize(word))
          .join(' ')
          .replace('Argocd', '')}
      </IconLabel>
    </Flex>
  </Flex>
);
