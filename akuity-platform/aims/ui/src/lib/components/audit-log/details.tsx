import { Typography } from 'antd';

import { ArgoCDExtensionInstallEntry, IPAllowListEntry } from '@/lib/apiclient/argocd/v1/argocd_pb';
import { AuditLog } from '@/lib/apiclient/organization/v1/organization_pb';
import { plural } from '@/utils';

export const AuditLogDetails = ({ auditLog }: { auditLog: AuditLog }) => {
  if (!auditLog.details?.message && !auditLog.details?.patch) {
    return <></>;
  }

  if (auditLog.details.message) {
    return <>{auditLog.details.message}</>;
  }

  const patch = JSON.parse(auditLog.details.patch);
  const patches = Object.keys(patch);

  if (!patches.length) {
    return <></>;
  }

  return (
    <div className='audit-log__patch-summary'>
      <h2 className='mb-2 underline'>{auditLog.actor.id} updated the following properties:</h2>
      <ul>
        {patches.map((patchName) => {
          let patchDetail: string | React.ReactElement = '';
          const value: unknown = patch[patchName];

          if (!value) {
            return <></>;
          }

          if (patchName === 'auto_upgrade_disabled') {
            patchDetail = <Enabled value={value as string}>Auto Update</Enabled>;
          }

          if (patchName === 'version') {
            patchDetail = `Version to ${value as string}`;
          }

          if (patchName === 'subdomain') {
            patchDetail = `Subdomain to ${value as string}`;
          }

          if (patchName === 'argocd_cm') {
            const configMapKeys = Object.keys(value as Record<string, string>);

            patchDetail = (
              <>
                <b>Argo CD Config Map:</b>
                <ul>
                  {configMapKeys.map((configMapKey) => {
                    let configMapDetail: string | React.ReactElement = <></>;
                    const configMapValue = (value as Record<string, string>)[configMapKey];

                    if (configMapKey === 'statusbadge.enabled') {
                      configMapDetail = <Enabled value={configMapValue}>Status Badge</Enabled>;
                    }

                    if (configMapKey === 'statusbadge.url') {
                      configMapDetail = (
                        <>
                          Status Badge URL to <code>{configMapValue}</code>
                        </>
                      );
                    }

                    if (configMapKey === 'users.anonymous.enabled') {
                      configMapDetail = (
                        <Enabled value={configMapValue}>Anonymous User Access</Enabled>
                      );
                    }

                    if (configMapKey === 'ui.bannerpermanent') {
                      configMapDetail = <Enabled value={configMapValue}>Permanent Banner</Enabled>;
                    }

                    if (configMapKey === 'ui.bannercontent') {
                      configMapDetail = `Banner Content to ${configMapValue}`;
                    }

                    if (configMapKey === 'ui.bannerurl') {
                      configMapDetail = `Banner URL to ${configMapValue}`;
                    }

                    if (configMapKey === 'help.chatUrl') {
                      configMapDetail = `Chat URL to ${configMapValue}`;
                    }

                    if (configMapKey === 'help.chatText') {
                      configMapDetail = `Chat Text to ${configMapValue}`;
                    }

                    if (configMapKey === 'exec.enabled') {
                      configMapDetail = <Enabled value={configMapValue}>Web Terminal</Enabled>;
                    }

                    if (configMapKey === 'exec.shells') {
                      configMapDetail = (
                        <>
                          Set allowed shells to <code>{configMapValue}</code>
                        </>
                      );
                    }

                    if (configMapKey === 'kustomize.enabled') {
                      configMapDetail = <Enabled value={configMapValue}>Kustomize</Enabled>;
                    }

                    if (configMapKey === 'kustomize.buildOptions') {
                      configMapDetail = `Kustomize Build Options to ${configMapValue}`;
                    }

                    if (configMapKey === 'helm.enabled') {
                      configMapDetail = <Enabled value={configMapValue}>Helm</Enabled>;
                    }

                    if (configMapKey === 'helm.valueFileSchemas') {
                      configMapDetail = `Helm value file schemas to ${configMapValue}`;
                    }

                    if (configMapKey === 'ga.trackingid') {
                      configMapDetail = (
                        <>
                          Google Analytics tracking ID to <code>{configMapValue}</code>
                        </>
                      );
                    }

                    if (configMapKey === 'ga.anonymizeusers') {
                      configMapDetail = (
                        <Enabled value={configMapValue}>Anonymize Google Analytics Users</Enabled>
                      );
                    }

                    if (configMapKey === 'resource.inclusions') {
                      configMapDetail = `Resource Inclusions`;
                    }

                    if (configMapKey === 'resource.exclusions') {
                      configMapDetail = `Resource Exclusions`;
                    }

                    if (configMapKey === 'resource.compareoptions') {
                      configMapDetail = `Resource Compare Options`;
                    }

                    if (configMapKey === 'oidc.config') {
                      configMapDetail = 'OIDC Config';
                    }

                    if (configMapKey === 'dex.config') {
                      configMapDetail = 'Dex Config';
                    }

                    if (configMapKey === 'admin.enabled') {
                      configMapDetail = <Enabled value={configMapValue}>Admin account</Enabled>;
                    }

                    if (configMapKey === 'server.rbac.log.enforce.enable') {
                      configMapDetail = <Enabled value={configMapValue}>Enforce Log RBAC</Enabled>;
                    }

                    if (configMapKey.startsWith('accounts.')) {
                      const [, user = ''] = configMapKey.split('.');

                      if (user) {
                        configMapDetail = `gave ${configMapValue} capabilities to user ${user}`;
                      }
                    }

                    if (configMapKey.startsWith('resource.customizations')) {
                      const [, , customization = ''] = configMapKey.split('.');

                      if (customization) {
                        configMapDetail = `Resource Customization ${customization}`;
                      }
                    }

                    return <li key={configMapKey}>{configMapDetail}</li>;
                  })}
                </ul>
              </>
            );
          }

          if (patchName === 'argocd_secret') {
            patchDetail = `Argo CD Secret`;

            const secrets = value as Record<string, string>;

            patchDetail = (
              <ul>
                {patchDetail}
                {Object.keys(secrets).map((secret) => {
                  if (secret.endsWith('passwordMtime')) {
                    return <></>;
                  }

                  if (secret.endsWith('password')) {
                    const account = secret.startsWith('admin') ? 'admin' : secret.split('.')[1];
                    return <li key={secret}>set password of {account} account</li>;
                  }

                  return <li key={secret}>set secret {secret}</li>;
                })}
              </ul>
            );
          }

          if (patchName === 'argocd_rbac_cm') {
            patchDetail = `Argo CD RBAC Configmap`;
          }

          if (patchName === 'spec') {
            const specKeys = Object.keys(value as Record<string, unknown>);

            patchDetail = (
              <ul>
                <b>Spec:</b>
                {specKeys.map((specKey) => {
                  let specDetail: string | React.ReactElement = '';
                  const specValue = (value as Record<string, unknown>)[specKey];

                  // instance specs
                  if (specKey === 'ip_allowlist') {
                    const allowedIps = (specValue || []) as IPAllowListEntry[];

                    if (allowedIps.length) {
                      specDetail = `Allowed IPs ${allowedIps
                        .map((val) => val.ip)
                        .join(', ')} to access Argo CD Instance`;
                    } else {
                      specDetail = `Allowed all IPs to access Argo CD Instance`;
                    }
                  }

                  if (specKey === 'declarative_management_enabled') {
                    specDetail = (
                      <Enabled value={specValue as boolean}>Declarative Management</Enabled>
                    );
                  }

                  if (specKey === 'css') {
                    specDetail = `CSS`;
                  }

                  if (specKey === 'extensions') {
                    const extensions = ((specValue || []) as ArgoCDExtensionInstallEntry[]).filter(
                      (ext) => ext.id
                    );

                    if (extensions.length) {
                      specDetail = `Installed Extension${plural(extensions) ? 's' : ''} ${extensions
                        .map((extension) => `${extension.id}(${extension.version})`)
                        .join(', ')}`;
                    } else {
                      specDetail = 'Uninstalled Extension(s)';
                    }
                  }

                  // cluster specs
                  if (specKey === 'size') {
                    specDetail = `Size to ${specValue}`;
                  }

                  if (specKey === 'customImageRegistryArgoproj') {
                    specDetail = `Image Registry for argoproj to ${specValue}`;
                  }

                  if (specKey === 'customImageRegistryAkuity') {
                    specDetail = `Image Registry for akuity to ${specValue}`;
                  }

                  if (specKey === 'labels' || specKey === 'annotations') {
                    specDetail = (
                      <>
                        {specKey} to <code>{JSON.stringify(specValue)}</code>
                      </>
                    );
                  }
                  if (specKey === 'cluster_customization_defaults') {
                    specDetail = `Changed Cluster Customization Defaults`;
                  }

                  if (specKey === 'auditExtensionEnabled') {
                    specDetail = (
                      <Enabled value={specValue as boolean}>Akuity Audit Extension</Enabled>
                    );
                  }

                  if (specKey === 'syncHistoryExtensionEnabled') {
                    specDetail = (
                      <Enabled value={specValue as boolean}>Sync History Extension</Enabled>
                    );
                  }

                  if (specKey === 'assistantExtensionEnabled') {
                    specDetail = (
                      <Enabled value={specValue as boolean}>AI Assistant Extension</Enabled>
                    );
                  }

                  if (specKey === 'agentRotationCount') {
                    specDetail = 'Agent/Cluster Credential Rotation';
                  }

                  if (specKey === 'targetVersion') {
                    specDetail = (
                      <>
                        Agent Target Version to <code>{specValue as string}</code>
                      </>
                    );
                  }

                  if (!specDetail || specDetail === '') {
                    specDetail = (
                      <>
                        {specKey}
                        {specValue && (
                          <>
                            {' '}
                            to <Typography.Text code>{JSON.stringify(specValue)}</Typography.Text>
                          </>
                        )}
                      </>
                    );
                  }

                  return <li key={specKey}>{specDetail}</li>;
                })}
              </ul>
            );
          }

          if (
            patchName === 'argocd_notifications_cm' ||
            patchName === 'argocd_notifications_secret'
          ) {
            patchDetail = `Argo CD Notifications`;
          }

          if (patchName === 'argocd_image_updater_enable') {
            patchDetail = <Enabled value={value as string}>Image Updater</Enabled>;
          }

          if (patchName === 'argocd_image_updater_cm') {
            const configs = value as Record<string, string>;

            patchDetail = (
              <ul>
                <>
                  <b>Argo CD Image Updater:</b>
                  {Object.keys(configs).map((config) => {
                    const configMapValue = configs[config];
                    if (config === 'registries.conf') {
                      return <li key={config}>Set registries</li>;
                    }

                    if (config === 'git.user') {
                      return <li key={config}>Set Git user to {configMapValue}</li>;
                    }

                    if (config === 'git.email') {
                      return <li key={config}>Set Git email to {configMapValue}</li>;
                    }

                    if (config === 'git.commit-message-template') {
                      return (
                        <li key={config}>
                          Set Git commit message template to <br />
                          <code>{configMapValue}</code>
                        </li>
                      );
                    }

                    if (config === 'log.level') {
                      return <li key={config}>Set log level to {configMapValue}</li>;
                    }
                  })}
                </>
              </ul>
            );
          }

          if (patchName === 'argocd_image_updater_secret') {
            patchDetail = 'Argo CD Image Updater Secret';
          }

          if (patchName === 'argocd_image_updater_ssh_cm') {
            const config = (value as Record<string, string>)?.config;

            if (config) {
              patchDetail = (
                <>
                  Set Image Updater SSH Client Config to
                  <br />
                  <code>{config}</code>
                </>
              );
            } else {
              patchDetail = <>Unset Image Updater SSH Client Config</>;
            }
          }

          if (patchName === 'name' && value) {
            patchDetail = `Name to ${value as string}`;
          }

          if (patchName === 'description' && value) {
            patchDetail = (
              <>
                Description to <code>{value as string}</code>
              </>
            );
          }

          if (patchName === 'argocd_config_management_plugins') {
            patchDetail = 'Config Management Plugins';
          }

          if (!patchDetail) {
            patchDetail = (
              <GenericPatchDetail patchName={patchName} value={JSON.stringify(value) as string} />
            );
          }

          if (!patchDetail) {
            return <></>;
          }

          return <li key={patchName}>{patchDetail}</li>;
        })}
      </ul>
    </div>
  );
};

const GenericPatchDetail = ({ patchName, value }: { patchName: string; value: string }) => {
  value = (value || '').toString().replace(/(^"|"$)/g, '');
  value = (value || '').toString().replace(/\\/g, '');

  let formatted = '';
  let obj = {};
  try {
    obj = JSON.parse(value);
    formatted = `${JSON.stringify(obj, null, 2)}`;
  } catch {
    formatted = value;
  }

  return (
    <div className='mb-3'>
      <b>{patchName}</b>:
      {Object.keys(obj)?.length ? (
        <pre className='bg-gray-100 text-xs rounded p-2'>{formatted}</pre>
      ) : (
        <Typography.Text code>{formatted}</Typography.Text>
      )}
    </div>
  );
};

const Enabled = (props: { value: string | boolean; children: React.ReactNode }) => {
  const { value } = props;
  const enabled = <span className='bg-green-100 p-1'>Enabled</span>;
  const disabled = <span className='bg-red-100 p-1'>Disabled</span>;

  let label = value === 'true' ? enabled : disabled;
  if (typeof value === 'boolean') {
    label = value ? enabled : disabled;
  }

  return (
    <>
      {label} {props.children}
    </>
  );
};
