import { faInfoCircle, faTimes } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { message } from 'antd/lib';
import Button from 'antd/lib/button';
import Table from 'antd/lib/table';
import Tooltip from 'antd/lib/tooltip';
import moment from 'moment';
import { useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';

import { useModal } from '@/hook';
import { useListAuditLogs } from '@/hook/api';
import { BasicOrganization } from '@/lib/apiclient/aims/v1/aims_pb';
import { AppliedFilters } from '@/lib/components/shared/applied-filter';
import { FilteredIcon } from '@/lib/components/shared/filter';
import { ARGO, InstanceType, KARGO } from '@/types';
import { capitalize, omit } from '@/utils';

import { Loading } from '../loading';

import { AuditLogDetailsModal } from './audit-log-details-modal';
import { AuditLogObject } from './audit-log-object';
import { ActionFilter } from './filters/action';
import { ActorFilter } from './filters/actor';
import { ObjectFilter } from './filters/object';
import { TimeFilter } from './filters/time';
import { useAuditFilter } from './filters/useAuditFilter';
import { auditFilterTransformer } from './filters/utils';
import { filterOverwrite, getAuditQueryParams, momentParse, shouldShowDetails } from './utils';

export const AuditLogTable = ({ organization }: { organization: BasicOrganization }) => {
  const { show: showDetailsModal } = useModal();
  const [search, setSearch] = useSearchParams();

  const filterType = search.get('filterType');

  const auditFilterState = useMemo(
    () => auditFilterTransformer.toRawAuditFilter(search.toString()),
    [search]
  );

  const {
    timeFilter: timefilterProps,
    actionFilter: actionfilterProps,
    actorFilter: actorfilterProps,
    objectFilter: objectfilterProps,
    appliedFilters
  } = useAuditFilter(auditFilterState, search, setSearch);

  const overwrittenAuditFilters = useMemo(
    () => filterOverwrite(auditFilterState),
    [auditFilterState]
  );

  const { data, isFetching } = useListAuditLogs(organization?.id, overwrittenAuditFilters);

  if (isFetching) {
    return <Loading />;
  }

  const handleFilterClick = (filterType: InstanceType) => {
    const currentParams = new URLSearchParams(search);
    const currentFilters = currentParams.getAll('filterType');

    if (currentFilters.includes(filterType)) {
      currentParams.delete('filterType');
      currentFilters
        .filter((type) => type !== filterType)
        .forEach((type) => currentParams.append('filterType', type));
    } else {
      currentParams.append('filterType', filterType);
    }

    const updatedSearch = getAuditQueryParams(currentParams);
    setSearch(updatedSearch);
  };

  return (
    <div className='block w-full'>
      <div className='mb-4'>
        <Button
          onClick={() => handleFilterClick(ARGO)}
          type={search.getAll('filterType').includes(ARGO) ? 'primary' : 'default'}
        >
          ArgoCD Usage
          {search.getAll('filterType').includes(ARGO) && (
            <FontAwesomeIcon icon={faTimes} className='ml-1' />
          )}
        </Button>
        <Button
          className='ml-2'
          onClick={() => handleFilterClick(KARGO)}
          type={search.getAll('filterType').includes(KARGO) ? 'primary' : 'default'}
        >
          Kargo Usage
          {search.getAll('filterType').includes(KARGO) && (
            <FontAwesomeIcon icon={faTimes} className='ml-1' />
          )}
        </Button>
      </div>
      {!filterType && <AppliedFilters appliedFilters={appliedFilters} className='mb-5' />}
      <Table
        className='w-full'
        pagination={{
          showSizeChanger: true,
          pageSize: auditFilterState.limit,
          current: (auditFilterState.offset / auditFilterState.limit || 0) + 1,
          pageSizeOptions: [5, 10, 50, 100],
          total: data?.count,
          onShowSizeChange(_, current) {
            setSearch(
              auditFilterTransformer.toSearchString({ ...auditFilterState, limit: current })
            );
          },
          onChange: (page, limit) => {
            const newSearch = auditFilterTransformer.toSearchString({
              ...auditFilterState,
              offset: (page - 1) * +auditFilterState.limit,
              limit: limit
            });

            setSearch(newSearch);
          },
          hideOnSinglePage: false
        }}
        rowKey={(r) => `${r?.timestamp}/${r?.object.id}`}
        rowClassName={(_, index) => (index % 2 === 0 ? '' : 'bg-gray-50')}
        columns={[
          {
            title: 'Time',
            width: 200,
            render: (_, record) => {
              const parsedTimestamp = momentParse.withGlobalFormat(record.timestamp);
              const readableTimestamp = parsedTimestamp.format('YYYY-MM-DD HH:mm:ss');

              return (
                <Tooltip title='Copy Timestamp'>
                  <div
                    className='font-mono text-sm cursor-pointer hover:text-blue-500'
                    onClick={async () => {
                      try {
                        await navigator.clipboard.writeText(readableTimestamp);
                        message.success('Copied to clipboard!');
                      } catch {
                        message.error('Failed to copy');
                      }
                    }}
                  >
                    {moment().diff(parsedTimestamp, 'hours') > 24
                      ? readableTimestamp
                      : parsedTimestamp.fromNow()}
                  </div>
                </Tooltip>
              );
            },
            filterDropdown: () => (
              <TimeFilter
                onApplyFilter={() => {
                  const newSearch = auditFilterTransformer.toSearchString({
                    ...auditFilterState,
                    ...timefilterProps.raw
                  });

                  setSearch(newSearch);
                }}
                {...timefilterProps}
              />
            )
          },
          {
            title: 'Object',
            render: (_, entry) => (
              <>
                {/* @ts-expect-error due to mismatch in organization object types as akp still uses types from deprecated generate.ts */}
                <AuditLogObject showParentObject auditLog={entry} organization={organization} />
              </>
            ),
            filterDropdown: () => (
              <ObjectFilter
                onApplyFilter={() => {
                  const partialAuditFilterState = omit(auditFilterState, [
                    'k8s_resource',
                    'argocd_application',
                    'argocd_cluster',
                    'argocd_instance',
                    'argocd_project',
                    'member',
                    'organization_invite',
                    'kargo_instance',
                    'kargo_agent',
                    'kargo_promotion',
                    'kargo_freight',
                    'custom_roles',
                    'notification_cfg',
                    'api_keys',
                    'addons',
                    'addon_repos',
                    'addon_marketplace_install'
                  ]);
                  const newSearch = auditFilterTransformer.toSearchString({
                    ...partialAuditFilterState,
                    ...objectfilterProps.raw
                  });

                  setSearch(newSearch);
                }}
                {...objectfilterProps}
              />
            ),
            filterIcon: FilteredIcon,
            filtered: Boolean(
              auditFilterState?.k8s_resource?.enabled ||
                auditFilterState?.argocd_application?.enabled ||
                auditFilterState?.argocd_cluster?.enabled ||
                auditFilterState?.argocd_instance?.enabled ||
                auditFilterState?.argocd_project?.enabled ||
                auditFilterState?.member?.enabled ||
                auditFilterState?.organization_invite?.enabled ||
                auditFilterState?.kargo_instance?.enabled ||
                auditFilterState?.kargo_agent?.enabled ||
                auditFilterState?.kargo_promotion?.enabled ||
                auditFilterState?.kargo_freight?.enabled ||
                auditFilterState?.custom_roles?.enabled ||
                auditFilterState?.notification_cfg?.enabled ||
                auditFilterState?.api_keys?.enabled ||
                auditFilterState?.addons?.enabled ||
                auditFilterState?.addon_repos?.enabled ||
                auditFilterState?.addon_marketplace_install?.enabled
            )
          },
          {
            title: 'Actor',
            render: (_, entry) => (
              <span>
                {entry.actor.id} {entry.actor.type == 'argocd_user' ? '(argocd user)' : ''}
              </span>
            ),
            filterDropdown: () => (
              <ActorFilter
                onApplyFilter={() => {
                  const newSearch = auditFilterTransformer.toSearchString({
                    ...auditFilterState,
                    actor_id: actorfilterProps.filterActor
                  });

                  setSearch(newSearch);
                }}
                {...actorfilterProps}
              />
            ),
            filterIcon: FilteredIcon,
            filtered: Boolean(auditFilterState.actor_id.length)
          },
          {
            title: 'Action',
            render: (_, entry) => <span>{capitalize(entry.action)}</span>,
            filterDropdown: () => (
              <ActionFilter
                onApplyFilter={() => {
                  const newSearch = auditFilterTransformer.toSearchString({
                    ...auditFilterState,
                    ...actionfilterProps.raw
                  });

                  setSearch(newSearch);
                }}
                availableActions={[
                  'created',
                  'deleted',
                  'updated',
                  'action-ran',
                  'sync-started',
                  'rollback-started',
                  'exported-as-csv'
                ]}
                {...actionfilterProps}
              />
            ),
            filterIcon: FilteredIcon,
            filtered: Boolean(auditFilterState.action.length)
          },
          {
            title: 'Details',
            render: (_, entry) =>
              shouldShowDetails(entry) ? (
                <Button
                  icon={<FontAwesomeIcon icon={faInfoCircle} />}
                  onClick={() =>
                    showDetailsModal((p) => (
                      <AuditLogDetailsModal
                        {...p}
                        auditDetails={entry}
                        // @ts-expect-error due to mismatch in organization object types as akp still uses types from deprecated generate.ts
                        organization={organization}
                      />
                    ))
                  }
                  type='text'
                  size='small'
                  className='text-gray-500'
                />
              ) : null
          }
        ]}
        dataSource={data?.auditLogs || []}
      />
    </div>
  );
};
