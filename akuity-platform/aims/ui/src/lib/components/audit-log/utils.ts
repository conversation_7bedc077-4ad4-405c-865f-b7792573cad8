import {
  faBell,
  faFileCode,
  faForward,
  faObjectGroup,
  faTerminal,
  faTruck
} from '@fortawesome/free-solid-svg-icons';
import {
  faEnvelope,
  faIdBadge,
  faLayerGroup,
  faProjectDiagram,
  faServer,
  faUsers,
  faUsersRectangle,
  faCircleDot,
  faShoePrints,
  faIdCard,
  faKey
} from '@fortawesome/free-solid-svg-icons';
import moment from 'moment';

import {
  AuditLog,
  AuditLog_AuditObject,
  ObjectFilter
} from '@/lib/apiclient/organization/v1/organization_pb';
import { ARGO, InstanceType, KARGO } from '@/types';
import { RawAuditFilters } from '@/types';

import { FILTERS_MAPPING } from './filters/utils';

export function getIconForAuditObject(obj: Pick<AuditLog_AuditObject, 'type' | 'id'>) {
  switch (obj.type) {
    case 'organization':
      return faUsers;
    case 'organization_notification_config':
      return faBell;
    case 'argocd_instance':
      return faServer;
    case 'argocd_project':
      return faObjectGroup;
    case 'argocd_application':
      return faLayerGroup;
    case 'member':
      return faIdBadge;
    case 'k8s_resource':
      return faFileCode;
    case 'argocd_cluster':
      return faProjectDiagram;
    case 'organization-invite':
      return faEnvelope;
    case 'team':
      return faUsersRectangle;
    case 'team_member':
      return faIdBadge;
    case 'workspace':
      return faCircleDot;
    case 'workspace_member':
      if (obj?.id.kind === 'user') {
        return faIdBadge;
      }
      return faUsersRectangle;
    case 'kargo_instance':
      return faServer;
    case 'kargo_agent':
      return faTerminal;
    case 'kargo_promotion':
      return faForward;
    case 'kargo_freight':
      return faTruck;
    case 'audit_log':
      return faShoePrints;
    case 'custom_role':
      return faIdCard;
    case 'api_key':
      return faKey;
  }
}

export const getIconForAuditParentObject = (obj: Pick<AuditLog_AuditObject, 'type'>) => {
  switch (obj?.type) {
    case 'argocd_application':
    case 'argocd_cluster':
      return getIconForAuditObject({ type: 'argocd_instance' });
    case 'k8s_resource':
      return getIconForAuditObject({ type: 'argocd_cluster' });
    case 'team_member':
      return getIconForAuditObject({ type: 'team' });
    case 'workspace_member':
      return getIconForAuditObject({ type: 'workspace' });
  }
};

export const getLabelForAuditParentObject = (obj: Pick<AuditLog_AuditObject, 'type'>) => {
  switch (obj?.type) {
    case 'argocd_application':
    case 'argocd_cluster':
      return 'Instance';
    case 'k8s_resource':
      return 'Cluster';
    case 'team_member':
      return 'Team';
    case 'workspace_member':
      return 'Workspace';
  }
};

export const shouldShowDetails = (entry: AuditLog) => {
  return (entry.details?.patch || entry.details?.message) && entry.action !== 'deleted';
};

export const shouldShowParentObjectDetails = (entry: Pick<AuditLog, 'object'>) =>
  entry.object?.type === 'argocd_application' ||
  entry.object?.type === 'k8s_resource' ||
  entry.object?.type === 'argocd_cluster' ||
  entry.object?.type === 'team_member' ||
  entry.object?.type === 'workspace_member';

export const auditFilterKargoInstanceScope = (
  instanceName: string,
  filters: RawAuditFilters
): RawAuditFilters => {
  const defaultFilters = (): Partial<RawAuditFilters> => {
    const instanceAsParentObjectFilter = new ObjectFilter({
      enabled: true,
      objectParentName: [instanceName]
    });

    const instanceAsGrandparentObjectFilter = new ObjectFilter({
      enabled: true,
      objectParentParentName: [instanceName]
    });

    return {
      kargo_agent: instanceAsParentObjectFilter,
      kargo_promotion: instanceAsGrandparentObjectFilter,
      kargo_freight: instanceAsGrandparentObjectFilter,
      kargo_instance: new ObjectFilter({ enabled: true, objectName: [instanceName] })
    };
  };

  const newFilters = { ...filters };

  if (newFilters?.kargo_agent?.enabled) {
    newFilters.kargo_agent.objectParentName = [instanceName];
    return newFilters;
  }

  const resourcesWithInstanceAsParentParent: (keyof Pick<
    RawAuditFilters,
    'kargo_promotion' | 'kargo_freight'
  >)[] = ['kargo_promotion', 'kargo_freight'];

  for (const resource of resourcesWithInstanceAsParentParent) {
    if (newFilters?.[resource]?.enabled) {
      newFilters[resource].objectParentParentName = [instanceName];
      return newFilters;
    }
  }

  return {
    ...filters,
    ...defaultFilters()
  };
};

export const auditFilterInstanceScope = (
  instanceName: string,
  filters: RawAuditFilters
): RawAuditFilters => {
  const defaultFilters = (): Partial<RawAuditFilters> => {
    const instanceAsParentObjectFilter = new ObjectFilter({
      enabled: true,
      objectParentName: [instanceName]
    });

    return {
      k8s_resource: new ObjectFilter({
        enabled: true,
        objectParentParentName: [instanceName]
      }),
      argocd_cluster: instanceAsParentObjectFilter,
      argocd_application: instanceAsParentObjectFilter,
      argocd_project: instanceAsParentObjectFilter,
      argocd_instance: new ObjectFilter({ enabled: true, objectName: [instanceName] }),
      addon_repos: instanceAsParentObjectFilter,
      addons: new ObjectFilter({ enabled: true, objectParentParentName: [instanceName] }),
      addon_marketplace_install: new ObjectFilter({
        enabled: true,
        objectParentParentName: [instanceName]
      })
    };
  };

  const newFilters = { ...filters };

  if (filters?.k8s_resource?.enabled) {
    newFilters.k8s_resource.objectParentParentName = [instanceName];
    return newFilters;
  }

  if (newFilters.addons?.enabled) {
    newFilters.addons.objectParentParentName = [instanceName];
    return newFilters;
  }

  if (newFilters.addon_marketplace_install?.enabled) {
    newFilters.addon_marketplace_install.objectParentParentName = [instanceName];
    return newFilters;
  }

  const resourcesWithInstanceAsParent: (keyof Pick<
    RawAuditFilters,
    'argocd_cluster' | 'argocd_application' | 'argocd_project' | 'addon_repos'
  >)[] = ['argocd_application', 'argocd_cluster', 'argocd_project', 'addon_repos'];

  for (const resource of resourcesWithInstanceAsParent) {
    if (newFilters?.[resource]?.enabled) {
      newFilters[resource].objectParentName = [instanceName];
      return newFilters;
    }
  }

  return {
    ...filters,
    ...defaultFilters()
  };
};

export const filterOverwrite = (
  filters: RawAuditFilters,
  type?: InstanceType,
  name?: string
): RawAuditFilters => {
  if (type === ARGO) return auditFilterInstanceScope(name, filters);
  else if (type === KARGO) return auditFilterKargoInstanceScope(name, filters);
  else return filters;
};

export const getAuditQueryParams = (params: URLSearchParams): string => {
  const activeFilters = params.getAll('filterType');
  let queryParts: string[] = [];

  activeFilters.forEach((filter) => {
    const filters = FILTERS_MAPPING[filter as InstanceType];
    if (filters) {
      queryParts = [...queryParts, ...filters];
    }
  });

  return queryParts.join('&');
};

const COMMON_DATE_FORMAT = 'YYYY-MM-DD HH:mm:ss Z';

export const momentParse = {
  withGlobalFormat: (timestamp: moment.MomentInput) => moment(timestamp, COMMON_DATE_FORMAT)
};
