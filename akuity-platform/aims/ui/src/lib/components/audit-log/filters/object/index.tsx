import { Radio, Space } from 'antd';
import { useState } from 'react';

import { Filter, FilterProps } from '@/lib/components/shared/filter';
import { FormContainer } from '@/lib/components/shared/forms';
import type { AuditFilterObjectType, TFilterScopes } from '@/types';

import { CommonInput } from './common-input';
import {
  auditObjectMapper as _auditObjectMapper,
  cleanObject,
  metadataForTypeAndInput,
  ObjectType,
  toRawObject
} from './utils';

export type ObjectFilterProps = Pick<FilterProps, 'onApplyFilter'> &
  ReturnType<typeof useObjectFilter> & {
    filterScopes?: TFilterScopes;
  };

export const useObjectFilter = ({
  onClearFilter,
  initialValue
}: {
  onClearFilter: () => void;
  initialValue?: Partial<ObjectType>;
}) => {
  const [object, setObject] = useState<ObjectType>({ ...cleanObject, ...initialValue });

  const onObjectNameChange = (name: string) => setObject({ ...object, name });

  const onObjectTypeSelect = (type: AuditFilterObjectType | '') =>
    setObject({ ...cleanObject, type });

  const onObjectGroupChange = (group: string) => setObject({ ...object, group });

  const onObjectKindChange = (kind: string) => setObject({ ...object, kind });

  const onObjectParentIdChange = (parentId: string) => setObject({ ...object, parentId });

  const _onClearFilter = () => {
    setObject({ ...cleanObject });
    onClearFilter();
  };

  return {
    object,
    inputControllers: {
      onObjectGroupChange,
      onObjectKindChange,
      onObjectParentIdChange,
      onObjectNameChange,
      onObjectTypeSelect
    },
    onClearFilter: _onClearFilter,
    raw: toRawObject(object)
  };
};

export const ObjectFilter = ({
  onApplyFilter,
  object,
  inputControllers,
  onClearFilter,
  filterScopes
}: ObjectFilterProps) => {
  const auditObjectMapper = { ..._auditObjectMapper };
  const inputs = {
    name: metadataForTypeAndInput(object.type, 'name'),
    group: metadataForTypeAndInput(object.type, 'group'),
    kind: metadataForTypeAndInput(object.type, 'kind'),
    parentId: metadataForTypeAndInput(object.type, 'parentId')
  };

  if (filterScopes === 'instance') {
    switch (object.type) {
      case 'argocd_application':
      case 'argocd_cluster':
      case 'argocd_project':
        delete inputs?.parentId;
        break;
    }

    delete auditObjectMapper.argocd_instance;
    delete auditObjectMapper.member;
    delete auditObjectMapper.organization_invite;
    delete auditObjectMapper.custom_roles;

    if (object.type === 'argocd_instance') {
      delete inputs.name;
      delete inputs.group;
      delete inputs.kind;
      delete inputs.parentId;
    }
  }

  if (filterScopes === 'kargo_instance') {
    delete auditObjectMapper.kargo_instance;
    delete auditObjectMapper.argocd_instance;
    delete auditObjectMapper.argocd_application;
    delete auditObjectMapper.argocd_cluster;
    delete auditObjectMapper.argocd_project;
    delete auditObjectMapper.member;
    delete auditObjectMapper.organization_invite;
    delete auditObjectMapper.k8s_resource;
    delete auditObjectMapper.custom_roles;

    if (object.type === 'kargo_instance') {
      delete inputs.name;
      delete inputs.group;
      delete inputs.kind;
      delete inputs.parentId;
    }
  }

  return (
    <Filter onApplyFilter={onApplyFilter} onClearFilter={onClearFilter}>
      <FormContainer label='Type' noBorder>
        <Radio.Group
          value={object.type}
          onChange={(e) => inputControllers.onObjectTypeSelect(e.target.value)}
        >
          <Space direction='vertical'>
            {Object.entries(auditObjectMapper).map(([objectType, objectTypeHumanReadable]) => (
              <Radio value={objectType} key={objectType}>
                {objectTypeHumanReadable}
              </Radio>
            ))}
          </Space>
        </Radio.Group>
      </FormContainer>

      {inputs.name?.shouldRender && (
        <CommonInput
          value={object.name}
          onChange={inputControllers.onObjectNameChange}
          {...inputs.name.metadataProps}
        />
      )}

      <div className='flex gap-3'>
        {inputs.group?.shouldRender && (
          <CommonInput
            value={object.group}
            onChange={inputControllers.onObjectGroupChange}
            {...inputs.group.metadataProps}
          />
        )}

        {inputs.kind?.shouldRender && (
          <CommonInput
            value={object.kind}
            onChange={inputControllers.onObjectKindChange}
            {...inputs.kind.metadataProps}
          />
        )}
      </div>

      {inputs.parentId?.shouldRender && (
        <CommonInput
          value={object.parentId}
          onChange={inputControllers.onObjectParentIdChange}
          {...inputs.parentId.metadataProps}
        />
      )}
    </Filter>
  );
};
