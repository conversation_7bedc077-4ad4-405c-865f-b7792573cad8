import { PlainMessage } from '@bufbuild/protobuf';

import { AuditFilters, ObjectFilter } from '@/lib/apiclient/organization/v1/organization_pb';
import { ARGO, InstanceType, KARGO } from '@/types';
import type { RawAuditFilters } from '@/types';
import { omit } from '@/utils';

import { defaultAuditFilters } from './filters';
import type { timerangeOpts, timerangeState } from './shared';

export const auditFilterKeys: Record<
  keyof Omit<PlainMessage<AuditFilters>, 'workspace' | 'workspaceMember'>,
  string
> = {
  // Pagination
  limit: 'filters.limit',
  offset: 'filters.offset',
  // Time
  startTime: 'filters.startTime',
  endTime: 'filters.endTime',
  // Object
  k8sResource: 'filters.k8sResource',
  argocdApplication: 'filters.argocdApplication',
  argocdCluster: 'filters.argocdCluster',
  argocdInstance: 'filters.argocdInstance',
  argocdProject: 'filters.argocdProject',
  member: 'filters.member',
  organizationInvite: 'filters.organizationInvite',
  kargoInstance: 'filters.kargoInstance',
  kargoAgent: 'filters.kargoAgent',
  kargoPromotion: 'filters.kargoPromotion',
  kargoFreight: 'filters.kargoFreight',
  customRoles: 'filters.customRoles',
  apiKeys: 'filters.apiKeys',
  notificationCfg: 'filters.notificationCfg',
  addons: 'filters.addons',
  addonRepos: 'filters.addonRepos',
  addonMarketplaceInstall: 'filters.addonMarketplaceInstall',
  // Actor
  actorId: 'filters.actorId',
  actorType: 'filters.actorType',
  // Action
  action: 'filters.action'
};

export const FILTERS_MAPPING: Record<InstanceType, string[]> = {
  [ARGO]: [
    `${auditFilterKeys.argocdInstance}.enabled=true`,
    `${auditFilterKeys.argocdApplication}.enabled=true`,
    `${auditFilterKeys.argocdCluster}.enabled=true`,
    `${auditFilterKeys.argocdProject}.enabled=true`,
    `${auditFilterKeys.k8sResource}.enabled=true`,
    `filterType=${ARGO}`
  ],
  [KARGO]: [
    `${auditFilterKeys.kargoInstance}.enabled=true`,
    `${auditFilterKeys.kargoAgent}.enabled=true`,
    `${auditFilterKeys.kargoFreight}.enabled=true`,
    `${auditFilterKeys.kargoPromotion}.enabled=true`,
    `filterType=${KARGO}`
  ]
};

export const getAuditFilterString = (type: InstanceType) => FILTERS_MAPPING[type].join('&');

export const customTimeRange = (type: timerangeOpts): timerangeState => {
  const state: timerangeState = {
    startTime: new Date(),
    endTime: new Date()
  };

  switch (type) {
    case '7-days':
      state.startTime.setDate(state.endTime.getDate() - 7);
      break;
    case '1-month':
      state.startTime.setMonth(state.endTime.getMonth() - 1);
      break;
    case '1-year':
      state.startTime.setFullYear(state.endTime.getFullYear() - 1);
      break;
  }
  return state;
};

export const flattenObject = <T>(obj: T): Record<string, unknown> => {
  const flat: Record<string, unknown> = {};

  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'object' && !Array.isArray(value)) {
      const nestedFlatten = flattenObject(value);

      for (const [nestedKey, nestedValue] of Object.entries(nestedFlatten)) {
        flat[`${key}.${nestedKey}`] = nestedValue;
      }

      continue;
    }

    flat[key] = value;
  }

  return flat;
};

export const auditFilterTransformer = {
  toRawAuditFilter: (searchString: string): RawAuditFilters => {
    const search = new URLSearchParams(searchString);

    const rawAuditFilters: Partial<RawAuditFilters> = {};

    const limit = search.get(auditFilterKeys.limit);
    if (limit) {
      rawAuditFilters['limit'] = Number(limit);
    }

    const offset = search.get(auditFilterKeys.offset);
    if (offset) {
      rawAuditFilters['offset'] = Number(offset);
    }

    const start_time = search.get(auditFilterKeys.startTime);
    if (start_time) {
      rawAuditFilters['start_time'] = start_time;
    }

    const end_time = search.get(auditFilterKeys.endTime);
    if (end_time) {
      rawAuditFilters['end_time'] = end_time;
    }

    const actor_id = search.getAll(auditFilterKeys.actorId);
    if (actor_id?.length) {
      rawAuditFilters['actor_id'] = actor_id;
    }

    const actor_type = search.getAll(auditFilterKeys.actorType);
    if (actor_type?.length) {
      rawAuditFilters['actor_type'] = actor_type;
    }

    const action = search.getAll(auditFilterKeys.action);
    if (action?.length) {
      rawAuditFilters['action'] = action;
    }

    for (const objectKey of objects) {
      let auditFilterKey: keyof AuditFilters;
      switch (objectKey) {
        case 'k8s_resource':
          auditFilterKey = 'k8sResource';
          break;
        case 'argocd_application':
          auditFilterKey = 'argocdApplication';
          break;
        case 'argocd_cluster':
          auditFilterKey = 'argocdCluster';
          break;
        case 'argocd_instance':
          auditFilterKey = 'argocdInstance';
          break;
        case 'argocd_project':
          auditFilterKey = 'argocdProject';
          break;
        case 'member':
          auditFilterKey = 'member';
          break;
        case 'organization_invite':
          auditFilterKey = 'organizationInvite';
          break;
        case 'kargo_instance':
          auditFilterKey = 'kargoInstance';
          break;
        case 'kargo_agent':
          auditFilterKey = 'kargoAgent';
          break;
        case 'kargo_promotion':
          auditFilterKey = 'kargoPromotion';
          break;
        case 'kargo_freight':
          auditFilterKey = 'kargoFreight';
          break;
        case 'custom_roles':
          auditFilterKey = 'customRoles';
          break;
        case 'notification_cfg':
          auditFilterKey = 'notificationCfg';
          break;
        case 'api_keys':
          auditFilterKey = 'apiKeys';
          break;
        case 'addons':
          auditFilterKey = 'addons';
          break;
        case 'addon_repos':
          auditFilterKey = 'addonRepos';
          break;
        case 'addon_marketplace_install':
          auditFilterKey = 'addonMarketplaceInstall';
          break;
      }

      rawAuditFilters[objectKey] = new ObjectFilter({});

      if (search.get(`${auditFilterKeys[auditFilterKey]}.enabled`) === 'true') {
        rawAuditFilters[objectKey].enabled = true;
        for (const resource of objectResources) {
          const resourceValues = search.getAll(`${auditFilterKeys[auditFilterKey]}.${resource}`);

          if (resourceValues?.length) {
            (rawAuditFilters[objectKey][resource] as string[]) = resourceValues;
          }
        }
      }
    }
    return { ...defaultAuditFilters, ...rawAuditFilters };
  },
  toSearchString: (rawAuditFilters: RawAuditFilters, _excludeKeys?: string[]): URLSearchParams => {
    const excludeKeys = _excludeKeys || [];
    const search = new URLSearchParams();
    const _append = (key: string, items: string[]) => {
      for (const item of items) {
        search.append(key, item);
      }
    };

    if (!excludeKeys.includes(auditFilterKeys.limit) && rawAuditFilters.limit) {
      search.set(auditFilterKeys.limit, rawAuditFilters.limit.toString());
    }

    if (!excludeKeys.includes(auditFilterKeys.offset) && rawAuditFilters.offset) {
      search.set(auditFilterKeys.offset, rawAuditFilters.offset.toString());
    }

    if (!excludeKeys.includes(auditFilterKeys.startTime) && rawAuditFilters.start_time) {
      search.set(auditFilterKeys.startTime, rawAuditFilters.start_time);
    }

    if (!excludeKeys.includes(auditFilterKeys.endTime) && rawAuditFilters.end_time) {
      search.set(auditFilterKeys.endTime, rawAuditFilters.end_time);
    }

    if (!excludeKeys.includes(auditFilterKeys.actorId) && rawAuditFilters.actor_id?.length) {
      _append(auditFilterKeys.actorId, rawAuditFilters.actor_id);
    }

    if (!excludeKeys.includes(auditFilterKeys.actorType) && rawAuditFilters.actor_type?.length) {
      _append(auditFilterKeys.actorType, rawAuditFilters.actor_type);
    }

    if (!excludeKeys.includes(auditFilterKeys.action) && rawAuditFilters.action?.length) {
      _append(auditFilterKeys.action, rawAuditFilters.action);
    }

    const resources: Array<{
      key: string;
      rawKey: TObjectTypes;
    }> = [
      {
        key: auditFilterKeys.k8sResource,
        rawKey: 'k8s_resource'
      },
      {
        key: auditFilterKeys.argocdApplication,
        rawKey: 'argocd_application'
      },
      {
        key: auditFilterKeys.argocdCluster,
        rawKey: 'argocd_cluster'
      },
      {
        key: auditFilterKeys.argocdInstance,
        rawKey: 'argocd_instance'
      },
      {
        key: auditFilterKeys.argocdProject,
        rawKey: 'argocd_project'
      },
      {
        key: auditFilterKeys.member,
        rawKey: 'member'
      },
      {
        key: auditFilterKeys.organizationInvite,
        rawKey: 'organization_invite'
      },
      {
        key: auditFilterKeys.kargoInstance,
        rawKey: 'kargo_instance'
      },
      {
        key: auditFilterKeys.kargoAgent,
        rawKey: 'kargo_agent'
      },
      {
        key: auditFilterKeys.kargoPromotion,
        rawKey: 'kargo_promotion'
      },
      {
        key: auditFilterKeys.kargoFreight,
        rawKey: 'kargo_freight'
      },
      {
        key: auditFilterKeys.customRoles,
        rawKey: 'custom_roles'
      },
      {
        key: auditFilterKeys.notificationCfg,
        rawKey: 'notification_cfg'
      },
      {
        key: auditFilterKeys.apiKeys,
        rawKey: 'api_keys'
      },
      {
        key: auditFilterKeys.addons,
        rawKey: 'addons'
      },
      {
        key: auditFilterKeys.addonRepos,
        rawKey: 'addon_repos'
      },
      {
        key: auditFilterKeys.addonMarketplaceInstall,
        rawKey: 'addon_marketplace_install'
      }
    ];

    for (const resource of resources) {
      if (!excludeKeys.includes(resource.key) && rawAuditFilters?.[resource.rawKey]?.enabled) {
        search.set(`${resource.key}.enabled`, 'true');

        const objects = flattenObject(omit(rawAuditFilters?.[resource.rawKey], ['enabled']));

        for (const [flatObjectKey, value] of Object.entries(objects)) {
          _append(`${resource.key}.${flatObjectKey}`, value as string[]);
        }
      }
    }

    return search;
  }
};

type TObjectTypes = keyof Pick<
  RawAuditFilters,
  | 'k8s_resource'
  | 'argocd_application'
  | 'argocd_cluster'
  | 'argocd_instance'
  | 'argocd_project'
  | 'member'
  | 'organization_invite'
  | 'kargo_instance'
  | 'kargo_agent'
  | 'kargo_promotion'
  | 'kargo_freight'
  | 'custom_roles'
  | 'notification_cfg'
  | 'api_keys'
  | 'addons'
  | 'addon_repos'
  | 'addon_marketplace_install'
>;

const objects: Array<TObjectTypes> = [
  'k8s_resource',
  'argocd_application',
  'argocd_cluster',
  'argocd_instance',
  'argocd_project',
  'member',
  'organization_invite',
  'kargo_instance',
  'kargo_agent',
  'kargo_promotion',
  'kargo_freight',
  'custom_roles',
  'notification_cfg',
  'api_keys',
  'addons',
  'addon_repos',
  'addon_marketplace_install'
];

const objectResources: Array<keyof PlainMessage<ObjectFilter>> = [
  'objectGroup',
  'objectKind',
  'objectName',
  'objectParentApplicationName',
  'objectParentName',
  'objectParentParentName'
];
