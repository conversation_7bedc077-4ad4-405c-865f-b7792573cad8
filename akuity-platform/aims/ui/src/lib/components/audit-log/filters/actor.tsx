import { Select, Tag } from 'antd';
import { useEffect, useState } from 'react';

import { Filter, FilterProps } from '@/lib/components/shared/filter';
import { FormContainer } from '@/lib/components/shared/forms';

export type ActorFilterProps = Pick<FilterProps, 'onApplyFilter'> &
  ReturnType<typeof useActorFilter> & {
    suggestedActors?: string[];
    suggestionLimit?: number;
  };

export const useActorFilter = ({
  onClearFilter,
  initialValue
}: {
  onClearFilter?: () => void;
  initialValue?: string[];
} = {}) => {
  const [actors, setActors] = useState<string[]>(initialValue || []);

  useEffect(() => {
    setActors(initialValue || []);
  }, [initialValue]);

  const onSelectActor = (actor: string) => {
    if (!actors.find((existingActor) => existingActor === actor)) {
      setActors([...actors, actor]);
    }
  };

  const onUnselectActor = (actor: string) =>
    setActors(actors.filter((existingActor) => existingActor !== actor));

  const _onClearFilter = () => {
    setActors([]);
    onClearFilter();
  };

  return {
    filterActor: actors,
    onSelectActor,
    onClearFilter: _onClearFilter,
    onUnselectActor,
    raw: {
      actor_id: actors
    }
  };
};

export const ActorFilter = ({
  onApplyFilter,
  onClearFilter,
  onSelectActor,
  filterActor,
  suggestedActors = [],
  suggestionLimit = 3,
  onUnselectActor
}: ActorFilterProps) => {
  const [searchActor, setSearchActor] = useState('');
  const suggestions = filterActor.length
    ? suggestedActors.filter((actor) => actor.includes(searchActor)).slice(0, suggestionLimit)
    : suggestedActors.slice(0, suggestionLimit);

  if (!suggestions.find((suggestion) => suggestion === 'argocd_auto_sync')) {
    suggestions.push('argocd_auto_sync');
  }

  if (searchActor && !suggestions.find((suggestion) => suggestion === searchActor)) {
    suggestions.push(searchActor);
  }

  return (
    <Filter onApplyFilter={onApplyFilter} onClearFilter={onClearFilter}>
      <FormContainer label='Actor Filter'>
        <Select
          showSearch
          value={searchActor || null}
          showArrow={false}
          filterOption={false}
          onChange={(actor) => {
            setSearchActor(actor);
          }}
          placeholder='Actor'
          notFoundContent={null}
          style={{ width: '100%' }}
          onSearch={setSearchActor}
          onSelect={onSelectActor}
        >
          {suggestions.map((suggestion) => (
            <Select.Option key={suggestion}>{suggestion}</Select.Option>
          ))}
        </Select>
      </FormContainer>
      {!!filterActor.length && (
        <div className='flex flex-wrap items-center gap-4'>
          {filterActor.map((actor) => (
            <Tag
              className='flex items-center'
              key={actor}
              closable
              onClose={() => onUnselectActor(actor)}
            >
              {actor}
            </Tag>
          ))}
        </div>
      )}
    </Filter>
  );
};
