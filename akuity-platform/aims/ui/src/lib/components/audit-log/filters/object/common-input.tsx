import { Input } from 'antd';

import { FormContainer } from '@/lib/components/shared/forms';

export type CommonInputProps = {
  value: string;
  onChange: (value: string) => void;
  label?: string;
  className?: string;
  placeholder?: string;
  type?: string;
};

export const CommonInput = (props: CommonInputProps) => (
  <FormContainer label={props.label} wrapperClassName={props.className}>
    <Input
      value={props.value}
      onChange={(e) => props.onChange(e.target.value)}
      placeholder={props.placeholder}
      type={props.type}
    />
  </FormContainer>
);
