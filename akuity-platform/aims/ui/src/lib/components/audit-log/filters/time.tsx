import { Radio } from 'antd';
import generatePicker from 'antd/es/date-picker/generatePicker';
import moment from 'moment';
import type { Moment } from 'moment';
import momentGenerateConfig from 'rc-picker/lib/generate/moment';
import { useEffect, useState } from 'react';

import { Filter } from '@/lib/components/shared/filter';
import { FormContainer } from '@/lib/components/shared/forms';

import { timerangeOpts, timerangeState } from './shared';
import { customTimeRange } from './utils';

export type TimeFilterProps = {
  onApplyFilter: () => void;
} & ReturnType<typeof useTimeFilter>;

// is decoupled to preserve state on filter apply
export const useTimeFilter = ({
  onClearFilter,
  initialValues
}: {
  onClearFilter?: () => void;
  initialValues?: timerangeState;
} = {}) => {
  const [timerange, setTimeRange] = useState<timerangeState>({
    startTime: initialValues?.startTime || null,
    endTime: initialValues?.endTime || null
  });

  const [rangeOpts, setRangeOpts] = useState<'' | timerangeOpts>('');

  useEffect(() => {
    if (rangeOpts !== '' && rangeOpts !== 'custom') {
      setTimeRange(customTimeRange(rangeOpts));
    }
  }, [rangeOpts]);

  const _onClearFilter = () => {
    setRangeOpts('');
    setTimeRange({ startTime: null, endTime: null });
    onClearFilter?.();
  };

  const onRangeOptsChange = (value: timerangeOpts) => setRangeOpts(value);

  const onCustomCalendarChange = (values: [moment.Moment, moment.Moment]) => {
    setTimeRange({
      startTime: values[0]?.toDate(),
      endTime: values[1]?.toDate()
    });
  };

  return {
    timerange,
    rangeOpts,
    onClearFilter: _onClearFilter,
    onRangeOptsChange,
    onCustomCalendarChange,
    raw: {
      start_time: timerange.startTime?.toISOString() || '',
      end_time: timerange.endTime?.toISOString() || ''
    }
  };
};

const DatePicker = generatePicker<Moment>(momentGenerateConfig);

export const TimeFilter = ({
  onApplyFilter,
  onClearFilter,
  rangeOpts,
  onRangeOptsChange,
  onCustomCalendarChange,
  timerange
}: TimeFilterProps) => {
  return (
    <Filter onApplyFilter={onApplyFilter} onClearFilter={onClearFilter}>
      <FormContainer label='Time range filter' noBorder>
        <Radio.Group
          value={rangeOpts}
          onChange={(e) => onRangeOptsChange(e.target.value)}
          className='mt-2'
        >
          <div className='flex flex-wrap gap-5'>
            <Radio value={'7-days'}>7 days</Radio>
            <Radio value={'1-month'}>1 month</Radio>
            <Radio value={'1-year'}>1 year</Radio>
            <Radio value={'custom'}>custom</Radio>
            <DatePicker.RangePicker
              showTime
              className='mt-2'
              onCalendarChange={onCustomCalendarChange}
              defaultValue={[
                timerange.startTime ? moment(timerange.startTime) : null,
                timerange.endTime ? moment(timerange.endTime) : null
              ]}
            />
          </div>
        </Radio.Group>
      </FormContainer>
    </Filter>
  );
};
