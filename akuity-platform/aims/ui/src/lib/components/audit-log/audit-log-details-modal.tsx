import { Modal } from 'antd';
import { Button } from 'antd';

import { AuditLog, Organization } from '@/lib/apiclient/organization/v1/organization_pb';

import { AuditLogObject } from './audit-log-object';
import { AuditLogDetails } from './details';

type Props = {
  hide: () => void;
  visible: boolean;
  auditDetails: AuditLog;
  organization: Organization;
};

export const AuditLogDetailsModal = ({ hide, auditDetails, organization, visible }: Props) => {
  return (
    <Modal
      open={visible}
      onCancel={hide}
      footer={
        <Button type='primary' onClick={hide}>
          Close
        </Button>
      }
      closable={false}
    >
      {auditDetails && (
        <>
          <AuditLogObject auditLog={auditDetails} organization={organization} />
          {Boolean(auditDetails.details) && (
            <div className='mt-5'>
              <AuditLogDetails auditLog={auditDetails} />
            </div>
          )}
        </>
      )}
    </Modal>
  );
};
