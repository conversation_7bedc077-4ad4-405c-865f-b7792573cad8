import { Alert } from 'antd';

type Props = {
  isUnsupported: boolean;
  version: string;
};

export const UnsupportedVersionBanner = ({ isUnsupported, version }: Props) => {
  if (!isUnsupported) {
    return null;
  }

  return (
    <Alert
      banner
      className='mb-6'
      message='Critical: Unsupported Version Detected'
      description={`This instance is running version ${version} which is no
                  longer supported. System security and stability are at risk! Most functionality is
                  now in read-only mode.`}
      type='error'
      showIcon
    />
  );
};
