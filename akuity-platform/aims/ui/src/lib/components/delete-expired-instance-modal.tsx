import { PlainMessage } from '@bufbuild/protobuf';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Input, Modal, Tag } from 'antd';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { useDeleteArgoInstance } from '@/hook/api';
import { Audit } from '@/lib/apiclient/aims/v1/aims_pb';
import { Instance } from '@/lib/apiclient/argocd/v1/argocd_pb';
import { AuditForm } from '@/lib/components/shared/audit-form';
import { FieldContainer } from '@/lib/components/shared/forms';
import { zodAuditSchema } from '@/utils';

const schema = z.object({
  confirm: z.string().min(1, 'Confirmation is required.'),
  audit: zodAuditSchema
});

type ConfirmForm = z.infer<typeof schema>;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type AnyError = any;

export const DeleteExpiredInstanceModal = (props: {
  visible: boolean;
  instToDelete: Instance | null;
  onClose: (confirmed: boolean, error?: AnyError) => void;
}) => {
  const { visible, instToDelete, onClose } = props;

  const deleteMutation = useDeleteArgoInstance();

  const deleteInstance = async (vals: ConfirmForm) => {
    if (vals.confirm !== instToDelete?.name) {
      return;
    }
    deleteMutation.mutate(
      {
        instanceId: instToDelete.id,
        audit: vals.audit as PlainMessage<Audit>
      },
      {
        onSuccess: () => {
          setValue('confirm', '');
          onClose(true);
        },
        onError: (e: AnyError) => {
          onClose(true, e);
        }
      }
    );
  };

  const { control, handleSubmit, watch, setValue } = useForm({
    defaultValues: {
      confirm: '',
      audit: {
        actor: '',
        reason: ''
      }
    },
    resolver: zodResolver(schema)
  });

  const confirm = watch('confirm');
  const audit = watch('audit');

  return (
    <Modal
      open={visible}
      closable={false}
      onCancel={() => onClose(false)}
      destroyOnClose={true}
      footer={
        <>
          <Button
            onClick={handleSubmit(deleteInstance)}
            disabled={confirm !== instToDelete?.name || !audit.actor || !audit.reason}
            danger
            type='primary'
          >
            Yes, Delete
          </Button>
          <Button
            onClick={() => {
              setValue('confirm', '');
              onClose(false);
            }}
          >
            No
          </Button>
        </>
      }
    >
      <div>
        <div className='mb-6 font-semibold text-xl'>
          Are you sure you want to delete the instance{' '}
          <Tag className='text-xl font-mono'>{instToDelete?.name}</Tag>?
        </div>
        <div>
          This will <b>permanently</b> delete the instance from the{' '}
          <Tag className='font-mono text-md'>{instToDelete?.ownerOrganizationName}</Tag>{' '}
          organization. Please note: It may take a few moments for the platform controller to delete
          the instance once it is marked for deletion. If the instance still appears in the list,
          try waiting a few minutes and refreshing the page.
        </div>
        <div className='mt-4'>
          To confirm, please type the name of the instance below:
          <FieldContainer name='confirm' control={control} className='mt-2'>
            {({ field }) => <Input {...field} placeholder={instToDelete?.name} />}
          </FieldContainer>
        </div>
        <AuditForm control={control} />
      </div>
    </Modal>
  );
};
