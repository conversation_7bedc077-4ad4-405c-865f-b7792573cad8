import { IconDefinition } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Flex, Tag, Typography } from 'antd';
import classNames from 'classnames';

import { capitalize } from '@/utils';

export interface AppliedFiltersProps {
  appliedFilters: Array<{
    key: string;
    value: string[];
    icon?: IconDefinition;
    iconClass?: string;
    onClear: () => void;
    label?: string;
  }>;
  className?: string;
  showMoreCount?: boolean;
  clearAllButton?: {
    text?: string;
    onClick: () => void;
  };
}

export const AppliedFilters = (props: AppliedFiltersProps) => {
  return props.appliedFilters.length ? (
    <Flex className={props.className} justify='space-between' align='center'>
      <Flex align='center' gap='middle'>
        <Typography.Text strong className='text-sm'>
          Applied Filters:{' '}
        </Typography.Text>
        <Flex wrap className='gap-2'>
          {props.appliedFilters.map((appliedFilter) => (
            <Tag
              key={appliedFilter.key}
              className='flex items-center gap-2 py-1 px-3 text-sm text-gray-600 border border-gray-200 shadow-sm rounded-md cursor-pointer hover:shadow'
              closable
              onClose={() => appliedFilter.onClear()}
            >
              {Boolean(appliedFilter.icon) && (
                <FontAwesomeIcon
                  className={classNames('text-blue-500 text-xs', appliedFilter.iconClass)}
                  icon={appliedFilter.icon}
                />
              )}
              <span>{appliedFilter?.label || capitalize(appliedFilter.key)}:</span>
              <span>
                {props.showMoreCount && appliedFilter.value.length > 2
                  ? `${appliedFilter.value.slice(0, 2).join(', ')} +${appliedFilter.value.length - 2}`
                  : appliedFilter.value.join('/')}
              </span>
            </Tag>
          ))}
        </Flex>
      </Flex>
      {props.clearAllButton && (
        <Button
          type='link'
          size='small'
          className='p-0 font-medium'
          onClick={props.clearAllButton.onClick}
        >
          {props.clearAllButton.text || 'Clear all'}
        </Button>
      )}
    </Flex>
  ) : null;
};
