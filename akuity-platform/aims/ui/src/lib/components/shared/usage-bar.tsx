import { faInfinity } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Flex } from 'antd';
import classNames from 'classnames';

export interface UsageBarProps {
  value: number;
  max: number;
  label: string;
  className?: string;
}

export interface InfoRowProps {
  label: string;
  data?: string;
  children?: React.ReactNode;
  className?: string;
}

export const InfoRow = ({ children, className, label, data }: InfoRowProps) => {
  return (
    <Flex className={classNames('py-1', className)}>
      <div>{label}</div>
      <div className='ml-auto font-semibold'>{data ? data : children}</div>
    </Flex>
  );
};

export const UsageBar = (props: UsageBarProps) => {
  const max = props.max < 0 ? 0 : props.max;
  const unlimited = props.max === 0;
  const exceeded = props.value > max;

  return (
    <div className={props.className}>
      <InfoRow label={props.label} className='w-full'>
        {props.value} / {unlimited ? <FontAwesomeIcon icon={faInfinity} /> : max}
      </InfoRow>
      <div className={'w-full rounded-full h-2 bg-gray-200'}>
        <div
          className={classNames('rounded-full h-2', {
            'bg-red-500': exceeded && !unlimited,
            'bg-blue-500': !exceeded && !unlimited,
            'bg-green-500': unlimited
          })}
          style={{ width: exceeded ? '100%' : `${(props.value / max) * 100}%` }}
        />
      </div>
    </div>
  );
};
