import { faFilter } from '@fortawesome/free-solid-svg-icons/faFilter';
import { faTimes } from '@fortawesome/free-solid-svg-icons/faTimes';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button } from 'antd';
import { ReactNode } from 'react';

export interface FilterProps {
  onApplyFilter: () => void;
  onClearFilter: () => void;
  children: ReactNode;
  close?: () => void;
}

export const Filter = ({ onApplyFilter, onClearFilter, children, close }: FilterProps) => {
  return (
    <div className='p-5' style={{ maxWidth: '550px', minWidth: '300px' }}>
      <div className='[&>label.ant-checkbox-wrapper]:font-normal [&>label.ant-radio-wrapper]:font-normal'>
        {children}
      </div>
      <div className='flex items-center justify-end gap-2 mt-5'>
        <Button
          icon={<FontAwesomeIcon icon={faTimes} />}
          onClick={() => {
            onClearFilter();
            close?.();
          }}
        >
          Clear
        </Button>
        <Button
          type='primary'
          className='audit-filter-btn__apply'
          onClick={() => {
            onApplyFilter();
            close?.();
          }}
        >
          Apply
        </Button>
      </div>
    </div>
  );
};

export const FilteredIcon = (filtered: boolean) => (
  <FontAwesomeIcon icon={faFilter} className={filtered ? 'text-blue-500' : ''} />
);
