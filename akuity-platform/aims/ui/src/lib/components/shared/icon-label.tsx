import type { IconDefinition, SizeProp } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Flex } from 'antd';
import * as React from 'react';

export type IconLabelProps = {
  label?: string;
  icon?: IconDefinition;
  iconNode?: React.ReactNode;
  spin?: boolean;
  iconClass?: string;
  iconTooltip?: string;
  iconSize?: string;
  className?: string;
  children?: React.ReactNode;
  onClick?: () => void;
};

export const IconLabel = (props: IconLabelProps) => {
  return (
    <Flex align='center' className={props.className} onClick={props.onClick}>
      {(props.icon || props.iconNode) && (
        <div style={{ width: '2ch' }} className='flex justify-center items-center mr-2'>
          <span title={props.iconTooltip}>
            {props.icon && (
              <FontAwesomeIcon
                icon={props.icon}
                className={props.iconClass}
                spin={props.spin}
                size={props.iconSize as SizeProp}
              />
            )}
            {props.iconNode}
          </span>
        </div>
      )}
      {props.label || props.children}
    </Flex>
  );
};

export default IconLabel;
