import { faTimes } from '@fortawesome/free-solid-svg-icons/faTimes';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { notification } from 'antd';

import { debounce } from '../../../utils';

// TODO: Convert to fn
export class UIVersionManager {
  currentUIVersion: string;
  isNotificationOn: boolean;
  defaultFetch: typeof window.fetch;
  notificationKey: string;

  constructor() {
    this.currentUIVersion = __UI_VERSION__;
    this.isNotificationOn = false;
    this.notificationKey = 'VERSION_NOTIFICATION';
  }

  checkAndTriggerVersionUpdateNotification = debounce((latestVersion: string) => {
    if (this.currentUIVersion === 'development') {
      return;
    }

    const makeNotificationOff = () => {
      this.isNotificationOn = false;
    };

    if (this.currentUIVersion !== latestVersion && !this.isNotificationOn) {
      this.isNotificationOn = true;
      notification.open({
        key: this.notificationKey,
        message: <h2 className='font-bold'>Please update UI!</h2>,
        description: (
          <>
            <span>
              It seems you have outdated version of UI. Please refresh the page to upgrade.
            </span>

            <button
              className='my-2 rounded-sm bg-blue-50 text-blue-400 hover:bg-blue-100 hover:text-blue-500 transition-colors duration-400'
              onClick={() => location.reload()}
            >
              Ctrl/Cmd + R
            </button>
          </>
        ),
        duration: 0,
        placement: 'bottomRight',
        closeIcon: <FontAwesomeIcon icon={faTimes} />,
        onClose: makeNotificationOff
      });
      return;
    }

    if (this.currentUIVersion === latestVersion && this.isNotificationOn) {
      this.isNotificationOn = false;
      notification.destroy(this.notificationKey);
    }
  }, 1000);
}
