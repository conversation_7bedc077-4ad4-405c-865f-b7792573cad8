import { Input } from 'antd';
import { Control, FieldValues, Path } from 'react-hook-form';

import { FieldContainer } from '@/lib/components/shared/forms';

type AuditFormProps<T extends FieldValues> = {
  control: Control<T>;
};

export const AuditForm = <T extends FieldValues>({ control }: AuditFormProps<T>) => {
  return (
    <div className=' rounded-md pt-5'>
      <h2 className='text-lg font-semibold mb-2'>Audit</h2>
      <FieldContainer control={control} label='Your Email' name={'audit.actor' as Path<T>}>
        {({ field }) => <Input placeholder='<EMAIL>' {...field} />}
      </FieldContainer>
      <FieldContainer
        control={control}
        label='Reason for this change'
        name={'audit.reason' as Path<T>}
      >
        {({ field }) => <Input.TextArea placeholder='Reason for this update' {...field} />}
      </FieldContainer>
    </div>
  );
};
