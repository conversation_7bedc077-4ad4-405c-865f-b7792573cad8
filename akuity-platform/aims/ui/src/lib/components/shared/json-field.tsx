const JSONField = (props: { children: React.ReactNode; label: string; className?: string }) => (
  <div className={`mb-6 w-5/12 mr-10 ${props?.className || ''}`}>
    <div className='font-semibold mb-2 text-gray-500 text-sm'>{props.label}</div>
    <pre className='text-lg max-h-72 overflow-y-auto rounded-md border-2 border-blue-100 p-4 tracking-wider'>
      {props.children}
    </pre>
  </div>
);

export default JSONField;
