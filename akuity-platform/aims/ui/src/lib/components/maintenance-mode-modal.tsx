import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { Checkbox, List, Modal, Select } from 'antd';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useDebounce } from 'use-debounce';
import { z } from 'zod';

import { ModalComponentProps } from '@/context/modal-context';
import { useGetInstanceClusters, useSetClusterInMaintenanceMode } from '@/hook/api';
import { AuditForm } from '@/lib/components/shared/audit-form';
import { FieldContainer } from '@/lib/components/shared/forms';
import { zodAuditSchema } from '@/utils';

import { queryKeys } from '../../constants/query-keys';
import { Audit, ClusterFilter } from '../apiclient/aims/v1/aims_pb';

import { Enabled } from './enabled';

type MaintenanceModeModalProps = ModalComponentProps & { instanceId: string };

const schema = z.object({
  audit: zodAuditSchema,
  clusterId: z.string().min(1),
  maintenanceMode: z.boolean()
});

export const MaintenanceModeModal = ({ hide, visible, instanceId }: MaintenanceModeModalProps) => {
  const queryClient = useQueryClient();

  const form = useForm({
    defaultValues: {
      clusterId: '',
      maintenanceMode: false,
      audit: {
        actor: '',
        reason: ''
      }
    },
    resolver: zodResolver(schema)
  });

  const { mutate, isPending } = useSetClusterInMaintenanceMode();

  const [search, setSearch] = useState('');

  const [debouncedSearch] = useDebounce(search, 500);

  const { data: clusters } = useGetInstanceClusters(
    instanceId,
    new ClusterFilter({ fuzz: debouncedSearch }),
    {
      placeholderData: (d) => d || []
    }
  );

  const selectedCluster = form.watch('clusterId');

  const shouldSelectedClusterBeInMaintenanceMode = form.watch('maintenanceMode');

  const isSelectedClusterInMaintenanceMode = clusters?.find((c) => c?.id === selectedCluster)?.data
    ?.maintenanceMode;

  const isThereAnyChange =
    shouldSelectedClusterBeInMaintenanceMode !== isSelectedClusterInMaintenanceMode;

  useEffect(() => {
    form.setValue('maintenanceMode', isSelectedClusterInMaintenanceMode);
  }, [isSelectedClusterInMaintenanceMode]);

  const onSubmit = form.handleSubmit((data) => {
    mutate(
      {
        instanceId,
        clusterId: data.clusterId,
        maintenanceMode: data.maintenanceMode,
        audit: new Audit(data.audit)
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: queryKeys.instances.argoInstance(instanceId).queryKey
          });
          hide();
        }
      }
    );
  });

  return (
    <Modal
      open={visible}
      onCancel={hide}
      okButtonProps={{
        loading: isPending,
        disabled: !isThereAnyChange
      }}
      title='Set Cluster Maintanence Mode'
      onOk={onSubmit}
    >
      <div className='w-full py-3'>
        <FieldContainer control={form.control} name='clusterId' label='Cluster'>
          {({ field }) => (
            <Select
              value={selectedCluster}
              searchValue={search}
              onSearch={setSearch}
              showSearch
              filterOption={false}
              onChange={(value) => field.onChange(value)}
              options={clusters.map((cluster) => ({
                label: cluster?.name,
                value: cluster?.id
              }))}
              className='w-full'
            />
          )}
        </FieldContainer>

        <FieldContainer control={form.control} name='maintenanceMode'>
          {({ field }) => (
            <div>
              <label>Maintenance Mode</label>
              <Checkbox
                checked={field.value}
                onChange={(e) => field.onChange(e.target.checked)}
                className='ml-2'
              />
            </div>
          )}
        </FieldContainer>

        {isSelectedClusterInMaintenanceMode && (
          <span className='text-xs italic'>
            This cluster is already in maintenance mode, you can disable it.
          </span>
        )}

        {isThereAnyChange && (
          <div className='bg-slate-50 rounded-md p-5'>
            <List
              header='Change'
              bordered
              dataSource={[
                {
                  key: 'maintenanceMode',
                  oldValue: isSelectedClusterInMaintenanceMode,
                  newValue: shouldSelectedClusterBeInMaintenanceMode
                }
              ]}
              renderItem={(item) => (
                <List.Item>
                  <Enabled value={item.newValue}>{item.key}</Enabled>
                </List.Item>
              )}
              className='mb-5'
            />
            <AuditForm control={form.control} />
          </div>
        )}
      </div>
    </Modal>
  );
};
