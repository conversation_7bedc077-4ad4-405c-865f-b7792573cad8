// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file team/v1/team.proto (package akuity.team.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3, Timestamp } from "@bufbuild/protobuf";

/**
 * @generated from message akuity.team.v1.Team
 */
export class Team extends Message<Team> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  /**
   * @generated from field: string description = 3;
   */
  description = "";

  /**
   * @generated from field: google.protobuf.Timestamp creation_time = 4;
   */
  creationTime?: Timestamp;

  constructor(data?: PartialMessage<Team>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.Team";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "creation_time", kind: "message", T: Timestamp },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Team {
    return new Team().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Team {
    return new Team().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Team {
    return new Team().fromJsonString(jsonString, options);
  }

  static equals(a: Team | PlainMessage<Team> | undefined, b: Team | PlainMessage<Team> | undefined): boolean {
    return proto3.util.equals(Team, a, b);
  }
}

/**
 * @generated from message akuity.team.v1.CreateTeamRequest
 */
export class CreateTeamRequest extends Message<CreateTeamRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  /**
   * @generated from field: optional string description = 3;
   */
  description?: string;

  constructor(data?: PartialMessage<CreateTeamRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.CreateTeamRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateTeamRequest {
    return new CreateTeamRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateTeamRequest {
    return new CreateTeamRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateTeamRequest {
    return new CreateTeamRequest().fromJsonString(jsonString, options);
  }

  static equals(a: CreateTeamRequest | PlainMessage<CreateTeamRequest> | undefined, b: CreateTeamRequest | PlainMessage<CreateTeamRequest> | undefined): boolean {
    return proto3.util.equals(CreateTeamRequest, a, b);
  }
}

/**
 * @generated from message akuity.team.v1.CreateTeamResponse
 */
export class CreateTeamResponse extends Message<CreateTeamResponse> {
  /**
   * @generated from field: akuity.team.v1.Team team = 1;
   */
  team?: Team;

  constructor(data?: PartialMessage<CreateTeamResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.CreateTeamResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "team", kind: "message", T: Team },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateTeamResponse {
    return new CreateTeamResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateTeamResponse {
    return new CreateTeamResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateTeamResponse {
    return new CreateTeamResponse().fromJsonString(jsonString, options);
  }

  static equals(a: CreateTeamResponse | PlainMessage<CreateTeamResponse> | undefined, b: CreateTeamResponse | PlainMessage<CreateTeamResponse> | undefined): boolean {
    return proto3.util.equals(CreateTeamResponse, a, b);
  }
}

/**
 * @generated from message akuity.team.v1.UpdateTeamRequest
 */
export class UpdateTeamRequest extends Message<UpdateTeamRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  /**
   * @generated from field: string description = 3;
   */
  description = "";

  constructor(data?: PartialMessage<UpdateTeamRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.UpdateTeamRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateTeamRequest {
    return new UpdateTeamRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateTeamRequest {
    return new UpdateTeamRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateTeamRequest {
    return new UpdateTeamRequest().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateTeamRequest | PlainMessage<UpdateTeamRequest> | undefined, b: UpdateTeamRequest | PlainMessage<UpdateTeamRequest> | undefined): boolean {
    return proto3.util.equals(UpdateTeamRequest, a, b);
  }
}

/**
 * @generated from message akuity.team.v1.UpdateTeamResponse
 */
export class UpdateTeamResponse extends Message<UpdateTeamResponse> {
  /**
   * @generated from field: akuity.team.v1.Team team = 1;
   */
  team?: Team;

  constructor(data?: PartialMessage<UpdateTeamResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.UpdateTeamResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "team", kind: "message", T: Team },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateTeamResponse {
    return new UpdateTeamResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateTeamResponse {
    return new UpdateTeamResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateTeamResponse {
    return new UpdateTeamResponse().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateTeamResponse | PlainMessage<UpdateTeamResponse> | undefined, b: UpdateTeamResponse | PlainMessage<UpdateTeamResponse> | undefined): boolean {
    return proto3.util.equals(UpdateTeamResponse, a, b);
  }
}

/**
 * @generated from message akuity.team.v1.GetTeamRequest
 */
export class GetTeamRequest extends Message<GetTeamRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  constructor(data?: PartialMessage<GetTeamRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.GetTeamRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetTeamRequest {
    return new GetTeamRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetTeamRequest {
    return new GetTeamRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetTeamRequest {
    return new GetTeamRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetTeamRequest | PlainMessage<GetTeamRequest> | undefined, b: GetTeamRequest | PlainMessage<GetTeamRequest> | undefined): boolean {
    return proto3.util.equals(GetTeamRequest, a, b);
  }
}

/**
 * @generated from message akuity.team.v1.GetTeamResponse
 */
export class GetTeamResponse extends Message<GetTeamResponse> {
  /**
   * @generated from field: akuity.team.v1.Team team = 1;
   */
  team?: Team;

  constructor(data?: PartialMessage<GetTeamResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.GetTeamResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "team", kind: "message", T: Team },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetTeamResponse {
    return new GetTeamResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetTeamResponse {
    return new GetTeamResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetTeamResponse {
    return new GetTeamResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetTeamResponse | PlainMessage<GetTeamResponse> | undefined, b: GetTeamResponse | PlainMessage<GetTeamResponse> | undefined): boolean {
    return proto3.util.equals(GetTeamResponse, a, b);
  }
}

/**
 * @generated from message akuity.team.v1.ListTeamsRequest
 */
export class ListTeamsRequest extends Message<ListTeamsRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize = 0;

  /**
   * @generated from field: string page_token = 3;
   */
  pageToken = "";

  constructor(data?: PartialMessage<ListTeamsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.ListTeamsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "page_size", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 3, name: "page_token", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListTeamsRequest {
    return new ListTeamsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListTeamsRequest {
    return new ListTeamsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListTeamsRequest {
    return new ListTeamsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListTeamsRequest | PlainMessage<ListTeamsRequest> | undefined, b: ListTeamsRequest | PlainMessage<ListTeamsRequest> | undefined): boolean {
    return proto3.util.equals(ListTeamsRequest, a, b);
  }
}

/**
 * @generated from message akuity.team.v1.ListTeamsResponse
 */
export class ListTeamsResponse extends Message<ListTeamsResponse> {
  /**
   * @generated from field: repeated akuity.team.v1.Team teams = 1;
   */
  teams: Team[] = [];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken = "";

  constructor(data?: PartialMessage<ListTeamsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.ListTeamsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "teams", kind: "message", T: Team, repeated: true },
    { no: 2, name: "next_page_token", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListTeamsResponse {
    return new ListTeamsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListTeamsResponse {
    return new ListTeamsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListTeamsResponse {
    return new ListTeamsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListTeamsResponse | PlainMessage<ListTeamsResponse> | undefined, b: ListTeamsResponse | PlainMessage<ListTeamsResponse> | undefined): boolean {
    return proto3.util.equals(ListTeamsResponse, a, b);
  }
}

/**
 * @generated from message akuity.team.v1.DeleteTeamRequest
 */
export class DeleteTeamRequest extends Message<DeleteTeamRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  constructor(data?: PartialMessage<DeleteTeamRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.DeleteTeamRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteTeamRequest {
    return new DeleteTeamRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteTeamRequest {
    return new DeleteTeamRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteTeamRequest {
    return new DeleteTeamRequest().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteTeamRequest | PlainMessage<DeleteTeamRequest> | undefined, b: DeleteTeamRequest | PlainMessage<DeleteTeamRequest> | undefined): boolean {
    return proto3.util.equals(DeleteTeamRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.team.v1.DeleteTeamResponse
 */
export class DeleteTeamResponse extends Message<DeleteTeamResponse> {
  constructor(data?: PartialMessage<DeleteTeamResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.DeleteTeamResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteTeamResponse {
    return new DeleteTeamResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteTeamResponse {
    return new DeleteTeamResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteTeamResponse {
    return new DeleteTeamResponse().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteTeamResponse | PlainMessage<DeleteTeamResponse> | undefined, b: DeleteTeamResponse | PlainMessage<DeleteTeamResponse> | undefined): boolean {
    return proto3.util.equals(DeleteTeamResponse, a, b);
  }
}

/**
 * @generated from message akuity.team.v1.TeamMember
 */
export class TeamMember extends Message<TeamMember> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string id = 2;
   */
  id = "";

  /**
   * @generated from field: string team_name = 3;
   */
  teamName = "";

  constructor(data?: PartialMessage<TeamMember>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.TeamMember";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "team_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TeamMember {
    return new TeamMember().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TeamMember {
    return new TeamMember().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TeamMember {
    return new TeamMember().fromJsonString(jsonString, options);
  }

  static equals(a: TeamMember | PlainMessage<TeamMember> | undefined, b: TeamMember | PlainMessage<TeamMember> | undefined): boolean {
    return proto3.util.equals(TeamMember, a, b);
  }
}

/**
 * @generated from message akuity.team.v1.AddTeamMemberRequest
 */
export class AddTeamMemberRequest extends Message<AddTeamMemberRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string team_name = 2;
   */
  teamName = "";

  /**
   * @generated from field: string user_id = 3;
   */
  userId = "";

  constructor(data?: PartialMessage<AddTeamMemberRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.AddTeamMemberRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "team_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "user_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddTeamMemberRequest {
    return new AddTeamMemberRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddTeamMemberRequest {
    return new AddTeamMemberRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddTeamMemberRequest {
    return new AddTeamMemberRequest().fromJsonString(jsonString, options);
  }

  static equals(a: AddTeamMemberRequest | PlainMessage<AddTeamMemberRequest> | undefined, b: AddTeamMemberRequest | PlainMessage<AddTeamMemberRequest> | undefined): boolean {
    return proto3.util.equals(AddTeamMemberRequest, a, b);
  }
}

/**
 * @generated from message akuity.team.v1.AddTeamMemberResponse
 */
export class AddTeamMemberResponse extends Message<AddTeamMemberResponse> {
  /**
   * @generated from field: akuity.team.v1.TeamMember team_member = 1;
   */
  teamMember?: TeamMember;

  constructor(data?: PartialMessage<AddTeamMemberResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.AddTeamMemberResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "team_member", kind: "message", T: TeamMember },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AddTeamMemberResponse {
    return new AddTeamMemberResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AddTeamMemberResponse {
    return new AddTeamMemberResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AddTeamMemberResponse {
    return new AddTeamMemberResponse().fromJsonString(jsonString, options);
  }

  static equals(a: AddTeamMemberResponse | PlainMessage<AddTeamMemberResponse> | undefined, b: AddTeamMemberResponse | PlainMessage<AddTeamMemberResponse> | undefined): boolean {
    return proto3.util.equals(AddTeamMemberResponse, a, b);
  }
}

/**
 * @generated from message akuity.team.v1.GetTeamMemberRequest
 */
export class GetTeamMemberRequest extends Message<GetTeamMemberRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string team_name = 2;
   */
  teamName = "";

  /**
   * @generated from field: string id = 3;
   */
  id = "";

  constructor(data?: PartialMessage<GetTeamMemberRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.GetTeamMemberRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "team_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetTeamMemberRequest {
    return new GetTeamMemberRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetTeamMemberRequest {
    return new GetTeamMemberRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetTeamMemberRequest {
    return new GetTeamMemberRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetTeamMemberRequest | PlainMessage<GetTeamMemberRequest> | undefined, b: GetTeamMemberRequest | PlainMessage<GetTeamMemberRequest> | undefined): boolean {
    return proto3.util.equals(GetTeamMemberRequest, a, b);
  }
}

/**
 * @generated from message akuity.team.v1.GetTeamMemberResponse
 */
export class GetTeamMemberResponse extends Message<GetTeamMemberResponse> {
  /**
   * @generated from field: akuity.team.v1.TeamMember team_member = 1;
   */
  teamMember?: TeamMember;

  constructor(data?: PartialMessage<GetTeamMemberResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.GetTeamMemberResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "team_member", kind: "message", T: TeamMember },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetTeamMemberResponse {
    return new GetTeamMemberResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetTeamMemberResponse {
    return new GetTeamMemberResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetTeamMemberResponse {
    return new GetTeamMemberResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetTeamMemberResponse | PlainMessage<GetTeamMemberResponse> | undefined, b: GetTeamMemberResponse | PlainMessage<GetTeamMemberResponse> | undefined): boolean {
    return proto3.util.equals(GetTeamMemberResponse, a, b);
  }
}

/**
 * @generated from message akuity.team.v1.ListTeamMembersRequest
 */
export class ListTeamMembersRequest extends Message<ListTeamMembersRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string team_name = 2;
   */
  teamName = "";

  /**
   * @generated from field: int32 page_size = 3;
   */
  pageSize = 0;

  /**
   * @generated from field: string page_token = 4;
   */
  pageToken = "";

  constructor(data?: PartialMessage<ListTeamMembersRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.ListTeamMembersRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "team_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "page_size", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 4, name: "page_token", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListTeamMembersRequest {
    return new ListTeamMembersRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListTeamMembersRequest {
    return new ListTeamMembersRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListTeamMembersRequest {
    return new ListTeamMembersRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListTeamMembersRequest | PlainMessage<ListTeamMembersRequest> | undefined, b: ListTeamMembersRequest | PlainMessage<ListTeamMembersRequest> | undefined): boolean {
    return proto3.util.equals(ListTeamMembersRequest, a, b);
  }
}

/**
 * @generated from message akuity.team.v1.ListTeamMembersResponse
 */
export class ListTeamMembersResponse extends Message<ListTeamMembersResponse> {
  /**
   * @generated from field: repeated akuity.team.v1.TeamMember team_members = 1;
   */
  teamMembers: TeamMember[] = [];

  /**
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken = "";

  constructor(data?: PartialMessage<ListTeamMembersResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.ListTeamMembersResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "team_members", kind: "message", T: TeamMember, repeated: true },
    { no: 2, name: "next_page_token", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListTeamMembersResponse {
    return new ListTeamMembersResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListTeamMembersResponse {
    return new ListTeamMembersResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListTeamMembersResponse {
    return new ListTeamMembersResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListTeamMembersResponse | PlainMessage<ListTeamMembersResponse> | undefined, b: ListTeamMembersResponse | PlainMessage<ListTeamMembersResponse> | undefined): boolean {
    return proto3.util.equals(ListTeamMembersResponse, a, b);
  }
}

/**
 * @generated from message akuity.team.v1.DeleteTeamMemberRequest
 */
export class DeleteTeamMemberRequest extends Message<DeleteTeamMemberRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string team_name = 2;
   */
  teamName = "";

  /**
   * @generated from field: string id = 3;
   */
  id = "";

  constructor(data?: PartialMessage<DeleteTeamMemberRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.DeleteTeamMemberRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "team_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteTeamMemberRequest {
    return new DeleteTeamMemberRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteTeamMemberRequest {
    return new DeleteTeamMemberRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteTeamMemberRequest {
    return new DeleteTeamMemberRequest().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteTeamMemberRequest | PlainMessage<DeleteTeamMemberRequest> | undefined, b: DeleteTeamMemberRequest | PlainMessage<DeleteTeamMemberRequest> | undefined): boolean {
    return proto3.util.equals(DeleteTeamMemberRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.team.v1.DeleteTeamMemberResponse
 */
export class DeleteTeamMemberResponse extends Message<DeleteTeamMemberResponse> {
  constructor(data?: PartialMessage<DeleteTeamMemberResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.team.v1.DeleteTeamMemberResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteTeamMemberResponse {
    return new DeleteTeamMemberResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteTeamMemberResponse {
    return new DeleteTeamMemberResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteTeamMemberResponse {
    return new DeleteTeamMemberResponse().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteTeamMemberResponse | PlainMessage<DeleteTeamMemberResponse> | undefined, b: DeleteTeamMemberResponse | PlainMessage<DeleteTeamMemberResponse> | undefined): boolean {
    return proto3.util.equals(DeleteTeamMemberResponse, a, b);
  }
}

