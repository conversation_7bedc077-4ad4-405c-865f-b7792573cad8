// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file system/v1/system.proto (package akuity.system.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3, protoInt64 } from "@bufbuild/protobuf";

/**
 * explicitly empty 
 *
 * @generated from message akuity.system.v1.GetVersionRequest
 */
export class GetVersionRequest extends Message<GetVersionRequest> {
  constructor(data?: PartialMessage<GetVersionRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.GetVersionRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetVersionRequest {
    return new GetVersionRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetVersionRequest {
    return new GetVersionRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetVersionRequest {
    return new GetVersionRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetVersionRequest | PlainMessage<GetVersionRequest> | undefined, b: GetVersionRequest | PlainMessage<GetVersionRequest> | undefined): boolean {
    return proto3.util.equals(GetVersionRequest, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.GetVersionResponse
 */
export class GetVersionResponse extends Message<GetVersionResponse> {
  /**
   * @generated from field: string version = 1;
   */
  version = "";

  constructor(data?: PartialMessage<GetVersionResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.GetVersionResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetVersionResponse {
    return new GetVersionResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetVersionResponse {
    return new GetVersionResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetVersionResponse {
    return new GetVersionResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetVersionResponse | PlainMessage<GetVersionResponse> | undefined, b: GetVersionResponse | PlainMessage<GetVersionResponse> | undefined): boolean {
    return proto3.util.equals(GetVersionResponse, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.system.v1.GetAgentVersionRequest
 */
export class GetAgentVersionRequest extends Message<GetAgentVersionRequest> {
  constructor(data?: PartialMessage<GetAgentVersionRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.GetAgentVersionRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAgentVersionRequest {
    return new GetAgentVersionRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAgentVersionRequest {
    return new GetAgentVersionRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAgentVersionRequest {
    return new GetAgentVersionRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetAgentVersionRequest | PlainMessage<GetAgentVersionRequest> | undefined, b: GetAgentVersionRequest | PlainMessage<GetAgentVersionRequest> | undefined): boolean {
    return proto3.util.equals(GetAgentVersionRequest, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.GetAgentVersionResponse
 */
export class GetAgentVersionResponse extends Message<GetAgentVersionResponse> {
  /**
   * @generated from field: string version = 1;
   */
  version = "";

  constructor(data?: PartialMessage<GetAgentVersionResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.GetAgentVersionResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAgentVersionResponse {
    return new GetAgentVersionResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAgentVersionResponse {
    return new GetAgentVersionResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAgentVersionResponse {
    return new GetAgentVersionResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetAgentVersionResponse | PlainMessage<GetAgentVersionResponse> | undefined, b: GetAgentVersionResponse | PlainMessage<GetAgentVersionResponse> | undefined): boolean {
    return proto3.util.equals(GetAgentVersionResponse, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.system.v1.GetSettingsRequest
 */
export class GetSettingsRequest extends Message<GetSettingsRequest> {
  constructor(data?: PartialMessage<GetSettingsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.GetSettingsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSettingsRequest {
    return new GetSettingsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSettingsRequest {
    return new GetSettingsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSettingsRequest {
    return new GetSettingsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetSettingsRequest | PlainMessage<GetSettingsRequest> | undefined, b: GetSettingsRequest | PlainMessage<GetSettingsRequest> | undefined): boolean {
    return proto3.util.equals(GetSettingsRequest, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.GetSettingsResponse
 */
export class GetSettingsResponse extends Message<GetSettingsResponse> {
  /**
   * @generated from field: string env = 1;
   */
  env = "";

  /**
   * @generated from field: string domain_suffix = 2;
   */
  domainSuffix = "";

  /**
   * @generated from field: string sentry_dsn = 3;
   */
  sentryDsn = "";

  /**
   * @generated from field: string google_tag_id = 4;
   */
  googleTagId = "";

  /**
   * @generated from field: akuity.system.v1.NameConfig name_config = 5;
   */
  nameConfig?: NameConfig;

  /**
   * @generated from field: bool self_hosted = 6;
   */
  selfHosted = false;

  /**
   * @generated from field: bool billing_enabled = 7;
   */
  billingEnabled = false;

  /**
   * @generated from field: string stripe_customer_portal_url = 8;
   */
  stripeCustomerPortalUrl = "";

  /**
   * list of features that can be enabled/disabled at a global level
   *
   * @generated from field: repeated string capabilities = 9;
   */
  capabilities: string[] = [];

  /**
   * @generated from field: int64 max_invitation_email_per_batch_count = 10;
   */
  maxInvitationEmailPerBatchCount = protoInt64.zero;

  /**
   * @generated from field: bool instance_sub_domains_enabled = 11;
   */
  instanceSubDomainsEnabled = false;

  constructor(data?: PartialMessage<GetSettingsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.GetSettingsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "env", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "domain_suffix", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "sentry_dsn", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "google_tag_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "name_config", kind: "message", T: NameConfig },
    { no: 6, name: "self_hosted", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 7, name: "billing_enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 8, name: "stripe_customer_portal_url", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 9, name: "capabilities", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 10, name: "max_invitation_email_per_batch_count", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 11, name: "instance_sub_domains_enabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetSettingsResponse {
    return new GetSettingsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetSettingsResponse {
    return new GetSettingsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetSettingsResponse {
    return new GetSettingsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetSettingsResponse | PlainMessage<GetSettingsResponse> | undefined, b: GetSettingsResponse | PlainMessage<GetSettingsResponse> | undefined): boolean {
    return proto3.util.equals(GetSettingsResponse, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.NameConfig
 */
export class NameConfig extends Message<NameConfig> {
  /**
   * @generated from field: int64 min_organization_name_length = 1;
   */
  minOrganizationNameLength = protoInt64.zero;

  /**
   * @generated from field: int64 min_instance_name_length = 2;
   */
  minInstanceNameLength = protoInt64.zero;

  /**
   * @generated from field: int64 min_cluster_name_length = 3;
   */
  minClusterNameLength = protoInt64.zero;

  /**
   * @generated from field: int64 min_subdomain_name_length = 4;
   */
  minSubdomainNameLength = protoInt64.zero;

  constructor(data?: PartialMessage<NameConfig>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.NameConfig";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "min_organization_name_length", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 2, name: "min_instance_name_length", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 3, name: "min_cluster_name_length", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
    { no: 4, name: "min_subdomain_name_length", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NameConfig {
    return new NameConfig().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NameConfig {
    return new NameConfig().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NameConfig {
    return new NameConfig().fromJsonString(jsonString, options);
  }

  static equals(a: NameConfig | PlainMessage<NameConfig> | undefined, b: NameConfig | PlainMessage<NameConfig> | undefined): boolean {
    return proto3.util.equals(NameConfig, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.GetStatusResponse
 */
export class GetStatusResponse extends Message<GetStatusResponse> {
  /**
   * @generated from field: string status = 1;
   */
  status = "";

  constructor(data?: PartialMessage<GetStatusResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.GetStatusResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "status", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetStatusResponse {
    return new GetStatusResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetStatusResponse {
    return new GetStatusResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetStatusResponse {
    return new GetStatusResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetStatusResponse | PlainMessage<GetStatusResponse> | undefined, b: GetStatusResponse | PlainMessage<GetStatusResponse> | undefined): boolean {
    return proto3.util.equals(GetStatusResponse, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.SecurityAdvisory
 */
export class SecurityAdvisory extends Message<SecurityAdvisory> {
  /**
   * @generated from field: string cve_id = 1;
   */
  cveId = "";

  constructor(data?: PartialMessage<SecurityAdvisory>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.SecurityAdvisory";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "cve_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SecurityAdvisory {
    return new SecurityAdvisory().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SecurityAdvisory {
    return new SecurityAdvisory().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SecurityAdvisory {
    return new SecurityAdvisory().fromJsonString(jsonString, options);
  }

  static equals(a: SecurityAdvisory | PlainMessage<SecurityAdvisory> | undefined, b: SecurityAdvisory | PlainMessage<SecurityAdvisory> | undefined): boolean {
    return proto3.util.equals(SecurityAdvisory, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.AKVersion
 */
export class AKVersion extends Message<AKVersion> {
  /**
   * @generated from field: string version = 1;
   */
  version = "";

  /**
   * @generated from field: repeated string features = 2;
   */
  features: string[] = [];

  constructor(data?: PartialMessage<AKVersion>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.AKVersion";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "features", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AKVersion {
    return new AKVersion().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AKVersion {
    return new AKVersion().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AKVersion {
    return new AKVersion().fromJsonString(jsonString, options);
  }

  static equals(a: AKVersion | PlainMessage<AKVersion> | undefined, b: AKVersion | PlainMessage<AKVersion> | undefined): boolean {
    return proto3.util.equals(AKVersion, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.ComponentVersion
 */
export class ComponentVersion extends Message<ComponentVersion> {
  /**
   * @generated from field: string version = 1;
   */
  version = "";

  /**
   * @generated from field: string label = 2;
   */
  label = "";

  /**
   * @generated from field: repeated akuity.system.v1.SecurityAdvisory security_advisories = 3;
   */
  securityAdvisories: SecurityAdvisory[] = [];

  /**
   * @generated from field: repeated akuity.system.v1.AKVersion ak_versions = 4;
   */
  akVersions: AKVersion[] = [];

  constructor(data?: PartialMessage<ComponentVersion>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.ComponentVersion";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "label", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "security_advisories", kind: "message", T: SecurityAdvisory, repeated: true },
    { no: 4, name: "ak_versions", kind: "message", T: AKVersion, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ComponentVersion {
    return new ComponentVersion().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ComponentVersion {
    return new ComponentVersion().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ComponentVersion {
    return new ComponentVersion().fromJsonString(jsonString, options);
  }

  static equals(a: ComponentVersion | PlainMessage<ComponentVersion> | undefined, b: ComponentVersion | PlainMessage<ComponentVersion> | undefined): boolean {
    return proto3.util.equals(ComponentVersion, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.GetArgoCDAgentSizeSpecResponse
 */
export class GetArgoCDAgentSizeSpecResponse extends Message<GetArgoCDAgentSizeSpecResponse> {
  /**
   * @generated from field: akuity.system.v1.GetArgoCDAgentSizeSpecResponse.Spec small = 1;
   */
  small?: GetArgoCDAgentSizeSpecResponse_Spec;

  /**
   * @generated from field: akuity.system.v1.GetArgoCDAgentSizeSpecResponse.Spec medium = 2;
   */
  medium?: GetArgoCDAgentSizeSpecResponse_Spec;

  /**
   * @generated from field: akuity.system.v1.GetArgoCDAgentSizeSpecResponse.Spec large = 3;
   */
  large?: GetArgoCDAgentSizeSpecResponse_Spec;

  constructor(data?: PartialMessage<GetArgoCDAgentSizeSpecResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.GetArgoCDAgentSizeSpecResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "small", kind: "message", T: GetArgoCDAgentSizeSpecResponse_Spec },
    { no: 2, name: "medium", kind: "message", T: GetArgoCDAgentSizeSpecResponse_Spec },
    { no: 3, name: "large", kind: "message", T: GetArgoCDAgentSizeSpecResponse_Spec },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetArgoCDAgentSizeSpecResponse {
    return new GetArgoCDAgentSizeSpecResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetArgoCDAgentSizeSpecResponse {
    return new GetArgoCDAgentSizeSpecResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetArgoCDAgentSizeSpecResponse {
    return new GetArgoCDAgentSizeSpecResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetArgoCDAgentSizeSpecResponse | PlainMessage<GetArgoCDAgentSizeSpecResponse> | undefined, b: GetArgoCDAgentSizeSpecResponse | PlainMessage<GetArgoCDAgentSizeSpecResponse> | undefined): boolean {
    return proto3.util.equals(GetArgoCDAgentSizeSpecResponse, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.GetArgoCDAgentSizeSpecResponse.Spec
 */
export class GetArgoCDAgentSizeSpecResponse_Spec extends Message<GetArgoCDAgentSizeSpecResponse_Spec> {
  /**
   * @generated from field: string controller_cpu = 1;
   */
  controllerCpu = "";

  /**
   * @generated from field: string controller_memory = 2;
   */
  controllerMemory = "";

  /**
   * @generated from field: int32 repo_server_replicas = 3;
   */
  repoServerReplicas = 0;

  /**
   * @generated from field: string repo_server_cpu = 4;
   */
  repoServerCpu = "";

  /**
   * @generated from field: string repo_server_memory = 5;
   */
  repoServerMemory = "";

  constructor(data?: PartialMessage<GetArgoCDAgentSizeSpecResponse_Spec>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.GetArgoCDAgentSizeSpecResponse.Spec";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "controller_cpu", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "controller_memory", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "repo_server_replicas", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 4, name: "repo_server_cpu", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "repo_server_memory", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetArgoCDAgentSizeSpecResponse_Spec {
    return new GetArgoCDAgentSizeSpecResponse_Spec().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetArgoCDAgentSizeSpecResponse_Spec {
    return new GetArgoCDAgentSizeSpecResponse_Spec().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetArgoCDAgentSizeSpecResponse_Spec {
    return new GetArgoCDAgentSizeSpecResponse_Spec().fromJsonString(jsonString, options);
  }

  static equals(a: GetArgoCDAgentSizeSpecResponse_Spec | PlainMessage<GetArgoCDAgentSizeSpecResponse_Spec> | undefined, b: GetArgoCDAgentSizeSpecResponse_Spec | PlainMessage<GetArgoCDAgentSizeSpecResponse_Spec> | undefined): boolean {
    return proto3.util.equals(GetArgoCDAgentSizeSpecResponse_Spec, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.GetKargoAgentSizeSpecResponse
 */
export class GetKargoAgentSizeSpecResponse extends Message<GetKargoAgentSizeSpecResponse> {
  /**
   * @generated from field: akuity.system.v1.GetKargoAgentSizeSpecResponse.Spec small = 1;
   */
  small?: GetKargoAgentSizeSpecResponse_Spec;

  /**
   * @generated from field: akuity.system.v1.GetKargoAgentSizeSpecResponse.Spec medium = 2;
   */
  medium?: GetKargoAgentSizeSpecResponse_Spec;

  /**
   * @generated from field: akuity.system.v1.GetKargoAgentSizeSpecResponse.Spec large = 3;
   */
  large?: GetKargoAgentSizeSpecResponse_Spec;

  constructor(data?: PartialMessage<GetKargoAgentSizeSpecResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.GetKargoAgentSizeSpecResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "small", kind: "message", T: GetKargoAgentSizeSpecResponse_Spec },
    { no: 2, name: "medium", kind: "message", T: GetKargoAgentSizeSpecResponse_Spec },
    { no: 3, name: "large", kind: "message", T: GetKargoAgentSizeSpecResponse_Spec },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetKargoAgentSizeSpecResponse {
    return new GetKargoAgentSizeSpecResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetKargoAgentSizeSpecResponse {
    return new GetKargoAgentSizeSpecResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetKargoAgentSizeSpecResponse {
    return new GetKargoAgentSizeSpecResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetKargoAgentSizeSpecResponse | PlainMessage<GetKargoAgentSizeSpecResponse> | undefined, b: GetKargoAgentSizeSpecResponse | PlainMessage<GetKargoAgentSizeSpecResponse> | undefined): boolean {
    return proto3.util.equals(GetKargoAgentSizeSpecResponse, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.GetKargoAgentSizeSpecResponse.Spec
 */
export class GetKargoAgentSizeSpecResponse_Spec extends Message<GetKargoAgentSizeSpecResponse_Spec> {
  /**
   * @generated from field: string controller_cpu = 1;
   */
  controllerCpu = "";

  /**
   * @generated from field: string controller_memory = 2;
   */
  controllerMemory = "";

  constructor(data?: PartialMessage<GetKargoAgentSizeSpecResponse_Spec>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.GetKargoAgentSizeSpecResponse.Spec";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "controller_cpu", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "controller_memory", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetKargoAgentSizeSpecResponse_Spec {
    return new GetKargoAgentSizeSpecResponse_Spec().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetKargoAgentSizeSpecResponse_Spec {
    return new GetKargoAgentSizeSpecResponse_Spec().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetKargoAgentSizeSpecResponse_Spec {
    return new GetKargoAgentSizeSpecResponse_Spec().fromJsonString(jsonString, options);
  }

  static equals(a: GetKargoAgentSizeSpecResponse_Spec | PlainMessage<GetKargoAgentSizeSpecResponse_Spec> | undefined, b: GetKargoAgentSizeSpecResponse_Spec | PlainMessage<GetKargoAgentSizeSpecResponse_Spec> | undefined): boolean {
    return proto3.util.equals(GetKargoAgentSizeSpecResponse_Spec, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.ListArgoCDVersionsResponse
 */
export class ListArgoCDVersionsResponse extends Message<ListArgoCDVersionsResponse> {
  /**
   * @generated from field: repeated akuity.system.v1.ComponentVersion argocd_versions = 1;
   */
  argocdVersions: ComponentVersion[] = [];

  constructor(data?: PartialMessage<ListArgoCDVersionsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.ListArgoCDVersionsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "argocd_versions", kind: "message", T: ComponentVersion, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListArgoCDVersionsResponse {
    return new ListArgoCDVersionsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListArgoCDVersionsResponse {
    return new ListArgoCDVersionsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListArgoCDVersionsResponse {
    return new ListArgoCDVersionsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListArgoCDVersionsResponse | PlainMessage<ListArgoCDVersionsResponse> | undefined, b: ListArgoCDVersionsResponse | PlainMessage<ListArgoCDVersionsResponse> | undefined): boolean {
    return proto3.util.equals(ListArgoCDVersionsResponse, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.ListKargoVersionsResponse
 */
export class ListKargoVersionsResponse extends Message<ListKargoVersionsResponse> {
  /**
   * @generated from field: repeated akuity.system.v1.ComponentVersion kargo_versions = 1;
   */
  kargoVersions: ComponentVersion[] = [];

  constructor(data?: PartialMessage<ListKargoVersionsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.ListKargoVersionsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "kargo_versions", kind: "message", T: ComponentVersion, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListKargoVersionsResponse {
    return new ListKargoVersionsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListKargoVersionsResponse {
    return new ListKargoVersionsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListKargoVersionsResponse {
    return new ListKargoVersionsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListKargoVersionsResponse | PlainMessage<ListKargoVersionsResponse> | undefined, b: ListKargoVersionsResponse | PlainMessage<ListKargoVersionsResponse> | undefined): boolean {
    return proto3.util.equals(ListKargoVersionsResponse, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.ExtensionInfo
 */
export class ExtensionInfo extends Message<ExtensionInfo> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  /**
   * @generated from field: string description = 3;
   */
  description = "";

  /**
   * @generated from field: string home = 4;
   */
  home = "";

  /**
   * @generated from field: string icon = 5;
   */
  icon = "";

  /**
   * @generated from field: repeated akuity.system.v1.ExtensionInfo.ExtensionVersion versions = 6;
   */
  versions: ExtensionInfo_ExtensionVersion[] = [];

  constructor(data?: PartialMessage<ExtensionInfo>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.ExtensionInfo";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "home", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "icon", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "versions", kind: "message", T: ExtensionInfo_ExtensionVersion, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ExtensionInfo {
    return new ExtensionInfo().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ExtensionInfo {
    return new ExtensionInfo().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ExtensionInfo {
    return new ExtensionInfo().fromJsonString(jsonString, options);
  }

  static equals(a: ExtensionInfo | PlainMessage<ExtensionInfo> | undefined, b: ExtensionInfo | PlainMessage<ExtensionInfo> | undefined): boolean {
    return proto3.util.equals(ExtensionInfo, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.ExtensionInfo.ExtensionVersion
 */
export class ExtensionInfo_ExtensionVersion extends Message<ExtensionInfo_ExtensionVersion> {
  /**
   * @generated from field: string version = 1;
   */
  version = "";

  /**
   * @generated from field: string url = 2;
   */
  url = "";

  /**
   * @generated from field: string sha256_sum = 3;
   */
  sha256Sum = "";

  constructor(data?: PartialMessage<ExtensionInfo_ExtensionVersion>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.ExtensionInfo.ExtensionVersion";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "version", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "url", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "sha256_sum", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ExtensionInfo_ExtensionVersion {
    return new ExtensionInfo_ExtensionVersion().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ExtensionInfo_ExtensionVersion {
    return new ExtensionInfo_ExtensionVersion().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ExtensionInfo_ExtensionVersion {
    return new ExtensionInfo_ExtensionVersion().fromJsonString(jsonString, options);
  }

  static equals(a: ExtensionInfo_ExtensionVersion | PlainMessage<ExtensionInfo_ExtensionVersion> | undefined, b: ExtensionInfo_ExtensionVersion | PlainMessage<ExtensionInfo_ExtensionVersion> | undefined): boolean {
    return proto3.util.equals(ExtensionInfo_ExtensionVersion, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.ListArgoCDExtensionsResponse
 */
export class ListArgoCDExtensionsResponse extends Message<ListArgoCDExtensionsResponse> {
  /**
   * @generated from field: repeated akuity.system.v1.ExtensionInfo extensions = 1;
   */
  extensions: ExtensionInfo[] = [];

  constructor(data?: PartialMessage<ListArgoCDExtensionsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.ListArgoCDExtensionsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "extensions", kind: "message", T: ExtensionInfo, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListArgoCDExtensionsResponse {
    return new ListArgoCDExtensionsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListArgoCDExtensionsResponse {
    return new ListArgoCDExtensionsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListArgoCDExtensionsResponse {
    return new ListArgoCDExtensionsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListArgoCDExtensionsResponse | PlainMessage<ListArgoCDExtensionsResponse> | undefined, b: ListArgoCDExtensionsResponse | PlainMessage<ListArgoCDExtensionsResponse> | undefined): boolean {
    return proto3.util.equals(ListArgoCDExtensionsResponse, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.ListAgentVersionsResponse
 */
export class ListAgentVersionsResponse extends Message<ListAgentVersionsResponse> {
  /**
   * @generated from field: repeated string agent_versions = 1;
   */
  agentVersions: string[] = [];

  constructor(data?: PartialMessage<ListAgentVersionsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.ListAgentVersionsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "agent_versions", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAgentVersionsResponse {
    return new ListAgentVersionsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAgentVersionsResponse {
    return new ListAgentVersionsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAgentVersionsResponse {
    return new ListAgentVersionsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListAgentVersionsResponse | PlainMessage<ListAgentVersionsResponse> | undefined, b: ListAgentVersionsResponse | PlainMessage<ListAgentVersionsResponse> | undefined): boolean {
    return proto3.util.equals(ListAgentVersionsResponse, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.Banner
 */
export class Banner extends Message<Banner> {
  /**
   * @generated from field: optional string title = 1;
   */
  title?: string;

  /**
   * @generated from field: string message = 2;
   */
  message = "";

  /**
   * @generated from field: optional string closable = 3;
   */
  closable?: string;

  /**
   * @generated from field: optional string type = 4;
   */
  type?: string;

  /**
   * @generated from field: repeated akuity.system.v1.Banner.BannerLink links = 5;
   */
  links: Banner_BannerLink[] = [];

  /**
   * @generated from field: optional bool paid_customers_only = 6;
   */
  paidCustomersOnly?: boolean;

  constructor(data?: PartialMessage<Banner>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.Banner";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "title", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 2, name: "message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "closable", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 4, name: "type", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 5, name: "links", kind: "message", T: Banner_BannerLink, repeated: true },
    { no: 6, name: "paid_customers_only", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Banner {
    return new Banner().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Banner {
    return new Banner().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Banner {
    return new Banner().fromJsonString(jsonString, options);
  }

  static equals(a: Banner | PlainMessage<Banner> | undefined, b: Banner | PlainMessage<Banner> | undefined): boolean {
    return proto3.util.equals(Banner, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.Banner.BannerLink
 */
export class Banner_BannerLink extends Message<Banner_BannerLink> {
  /**
   * @generated from field: string name = 1;
   */
  name = "";

  /**
   * @generated from field: string url = 2;
   */
  url = "";

  constructor(data?: PartialMessage<Banner_BannerLink>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.Banner.BannerLink";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "url", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Banner_BannerLink {
    return new Banner_BannerLink().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Banner_BannerLink {
    return new Banner_BannerLink().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Banner_BannerLink {
    return new Banner_BannerLink().fromJsonString(jsonString, options);
  }

  static equals(a: Banner_BannerLink | PlainMessage<Banner_BannerLink> | undefined, b: Banner_BannerLink | PlainMessage<Banner_BannerLink> | undefined): boolean {
    return proto3.util.equals(Banner_BannerLink, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.GetAnnouncementResponse
 */
export class GetAnnouncementResponse extends Message<GetAnnouncementResponse> {
  /**
   * @generated from field: akuity.system.v1.Banner banner = 1;
   */
  banner?: Banner;

  constructor(data?: PartialMessage<GetAnnouncementResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.GetAnnouncementResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "banner", kind: "message", T: Banner },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAnnouncementResponse {
    return new GetAnnouncementResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAnnouncementResponse {
    return new GetAnnouncementResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAnnouncementResponse {
    return new GetAnnouncementResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetAnnouncementResponse | PlainMessage<GetAnnouncementResponse> | undefined, b: GetAnnouncementResponse | PlainMessage<GetAnnouncementResponse> | undefined): boolean {
    return proto3.util.equals(GetAnnouncementResponse, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.system.v1.ListValidWebhookEventsRequest
 */
export class ListValidWebhookEventsRequest extends Message<ListValidWebhookEventsRequest> {
  constructor(data?: PartialMessage<ListValidWebhookEventsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.ListValidWebhookEventsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListValidWebhookEventsRequest {
    return new ListValidWebhookEventsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListValidWebhookEventsRequest {
    return new ListValidWebhookEventsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListValidWebhookEventsRequest {
    return new ListValidWebhookEventsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListValidWebhookEventsRequest | PlainMessage<ListValidWebhookEventsRequest> | undefined, b: ListValidWebhookEventsRequest | PlainMessage<ListValidWebhookEventsRequest> | undefined): boolean {
    return proto3.util.equals(ListValidWebhookEventsRequest, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.ListValidWebhookEventsResponse
 */
export class ListValidWebhookEventsResponse extends Message<ListValidWebhookEventsResponse> {
  /**
   * @generated from field: repeated string events = 1;
   */
  events: string[] = [];

  constructor(data?: PartialMessage<ListValidWebhookEventsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.ListValidWebhookEventsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "events", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListValidWebhookEventsResponse {
    return new ListValidWebhookEventsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListValidWebhookEventsResponse {
    return new ListValidWebhookEventsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListValidWebhookEventsResponse {
    return new ListValidWebhookEventsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListValidWebhookEventsResponse | PlainMessage<ListValidWebhookEventsResponse> | undefined, b: ListValidWebhookEventsResponse | PlainMessage<ListValidWebhookEventsResponse> | undefined): boolean {
    return proto3.util.equals(ListValidWebhookEventsResponse, a, b);
  }
}

/**
 * @generated from message akuity.system.v1.ListArgoCDImageUpadterVersionsResponse
 */
export class ListArgoCDImageUpadterVersionsResponse extends Message<ListArgoCDImageUpadterVersionsResponse> {
  /**
   * @generated from field: repeated string versions = 1;
   */
  versions: string[] = [];

  constructor(data?: PartialMessage<ListArgoCDImageUpadterVersionsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.system.v1.ListArgoCDImageUpadterVersionsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "versions", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListArgoCDImageUpadterVersionsResponse {
    return new ListArgoCDImageUpadterVersionsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListArgoCDImageUpadterVersionsResponse {
    return new ListArgoCDImageUpadterVersionsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListArgoCDImageUpadterVersionsResponse {
    return new ListArgoCDImageUpadterVersionsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListArgoCDImageUpadterVersionsResponse | PlainMessage<ListArgoCDImageUpadterVersionsResponse> | undefined, b: ListArgoCDImageUpadterVersionsResponse | PlainMessage<ListArgoCDImageUpadterVersionsResponse> | undefined): boolean {
    return proto3.util.equals(ListArgoCDImageUpadterVersionsResponse, a, b);
  }
}

