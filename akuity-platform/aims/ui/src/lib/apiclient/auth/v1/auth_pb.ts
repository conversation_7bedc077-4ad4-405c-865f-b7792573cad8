// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file auth/v1/auth.proto (package akuity.auth.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * explicitly empty 
 *
 * @generated from message akuity.auth.v1.GetDeviceCodeRequest
 */
export class GetDeviceCodeRequest extends Message<GetDeviceCodeRequest> {
  constructor(data?: PartialMessage<GetDeviceCodeRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.auth.v1.GetDeviceCodeRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetDeviceCodeRequest {
    return new GetDeviceCodeRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetDeviceCodeRequest {
    return new GetDeviceCodeRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetDeviceCodeRequest {
    return new GetDeviceCodeRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetDeviceCodeRequest | PlainMessage<GetDeviceCodeRequest> | undefined, b: GetDeviceCodeRequest | PlainMessage<GetDeviceCodeRequest> | undefined): boolean {
    return proto3.util.equals(GetDeviceCodeRequest, a, b);
  }
}

/**
 * @generated from message akuity.auth.v1.GetDeviceCodeResponse
 */
export class GetDeviceCodeResponse extends Message<GetDeviceCodeResponse> {
  /**
   * @generated from field: string device_code = 1;
   */
  deviceCode = "";

  /**
   * @generated from field: string user_code = 2;
   */
  userCode = "";

  /**
   * @generated from field: string verification_uri = 3;
   */
  verificationUri = "";

  /**
   * @generated from field: string verification_uri_complete = 4;
   */
  verificationUriComplete = "";

  /**
   * @generated from field: int32 expires_in_seconds = 5;
   */
  expiresInSeconds = 0;

  /**
   * @generated from field: int32 interval_seconds = 6;
   */
  intervalSeconds = 0;

  constructor(data?: PartialMessage<GetDeviceCodeResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.auth.v1.GetDeviceCodeResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "device_code", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "user_code", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "verification_uri", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "verification_uri_complete", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "expires_in_seconds", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 6, name: "interval_seconds", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetDeviceCodeResponse {
    return new GetDeviceCodeResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetDeviceCodeResponse {
    return new GetDeviceCodeResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetDeviceCodeResponse {
    return new GetDeviceCodeResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetDeviceCodeResponse | PlainMessage<GetDeviceCodeResponse> | undefined, b: GetDeviceCodeResponse | PlainMessage<GetDeviceCodeResponse> | undefined): boolean {
    return proto3.util.equals(GetDeviceCodeResponse, a, b);
  }
}

/**
 * @generated from message akuity.auth.v1.GetDeviceTokenRequest
 */
export class GetDeviceTokenRequest extends Message<GetDeviceTokenRequest> {
  /**
   * @generated from field: string device_code = 1;
   */
  deviceCode = "";

  constructor(data?: PartialMessage<GetDeviceTokenRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.auth.v1.GetDeviceTokenRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "device_code", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetDeviceTokenRequest {
    return new GetDeviceTokenRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetDeviceTokenRequest {
    return new GetDeviceTokenRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetDeviceTokenRequest {
    return new GetDeviceTokenRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetDeviceTokenRequest | PlainMessage<GetDeviceTokenRequest> | undefined, b: GetDeviceTokenRequest | PlainMessage<GetDeviceTokenRequest> | undefined): boolean {
    return proto3.util.equals(GetDeviceTokenRequest, a, b);
  }
}

/**
 * @generated from message akuity.auth.v1.GetDeviceTokenResponse
 */
export class GetDeviceTokenResponse extends Message<GetDeviceTokenResponse> {
  /**
   * @generated from field: string access_token = 1;
   */
  accessToken = "";

  constructor(data?: PartialMessage<GetDeviceTokenResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.auth.v1.GetDeviceTokenResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "access_token", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetDeviceTokenResponse {
    return new GetDeviceTokenResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetDeviceTokenResponse {
    return new GetDeviceTokenResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetDeviceTokenResponse {
    return new GetDeviceTokenResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetDeviceTokenResponse | PlainMessage<GetDeviceTokenResponse> | undefined, b: GetDeviceTokenResponse | PlainMessage<GetDeviceTokenResponse> | undefined): boolean {
    return proto3.util.equals(GetDeviceTokenResponse, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.auth.v1.RefreshAccessTokenRequest
 */
export class RefreshAccessTokenRequest extends Message<RefreshAccessTokenRequest> {
  constructor(data?: PartialMessage<RefreshAccessTokenRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.auth.v1.RefreshAccessTokenRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshAccessTokenRequest {
    return new RefreshAccessTokenRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshAccessTokenRequest {
    return new RefreshAccessTokenRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshAccessTokenRequest {
    return new RefreshAccessTokenRequest().fromJsonString(jsonString, options);
  }

  static equals(a: RefreshAccessTokenRequest | PlainMessage<RefreshAccessTokenRequest> | undefined, b: RefreshAccessTokenRequest | PlainMessage<RefreshAccessTokenRequest> | undefined): boolean {
    return proto3.util.equals(RefreshAccessTokenRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.auth.v1.RefreshAccessTokenResponse
 */
export class RefreshAccessTokenResponse extends Message<RefreshAccessTokenResponse> {
  constructor(data?: PartialMessage<RefreshAccessTokenResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.auth.v1.RefreshAccessTokenResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): RefreshAccessTokenResponse {
    return new RefreshAccessTokenResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): RefreshAccessTokenResponse {
    return new RefreshAccessTokenResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): RefreshAccessTokenResponse {
    return new RefreshAccessTokenResponse().fromJsonString(jsonString, options);
  }

  static equals(a: RefreshAccessTokenResponse | PlainMessage<RefreshAccessTokenResponse> | undefined, b: RefreshAccessTokenResponse | PlainMessage<RefreshAccessTokenResponse> | undefined): boolean {
    return proto3.util.equals(RefreshAccessTokenResponse, a, b);
  }
}

/**
 * @generated from message akuity.auth.v1.GetOIDCProviderDetailsRequest
 */
export class GetOIDCProviderDetailsRequest extends Message<GetOIDCProviderDetailsRequest> {
  /**
   * @generated from field: string discovery_url = 1;
   */
  discoveryUrl = "";

  constructor(data?: PartialMessage<GetOIDCProviderDetailsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.auth.v1.GetOIDCProviderDetailsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "discovery_url", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetOIDCProviderDetailsRequest {
    return new GetOIDCProviderDetailsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetOIDCProviderDetailsRequest {
    return new GetOIDCProviderDetailsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetOIDCProviderDetailsRequest {
    return new GetOIDCProviderDetailsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetOIDCProviderDetailsRequest | PlainMessage<GetOIDCProviderDetailsRequest> | undefined, b: GetOIDCProviderDetailsRequest | PlainMessage<GetOIDCProviderDetailsRequest> | undefined): boolean {
    return proto3.util.equals(GetOIDCProviderDetailsRequest, a, b);
  }
}

/**
 * @generated from message akuity.auth.v1.GetOIDCProviderDetailsResponse
 */
export class GetOIDCProviderDetailsResponse extends Message<GetOIDCProviderDetailsResponse> {
  /**
   * @generated from field: string issuer = 1;
   */
  issuer = "";

  /**
   * @generated from field: string authorization_endpoint = 2;
   */
  authorizationEndpoint = "";

  /**
   * @generated from field: string token_endpoint = 3;
   */
  tokenEndpoint = "";

  /**
   * @generated from field: string userinfo_endpoint = 4;
   */
  userinfoEndpoint = "";

  /**
   * @generated from field: string jwks_uri = 5;
   */
  jwksUri = "";

  constructor(data?: PartialMessage<GetOIDCProviderDetailsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.auth.v1.GetOIDCProviderDetailsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "issuer", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "authorization_endpoint", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "token_endpoint", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "userinfo_endpoint", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 5, name: "jwks_uri", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetOIDCProviderDetailsResponse {
    return new GetOIDCProviderDetailsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetOIDCProviderDetailsResponse {
    return new GetOIDCProviderDetailsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetOIDCProviderDetailsResponse {
    return new GetOIDCProviderDetailsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetOIDCProviderDetailsResponse | PlainMessage<GetOIDCProviderDetailsResponse> | undefined, b: GetOIDCProviderDetailsResponse | PlainMessage<GetOIDCProviderDetailsResponse> | undefined): boolean {
    return proto3.util.equals(GetOIDCProviderDetailsResponse, a, b);
  }
}

