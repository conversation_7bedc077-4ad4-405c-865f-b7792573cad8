// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file aims/v1/aims.proto (package akuity.aims.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3, protoInt64, Struct, Timestamp } from "@bufbuild/protobuf";
import { Cluster, Instance } from "../../argocd/v1/argocd_pb.js";
import { AuditFilters, AuditLog as AuditLog$1, BillingDetails, DomainVerification, KubeVisionUsage, OrganizationStatus } from "../../organization/v1/organization_pb.js";
import { OrganizationFeatureGates, OrganizationQuota, OrganizationUsage, SystemFeatureGates } from "../../types/features/v1/features_pb.js";
import { KargoAgent, KargoInstance } from "../../kargo/v1/kargo_pb.js";

/**
 * @generated from enum akuity.aims.v1.NotificationCategory
 */
export enum NotificationCategory {
  /**
   * @generated from enum value: NOTIFICATION_CATEGORY_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: NOTIFICATION_CATEGORY_CUSTOM = 1;
   */
  CUSTOM = 1,

  /**
   * @generated from enum value: NOTIFICATION_CATEGORY_NEW_FEATURE = 2;
   */
  NEW_FEATURE = 2,
}
// Retrieve enum metadata with: proto3.getEnumType(NotificationCategory)
proto3.util.setEnumType(NotificationCategory, "akuity.aims.v1.NotificationCategory", [
  { no: 0, name: "NOTIFICATION_CATEGORY_UNSPECIFIED" },
  { no: 1, name: "NOTIFICATION_CATEGORY_CUSTOM" },
  { no: 2, name: "NOTIFICATION_CATEGORY_NEW_FEATURE" },
]);

/**
 * @generated from enum akuity.aims.v1.Sort
 */
export enum Sort {
  /**
   * @generated from enum value: SORT_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: SORT_ASCENDING = 1;
   */
  ASCENDING = 1,

  /**
   * @generated from enum value: SORT_DESCENDING = 2;
   */
  DESCENDING = 2,
}
// Retrieve enum metadata with: proto3.getEnumType(Sort)
proto3.util.setEnumType(Sort, "akuity.aims.v1.Sort", [
  { no: 0, name: "SORT_UNSPECIFIED" },
  { no: 1, name: "SORT_ASCENDING" },
  { no: 2, name: "SORT_DESCENDING" },
]);

/**
 * @generated from enum akuity.aims.v1.WorkspaceMemberRole
 */
export enum WorkspaceMemberRole {
  /**
   * @generated from enum value: WORKSPACE_MEMBER_ROLE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: WORKSPACE_MEMBER_ROLE_MEMBER = 1;
   */
  MEMBER = 1,

  /**
   * @generated from enum value: WORKSPACE_MEMBER_ROLE_ADMIN = 2;
   */
  ADMIN = 2,
}
// Retrieve enum metadata with: proto3.getEnumType(WorkspaceMemberRole)
proto3.util.setEnumType(WorkspaceMemberRole, "akuity.aims.v1.WorkspaceMemberRole", [
  { no: 0, name: "WORKSPACE_MEMBER_ROLE_UNSPECIFIED" },
  { no: 1, name: "WORKSPACE_MEMBER_ROLE_MEMBER" },
  { no: 2, name: "WORKSPACE_MEMBER_ROLE_ADMIN" },
]);

/**
 * @generated from message akuity.aims.v1.SendNotificationRequest
 */
export class SendNotificationRequest extends Message<SendNotificationRequest> {
  /**
   * test mode ensures that notification
   * 1. is only sent to the organization containing akuity member
   * 2. member in the audit email should be same in that organization
   *
   * @generated from field: optional bool test_mode = 1;
   */
  testMode?: boolean;

  /**
   * currently, only used when test mode is on
   *
   * @generated from field: optional string organization_id = 2;
   */
  organizationId?: string;

  /**
   * @generated from field: akuity.aims.v1.Audit audit = 3;
   */
  audit?: Audit;

  /**
   * @generated from field: akuity.aims.v1.NotificationCategory category = 4;
   */
  category = NotificationCategory.UNSPECIFIED;

  /**
   * this should match
   *
   * @generated from field: google.protobuf.Struct metadata = 5;
   */
  metadata?: Struct;

  constructor(data?: PartialMessage<SendNotificationRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.SendNotificationRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "test_mode", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 2, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 3, name: "audit", kind: "message", T: Audit },
    { no: 4, name: "category", kind: "enum", T: proto3.getEnumType(NotificationCategory) },
    { no: 5, name: "metadata", kind: "message", T: Struct },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SendNotificationRequest {
    return new SendNotificationRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SendNotificationRequest {
    return new SendNotificationRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SendNotificationRequest {
    return new SendNotificationRequest().fromJsonString(jsonString, options);
  }

  static equals(a: SendNotificationRequest | PlainMessage<SendNotificationRequest> | undefined, b: SendNotificationRequest | PlainMessage<SendNotificationRequest> | undefined): boolean {
    return proto3.util.equals(SendNotificationRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.aims.v1.SendNotificationResponse
 */
export class SendNotificationResponse extends Message<SendNotificationResponse> {
  constructor(data?: PartialMessage<SendNotificationResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.SendNotificationResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SendNotificationResponse {
    return new SendNotificationResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SendNotificationResponse {
    return new SendNotificationResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SendNotificationResponse {
    return new SendNotificationResponse().fromJsonString(jsonString, options);
  }

  static equals(a: SendNotificationResponse | PlainMessage<SendNotificationResponse> | undefined, b: SendNotificationResponse | PlainMessage<SendNotificationResponse> | undefined): boolean {
    return proto3.util.equals(SendNotificationResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ArgoInstanceFilter
 */
export class ArgoInstanceFilter extends Message<ArgoInstanceFilter> {
  /**
   * @generated from field: optional bool paid = 1;
   */
  paid?: boolean;

  /**
   * @generated from field: optional bool unpaid = 2;
   */
  unpaid?: boolean;

  /**
   * search by instance name/id
   *
   * @generated from field: string fuzz = 3;
   */
  fuzz = "";

  /**
   * @generated from field: optional string time_from = 4;
   */
  timeFrom?: string;

  /**
   * filter by organization ID
   *
   * @generated from field: optional string organization_id = 5;
   */
  organizationId?: string;

  constructor(data?: PartialMessage<ArgoInstanceFilter>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ArgoInstanceFilter";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "paid", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 2, name: "unpaid", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 3, name: "fuzz", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "time_from", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 5, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ArgoInstanceFilter {
    return new ArgoInstanceFilter().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ArgoInstanceFilter {
    return new ArgoInstanceFilter().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ArgoInstanceFilter {
    return new ArgoInstanceFilter().fromJsonString(jsonString, options);
  }

  static equals(a: ArgoInstanceFilter | PlainMessage<ArgoInstanceFilter> | undefined, b: ArgoInstanceFilter | PlainMessage<ArgoInstanceFilter> | undefined): boolean {
    return proto3.util.equals(ArgoInstanceFilter, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListArgoInstancesRequest
 */
export class ListArgoInstancesRequest extends Message<ListArgoInstancesRequest> {
  /**
   * @generated from field: optional akuity.aims.v1.ArgoInstanceFilter filter = 1;
   */
  filter?: ArgoInstanceFilter;

  constructor(data?: PartialMessage<ListArgoInstancesRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListArgoInstancesRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "filter", kind: "message", T: ArgoInstanceFilter, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListArgoInstancesRequest {
    return new ListArgoInstancesRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListArgoInstancesRequest {
    return new ListArgoInstancesRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListArgoInstancesRequest {
    return new ListArgoInstancesRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListArgoInstancesRequest | PlainMessage<ListArgoInstancesRequest> | undefined, b: ListArgoInstancesRequest | PlainMessage<ListArgoInstancesRequest> | undefined): boolean {
    return proto3.util.equals(ListArgoInstancesRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListArgoInstancesResponse
 */
export class ListArgoInstancesResponse extends Message<ListArgoInstancesResponse> {
  /**
   * @generated from field: repeated akuity.aims.v1.InternalInstance instances = 1;
   */
  instances: InternalInstance[] = [];

  constructor(data?: PartialMessage<ListArgoInstancesResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListArgoInstancesResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instances", kind: "message", T: InternalInstance, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListArgoInstancesResponse {
    return new ListArgoInstancesResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListArgoInstancesResponse {
    return new ListArgoInstancesResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListArgoInstancesResponse {
    return new ListArgoInstancesResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListArgoInstancesResponse | PlainMessage<ListArgoInstancesResponse> | undefined, b: ListArgoInstancesResponse | PlainMessage<ListArgoInstancesResponse> | undefined): boolean {
    return proto3.util.equals(ListArgoInstancesResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.DeleteUnpaidInstanceRequest
 */
export class DeleteUnpaidInstanceRequest extends Message<DeleteUnpaidInstanceRequest> {
  /**
   * @generated from field: string instance_id = 1;
   */
  instanceId = "";

  /**
   * @generated from field: akuity.aims.v1.Audit audit = 2;
   */
  audit?: Audit;

  constructor(data?: PartialMessage<DeleteUnpaidInstanceRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.DeleteUnpaidInstanceRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "audit", kind: "message", T: Audit },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteUnpaidInstanceRequest {
    return new DeleteUnpaidInstanceRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteUnpaidInstanceRequest {
    return new DeleteUnpaidInstanceRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteUnpaidInstanceRequest {
    return new DeleteUnpaidInstanceRequest().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteUnpaidInstanceRequest | PlainMessage<DeleteUnpaidInstanceRequest> | undefined, b: DeleteUnpaidInstanceRequest | PlainMessage<DeleteUnpaidInstanceRequest> | undefined): boolean {
    return proto3.util.equals(DeleteUnpaidInstanceRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.aims.v1.DeleteUnpaidInstanceResponse
 */
export class DeleteUnpaidInstanceResponse extends Message<DeleteUnpaidInstanceResponse> {
  constructor(data?: PartialMessage<DeleteUnpaidInstanceResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.DeleteUnpaidInstanceResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteUnpaidInstanceResponse {
    return new DeleteUnpaidInstanceResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteUnpaidInstanceResponse {
    return new DeleteUnpaidInstanceResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteUnpaidInstanceResponse {
    return new DeleteUnpaidInstanceResponse().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteUnpaidInstanceResponse | PlainMessage<DeleteUnpaidInstanceResponse> | undefined, b: DeleteUnpaidInstanceResponse | PlainMessage<DeleteUnpaidInstanceResponse> | undefined): boolean {
    return proto3.util.equals(DeleteUnpaidInstanceResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.OnboardManualCustomerRequest
 */
export class OnboardManualCustomerRequest extends Message<OnboardManualCustomerRequest> {
  /**
   * @generated from field: akuity.aims.v1.Customer customer = 1;
   */
  customer?: Customer;

  /**
   * @generated from field: akuity.aims.v1.Audit audit = 2;
   */
  audit?: Audit;

  constructor(data?: PartialMessage<OnboardManualCustomerRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.OnboardManualCustomerRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "customer", kind: "message", T: Customer },
    { no: 2, name: "audit", kind: "message", T: Audit },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OnboardManualCustomerRequest {
    return new OnboardManualCustomerRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OnboardManualCustomerRequest {
    return new OnboardManualCustomerRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OnboardManualCustomerRequest {
    return new OnboardManualCustomerRequest().fromJsonString(jsonString, options);
  }

  static equals(a: OnboardManualCustomerRequest | PlainMessage<OnboardManualCustomerRequest> | undefined, b: OnboardManualCustomerRequest | PlainMessage<OnboardManualCustomerRequest> | undefined): boolean {
    return proto3.util.equals(OnboardManualCustomerRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.aims.v1.OnboardManualCustomerResponse
 */
export class OnboardManualCustomerResponse extends Message<OnboardManualCustomerResponse> {
  constructor(data?: PartialMessage<OnboardManualCustomerResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.OnboardManualCustomerResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OnboardManualCustomerResponse {
    return new OnboardManualCustomerResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OnboardManualCustomerResponse {
    return new OnboardManualCustomerResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OnboardManualCustomerResponse {
    return new OnboardManualCustomerResponse().fromJsonString(jsonString, options);
  }

  static equals(a: OnboardManualCustomerResponse | PlainMessage<OnboardManualCustomerResponse> | undefined, b: OnboardManualCustomerResponse | PlainMessage<OnboardManualCustomerResponse> | undefined): boolean {
    return proto3.util.equals(OnboardManualCustomerResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.NotificationConfig
 */
export class NotificationConfig extends Message<NotificationConfig> {
  /**
   * @generated from field: map<string, string> config = 1;
   */
  config: { [key: string]: string } = {};

  constructor(data?: PartialMessage<NotificationConfig>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.NotificationConfig";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "config", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 9 /* ScalarType.STRING */} },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NotificationConfig {
    return new NotificationConfig().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NotificationConfig {
    return new NotificationConfig().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NotificationConfig {
    return new NotificationConfig().fromJsonString(jsonString, options);
  }

  static equals(a: NotificationConfig | PlainMessage<NotificationConfig> | undefined, b: NotificationConfig | PlainMessage<NotificationConfig> | undefined): boolean {
    return proto3.util.equals(NotificationConfig, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.InternalInstance
 */
export class InternalInstance extends Message<InternalInstance> {
  /**
   * @generated from field: akuity.argocd.v1.Instance instance = 1;
   */
  instance?: Instance;

  /**
   * @generated from field: optional google.protobuf.Timestamp create_time = 2;
   */
  createTime?: Timestamp;

  /**
   * @generated from field: bool expired = 3;
   */
  expired = false;

  /**
   * @generated from field: uint32 connected_clusters = 4;
   */
  connectedClusters = 0;

  /**
   * @generated from field: optional string status_processed_info = 5;
   */
  statusProcessedInfo?: string;

  /**
   * @generated from field: string org_id = 6;
   */
  orgId = "";

  /**
   * @generated from field: akuity.aims.v1.NotificationConfig notification_config = 7;
   */
  notificationConfig?: NotificationConfig;

  /**
   * @generated from field: optional akuity.aims.v1.Workspace workspace = 8;
   */
  workspace?: Workspace;

  constructor(data?: PartialMessage<InternalInstance>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.InternalInstance";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instance", kind: "message", T: Instance },
    { no: 2, name: "create_time", kind: "message", T: Timestamp, opt: true },
    { no: 3, name: "expired", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 4, name: "connected_clusters", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 5, name: "status_processed_info", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 6, name: "org_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 7, name: "notification_config", kind: "message", T: NotificationConfig },
    { no: 8, name: "workspace", kind: "message", T: Workspace, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InternalInstance {
    return new InternalInstance().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InternalInstance {
    return new InternalInstance().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InternalInstance {
    return new InternalInstance().fromJsonString(jsonString, options);
  }

  static equals(a: InternalInstance | PlainMessage<InternalInstance> | undefined, b: InternalInstance | PlainMessage<InternalInstance> | undefined): boolean {
    return proto3.util.equals(InternalInstance, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.Customer
 */
export class Customer extends Message<Customer> {
  /**
   * @generated from field: string stripe_id = 1;
   */
  stripeId = "";

  /**
   * @generated from field: string organization_id = 2;
   */
  organizationId = "";

  /**
   * @generated from field: optional string billing_name = 3;
   */
  billingName?: string;

  /**
   * @generated from field: optional string billing_email = 4;
   */
  billingEmail?: string;

  constructor(data?: PartialMessage<Customer>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.Customer";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "stripe_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "billing_name", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 4, name: "billing_email", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Customer {
    return new Customer().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Customer {
    return new Customer().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Customer {
    return new Customer().fromJsonString(jsonString, options);
  }

  static equals(a: Customer | PlainMessage<Customer> | undefined, b: Customer | PlainMessage<Customer> | undefined): boolean {
    return proto3.util.equals(Customer, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListOrganizationDomainsRequest
 */
export class ListOrganizationDomainsRequest extends Message<ListOrganizationDomainsRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  constructor(data?: PartialMessage<ListOrganizationDomainsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListOrganizationDomainsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListOrganizationDomainsRequest {
    return new ListOrganizationDomainsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListOrganizationDomainsRequest {
    return new ListOrganizationDomainsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListOrganizationDomainsRequest {
    return new ListOrganizationDomainsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListOrganizationDomainsRequest | PlainMessage<ListOrganizationDomainsRequest> | undefined, b: ListOrganizationDomainsRequest | PlainMessage<ListOrganizationDomainsRequest> | undefined): boolean {
    return proto3.util.equals(ListOrganizationDomainsRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListOrganizationDomainsResponse
 */
export class ListOrganizationDomainsResponse extends Message<ListOrganizationDomainsResponse> {
  /**
   * @generated from field: repeated akuity.organization.v1.DomainVerification domains = 1;
   */
  domains: DomainVerification[] = [];

  constructor(data?: PartialMessage<ListOrganizationDomainsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListOrganizationDomainsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "domains", kind: "message", T: DomainVerification, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListOrganizationDomainsResponse {
    return new ListOrganizationDomainsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListOrganizationDomainsResponse {
    return new ListOrganizationDomainsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListOrganizationDomainsResponse {
    return new ListOrganizationDomainsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListOrganizationDomainsResponse | PlainMessage<ListOrganizationDomainsResponse> | undefined, b: ListOrganizationDomainsResponse | PlainMessage<ListOrganizationDomainsResponse> | undefined): boolean {
    return proto3.util.equals(ListOrganizationDomainsResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.UpdateOrganizationDomainsRequest
 */
export class UpdateOrganizationDomainsRequest extends Message<UpdateOrganizationDomainsRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: repeated akuity.organization.v1.DomainVerification domains = 2;
   */
  domains: DomainVerification[] = [];

  /**
   * @generated from field: akuity.aims.v1.Audit audit = 3;
   */
  audit?: Audit;

  constructor(data?: PartialMessage<UpdateOrganizationDomainsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.UpdateOrganizationDomainsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "domains", kind: "message", T: DomainVerification, repeated: true },
    { no: 3, name: "audit", kind: "message", T: Audit },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateOrganizationDomainsRequest {
    return new UpdateOrganizationDomainsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateOrganizationDomainsRequest {
    return new UpdateOrganizationDomainsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateOrganizationDomainsRequest {
    return new UpdateOrganizationDomainsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateOrganizationDomainsRequest | PlainMessage<UpdateOrganizationDomainsRequest> | undefined, b: UpdateOrganizationDomainsRequest | PlainMessage<UpdateOrganizationDomainsRequest> | undefined): boolean {
    return proto3.util.equals(UpdateOrganizationDomainsRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.UpdateOrganizationDomainsResponse
 */
export class UpdateOrganizationDomainsResponse extends Message<UpdateOrganizationDomainsResponse> {
  /**
   * @generated from field: repeated akuity.organization.v1.DomainVerification domains = 1;
   */
  domains: DomainVerification[] = [];

  constructor(data?: PartialMessage<UpdateOrganizationDomainsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.UpdateOrganizationDomainsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "domains", kind: "message", T: DomainVerification, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateOrganizationDomainsResponse {
    return new UpdateOrganizationDomainsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateOrganizationDomainsResponse {
    return new UpdateOrganizationDomainsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateOrganizationDomainsResponse {
    return new UpdateOrganizationDomainsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateOrganizationDomainsResponse | PlainMessage<UpdateOrganizationDomainsResponse> | undefined, b: UpdateOrganizationDomainsResponse | PlainMessage<UpdateOrganizationDomainsResponse> | undefined): boolean {
    return proto3.util.equals(UpdateOrganizationDomainsResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListOrganizationMembersRequest
 */
export class ListOrganizationMembersRequest extends Message<ListOrganizationMembersRequest> {
  /**
   * @generated from field: repeated string organization_id = 1;
   */
  organizationId: string[] = [];

  constructor(data?: PartialMessage<ListOrganizationMembersRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListOrganizationMembersRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListOrganizationMembersRequest {
    return new ListOrganizationMembersRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListOrganizationMembersRequest {
    return new ListOrganizationMembersRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListOrganizationMembersRequest {
    return new ListOrganizationMembersRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListOrganizationMembersRequest | PlainMessage<ListOrganizationMembersRequest> | undefined, b: ListOrganizationMembersRequest | PlainMessage<ListOrganizationMembersRequest> | undefined): boolean {
    return proto3.util.equals(ListOrganizationMembersRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.OrganizationMembers
 */
export class OrganizationMembers extends Message<OrganizationMembers> {
  /**
   * @generated from field: repeated string email = 1;
   */
  email: string[] = [];

  constructor(data?: PartialMessage<OrganizationMembers>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.OrganizationMembers";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "email", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OrganizationMembers {
    return new OrganizationMembers().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OrganizationMembers {
    return new OrganizationMembers().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OrganizationMembers {
    return new OrganizationMembers().fromJsonString(jsonString, options);
  }

  static equals(a: OrganizationMembers | PlainMessage<OrganizationMembers> | undefined, b: OrganizationMembers | PlainMessage<OrganizationMembers> | undefined): boolean {
    return proto3.util.equals(OrganizationMembers, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListOrganizationMembersResponse
 */
export class ListOrganizationMembersResponse extends Message<ListOrganizationMembersResponse> {
  /**
   * @generated from field: map<string, akuity.aims.v1.OrganizationMembers> members = 1;
   */
  members: { [key: string]: OrganizationMembers } = {};

  constructor(data?: PartialMessage<ListOrganizationMembersResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListOrganizationMembersResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "members", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "message", T: OrganizationMembers} },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListOrganizationMembersResponse {
    return new ListOrganizationMembersResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListOrganizationMembersResponse {
    return new ListOrganizationMembersResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListOrganizationMembersResponse {
    return new ListOrganizationMembersResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListOrganizationMembersResponse | PlainMessage<ListOrganizationMembersResponse> | undefined, b: ListOrganizationMembersResponse | PlainMessage<ListOrganizationMembersResponse> | undefined): boolean {
    return proto3.util.equals(ListOrganizationMembersResponse, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.aims.v1.ListUnbilledOrganizationsRequest
 */
export class ListUnbilledOrganizationsRequest extends Message<ListUnbilledOrganizationsRequest> {
  constructor(data?: PartialMessage<ListUnbilledOrganizationsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListUnbilledOrganizationsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListUnbilledOrganizationsRequest {
    return new ListUnbilledOrganizationsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListUnbilledOrganizationsRequest {
    return new ListUnbilledOrganizationsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListUnbilledOrganizationsRequest {
    return new ListUnbilledOrganizationsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListUnbilledOrganizationsRequest | PlainMessage<ListUnbilledOrganizationsRequest> | undefined, b: ListUnbilledOrganizationsRequest | PlainMessage<ListUnbilledOrganizationsRequest> | undefined): boolean {
    return proto3.util.equals(ListUnbilledOrganizationsRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListUnbilledOrganizationsResponse
 */
export class ListUnbilledOrganizationsResponse extends Message<ListUnbilledOrganizationsResponse> {
  /**
   * @generated from field: repeated akuity.aims.v1.BasicOrganization organizations = 1;
   */
  organizations: BasicOrganization[] = [];

  constructor(data?: PartialMessage<ListUnbilledOrganizationsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListUnbilledOrganizationsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organizations", kind: "message", T: BasicOrganization, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListUnbilledOrganizationsResponse {
    return new ListUnbilledOrganizationsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListUnbilledOrganizationsResponse {
    return new ListUnbilledOrganizationsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListUnbilledOrganizationsResponse {
    return new ListUnbilledOrganizationsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListUnbilledOrganizationsResponse | PlainMessage<ListUnbilledOrganizationsResponse> | undefined, b: ListUnbilledOrganizationsResponse | PlainMessage<ListUnbilledOrganizationsResponse> | undefined): boolean {
    return proto3.util.equals(ListUnbilledOrganizationsResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.OrganizationFilter
 */
export class OrganizationFilter extends Message<OrganizationFilter> {
  /**
   * default limit 50
   *
   * @generated from field: optional uint32 limit = 1;
   */
  limit?: number;

  /**
   * @generated from field: optional uint32 offset = 2;
   */
  offset?: number;

  /**
   * fuzzy search
   * by name
   * by id
   * by member
   *
   * @generated from field: optional string fuzz = 3;
   */
  fuzz?: string;

  /**
   * @generated from field: optional akuity.aims.v1.Sort sort_by_creation = 4;
   */
  sortByCreation?: Sort;

  /**
   * @generated from field: optional bool billed = 5;
   */
  billed?: boolean;

  /**
   * @generated from field: optional bool manually_verified = 6;
   */
  manuallyVerified?: boolean;

  /**
   * @generated from field: repeated string plans = 7;
   */
  plans: string[] = [];

  /**
   * @generated from field: optional string start_time = 8;
   */
  startTime?: string;

  /**
   * @generated from field: optional string end_time = 9;
   */
  endTime?: string;

  constructor(data?: PartialMessage<OrganizationFilter>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.OrganizationFilter";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "limit", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
    { no: 2, name: "offset", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
    { no: 3, name: "fuzz", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 4, name: "sort_by_creation", kind: "enum", T: proto3.getEnumType(Sort), opt: true },
    { no: 5, name: "billed", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 6, name: "manually_verified", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 7, name: "plans", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 8, name: "start_time", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 9, name: "end_time", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OrganizationFilter {
    return new OrganizationFilter().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OrganizationFilter {
    return new OrganizationFilter().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OrganizationFilter {
    return new OrganizationFilter().fromJsonString(jsonString, options);
  }

  static equals(a: OrganizationFilter | PlainMessage<OrganizationFilter> | undefined, b: OrganizationFilter | PlainMessage<OrganizationFilter> | undefined): boolean {
    return proto3.util.equals(OrganizationFilter, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListAllOrganizationsRequest
 */
export class ListAllOrganizationsRequest extends Message<ListAllOrganizationsRequest> {
  /**
   * @generated from field: optional akuity.aims.v1.OrganizationFilter filters = 1;
   */
  filters?: OrganizationFilter;

  constructor(data?: PartialMessage<ListAllOrganizationsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListAllOrganizationsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "filters", kind: "message", T: OrganizationFilter, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAllOrganizationsRequest {
    return new ListAllOrganizationsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAllOrganizationsRequest {
    return new ListAllOrganizationsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAllOrganizationsRequest {
    return new ListAllOrganizationsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListAllOrganizationsRequest | PlainMessage<ListAllOrganizationsRequest> | undefined, b: ListAllOrganizationsRequest | PlainMessage<ListAllOrganizationsRequest> | undefined): boolean {
    return proto3.util.equals(ListAllOrganizationsRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListAllOrganizationsResponse
 */
export class ListAllOrganizationsResponse extends Message<ListAllOrganizationsResponse> {
  /**
   * @generated from field: repeated akuity.aims.v1.BasicOrganization organizations = 1;
   */
  organizations: BasicOrganization[] = [];

  /**
   * @generated from field: uint32 count = 2;
   */
  count = 0;

  constructor(data?: PartialMessage<ListAllOrganizationsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListAllOrganizationsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organizations", kind: "message", T: BasicOrganization, repeated: true },
    { no: 2, name: "count", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAllOrganizationsResponse {
    return new ListAllOrganizationsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAllOrganizationsResponse {
    return new ListAllOrganizationsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAllOrganizationsResponse {
    return new ListAllOrganizationsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListAllOrganizationsResponse | PlainMessage<ListAllOrganizationsResponse> | undefined, b: ListAllOrganizationsResponse | PlainMessage<ListAllOrganizationsResponse> | undefined): boolean {
    return proto3.util.equals(ListAllOrganizationsResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetOrganizationRequest
 */
export class GetOrganizationRequest extends Message<GetOrganizationRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  constructor(data?: PartialMessage<GetOrganizationRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetOrganizationRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetOrganizationRequest {
    return new GetOrganizationRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetOrganizationRequest {
    return new GetOrganizationRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetOrganizationRequest {
    return new GetOrganizationRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetOrganizationRequest | PlainMessage<GetOrganizationRequest> | undefined, b: GetOrganizationRequest | PlainMessage<GetOrganizationRequest> | undefined): boolean {
    return proto3.util.equals(GetOrganizationRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetOrganizationResponse
 */
export class GetOrganizationResponse extends Message<GetOrganizationResponse> {
  /**
   * @generated from field: akuity.aims.v1.BasicOrganization organization = 1;
   */
  organization?: BasicOrganization;

  constructor(data?: PartialMessage<GetOrganizationResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetOrganizationResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization", kind: "message", T: BasicOrganization },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetOrganizationResponse {
    return new GetOrganizationResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetOrganizationResponse {
    return new GetOrganizationResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetOrganizationResponse {
    return new GetOrganizationResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetOrganizationResponse | PlainMessage<GetOrganizationResponse> | undefined, b: GetOrganizationResponse | PlainMessage<GetOrganizationResponse> | undefined): boolean {
    return proto3.util.equals(GetOrganizationResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.StripeData
 */
export class StripeData extends Message<StripeData> {
  /**
   * @generated from field: google.protobuf.Timestamp subscription_end_time = 2;
   */
  subscriptionEndTime?: Timestamp;

  /**
   * @generated from field: bool stale = 3;
   */
  stale = false;

  constructor(data?: PartialMessage<StripeData>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.StripeData";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 2, name: "subscription_end_time", kind: "message", T: Timestamp },
    { no: 3, name: "stale", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): StripeData {
    return new StripeData().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): StripeData {
    return new StripeData().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): StripeData {
    return new StripeData().fromJsonString(jsonString, options);
  }

  static equals(a: StripeData | PlainMessage<StripeData> | undefined, b: StripeData | PlainMessage<StripeData> | undefined): boolean {
    return proto3.util.equals(StripeData, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.BasicOrganization
 */
export class BasicOrganization extends Message<BasicOrganization> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  /**
   * @generated from field: bool billed = 3;
   */
  billed = false;

  /**
   * @generated from field: repeated string emails = 4;
   */
  emails: string[] = [];

  /**
   * @generated from field: bool manually_verified = 6;
   */
  manuallyVerified = false;

  /**
   * @generated from field: uint64 num_instances = 8;
   */
  numInstances = protoInt64.zero;

  /**
   * @generated from field: string plan = 9;
   */
  plan = "";

  /**
   * @generated from field: optional akuity.organization.v1.OrganizationStatus status = 10;
   */
  status?: OrganizationStatus;

  /**
   * @generated from field: akuity.types.features.v1.OrganizationQuota quota = 11;
   */
  quota?: OrganizationQuota;

  /**
   * @generated from field: akuity.types.features.v1.OrganizationUsage usage = 12;
   */
  usage?: OrganizationUsage;

  /**
   * @generated from field: akuity.organization.v1.BillingDetails billing_details = 13;
   */
  billingDetails?: BillingDetails;

  /**
   * @generated from field: akuity.aims.v1.StripeData stripe_data = 14;
   */
  stripeData?: StripeData;

  /**
   * @generated from field: optional google.protobuf.Timestamp creation_timestamp = 15;
   */
  creationTimestamp?: Timestamp;

  /**
   * @generated from field: bool inactive = 16;
   */
  inactive = false;

  /**
   * @generated from field: bool can_delete = 17;
   */
  canDelete = false;

  /**
   * @generated from field: map<string, int32> misc = 18;
   */
  misc: { [key: string]: number } = {};

  constructor(data?: PartialMessage<BasicOrganization>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.BasicOrganization";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "billed", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 4, name: "emails", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 6, name: "manually_verified", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 8, name: "num_instances", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 9, name: "plan", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 10, name: "status", kind: "message", T: OrganizationStatus, opt: true },
    { no: 11, name: "quota", kind: "message", T: OrganizationQuota },
    { no: 12, name: "usage", kind: "message", T: OrganizationUsage },
    { no: 13, name: "billing_details", kind: "message", T: BillingDetails },
    { no: 14, name: "stripe_data", kind: "message", T: StripeData },
    { no: 15, name: "creation_timestamp", kind: "message", T: Timestamp, opt: true },
    { no: 16, name: "inactive", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 17, name: "can_delete", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 18, name: "misc", kind: "map", K: 9 /* ScalarType.STRING */, V: {kind: "scalar", T: 5 /* ScalarType.INT32 */} },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): BasicOrganization {
    return new BasicOrganization().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): BasicOrganization {
    return new BasicOrganization().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): BasicOrganization {
    return new BasicOrganization().fromJsonString(jsonString, options);
  }

  static equals(a: BasicOrganization | PlainMessage<BasicOrganization> | undefined, b: BasicOrganization | PlainMessage<BasicOrganization> | undefined): boolean {
    return proto3.util.equals(BasicOrganization, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.PlanUsage
 */
export class PlanUsage extends Message<PlanUsage> {
  /**
   * @generated from field: int32 control_planes = 1;
   */
  controlPlanes = 0;

  /**
   * @generated from field: int32 applications = 2;
   */
  applications = 0;

  /**
   * @generated from field: int32 clusters = 3;
   */
  clusters = 0;

  /**
   * @generated from field: int32 members = 4;
   */
  members = 0;

  constructor(data?: PartialMessage<PlanUsage>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.PlanUsage";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "control_planes", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 2, name: "applications", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 3, name: "clusters", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
    { no: 4, name: "members", kind: "scalar", T: 5 /* ScalarType.INT32 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PlanUsage {
    return new PlanUsage().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PlanUsage {
    return new PlanUsage().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PlanUsage {
    return new PlanUsage().fromJsonString(jsonString, options);
  }

  static equals(a: PlanUsage | PlainMessage<PlanUsage> | undefined, b: PlanUsage | PlainMessage<PlanUsage> | undefined): boolean {
    return proto3.util.equals(PlanUsage, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.Audit
 */
export class Audit extends Message<Audit> {
  /**
   * @generated from field: string actor = 1;
   */
  actor = "";

  /**
   * @generated from field: string reason = 2;
   */
  reason = "";

  constructor(data?: PartialMessage<Audit>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.Audit";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "actor", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "reason", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Audit {
    return new Audit().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Audit {
    return new Audit().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Audit {
    return new Audit().fromJsonString(jsonString, options);
  }

  static equals(a: Audit | PlainMessage<Audit> | undefined, b: Audit | PlainMessage<Audit> | undefined): boolean {
    return proto3.util.equals(Audit, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.PatchFeatureGatesRequest
 */
export class PatchFeatureGatesRequest extends Message<PatchFeatureGatesRequest> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: akuity.types.features.v1.OrganizationFeatureGates feature_gates = 2;
   */
  featureGates?: OrganizationFeatureGates;

  /**
   * @generated from field: akuity.aims.v1.Audit audit = 3;
   */
  audit?: Audit;

  constructor(data?: PartialMessage<PatchFeatureGatesRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.PatchFeatureGatesRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "feature_gates", kind: "message", T: OrganizationFeatureGates },
    { no: 3, name: "audit", kind: "message", T: Audit },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PatchFeatureGatesRequest {
    return new PatchFeatureGatesRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PatchFeatureGatesRequest {
    return new PatchFeatureGatesRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PatchFeatureGatesRequest {
    return new PatchFeatureGatesRequest().fromJsonString(jsonString, options);
  }

  static equals(a: PatchFeatureGatesRequest | PlainMessage<PatchFeatureGatesRequest> | undefined, b: PatchFeatureGatesRequest | PlainMessage<PatchFeatureGatesRequest> | undefined): boolean {
    return proto3.util.equals(PatchFeatureGatesRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.PatchFeatureGatesResponse
 */
export class PatchFeatureGatesResponse extends Message<PatchFeatureGatesResponse> {
  /**
   * @generated from field: akuity.types.features.v1.OrganizationFeatureGates feature_gates = 1;
   */
  featureGates?: OrganizationFeatureGates;

  constructor(data?: PartialMessage<PatchFeatureGatesResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.PatchFeatureGatesResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "feature_gates", kind: "message", T: OrganizationFeatureGates },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): PatchFeatureGatesResponse {
    return new PatchFeatureGatesResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): PatchFeatureGatesResponse {
    return new PatchFeatureGatesResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): PatchFeatureGatesResponse {
    return new PatchFeatureGatesResponse().fromJsonString(jsonString, options);
  }

  static equals(a: PatchFeatureGatesResponse | PlainMessage<PatchFeatureGatesResponse> | undefined, b: PatchFeatureGatesResponse | PlainMessage<PatchFeatureGatesResponse> | undefined): boolean {
    return proto3.util.equals(PatchFeatureGatesResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetFeatureGatesRequest
 */
export class GetFeatureGatesRequest extends Message<GetFeatureGatesRequest> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  constructor(data?: PartialMessage<GetFeatureGatesRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetFeatureGatesRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetFeatureGatesRequest {
    return new GetFeatureGatesRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetFeatureGatesRequest {
    return new GetFeatureGatesRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetFeatureGatesRequest {
    return new GetFeatureGatesRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetFeatureGatesRequest | PlainMessage<GetFeatureGatesRequest> | undefined, b: GetFeatureGatesRequest | PlainMessage<GetFeatureGatesRequest> | undefined): boolean {
    return proto3.util.equals(GetFeatureGatesRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetFeatureGatesResponse
 */
export class GetFeatureGatesResponse extends Message<GetFeatureGatesResponse> {
  /**
   * @generated from field: akuity.types.features.v1.OrganizationFeatureGates feature_gates = 1;
   */
  featureGates?: OrganizationFeatureGates;

  /**
   * @generated from field: akuity.types.features.v1.SystemFeatureGates system_feature_gates = 2;
   */
  systemFeatureGates?: SystemFeatureGates;

  constructor(data?: PartialMessage<GetFeatureGatesResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetFeatureGatesResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "feature_gates", kind: "message", T: OrganizationFeatureGates },
    { no: 2, name: "system_feature_gates", kind: "message", T: SystemFeatureGates },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetFeatureGatesResponse {
    return new GetFeatureGatesResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetFeatureGatesResponse {
    return new GetFeatureGatesResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetFeatureGatesResponse {
    return new GetFeatureGatesResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetFeatureGatesResponse | PlainMessage<GetFeatureGatesResponse> | undefined, b: GetFeatureGatesResponse | PlainMessage<GetFeatureGatesResponse> | undefined): boolean {
    return proto3.util.equals(GetFeatureGatesResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.UpdateQuotasRequest
 */
export class UpdateQuotasRequest extends Message<UpdateQuotasRequest> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: akuity.aims.v1.Audit audit = 2;
   */
  audit?: Audit;

  /**
   * @generated from field: akuity.types.features.v1.OrganizationQuota quota = 3;
   */
  quota?: OrganizationQuota;

  constructor(data?: PartialMessage<UpdateQuotasRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.UpdateQuotasRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "audit", kind: "message", T: Audit },
    { no: 3, name: "quota", kind: "message", T: OrganizationQuota },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateQuotasRequest {
    return new UpdateQuotasRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateQuotasRequest {
    return new UpdateQuotasRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateQuotasRequest {
    return new UpdateQuotasRequest().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateQuotasRequest | PlainMessage<UpdateQuotasRequest> | undefined, b: UpdateQuotasRequest | PlainMessage<UpdateQuotasRequest> | undefined): boolean {
    return proto3.util.equals(UpdateQuotasRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.UpdateQuotasResponse
 */
export class UpdateQuotasResponse extends Message<UpdateQuotasResponse> {
  /**
   * @generated from field: akuity.types.features.v1.OrganizationQuota quota = 1;
   */
  quota?: OrganizationQuota;

  /**
   * @generated from field: optional google.protobuf.Timestamp creation_timestamp = 9;
   */
  creationTimestamp?: Timestamp;

  constructor(data?: PartialMessage<UpdateQuotasResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.UpdateQuotasResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "quota", kind: "message", T: OrganizationQuota },
    { no: 9, name: "creation_timestamp", kind: "message", T: Timestamp, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateQuotasResponse {
    return new UpdateQuotasResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateQuotasResponse {
    return new UpdateQuotasResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateQuotasResponse {
    return new UpdateQuotasResponse().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateQuotasResponse | PlainMessage<UpdateQuotasResponse> | undefined, b: UpdateQuotasResponse | PlainMessage<UpdateQuotasResponse> | undefined): boolean {
    return proto3.util.equals(UpdateQuotasResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.UpdateOrganizationTrialExpirationRequest
 */
export class UpdateOrganizationTrialExpirationRequest extends Message<UpdateOrganizationTrialExpirationRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: uint64 trial_expiration = 2;
   */
  trialExpiration = protoInt64.zero;

  /**
   * @generated from field: akuity.aims.v1.Audit audit = 3;
   */
  audit?: Audit;

  constructor(data?: PartialMessage<UpdateOrganizationTrialExpirationRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.UpdateOrganizationTrialExpirationRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "trial_expiration", kind: "scalar", T: 4 /* ScalarType.UINT64 */ },
    { no: 3, name: "audit", kind: "message", T: Audit },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateOrganizationTrialExpirationRequest {
    return new UpdateOrganizationTrialExpirationRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateOrganizationTrialExpirationRequest {
    return new UpdateOrganizationTrialExpirationRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateOrganizationTrialExpirationRequest {
    return new UpdateOrganizationTrialExpirationRequest().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateOrganizationTrialExpirationRequest | PlainMessage<UpdateOrganizationTrialExpirationRequest> | undefined, b: UpdateOrganizationTrialExpirationRequest | PlainMessage<UpdateOrganizationTrialExpirationRequest> | undefined): boolean {
    return proto3.util.equals(UpdateOrganizationTrialExpirationRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.aims.v1.UpdateOrganizationTrialExpirationResponse
 */
export class UpdateOrganizationTrialExpirationResponse extends Message<UpdateOrganizationTrialExpirationResponse> {
  constructor(data?: PartialMessage<UpdateOrganizationTrialExpirationResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.UpdateOrganizationTrialExpirationResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateOrganizationTrialExpirationResponse {
    return new UpdateOrganizationTrialExpirationResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateOrganizationTrialExpirationResponse {
    return new UpdateOrganizationTrialExpirationResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateOrganizationTrialExpirationResponse {
    return new UpdateOrganizationTrialExpirationResponse().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateOrganizationTrialExpirationResponse | PlainMessage<UpdateOrganizationTrialExpirationResponse> | undefined, b: UpdateOrganizationTrialExpirationResponse | PlainMessage<UpdateOrganizationTrialExpirationResponse> | undefined): boolean {
    return proto3.util.equals(UpdateOrganizationTrialExpirationResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.DecrementInstanceGenerationRequest
 */
export class DecrementInstanceGenerationRequest extends Message<DecrementInstanceGenerationRequest> {
  /**
   * @generated from field: string instance_id = 1;
   */
  instanceId = "";

  /**
   * @generated from field: akuity.aims.v1.Audit audit = 2;
   */
  audit?: Audit;

  constructor(data?: PartialMessage<DecrementInstanceGenerationRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.DecrementInstanceGenerationRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "audit", kind: "message", T: Audit },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DecrementInstanceGenerationRequest {
    return new DecrementInstanceGenerationRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DecrementInstanceGenerationRequest {
    return new DecrementInstanceGenerationRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DecrementInstanceGenerationRequest {
    return new DecrementInstanceGenerationRequest().fromJsonString(jsonString, options);
  }

  static equals(a: DecrementInstanceGenerationRequest | PlainMessage<DecrementInstanceGenerationRequest> | undefined, b: DecrementInstanceGenerationRequest | PlainMessage<DecrementInstanceGenerationRequest> | undefined): boolean {
    return proto3.util.equals(DecrementInstanceGenerationRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.aims.v1.DecrementInstanceGenerationResponse
 */
export class DecrementInstanceGenerationResponse extends Message<DecrementInstanceGenerationResponse> {
  constructor(data?: PartialMessage<DecrementInstanceGenerationResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.DecrementInstanceGenerationResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DecrementInstanceGenerationResponse {
    return new DecrementInstanceGenerationResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DecrementInstanceGenerationResponse {
    return new DecrementInstanceGenerationResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DecrementInstanceGenerationResponse {
    return new DecrementInstanceGenerationResponse().fromJsonString(jsonString, options);
  }

  static equals(a: DecrementInstanceGenerationResponse | PlainMessage<DecrementInstanceGenerationResponse> | undefined, b: DecrementInstanceGenerationResponse | PlainMessage<DecrementInstanceGenerationResponse> | undefined): boolean {
    return proto3.util.equals(DecrementInstanceGenerationResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetInstanceByIdRequest
 */
export class GetInstanceByIdRequest extends Message<GetInstanceByIdRequest> {
  /**
   * @generated from field: string instance_id = 1;
   */
  instanceId = "";

  constructor(data?: PartialMessage<GetInstanceByIdRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetInstanceByIdRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetInstanceByIdRequest {
    return new GetInstanceByIdRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetInstanceByIdRequest {
    return new GetInstanceByIdRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetInstanceByIdRequest {
    return new GetInstanceByIdRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetInstanceByIdRequest | PlainMessage<GetInstanceByIdRequest> | undefined, b: GetInstanceByIdRequest | PlainMessage<GetInstanceByIdRequest> | undefined): boolean {
    return proto3.util.equals(GetInstanceByIdRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetInstanceByIdResponse
 */
export class GetInstanceByIdResponse extends Message<GetInstanceByIdResponse> {
  /**
   * @generated from field: akuity.aims.v1.InternalInstance instance = 1;
   */
  instance?: InternalInstance;

  constructor(data?: PartialMessage<GetInstanceByIdResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetInstanceByIdResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instance", kind: "message", T: InternalInstance },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetInstanceByIdResponse {
    return new GetInstanceByIdResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetInstanceByIdResponse {
    return new GetInstanceByIdResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetInstanceByIdResponse {
    return new GetInstanceByIdResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetInstanceByIdResponse | PlainMessage<GetInstanceByIdResponse> | undefined, b: GetInstanceByIdResponse | PlainMessage<GetInstanceByIdResponse> | undefined): boolean {
    return proto3.util.equals(GetInstanceByIdResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetKargoInstanceByIdRequest
 */
export class GetKargoInstanceByIdRequest extends Message<GetKargoInstanceByIdRequest> {
  /**
   * @generated from field: string instance_id = 1;
   */
  instanceId = "";

  constructor(data?: PartialMessage<GetKargoInstanceByIdRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetKargoInstanceByIdRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetKargoInstanceByIdRequest {
    return new GetKargoInstanceByIdRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetKargoInstanceByIdRequest {
    return new GetKargoInstanceByIdRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetKargoInstanceByIdRequest {
    return new GetKargoInstanceByIdRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetKargoInstanceByIdRequest | PlainMessage<GetKargoInstanceByIdRequest> | undefined, b: GetKargoInstanceByIdRequest | PlainMessage<GetKargoInstanceByIdRequest> | undefined): boolean {
    return proto3.util.equals(GetKargoInstanceByIdRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetKargoInstanceByIdResponse
 */
export class GetKargoInstanceByIdResponse extends Message<GetKargoInstanceByIdResponse> {
  /**
   * @generated from field: akuity.aims.v1.InternalKargoInstance instance = 1;
   */
  instance?: InternalKargoInstance;

  constructor(data?: PartialMessage<GetKargoInstanceByIdResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetKargoInstanceByIdResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instance", kind: "message", T: InternalKargoInstance },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetKargoInstanceByIdResponse {
    return new GetKargoInstanceByIdResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetKargoInstanceByIdResponse {
    return new GetKargoInstanceByIdResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetKargoInstanceByIdResponse {
    return new GetKargoInstanceByIdResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetKargoInstanceByIdResponse | PlainMessage<GetKargoInstanceByIdResponse> | undefined, b: GetKargoInstanceByIdResponse | PlainMessage<GetKargoInstanceByIdResponse> | undefined): boolean {
    return proto3.util.equals(GetKargoInstanceByIdResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ClusterFilter
 */
export class ClusterFilter extends Message<ClusterFilter> {
  /**
   * search by name/id/namespace
   *
   * @generated from field: string fuzz = 1;
   */
  fuzz = "";

  /**
   * @generated from field: optional string time_from = 3;
   */
  timeFrom?: string;

  constructor(data?: PartialMessage<ClusterFilter>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ClusterFilter";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "fuzz", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "time_from", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ClusterFilter {
    return new ClusterFilter().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ClusterFilter {
    return new ClusterFilter().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ClusterFilter {
    return new ClusterFilter().fromJsonString(jsonString, options);
  }

  static equals(a: ClusterFilter | PlainMessage<ClusterFilter> | undefined, b: ClusterFilter | PlainMessage<ClusterFilter> | undefined): boolean {
    return proto3.util.equals(ClusterFilter, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListClustersForInstanceRequest
 */
export class ListClustersForInstanceRequest extends Message<ListClustersForInstanceRequest> {
  /**
   * @generated from field: string instance_id = 1;
   */
  instanceId = "";

  /**
   * @generated from field: akuity.aims.v1.ClusterFilter filter = 2;
   */
  filter?: ClusterFilter;

  constructor(data?: PartialMessage<ListClustersForInstanceRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListClustersForInstanceRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "filter", kind: "message", T: ClusterFilter },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListClustersForInstanceRequest {
    return new ListClustersForInstanceRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListClustersForInstanceRequest {
    return new ListClustersForInstanceRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListClustersForInstanceRequest {
    return new ListClustersForInstanceRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListClustersForInstanceRequest | PlainMessage<ListClustersForInstanceRequest> | undefined, b: ListClustersForInstanceRequest | PlainMessage<ListClustersForInstanceRequest> | undefined): boolean {
    return proto3.util.equals(ListClustersForInstanceRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListClustersForInstanceResponse
 */
export class ListClustersForInstanceResponse extends Message<ListClustersForInstanceResponse> {
  /**
   * @generated from field: repeated akuity.argocd.v1.Cluster clusters = 1;
   */
  clusters: Cluster[] = [];

  constructor(data?: PartialMessage<ListClustersForInstanceResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListClustersForInstanceResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "clusters", kind: "message", T: Cluster, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListClustersForInstanceResponse {
    return new ListClustersForInstanceResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListClustersForInstanceResponse {
    return new ListClustersForInstanceResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListClustersForInstanceResponse {
    return new ListClustersForInstanceResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListClustersForInstanceResponse | PlainMessage<ListClustersForInstanceResponse> | undefined, b: ListClustersForInstanceResponse | PlainMessage<ListClustersForInstanceResponse> | undefined): boolean {
    return proto3.util.equals(ListClustersForInstanceResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListAgentsForKargoInstanceRequest
 */
export class ListAgentsForKargoInstanceRequest extends Message<ListAgentsForKargoInstanceRequest> {
  /**
   * @generated from field: string instance_id = 1;
   */
  instanceId = "";

  constructor(data?: PartialMessage<ListAgentsForKargoInstanceRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListAgentsForKargoInstanceRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAgentsForKargoInstanceRequest {
    return new ListAgentsForKargoInstanceRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAgentsForKargoInstanceRequest {
    return new ListAgentsForKargoInstanceRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAgentsForKargoInstanceRequest {
    return new ListAgentsForKargoInstanceRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListAgentsForKargoInstanceRequest | PlainMessage<ListAgentsForKargoInstanceRequest> | undefined, b: ListAgentsForKargoInstanceRequest | PlainMessage<ListAgentsForKargoInstanceRequest> | undefined): boolean {
    return proto3.util.equals(ListAgentsForKargoInstanceRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListAgentsForKargoInstanceResponse
 */
export class ListAgentsForKargoInstanceResponse extends Message<ListAgentsForKargoInstanceResponse> {
  /**
   * @generated from field: repeated akuity.kargo.v1.KargoAgent agents = 1;
   */
  agents: KargoAgent[] = [];

  constructor(data?: PartialMessage<ListAgentsForKargoInstanceResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListAgentsForKargoInstanceResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "agents", kind: "message", T: KargoAgent, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAgentsForKargoInstanceResponse {
    return new ListAgentsForKargoInstanceResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAgentsForKargoInstanceResponse {
    return new ListAgentsForKargoInstanceResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAgentsForKargoInstanceResponse {
    return new ListAgentsForKargoInstanceResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListAgentsForKargoInstanceResponse | PlainMessage<ListAgentsForKargoInstanceResponse> | undefined, b: ListAgentsForKargoInstanceResponse | PlainMessage<ListAgentsForKargoInstanceResponse> | undefined): boolean {
    return proto3.util.equals(ListAgentsForKargoInstanceResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.SetManuallyVerifiedRequest
 */
export class SetManuallyVerifiedRequest extends Message<SetManuallyVerifiedRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: bool verified = 2;
   */
  verified = false;

  /**
   * @generated from field: akuity.aims.v1.Audit audit = 3;
   */
  audit?: Audit;

  constructor(data?: PartialMessage<SetManuallyVerifiedRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.SetManuallyVerifiedRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "verified", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 3, name: "audit", kind: "message", T: Audit },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SetManuallyVerifiedRequest {
    return new SetManuallyVerifiedRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SetManuallyVerifiedRequest {
    return new SetManuallyVerifiedRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SetManuallyVerifiedRequest {
    return new SetManuallyVerifiedRequest().fromJsonString(jsonString, options);
  }

  static equals(a: SetManuallyVerifiedRequest | PlainMessage<SetManuallyVerifiedRequest> | undefined, b: SetManuallyVerifiedRequest | PlainMessage<SetManuallyVerifiedRequest> | undefined): boolean {
    return proto3.util.equals(SetManuallyVerifiedRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.aims.v1.SetManuallyVerifiedResponse
 */
export class SetManuallyVerifiedResponse extends Message<SetManuallyVerifiedResponse> {
  constructor(data?: PartialMessage<SetManuallyVerifiedResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.SetManuallyVerifiedResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SetManuallyVerifiedResponse {
    return new SetManuallyVerifiedResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SetManuallyVerifiedResponse {
    return new SetManuallyVerifiedResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SetManuallyVerifiedResponse {
    return new SetManuallyVerifiedResponse().fromJsonString(jsonString, options);
  }

  static equals(a: SetManuallyVerifiedResponse | PlainMessage<SetManuallyVerifiedResponse> | undefined, b: SetManuallyVerifiedResponse | PlainMessage<SetManuallyVerifiedResponse> | undefined): boolean {
    return proto3.util.equals(SetManuallyVerifiedResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.SetDisabledInstanceCreationRequest
 */
export class SetDisabledInstanceCreationRequest extends Message<SetDisabledInstanceCreationRequest> {
  /**
   * @generated from field: bool disabled = 1;
   */
  disabled = false;

  /**
   * @generated from field: akuity.aims.v1.Audit audit = 2;
   */
  audit?: Audit;

  constructor(data?: PartialMessage<SetDisabledInstanceCreationRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.SetDisabledInstanceCreationRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "disabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "audit", kind: "message", T: Audit },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SetDisabledInstanceCreationRequest {
    return new SetDisabledInstanceCreationRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SetDisabledInstanceCreationRequest {
    return new SetDisabledInstanceCreationRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SetDisabledInstanceCreationRequest {
    return new SetDisabledInstanceCreationRequest().fromJsonString(jsonString, options);
  }

  static equals(a: SetDisabledInstanceCreationRequest | PlainMessage<SetDisabledInstanceCreationRequest> | undefined, b: SetDisabledInstanceCreationRequest | PlainMessage<SetDisabledInstanceCreationRequest> | undefined): boolean {
    return proto3.util.equals(SetDisabledInstanceCreationRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.aims.v1.SetDisabledInstanceCreationResponse
 */
export class SetDisabledInstanceCreationResponse extends Message<SetDisabledInstanceCreationResponse> {
  constructor(data?: PartialMessage<SetDisabledInstanceCreationResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.SetDisabledInstanceCreationResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): SetDisabledInstanceCreationResponse {
    return new SetDisabledInstanceCreationResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): SetDisabledInstanceCreationResponse {
    return new SetDisabledInstanceCreationResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): SetDisabledInstanceCreationResponse {
    return new SetDisabledInstanceCreationResponse().fromJsonString(jsonString, options);
  }

  static equals(a: SetDisabledInstanceCreationResponse | PlainMessage<SetDisabledInstanceCreationResponse> | undefined, b: SetDisabledInstanceCreationResponse | PlainMessage<SetDisabledInstanceCreationResponse> | undefined): boolean {
    return proto3.util.equals(SetDisabledInstanceCreationResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.DeleteOrganizationRequest
 */
export class DeleteOrganizationRequest extends Message<DeleteOrganizationRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: akuity.aims.v1.Audit audit = 2;
   */
  audit?: Audit;

  constructor(data?: PartialMessage<DeleteOrganizationRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.DeleteOrganizationRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "audit", kind: "message", T: Audit },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteOrganizationRequest {
    return new DeleteOrganizationRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteOrganizationRequest {
    return new DeleteOrganizationRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteOrganizationRequest {
    return new DeleteOrganizationRequest().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteOrganizationRequest | PlainMessage<DeleteOrganizationRequest> | undefined, b: DeleteOrganizationRequest | PlainMessage<DeleteOrganizationRequest> | undefined): boolean {
    return proto3.util.equals(DeleteOrganizationRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.aims.v1.DeleteOrganizationResponse
 */
export class DeleteOrganizationResponse extends Message<DeleteOrganizationResponse> {
  constructor(data?: PartialMessage<DeleteOrganizationResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.DeleteOrganizationResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteOrganizationResponse {
    return new DeleteOrganizationResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteOrganizationResponse {
    return new DeleteOrganizationResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteOrganizationResponse {
    return new DeleteOrganizationResponse().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteOrganizationResponse | PlainMessage<DeleteOrganizationResponse> | undefined, b: DeleteOrganizationResponse | PlainMessage<DeleteOrganizationResponse> | undefined): boolean {
    return proto3.util.equals(DeleteOrganizationResponse, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.aims.v1.GetInternalConfigRequest
 */
export class GetInternalConfigRequest extends Message<GetInternalConfigRequest> {
  constructor(data?: PartialMessage<GetInternalConfigRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetInternalConfigRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetInternalConfigRequest {
    return new GetInternalConfigRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetInternalConfigRequest {
    return new GetInternalConfigRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetInternalConfigRequest {
    return new GetInternalConfigRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetInternalConfigRequest | PlainMessage<GetInternalConfigRequest> | undefined, b: GetInternalConfigRequest | PlainMessage<GetInternalConfigRequest> | undefined): boolean {
    return proto3.util.equals(GetInternalConfigRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.InternalConfig
 */
export class InternalConfig extends Message<InternalConfig> {
  /**
   * @generated from field: bool disable_free_instance_creation = 1;
   */
  disableFreeInstanceCreation = false;

  constructor(data?: PartialMessage<InternalConfig>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.InternalConfig";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "disable_free_instance_creation", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InternalConfig {
    return new InternalConfig().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InternalConfig {
    return new InternalConfig().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InternalConfig {
    return new InternalConfig().fromJsonString(jsonString, options);
  }

  static equals(a: InternalConfig | PlainMessage<InternalConfig> | undefined, b: InternalConfig | PlainMessage<InternalConfig> | undefined): boolean {
    return proto3.util.equals(InternalConfig, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetInternalConfigResponse
 */
export class GetInternalConfigResponse extends Message<GetInternalConfigResponse> {
  /**
   * @generated from field: akuity.aims.v1.InternalConfig config = 1;
   */
  config?: InternalConfig;

  constructor(data?: PartialMessage<GetInternalConfigResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetInternalConfigResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "config", kind: "message", T: InternalConfig },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetInternalConfigResponse {
    return new GetInternalConfigResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetInternalConfigResponse {
    return new GetInternalConfigResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetInternalConfigResponse {
    return new GetInternalConfigResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetInternalConfigResponse | PlainMessage<GetInternalConfigResponse> | undefined, b: GetInternalConfigResponse | PlainMessage<GetInternalConfigResponse> | undefined): boolean {
    return proto3.util.equals(GetInternalConfigResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.InternalKargoInstance
 */
export class InternalKargoInstance extends Message<InternalKargoInstance> {
  /**
   * @generated from field: akuity.kargo.v1.KargoInstance instance = 1;
   */
  instance?: KargoInstance;

  /**
   * only basic fields of BasicOrganization
   *
   * @generated from field: akuity.aims.v1.BasicOrganization organization = 2;
   */
  organization?: BasicOrganization;

  /**
   * @generated from field: optional google.protobuf.Timestamp creation_timestamp = 3;
   */
  creationTimestamp?: Timestamp;

  /**
   * @generated from field: optional akuity.aims.v1.Workspace workspace = 4;
   */
  workspace?: Workspace;

  /**
   * @generated from field: optional google.protobuf.Struct status_info = 5;
   */
  statusInfo?: Struct;

  /**
   * Additional configuration fields
   *
   * @generated from field: repeated google.protobuf.Struct promotions = 6;
   */
  promotions: Struct[] = [];

  /**
   * @generated from field: optional google.protobuf.Struct oidc_config = 7;
   */
  oidcConfig?: Struct;

  /**
   * @generated from field: optional google.protobuf.Struct controller_config = 8;
   */
  controllerConfig?: Struct;

  /**
   * @generated from field: optional google.protobuf.Struct webhook_config = 9;
   */
  webhookConfig?: Struct;

  /**
   * @generated from field: optional google.protobuf.Struct instance_specs = 10;
   */
  instanceSpecs?: Struct;

  /**
   * @generated from field: optional google.protobuf.Struct api_cm = 11;
   */
  apiCm?: Struct;

  /**
   * @generated from field: optional google.protobuf.Struct api_secret = 12;
   */
  apiSecret?: Struct;

  /**
   * @generated from field: optional google.protobuf.Struct miscellaneous_secrets = 13;
   */
  miscellaneousSecrets?: Struct;

  /**
   * @generated from field: optional google.protobuf.Struct certificate_status = 14;
   */
  certificateStatus?: Struct;

  constructor(data?: PartialMessage<InternalKargoInstance>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.InternalKargoInstance";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instance", kind: "message", T: KargoInstance },
    { no: 2, name: "organization", kind: "message", T: BasicOrganization },
    { no: 3, name: "creation_timestamp", kind: "message", T: Timestamp, opt: true },
    { no: 4, name: "workspace", kind: "message", T: Workspace, opt: true },
    { no: 5, name: "status_info", kind: "message", T: Struct, opt: true },
    { no: 6, name: "promotions", kind: "message", T: Struct, repeated: true },
    { no: 7, name: "oidc_config", kind: "message", T: Struct, opt: true },
    { no: 8, name: "controller_config", kind: "message", T: Struct, opt: true },
    { no: 9, name: "webhook_config", kind: "message", T: Struct, opt: true },
    { no: 10, name: "instance_specs", kind: "message", T: Struct, opt: true },
    { no: 11, name: "api_cm", kind: "message", T: Struct, opt: true },
    { no: 12, name: "api_secret", kind: "message", T: Struct, opt: true },
    { no: 13, name: "miscellaneous_secrets", kind: "message", T: Struct, opt: true },
    { no: 14, name: "certificate_status", kind: "message", T: Struct, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InternalKargoInstance {
    return new InternalKargoInstance().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InternalKargoInstance {
    return new InternalKargoInstance().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InternalKargoInstance {
    return new InternalKargoInstance().fromJsonString(jsonString, options);
  }

  static equals(a: InternalKargoInstance | PlainMessage<InternalKargoInstance> | undefined, b: InternalKargoInstance | PlainMessage<InternalKargoInstance> | undefined): boolean {
    return proto3.util.equals(InternalKargoInstance, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.KargoInstanceFilter
 */
export class KargoInstanceFilter extends Message<KargoInstanceFilter> {
  /**
   * @generated from field: optional bool paid = 1;
   */
  paid?: boolean;

  /**
   * @generated from field: optional bool unpaid = 2;
   */
  unpaid?: boolean;

  /**
   * search by instance name/id
   *
   * @generated from field: string fuzz = 3;
   */
  fuzz = "";

  /**
   * @generated from field: optional string time_from = 4;
   */
  timeFrom?: string;

  /**
   * filter by organization ID
   *
   * @generated from field: optional string organization_id = 5;
   */
  organizationId?: string;

  constructor(data?: PartialMessage<KargoInstanceFilter>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.KargoInstanceFilter";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "paid", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 2, name: "unpaid", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 3, name: "fuzz", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "time_from", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 5, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): KargoInstanceFilter {
    return new KargoInstanceFilter().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): KargoInstanceFilter {
    return new KargoInstanceFilter().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): KargoInstanceFilter {
    return new KargoInstanceFilter().fromJsonString(jsonString, options);
  }

  static equals(a: KargoInstanceFilter | PlainMessage<KargoInstanceFilter> | undefined, b: KargoInstanceFilter | PlainMessage<KargoInstanceFilter> | undefined): boolean {
    return proto3.util.equals(KargoInstanceFilter, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListKargoInstancesRequest
 */
export class ListKargoInstancesRequest extends Message<ListKargoInstancesRequest> {
  /**
   * @generated from field: optional akuity.aims.v1.KargoInstanceFilter filter = 1;
   */
  filter?: KargoInstanceFilter;

  constructor(data?: PartialMessage<ListKargoInstancesRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListKargoInstancesRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "filter", kind: "message", T: KargoInstanceFilter, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListKargoInstancesRequest {
    return new ListKargoInstancesRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListKargoInstancesRequest {
    return new ListKargoInstancesRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListKargoInstancesRequest {
    return new ListKargoInstancesRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListKargoInstancesRequest | PlainMessage<ListKargoInstancesRequest> | undefined, b: ListKargoInstancesRequest | PlainMessage<ListKargoInstancesRequest> | undefined): boolean {
    return proto3.util.equals(ListKargoInstancesRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListKargoInstancesResponse
 */
export class ListKargoInstancesResponse extends Message<ListKargoInstancesResponse> {
  /**
   * @generated from field: repeated akuity.aims.v1.InternalKargoInstance instances = 1;
   */
  instances: InternalKargoInstance[] = [];

  constructor(data?: PartialMessage<ListKargoInstancesResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListKargoInstancesResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instances", kind: "message", T: InternalKargoInstance, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListKargoInstancesResponse {
    return new ListKargoInstancesResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListKargoInstancesResponse {
    return new ListKargoInstancesResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListKargoInstancesResponse {
    return new ListKargoInstancesResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListKargoInstancesResponse | PlainMessage<ListKargoInstancesResponse> | undefined, b: ListKargoInstancesResponse | PlainMessage<ListKargoInstancesResponse> | undefined): boolean {
    return proto3.util.equals(ListKargoInstancesResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.InstanceClusterMaintenanceRequest
 */
export class InstanceClusterMaintenanceRequest extends Message<InstanceClusterMaintenanceRequest> {
  /**
   * @generated from field: string instance_id = 1;
   */
  instanceId = "";

  /**
   * @generated from field: string cluster_id = 2;
   */
  clusterId = "";

  /**
   * @generated from field: bool maintanence_mode = 3;
   */
  maintanenceMode = false;

  /**
   * @generated from field: akuity.aims.v1.Audit audit = 4;
   */
  audit?: Audit;

  constructor(data?: PartialMessage<InstanceClusterMaintenanceRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.InstanceClusterMaintenanceRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "cluster_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "maintanence_mode", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 4, name: "audit", kind: "message", T: Audit },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InstanceClusterMaintenanceRequest {
    return new InstanceClusterMaintenanceRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InstanceClusterMaintenanceRequest {
    return new InstanceClusterMaintenanceRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InstanceClusterMaintenanceRequest {
    return new InstanceClusterMaintenanceRequest().fromJsonString(jsonString, options);
  }

  static equals(a: InstanceClusterMaintenanceRequest | PlainMessage<InstanceClusterMaintenanceRequest> | undefined, b: InstanceClusterMaintenanceRequest | PlainMessage<InstanceClusterMaintenanceRequest> | undefined): boolean {
    return proto3.util.equals(InstanceClusterMaintenanceRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.aims.v1.InstanceClusterMaintenanceResponse
 */
export class InstanceClusterMaintenanceResponse extends Message<InstanceClusterMaintenanceResponse> {
  constructor(data?: PartialMessage<InstanceClusterMaintenanceResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.InstanceClusterMaintenanceResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InstanceClusterMaintenanceResponse {
    return new InstanceClusterMaintenanceResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InstanceClusterMaintenanceResponse {
    return new InstanceClusterMaintenanceResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InstanceClusterMaintenanceResponse {
    return new InstanceClusterMaintenanceResponse().fromJsonString(jsonString, options);
  }

  static equals(a: InstanceClusterMaintenanceResponse | PlainMessage<InstanceClusterMaintenanceResponse> | undefined, b: InstanceClusterMaintenanceResponse | PlainMessage<InstanceClusterMaintenanceResponse> | undefined): boolean {
    return proto3.util.equals(InstanceClusterMaintenanceResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListAuditLogsRequest
 */
export class ListAuditLogsRequest extends Message<ListAuditLogsRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: optional akuity.organization.v1.AuditFilters filters = 2;
   */
  filters?: AuditFilters;

  constructor(data?: PartialMessage<ListAuditLogsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListAuditLogsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "filters", kind: "message", T: AuditFilters, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAuditLogsRequest {
    return new ListAuditLogsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAuditLogsRequest {
    return new ListAuditLogsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAuditLogsRequest {
    return new ListAuditLogsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListAuditLogsRequest | PlainMessage<ListAuditLogsRequest> | undefined, b: ListAuditLogsRequest | PlainMessage<ListAuditLogsRequest> | undefined): boolean {
    return proto3.util.equals(ListAuditLogsRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListAuditLogsResponse
 */
export class ListAuditLogsResponse extends Message<ListAuditLogsResponse> {
  /**
   * @generated from field: repeated akuity.organization.v1.AuditLog audit_logs = 1;
   */
  auditLogs: AuditLog$1[] = [];

  /**
   * @generated from field: uint32 count = 2;
   */
  count = 0;

  constructor(data?: PartialMessage<ListAuditLogsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListAuditLogsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "audit_logs", kind: "message", T: AuditLog$1, repeated: true },
    { no: 2, name: "count", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAuditLogsResponse {
    return new ListAuditLogsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAuditLogsResponse {
    return new ListAuditLogsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAuditLogsResponse {
    return new ListAuditLogsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListAuditLogsResponse | PlainMessage<ListAuditLogsResponse> | undefined, b: ListAuditLogsResponse | PlainMessage<ListAuditLogsResponse> | undefined): boolean {
    return proto3.util.equals(ListAuditLogsResponse, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.aims.v1.ListAvailablePlansRequest
 */
export class ListAvailablePlansRequest extends Message<ListAvailablePlansRequest> {
  constructor(data?: PartialMessage<ListAvailablePlansRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListAvailablePlansRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAvailablePlansRequest {
    return new ListAvailablePlansRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAvailablePlansRequest {
    return new ListAvailablePlansRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAvailablePlansRequest {
    return new ListAvailablePlansRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListAvailablePlansRequest | PlainMessage<ListAvailablePlansRequest> | undefined, b: ListAvailablePlansRequest | PlainMessage<ListAvailablePlansRequest> | undefined): boolean {
    return proto3.util.equals(ListAvailablePlansRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.Plan
 */
export class Plan extends Message<Plan> {
  /**
   * @generated from field: string name = 1;
   */
  name = "";

  /**
   * @generated from field: string product_id = 2;
   */
  productId = "";

  /**
   * @generated from field: google.protobuf.Struct features = 3;
   */
  features?: Struct;

  /**
   * @generated from field: google.protobuf.Struct quotas = 4;
   */
  quotas?: Struct;

  /**
   * @generated from field: bool default = 5;
   */
  default = false;

  constructor(data?: PartialMessage<Plan>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.Plan";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "product_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "features", kind: "message", T: Struct },
    { no: 4, name: "quotas", kind: "message", T: Struct },
    { no: 5, name: "default", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Plan {
    return new Plan().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Plan {
    return new Plan().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Plan {
    return new Plan().fromJsonString(jsonString, options);
  }

  static equals(a: Plan | PlainMessage<Plan> | undefined, b: Plan | PlainMessage<Plan> | undefined): boolean {
    return proto3.util.equals(Plan, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListAvailablePlansResponse
 */
export class ListAvailablePlansResponse extends Message<ListAvailablePlansResponse> {
  /**
   * @generated from field: repeated akuity.aims.v1.Plan plans = 1;
   */
  plans: Plan[] = [];

  constructor(data?: PartialMessage<ListAvailablePlansResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListAvailablePlansResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "plans", kind: "message", T: Plan, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListAvailablePlansResponse {
    return new ListAvailablePlansResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListAvailablePlansResponse {
    return new ListAvailablePlansResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListAvailablePlansResponse {
    return new ListAvailablePlansResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListAvailablePlansResponse | PlainMessage<ListAvailablePlansResponse> | undefined, b: ListAvailablePlansResponse | PlainMessage<ListAvailablePlansResponse> | undefined): boolean {
    return proto3.util.equals(ListAvailablePlansResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.UpdateOrganizationBillingPlanRequest
 */
export class UpdateOrganizationBillingPlanRequest extends Message<UpdateOrganizationBillingPlanRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string plan = 2;
   */
  plan = "";

  /**
   * @generated from field: akuity.aims.v1.Audit audit = 3;
   */
  audit?: Audit;

  constructor(data?: PartialMessage<UpdateOrganizationBillingPlanRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.UpdateOrganizationBillingPlanRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "plan", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "audit", kind: "message", T: Audit },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateOrganizationBillingPlanRequest {
    return new UpdateOrganizationBillingPlanRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateOrganizationBillingPlanRequest {
    return new UpdateOrganizationBillingPlanRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateOrganizationBillingPlanRequest {
    return new UpdateOrganizationBillingPlanRequest().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateOrganizationBillingPlanRequest | PlainMessage<UpdateOrganizationBillingPlanRequest> | undefined, b: UpdateOrganizationBillingPlanRequest | PlainMessage<UpdateOrganizationBillingPlanRequest> | undefined): boolean {
    return proto3.util.equals(UpdateOrganizationBillingPlanRequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.aims.v1.UpdateOrganizationBillingPlanResponse
 */
export class UpdateOrganizationBillingPlanResponse extends Message<UpdateOrganizationBillingPlanResponse> {
  constructor(data?: PartialMessage<UpdateOrganizationBillingPlanResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.UpdateOrganizationBillingPlanResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateOrganizationBillingPlanResponse {
    return new UpdateOrganizationBillingPlanResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateOrganizationBillingPlanResponse {
    return new UpdateOrganizationBillingPlanResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateOrganizationBillingPlanResponse {
    return new UpdateOrganizationBillingPlanResponse().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateOrganizationBillingPlanResponse | PlainMessage<UpdateOrganizationBillingPlanResponse> | undefined, b: UpdateOrganizationBillingPlanResponse | PlainMessage<UpdateOrganizationBillingPlanResponse> | undefined): boolean {
    return proto3.util.equals(UpdateOrganizationBillingPlanResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetKubeVisionUsageRequest
 */
export class GetKubeVisionUsageRequest extends Message<GetKubeVisionUsageRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: optional google.protobuf.Timestamp start_time = 2;
   */
  startTime?: Timestamp;

  /**
   * @generated from field: optional google.protobuf.Timestamp end_time = 3;
   */
  endTime?: Timestamp;

  constructor(data?: PartialMessage<GetKubeVisionUsageRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetKubeVisionUsageRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "start_time", kind: "message", T: Timestamp, opt: true },
    { no: 3, name: "end_time", kind: "message", T: Timestamp, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetKubeVisionUsageRequest {
    return new GetKubeVisionUsageRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetKubeVisionUsageRequest {
    return new GetKubeVisionUsageRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetKubeVisionUsageRequest {
    return new GetKubeVisionUsageRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetKubeVisionUsageRequest | PlainMessage<GetKubeVisionUsageRequest> | undefined, b: GetKubeVisionUsageRequest | PlainMessage<GetKubeVisionUsageRequest> | undefined): boolean {
    return proto3.util.equals(GetKubeVisionUsageRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetKubeVisionUsageResponse
 */
export class GetKubeVisionUsageResponse extends Message<GetKubeVisionUsageResponse> {
  /**
   * @generated from field: repeated akuity.organization.v1.KubeVisionUsage usage = 1;
   */
  usage: KubeVisionUsage[] = [];

  constructor(data?: PartialMessage<GetKubeVisionUsageResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetKubeVisionUsageResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "usage", kind: "message", T: KubeVisionUsage, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetKubeVisionUsageResponse {
    return new GetKubeVisionUsageResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetKubeVisionUsageResponse {
    return new GetKubeVisionUsageResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetKubeVisionUsageResponse {
    return new GetKubeVisionUsageResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetKubeVisionUsageResponse | PlainMessage<GetKubeVisionUsageResponse> | undefined, b: GetKubeVisionUsageResponse | PlainMessage<GetKubeVisionUsageResponse> | undefined): boolean {
    return proto3.util.equals(GetKubeVisionUsageResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ResetMFARequest
 */
export class ResetMFARequest extends Message<ResetMFARequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string email = 2;
   */
  email = "";

  /**
   * @generated from field: akuity.aims.v1.Audit audit = 3;
   */
  audit?: Audit;

  constructor(data?: PartialMessage<ResetMFARequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ResetMFARequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "email", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "audit", kind: "message", T: Audit },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ResetMFARequest {
    return new ResetMFARequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ResetMFARequest {
    return new ResetMFARequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ResetMFARequest {
    return new ResetMFARequest().fromJsonString(jsonString, options);
  }

  static equals(a: ResetMFARequest | PlainMessage<ResetMFARequest> | undefined, b: ResetMFARequest | PlainMessage<ResetMFARequest> | undefined): boolean {
    return proto3.util.equals(ResetMFARequest, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.aims.v1.ResetMFAResponse
 */
export class ResetMFAResponse extends Message<ResetMFAResponse> {
  constructor(data?: PartialMessage<ResetMFAResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ResetMFAResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ResetMFAResponse {
    return new ResetMFAResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ResetMFAResponse {
    return new ResetMFAResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ResetMFAResponse {
    return new ResetMFAResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ResetMFAResponse | PlainMessage<ResetMFAResponse> | undefined, b: ResetMFAResponse | PlainMessage<ResetMFAResponse> | undefined): boolean {
    return proto3.util.equals(ResetMFAResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.Team
 */
export class Team extends Message<Team> {
  /**
   * @generated from field: string name = 1;
   */
  name = "";

  /**
   * @generated from field: string description = 2;
   */
  description = "";

  /**
   * @generated from field: google.protobuf.Timestamp create_time = 3;
   */
  createTime?: Timestamp;

  /**
   * @generated from field: int64 member_count = 4;
   */
  memberCount = protoInt64.zero;

  constructor(data?: PartialMessage<Team>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.Team";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "create_time", kind: "message", T: Timestamp },
    { no: 4, name: "member_count", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Team {
    return new Team().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Team {
    return new Team().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Team {
    return new Team().fromJsonString(jsonString, options);
  }

  static equals(a: Team | PlainMessage<Team> | undefined, b: Team | PlainMessage<Team> | undefined): boolean {
    return proto3.util.equals(Team, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.UserTeam
 */
export class UserTeam extends Message<UserTeam> {
  /**
   * @generated from field: akuity.aims.v1.Team team = 1;
   */
  team?: Team;

  /**
   * @generated from field: bool is_member = 2;
   */
  isMember = false;

  constructor(data?: PartialMessage<UserTeam>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.UserTeam";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "team", kind: "message", T: Team },
    { no: 2, name: "is_member", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UserTeam {
    return new UserTeam().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UserTeam {
    return new UserTeam().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UserTeam {
    return new UserTeam().fromJsonString(jsonString, options);
  }

  static equals(a: UserTeam | PlainMessage<UserTeam> | undefined, b: UserTeam | PlainMessage<UserTeam> | undefined): boolean {
    return proto3.util.equals(UserTeam, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListTeamsRequest
 */
export class ListTeamsRequest extends Message<ListTeamsRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: optional uint32 limit = 2;
   */
  limit?: number;

  /**
   * @generated from field: optional uint32 offset = 3;
   */
  offset?: number;

  constructor(data?: PartialMessage<ListTeamsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListTeamsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "limit", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
    { no: 3, name: "offset", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListTeamsRequest {
    return new ListTeamsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListTeamsRequest {
    return new ListTeamsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListTeamsRequest {
    return new ListTeamsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListTeamsRequest | PlainMessage<ListTeamsRequest> | undefined, b: ListTeamsRequest | PlainMessage<ListTeamsRequest> | undefined): boolean {
    return proto3.util.equals(ListTeamsRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListTeamsResponse
 */
export class ListTeamsResponse extends Message<ListTeamsResponse> {
  /**
   * @generated from field: repeated akuity.aims.v1.Team teams = 1;
   */
  teams: Team[] = [];

  /**
   * @generated from field: uint32 count = 2;
   */
  count = 0;

  constructor(data?: PartialMessage<ListTeamsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListTeamsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "teams", kind: "message", T: Team, repeated: true },
    { no: 2, name: "count", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListTeamsResponse {
    return new ListTeamsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListTeamsResponse {
    return new ListTeamsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListTeamsResponse {
    return new ListTeamsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListTeamsResponse | PlainMessage<ListTeamsResponse> | undefined, b: ListTeamsResponse | PlainMessage<ListTeamsResponse> | undefined): boolean {
    return proto3.util.equals(ListTeamsResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.TeamMember
 */
export class TeamMember extends Message<TeamMember> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string email = 2;
   */
  email = "";

  constructor(data?: PartialMessage<TeamMember>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.TeamMember";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "email", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TeamMember {
    return new TeamMember().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TeamMember {
    return new TeamMember().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TeamMember {
    return new TeamMember().fromJsonString(jsonString, options);
  }

  static equals(a: TeamMember | PlainMessage<TeamMember> | undefined, b: TeamMember | PlainMessage<TeamMember> | undefined): boolean {
    return proto3.util.equals(TeamMember, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListTeamMembersRequest
 */
export class ListTeamMembersRequest extends Message<ListTeamMembersRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string team_name = 2;
   */
  teamName = "";

  /**
   * @generated from field: optional int64 limit = 3;
   */
  limit?: bigint;

  /**
   * @generated from field: optional int64 offset = 4;
   */
  offset?: bigint;

  constructor(data?: PartialMessage<ListTeamMembersRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListTeamMembersRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "team_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "limit", kind: "scalar", T: 3 /* ScalarType.INT64 */, opt: true },
    { no: 4, name: "offset", kind: "scalar", T: 3 /* ScalarType.INT64 */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListTeamMembersRequest {
    return new ListTeamMembersRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListTeamMembersRequest {
    return new ListTeamMembersRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListTeamMembersRequest {
    return new ListTeamMembersRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListTeamMembersRequest | PlainMessage<ListTeamMembersRequest> | undefined, b: ListTeamMembersRequest | PlainMessage<ListTeamMembersRequest> | undefined): boolean {
    return proto3.util.equals(ListTeamMembersRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListTeamMembersResponse
 */
export class ListTeamMembersResponse extends Message<ListTeamMembersResponse> {
  /**
   * @generated from field: repeated akuity.aims.v1.TeamMember team_members = 1;
   */
  teamMembers: TeamMember[] = [];

  /**
   * @generated from field: int64 count = 2;
   */
  count = protoInt64.zero;

  constructor(data?: PartialMessage<ListTeamMembersResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListTeamMembersResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "team_members", kind: "message", T: TeamMember, repeated: true },
    { no: 2, name: "count", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListTeamMembersResponse {
    return new ListTeamMembersResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListTeamMembersResponse {
    return new ListTeamMembersResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListTeamMembersResponse {
    return new ListTeamMembersResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListTeamMembersResponse | PlainMessage<ListTeamMembersResponse> | undefined, b: ListTeamMembersResponse | PlainMessage<ListTeamMembersResponse> | undefined): boolean {
    return proto3.util.equals(ListTeamMembersResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.WorkspaceArgoCDInstance
 */
export class WorkspaceArgoCDInstance extends Message<WorkspaceArgoCDInstance> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  constructor(data?: PartialMessage<WorkspaceArgoCDInstance>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.WorkspaceArgoCDInstance";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WorkspaceArgoCDInstance {
    return new WorkspaceArgoCDInstance().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WorkspaceArgoCDInstance {
    return new WorkspaceArgoCDInstance().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WorkspaceArgoCDInstance {
    return new WorkspaceArgoCDInstance().fromJsonString(jsonString, options);
  }

  static equals(a: WorkspaceArgoCDInstance | PlainMessage<WorkspaceArgoCDInstance> | undefined, b: WorkspaceArgoCDInstance | PlainMessage<WorkspaceArgoCDInstance> | undefined): boolean {
    return proto3.util.equals(WorkspaceArgoCDInstance, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.WorkspaceKargoInstance
 */
export class WorkspaceKargoInstance extends Message<WorkspaceKargoInstance> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  constructor(data?: PartialMessage<WorkspaceKargoInstance>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.WorkspaceKargoInstance";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WorkspaceKargoInstance {
    return new WorkspaceKargoInstance().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WorkspaceKargoInstance {
    return new WorkspaceKargoInstance().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WorkspaceKargoInstance {
    return new WorkspaceKargoInstance().fromJsonString(jsonString, options);
  }

  static equals(a: WorkspaceKargoInstance | PlainMessage<WorkspaceKargoInstance> | undefined, b: WorkspaceKargoInstance | PlainMessage<WorkspaceKargoInstance> | undefined): boolean {
    return proto3.util.equals(WorkspaceKargoInstance, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.WorkspaceUserMember
 */
export class WorkspaceUserMember extends Message<WorkspaceUserMember> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string email = 2;
   */
  email = "";

  constructor(data?: PartialMessage<WorkspaceUserMember>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.WorkspaceUserMember";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "email", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WorkspaceUserMember {
    return new WorkspaceUserMember().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WorkspaceUserMember {
    return new WorkspaceUserMember().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WorkspaceUserMember {
    return new WorkspaceUserMember().fromJsonString(jsonString, options);
  }

  static equals(a: WorkspaceUserMember | PlainMessage<WorkspaceUserMember> | undefined, b: WorkspaceUserMember | PlainMessage<WorkspaceUserMember> | undefined): boolean {
    return proto3.util.equals(WorkspaceUserMember, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.WorkspaceTeamMember
 */
export class WorkspaceTeamMember extends Message<WorkspaceTeamMember> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  /**
   * @generated from field: string description = 3;
   */
  description = "";

  /**
   * @generated from field: google.protobuf.Timestamp create_time = 4;
   */
  createTime?: Timestamp;

  /**
   * @generated from field: int64 member_count = 5;
   */
  memberCount = protoInt64.zero;

  constructor(data?: PartialMessage<WorkspaceTeamMember>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.WorkspaceTeamMember";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "create_time", kind: "message", T: Timestamp },
    { no: 5, name: "member_count", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WorkspaceTeamMember {
    return new WorkspaceTeamMember().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WorkspaceTeamMember {
    return new WorkspaceTeamMember().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WorkspaceTeamMember {
    return new WorkspaceTeamMember().fromJsonString(jsonString, options);
  }

  static equals(a: WorkspaceTeamMember | PlainMessage<WorkspaceTeamMember> | undefined, b: WorkspaceTeamMember | PlainMessage<WorkspaceTeamMember> | undefined): boolean {
    return proto3.util.equals(WorkspaceTeamMember, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.WorkspaceMember
 */
export class WorkspaceMember extends Message<WorkspaceMember> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: akuity.aims.v1.WorkspaceMemberRole role = 2;
   */
  role = WorkspaceMemberRole.UNSPECIFIED;

  /**
   * @generated from oneof akuity.aims.v1.WorkspaceMember.member
   */
  member: {
    /**
     * @generated from field: akuity.aims.v1.WorkspaceUserMember user = 3;
     */
    value: WorkspaceUserMember;
    case: "user";
  } | {
    /**
     * @generated from field: akuity.aims.v1.WorkspaceTeamMember team = 4;
     */
    value: WorkspaceTeamMember;
    case: "team";
  } | { case: undefined; value?: undefined } = { case: undefined };

  constructor(data?: PartialMessage<WorkspaceMember>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.WorkspaceMember";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "role", kind: "enum", T: proto3.getEnumType(WorkspaceMemberRole) },
    { no: 3, name: "user", kind: "message", T: WorkspaceUserMember, oneof: "member" },
    { no: 4, name: "team", kind: "message", T: WorkspaceTeamMember, oneof: "member" },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WorkspaceMember {
    return new WorkspaceMember().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WorkspaceMember {
    return new WorkspaceMember().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WorkspaceMember {
    return new WorkspaceMember().fromJsonString(jsonString, options);
  }

  static equals(a: WorkspaceMember | PlainMessage<WorkspaceMember> | undefined, b: WorkspaceMember | PlainMessage<WorkspaceMember> | undefined): boolean {
    return proto3.util.equals(WorkspaceMember, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.WorkspaceMemberRef
 */
export class WorkspaceMemberRef extends Message<WorkspaceMemberRef> {
  /**
   * @generated from field: akuity.aims.v1.WorkspaceMemberRole role = 1;
   */
  role = WorkspaceMemberRole.UNSPECIFIED;

  /**
   * @generated from oneof akuity.aims.v1.WorkspaceMemberRef.member
   */
  member: {
    /**
     * @generated from field: string user_id = 2;
     */
    value: string;
    case: "userId";
  } | {
    /**
     * @generated from field: string user_email = 3;
     */
    value: string;
    case: "userEmail";
  } | {
    /**
     * @generated from field: string team_name = 4;
     */
    value: string;
    case: "teamName";
  } | { case: undefined; value?: undefined } = { case: undefined };

  constructor(data?: PartialMessage<WorkspaceMemberRef>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.WorkspaceMemberRef";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "role", kind: "enum", T: proto3.getEnumType(WorkspaceMemberRole) },
    { no: 2, name: "user_id", kind: "scalar", T: 9 /* ScalarType.STRING */, oneof: "member" },
    { no: 3, name: "user_email", kind: "scalar", T: 9 /* ScalarType.STRING */, oneof: "member" },
    { no: 4, name: "team_name", kind: "scalar", T: 9 /* ScalarType.STRING */, oneof: "member" },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WorkspaceMemberRef {
    return new WorkspaceMemberRef().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WorkspaceMemberRef {
    return new WorkspaceMemberRef().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WorkspaceMemberRef {
    return new WorkspaceMemberRef().fromJsonString(jsonString, options);
  }

  static equals(a: WorkspaceMemberRef | PlainMessage<WorkspaceMemberRef> | undefined, b: WorkspaceMemberRef | PlainMessage<WorkspaceMemberRef> | undefined): boolean {
    return proto3.util.equals(WorkspaceMemberRef, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.CreateWorkspaceRequest
 */
export class CreateWorkspaceRequest extends Message<CreateWorkspaceRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  /**
   * @generated from field: optional string description = 3;
   */
  description?: string;

  constructor(data?: PartialMessage<CreateWorkspaceRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.CreateWorkspaceRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateWorkspaceRequest {
    return new CreateWorkspaceRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateWorkspaceRequest {
    return new CreateWorkspaceRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateWorkspaceRequest {
    return new CreateWorkspaceRequest().fromJsonString(jsonString, options);
  }

  static equals(a: CreateWorkspaceRequest | PlainMessage<CreateWorkspaceRequest> | undefined, b: CreateWorkspaceRequest | PlainMessage<CreateWorkspaceRequest> | undefined): boolean {
    return proto3.util.equals(CreateWorkspaceRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.CreateWorkspaceResponse
 */
export class CreateWorkspaceResponse extends Message<CreateWorkspaceResponse> {
  /**
   * @generated from field: akuity.aims.v1.Workspace workspace = 1;
   */
  workspace?: Workspace;

  constructor(data?: PartialMessage<CreateWorkspaceResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.CreateWorkspaceResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "workspace", kind: "message", T: Workspace },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CreateWorkspaceResponse {
    return new CreateWorkspaceResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CreateWorkspaceResponse {
    return new CreateWorkspaceResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CreateWorkspaceResponse {
    return new CreateWorkspaceResponse().fromJsonString(jsonString, options);
  }

  static equals(a: CreateWorkspaceResponse | PlainMessage<CreateWorkspaceResponse> | undefined, b: CreateWorkspaceResponse | PlainMessage<CreateWorkspaceResponse> | undefined): boolean {
    return proto3.util.equals(CreateWorkspaceResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.Workspace
 */
export class Workspace extends Message<Workspace> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  /**
   * @generated from field: string description = 3;
   */
  description = "";

  /**
   * @generated from field: google.protobuf.Timestamp create_time = 4;
   */
  createTime?: Timestamp;

  /**
   * @generated from field: repeated akuity.aims.v1.WorkspaceArgoCDInstance argocd_instances = 5;
   */
  argocdInstances: WorkspaceArgoCDInstance[] = [];

  /**
   * @generated from field: repeated akuity.aims.v1.WorkspaceKargoInstance kargo_instances = 6;
   */
  kargoInstances: WorkspaceKargoInstance[] = [];

  /**
   * @generated from field: uint32 team_member_count = 7;
   */
  teamMemberCount = 0;

  /**
   * @generated from field: uint32 user_member_count = 8;
   */
  userMemberCount = 0;

  /**
   * @generated from field: bool is_default = 9;
   */
  isDefault = false;

  constructor(data?: PartialMessage<Workspace>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.Workspace";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "create_time", kind: "message", T: Timestamp },
    { no: 5, name: "argocd_instances", kind: "message", T: WorkspaceArgoCDInstance, repeated: true },
    { no: 6, name: "kargo_instances", kind: "message", T: WorkspaceKargoInstance, repeated: true },
    { no: 7, name: "team_member_count", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 8, name: "user_member_count", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 9, name: "is_default", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Workspace {
    return new Workspace().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Workspace {
    return new Workspace().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Workspace {
    return new Workspace().fromJsonString(jsonString, options);
  }

  static equals(a: Workspace | PlainMessage<Workspace> | undefined, b: Workspace | PlainMessage<Workspace> | undefined): boolean {
    return proto3.util.equals(Workspace, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListWorkspacesRequest
 */
export class ListWorkspacesRequest extends Message<ListWorkspacesRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: optional uint32 limit = 2;
   */
  limit?: number;

  /**
   * @generated from field: optional uint32 offset = 3;
   */
  offset?: number;

  constructor(data?: PartialMessage<ListWorkspacesRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListWorkspacesRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "limit", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
    { no: 3, name: "offset", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListWorkspacesRequest {
    return new ListWorkspacesRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListWorkspacesRequest {
    return new ListWorkspacesRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListWorkspacesRequest {
    return new ListWorkspacesRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListWorkspacesRequest | PlainMessage<ListWorkspacesRequest> | undefined, b: ListWorkspacesRequest | PlainMessage<ListWorkspacesRequest> | undefined): boolean {
    return proto3.util.equals(ListWorkspacesRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListWorkspacesResponse
 */
export class ListWorkspacesResponse extends Message<ListWorkspacesResponse> {
  /**
   * @generated from field: repeated akuity.aims.v1.Workspace workspaces = 1;
   */
  workspaces: Workspace[] = [];

  /**
   * @generated from field: uint32 count = 2;
   */
  count = 0;

  constructor(data?: PartialMessage<ListWorkspacesResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListWorkspacesResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "workspaces", kind: "message", T: Workspace, repeated: true },
    { no: 2, name: "count", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListWorkspacesResponse {
    return new ListWorkspacesResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListWorkspacesResponse {
    return new ListWorkspacesResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListWorkspacesResponse {
    return new ListWorkspacesResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListWorkspacesResponse | PlainMessage<ListWorkspacesResponse> | undefined, b: ListWorkspacesResponse | PlainMessage<ListWorkspacesResponse> | undefined): boolean {
    return proto3.util.equals(ListWorkspacesResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetWorkspaceRequest
 */
export class GetWorkspaceRequest extends Message<GetWorkspaceRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string workspace_id = 2;
   */
  workspaceId = "";

  constructor(data?: PartialMessage<GetWorkspaceRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetWorkspaceRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetWorkspaceRequest {
    return new GetWorkspaceRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetWorkspaceRequest {
    return new GetWorkspaceRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetWorkspaceRequest {
    return new GetWorkspaceRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetWorkspaceRequest | PlainMessage<GetWorkspaceRequest> | undefined, b: GetWorkspaceRequest | PlainMessage<GetWorkspaceRequest> | undefined): boolean {
    return proto3.util.equals(GetWorkspaceRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetWorkspaceResponse
 */
export class GetWorkspaceResponse extends Message<GetWorkspaceResponse> {
  /**
   * @generated from field: akuity.aims.v1.Workspace workspace = 1;
   */
  workspace?: Workspace;

  constructor(data?: PartialMessage<GetWorkspaceResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetWorkspaceResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "workspace", kind: "message", T: Workspace },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetWorkspaceResponse {
    return new GetWorkspaceResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetWorkspaceResponse {
    return new GetWorkspaceResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetWorkspaceResponse {
    return new GetWorkspaceResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetWorkspaceResponse | PlainMessage<GetWorkspaceResponse> | undefined, b: GetWorkspaceResponse | PlainMessage<GetWorkspaceResponse> | undefined): boolean {
    return proto3.util.equals(GetWorkspaceResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListWorkspaceMembersRequest
 */
export class ListWorkspaceMembersRequest extends Message<ListWorkspaceMembersRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string workspace_id = 2;
   */
  workspaceId = "";

  /**
   * @generated from field: optional uint32 limit = 3;
   */
  limit?: number;

  /**
   * @generated from field: optional uint32 offset = 4;
   */
  offset?: number;

  constructor(data?: PartialMessage<ListWorkspaceMembersRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListWorkspaceMembersRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "limit", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
    { no: 4, name: "offset", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListWorkspaceMembersRequest {
    return new ListWorkspaceMembersRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListWorkspaceMembersRequest {
    return new ListWorkspaceMembersRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListWorkspaceMembersRequest {
    return new ListWorkspaceMembersRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListWorkspaceMembersRequest | PlainMessage<ListWorkspaceMembersRequest> | undefined, b: ListWorkspaceMembersRequest | PlainMessage<ListWorkspaceMembersRequest> | undefined): boolean {
    return proto3.util.equals(ListWorkspaceMembersRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListWorkspaceMembersResponse
 */
export class ListWorkspaceMembersResponse extends Message<ListWorkspaceMembersResponse> {
  /**
   * @generated from field: repeated akuity.aims.v1.WorkspaceMember workspace_members = 1;
   */
  workspaceMembers: WorkspaceMember[] = [];

  /**
   * @generated from field: uint32 team_member_count = 2;
   */
  teamMemberCount = 0;

  /**
   * @generated from field: uint32 user_member_count = 3;
   */
  userMemberCount = 0;

  constructor(data?: PartialMessage<ListWorkspaceMembersResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListWorkspaceMembersResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "workspace_members", kind: "message", T: WorkspaceMember, repeated: true },
    { no: 2, name: "team_member_count", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 3, name: "user_member_count", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListWorkspaceMembersResponse {
    return new ListWorkspaceMembersResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListWorkspaceMembersResponse {
    return new ListWorkspaceMembersResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListWorkspaceMembersResponse {
    return new ListWorkspaceMembersResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListWorkspaceMembersResponse | PlainMessage<ListWorkspaceMembersResponse> | undefined, b: ListWorkspaceMembersResponse | PlainMessage<ListWorkspaceMembersResponse> | undefined): boolean {
    return proto3.util.equals(ListWorkspaceMembersResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.CustomRole
 */
export class CustomRole extends Message<CustomRole> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  /**
   * @generated from field: string description = 3;
   */
  description = "";

  /**
   * @generated from field: string policy = 4;
   */
  policy = "";

  constructor(data?: PartialMessage<CustomRole>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.CustomRole";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "description", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "policy", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): CustomRole {
    return new CustomRole().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): CustomRole {
    return new CustomRole().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): CustomRole {
    return new CustomRole().fromJsonString(jsonString, options);
  }

  static equals(a: CustomRole | PlainMessage<CustomRole> | undefined, b: CustomRole | PlainMessage<CustomRole> | undefined): boolean {
    return proto3.util.equals(CustomRole, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListWorkspaceCustomRolesRequest
 */
export class ListWorkspaceCustomRolesRequest extends Message<ListWorkspaceCustomRolesRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: string workspace_id = 2;
   */
  workspaceId = "";

  /**
   * @generated from field: optional uint32 limit = 3;
   */
  limit?: number;

  /**
   * @generated from field: optional uint32 offset = 4;
   */
  offset?: number;

  constructor(data?: PartialMessage<ListWorkspaceCustomRolesRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListWorkspaceCustomRolesRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "limit", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
    { no: 4, name: "offset", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListWorkspaceCustomRolesRequest {
    return new ListWorkspaceCustomRolesRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListWorkspaceCustomRolesRequest {
    return new ListWorkspaceCustomRolesRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListWorkspaceCustomRolesRequest {
    return new ListWorkspaceCustomRolesRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListWorkspaceCustomRolesRequest | PlainMessage<ListWorkspaceCustomRolesRequest> | undefined, b: ListWorkspaceCustomRolesRequest | PlainMessage<ListWorkspaceCustomRolesRequest> | undefined): boolean {
    return proto3.util.equals(ListWorkspaceCustomRolesRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListWorkspaceCustomRolesResponse
 */
export class ListWorkspaceCustomRolesResponse extends Message<ListWorkspaceCustomRolesResponse> {
  /**
   * @generated from field: repeated akuity.aims.v1.CustomRole custom_roles = 1;
   */
  customRoles: CustomRole[] = [];

  /**
   * @generated from field: string workspace_id = 2;
   */
  workspaceId = "";

  /**
   * @generated from field: int64 total_count = 3;
   */
  totalCount = protoInt64.zero;

  constructor(data?: PartialMessage<ListWorkspaceCustomRolesResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListWorkspaceCustomRolesResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "custom_roles", kind: "message", T: CustomRole, repeated: true },
    { no: 2, name: "workspace_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "total_count", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListWorkspaceCustomRolesResponse {
    return new ListWorkspaceCustomRolesResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListWorkspaceCustomRolesResponse {
    return new ListWorkspaceCustomRolesResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListWorkspaceCustomRolesResponse {
    return new ListWorkspaceCustomRolesResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListWorkspaceCustomRolesResponse | PlainMessage<ListWorkspaceCustomRolesResponse> | undefined, b: ListWorkspaceCustomRolesResponse | PlainMessage<ListWorkspaceCustomRolesResponse> | undefined): boolean {
    return proto3.util.equals(ListWorkspaceCustomRolesResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListOrganizationCustomRolesRequest
 */
export class ListOrganizationCustomRolesRequest extends Message<ListOrganizationCustomRolesRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  /**
   * @generated from field: optional uint32 limit = 2;
   */
  limit?: number;

  /**
   * @generated from field: optional uint32 offset = 3;
   */
  offset?: number;

  constructor(data?: PartialMessage<ListOrganizationCustomRolesRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListOrganizationCustomRolesRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "limit", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
    { no: 3, name: "offset", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListOrganizationCustomRolesRequest {
    return new ListOrganizationCustomRolesRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListOrganizationCustomRolesRequest {
    return new ListOrganizationCustomRolesRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListOrganizationCustomRolesRequest {
    return new ListOrganizationCustomRolesRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListOrganizationCustomRolesRequest | PlainMessage<ListOrganizationCustomRolesRequest> | undefined, b: ListOrganizationCustomRolesRequest | PlainMessage<ListOrganizationCustomRolesRequest> | undefined): boolean {
    return proto3.util.equals(ListOrganizationCustomRolesRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListOrganizationCustomRolesResponse
 */
export class ListOrganizationCustomRolesResponse extends Message<ListOrganizationCustomRolesResponse> {
  /**
   * @generated from field: repeated akuity.aims.v1.CustomRole custom_roles = 1;
   */
  customRoles: CustomRole[] = [];

  /**
   * @generated from field: int64 total_count = 2;
   */
  totalCount = protoInt64.zero;

  constructor(data?: PartialMessage<ListOrganizationCustomRolesResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListOrganizationCustomRolesResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "custom_roles", kind: "message", T: CustomRole, repeated: true },
    { no: 2, name: "total_count", kind: "scalar", T: 3 /* ScalarType.INT64 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListOrganizationCustomRolesResponse {
    return new ListOrganizationCustomRolesResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListOrganizationCustomRolesResponse {
    return new ListOrganizationCustomRolesResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListOrganizationCustomRolesResponse {
    return new ListOrganizationCustomRolesResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListOrganizationCustomRolesResponse | PlainMessage<ListOrganizationCustomRolesResponse> | undefined, b: ListOrganizationCustomRolesResponse | PlainMessage<ListOrganizationCustomRolesResponse> | undefined): boolean {
    return proto3.util.equals(ListOrganizationCustomRolesResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListOrganizationUsersRequest
 */
export class ListOrganizationUsersRequest extends Message<ListOrganizationUsersRequest> {
  /**
   * @generated from field: string organization_id = 1;
   */
  organizationId = "";

  constructor(data?: PartialMessage<ListOrganizationUsersRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListOrganizationUsersRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "organization_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListOrganizationUsersRequest {
    return new ListOrganizationUsersRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListOrganizationUsersRequest {
    return new ListOrganizationUsersRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListOrganizationUsersRequest {
    return new ListOrganizationUsersRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListOrganizationUsersRequest | PlainMessage<ListOrganizationUsersRequest> | undefined, b: ListOrganizationUsersRequest | PlainMessage<ListOrganizationUsersRequest> | undefined): boolean {
    return proto3.util.equals(ListOrganizationUsersRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.ListOrganizationUsersResponse
 */
export class ListOrganizationUsersResponse extends Message<ListOrganizationUsersResponse> {
  /**
   * @generated from field: repeated akuity.aims.v1.OrganizationUser users = 1;
   */
  users: OrganizationUser[] = [];

  constructor(data?: PartialMessage<ListOrganizationUsersResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.ListOrganizationUsersResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "users", kind: "message", T: OrganizationUser, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListOrganizationUsersResponse {
    return new ListOrganizationUsersResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListOrganizationUsersResponse {
    return new ListOrganizationUsersResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListOrganizationUsersResponse {
    return new ListOrganizationUsersResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListOrganizationUsersResponse | PlainMessage<ListOrganizationUsersResponse> | undefined, b: ListOrganizationUsersResponse | PlainMessage<ListOrganizationUsersResponse> | undefined): boolean {
    return proto3.util.equals(ListOrganizationUsersResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.WorkspaceInfo
 */
export class WorkspaceInfo extends Message<WorkspaceInfo> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  constructor(data?: PartialMessage<WorkspaceInfo>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.WorkspaceInfo";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WorkspaceInfo {
    return new WorkspaceInfo().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WorkspaceInfo {
    return new WorkspaceInfo().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WorkspaceInfo {
    return new WorkspaceInfo().fromJsonString(jsonString, options);
  }

  static equals(a: WorkspaceInfo | PlainMessage<WorkspaceInfo> | undefined, b: WorkspaceInfo | PlainMessage<WorkspaceInfo> | undefined): boolean {
    return proto3.util.equals(WorkspaceInfo, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.TeamInfo
 */
export class TeamInfo extends Message<TeamInfo> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  constructor(data?: PartialMessage<TeamInfo>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.TeamInfo";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): TeamInfo {
    return new TeamInfo().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): TeamInfo {
    return new TeamInfo().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): TeamInfo {
    return new TeamInfo().fromJsonString(jsonString, options);
  }

  static equals(a: TeamInfo | PlainMessage<TeamInfo> | undefined, b: TeamInfo | PlainMessage<TeamInfo> | undefined): boolean {
    return proto3.util.equals(TeamInfo, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.OrganizationUser
 */
export class OrganizationUser extends Message<OrganizationUser> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string email = 2;
   */
  email = "";

  /**
   * @generated from field: string role = 3;
   */
  role = "";

  /**
   * @generated from field: repeated akuity.aims.v1.WorkspaceInfo workspaces = 4;
   */
  workspaces: WorkspaceInfo[] = [];

  /**
   * @generated from field: repeated akuity.aims.v1.TeamInfo teams = 5;
   */
  teams: TeamInfo[] = [];

  constructor(data?: PartialMessage<OrganizationUser>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.OrganizationUser";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "email", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "role", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "workspaces", kind: "message", T: WorkspaceInfo, repeated: true },
    { no: 5, name: "teams", kind: "message", T: TeamInfo, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OrganizationUser {
    return new OrganizationUser().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OrganizationUser {
    return new OrganizationUser().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OrganizationUser {
    return new OrganizationUser().fromJsonString(jsonString, options);
  }

  static equals(a: OrganizationUser | PlainMessage<OrganizationUser> | undefined, b: OrganizationUser | PlainMessage<OrganizationUser> | undefined): boolean {
    return proto3.util.equals(OrganizationUser, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.AuditLog
 */
export class AuditLog extends Message<AuditLog> {
  /**
   * @generated from field: string timestamp = 1;
   */
  timestamp = "";

  /**
   * @generated from field: string action = 2;
   */
  action = "";

  /**
   * @generated from field: akuity.aims.v1.AuditLog.AuditActor actor = 3;
   */
  actor?: AuditLog_AuditActor;

  /**
   * @generated from field: akuity.aims.v1.AuditLog.AuditObject object = 4;
   */
  object?: AuditLog_AuditObject;

  /**
   * @generated from field: akuity.aims.v1.AuditLog.AuditDetails details = 5;
   */
  details?: AuditLog_AuditDetails;

  constructor(data?: PartialMessage<AuditLog>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.AuditLog";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "timestamp", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "action", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "actor", kind: "message", T: AuditLog_AuditActor },
    { no: 4, name: "object", kind: "message", T: AuditLog_AuditObject },
    { no: 5, name: "details", kind: "message", T: AuditLog_AuditDetails },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AuditLog {
    return new AuditLog().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AuditLog {
    return new AuditLog().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AuditLog {
    return new AuditLog().fromJsonString(jsonString, options);
  }

  static equals(a: AuditLog | PlainMessage<AuditLog> | undefined, b: AuditLog | PlainMessage<AuditLog> | undefined): boolean {
    return proto3.util.equals(AuditLog, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.AuditLog.AuditActor
 */
export class AuditLog_AuditActor extends Message<AuditLog_AuditActor> {
  /**
   * @generated from field: string type = 1;
   */
  type = "";

  /**
   * @generated from field: string id = 2;
   */
  id = "";

  /**
   * @generated from field: optional string ip = 3;
   */
  ip?: string;

  constructor(data?: PartialMessage<AuditLog_AuditActor>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.AuditLog.AuditActor";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "type", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "ip", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AuditLog_AuditActor {
    return new AuditLog_AuditActor().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AuditLog_AuditActor {
    return new AuditLog_AuditActor().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AuditLog_AuditActor {
    return new AuditLog_AuditActor().fromJsonString(jsonString, options);
  }

  static equals(a: AuditLog_AuditActor | PlainMessage<AuditLog_AuditActor> | undefined, b: AuditLog_AuditActor | PlainMessage<AuditLog_AuditActor> | undefined): boolean {
    return proto3.util.equals(AuditLog_AuditActor, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.AuditLog.AuditObject
 */
export class AuditLog_AuditObject extends Message<AuditLog_AuditObject> {
  /**
   * @generated from field: string type = 1;
   */
  type = "";

  /**
   * @generated from field: string id = 2;
   */
  id = "";

  constructor(data?: PartialMessage<AuditLog_AuditObject>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.AuditLog.AuditObject";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "type", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AuditLog_AuditObject {
    return new AuditLog_AuditObject().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AuditLog_AuditObject {
    return new AuditLog_AuditObject().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AuditLog_AuditObject {
    return new AuditLog_AuditObject().fromJsonString(jsonString, options);
  }

  static equals(a: AuditLog_AuditObject | PlainMessage<AuditLog_AuditObject> | undefined, b: AuditLog_AuditObject | PlainMessage<AuditLog_AuditObject> | undefined): boolean {
    return proto3.util.equals(AuditLog_AuditObject, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.AuditLog.AuditDetails
 */
export class AuditLog_AuditDetails extends Message<AuditLog_AuditDetails> {
  /**
   * @generated from field: string message = 1;
   */
  message = "";

  /**
   * @generated from field: string patch = 2;
   */
  patch = "";

  constructor(data?: PartialMessage<AuditLog_AuditDetails>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.AuditLog.AuditDetails";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "patch", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): AuditLog_AuditDetails {
    return new AuditLog_AuditDetails().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): AuditLog_AuditDetails {
    return new AuditLog_AuditDetails().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): AuditLog_AuditDetails {
    return new AuditLog_AuditDetails().fromJsonString(jsonString, options);
  }

  static equals(a: AuditLog_AuditDetails | PlainMessage<AuditLog_AuditDetails> | undefined, b: AuditLog_AuditDetails | PlainMessage<AuditLog_AuditDetails> | undefined): boolean {
    return proto3.util.equals(AuditLog_AuditDetails, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.InternalAuditFilters
 */
export class InternalAuditFilters extends Message<InternalAuditFilters> {
  /**
   * @generated from field: repeated string actor_id = 1;
   */
  actorId: string[] = [];

  /**
   * @generated from field: optional string object_type = 2;
   */
  objectType?: string;

  /**
   * @generated from field: repeated string action = 3;
   */
  action: string[] = [];

  /**
   * @generated from field: optional string start_time = 4;
   */
  startTime?: string;

  /**
   * @generated from field: optional string end_time = 5;
   */
  endTime?: string;

  /**
   * @generated from field: optional uint32 limit = 6;
   */
  limit?: number;

  /**
   * @generated from field: optional uint32 offset = 7;
   */
  offset?: number;

  constructor(data?: PartialMessage<InternalAuditFilters>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.InternalAuditFilters";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "actor_id", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 2, name: "object_type", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 3, name: "action", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
    { no: 4, name: "start_time", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 5, name: "end_time", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 6, name: "limit", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
    { no: 7, name: "offset", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): InternalAuditFilters {
    return new InternalAuditFilters().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): InternalAuditFilters {
    return new InternalAuditFilters().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): InternalAuditFilters {
    return new InternalAuditFilters().fromJsonString(jsonString, options);
  }

  static equals(a: InternalAuditFilters | PlainMessage<InternalAuditFilters> | undefined, b: InternalAuditFilters | PlainMessage<InternalAuditFilters> | undefined): boolean {
    return proto3.util.equals(InternalAuditFilters, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetInternalAuditLogsRequest
 */
export class GetInternalAuditLogsRequest extends Message<GetInternalAuditLogsRequest> {
  /**
   * @generated from field: akuity.aims.v1.InternalAuditFilters filters = 1;
   */
  filters?: InternalAuditFilters;

  constructor(data?: PartialMessage<GetInternalAuditLogsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetInternalAuditLogsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "filters", kind: "message", T: InternalAuditFilters },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetInternalAuditLogsRequest {
    return new GetInternalAuditLogsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetInternalAuditLogsRequest {
    return new GetInternalAuditLogsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetInternalAuditLogsRequest {
    return new GetInternalAuditLogsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetInternalAuditLogsRequest | PlainMessage<GetInternalAuditLogsRequest> | undefined, b: GetInternalAuditLogsRequest | PlainMessage<GetInternalAuditLogsRequest> | undefined): boolean {
    return proto3.util.equals(GetInternalAuditLogsRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetInternalAuditLogsResponse
 */
export class GetInternalAuditLogsResponse extends Message<GetInternalAuditLogsResponse> {
  /**
   * @generated from field: repeated akuity.aims.v1.AuditLog items = 1;
   */
  items: AuditLog[] = [];

  /**
   * @generated from field: uint32 total_count = 2;
   */
  totalCount = 0;

  constructor(data?: PartialMessage<GetInternalAuditLogsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetInternalAuditLogsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "items", kind: "message", T: AuditLog, repeated: true },
    { no: 2, name: "total_count", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetInternalAuditLogsResponse {
    return new GetInternalAuditLogsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetInternalAuditLogsResponse {
    return new GetInternalAuditLogsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetInternalAuditLogsResponse {
    return new GetInternalAuditLogsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetInternalAuditLogsResponse | PlainMessage<GetInternalAuditLogsResponse> | undefined, b: GetInternalAuditLogsResponse | PlainMessage<GetInternalAuditLogsResponse> | undefined): boolean {
    return proto3.util.equals(GetInternalAuditLogsResponse, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetClusterManifestsRequest
 */
export class GetClusterManifestsRequest extends Message<GetClusterManifestsRequest> {
  /**
   * @generated from field: string instance_id = 1;
   */
  instanceId = "";

  /**
   * @generated from field: string cluster_id = 2;
   */
  clusterId = "";

  /**
   * @generated from field: akuity.aims.v1.Audit audit = 3;
   */
  audit?: Audit;

  constructor(data?: PartialMessage<GetClusterManifestsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetClusterManifestsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "cluster_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "audit", kind: "message", T: Audit },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetClusterManifestsRequest {
    return new GetClusterManifestsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetClusterManifestsRequest {
    return new GetClusterManifestsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetClusterManifestsRequest {
    return new GetClusterManifestsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetClusterManifestsRequest | PlainMessage<GetClusterManifestsRequest> | undefined, b: GetClusterManifestsRequest | PlainMessage<GetClusterManifestsRequest> | undefined): boolean {
    return proto3.util.equals(GetClusterManifestsRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetKargoAgentManifestsRequest
 */
export class GetKargoAgentManifestsRequest extends Message<GetKargoAgentManifestsRequest> {
  /**
   * @generated from field: string instance_id = 1;
   */
  instanceId = "";

  /**
   * @generated from field: string agent_id = 2;
   */
  agentId = "";

  /**
   * @generated from field: akuity.aims.v1.Audit audit = 3;
   */
  audit?: Audit;

  constructor(data?: PartialMessage<GetKargoAgentManifestsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetKargoAgentManifestsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "instance_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "agent_id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "audit", kind: "message", T: Audit },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetKargoAgentManifestsRequest {
    return new GetKargoAgentManifestsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetKargoAgentManifestsRequest {
    return new GetKargoAgentManifestsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetKargoAgentManifestsRequest {
    return new GetKargoAgentManifestsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetKargoAgentManifestsRequest | PlainMessage<GetKargoAgentManifestsRequest> | undefined, b: GetKargoAgentManifestsRequest | PlainMessage<GetKargoAgentManifestsRequest> | undefined): boolean {
    return proto3.util.equals(GetKargoAgentManifestsRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.UpdateAnnouncementBannerRequest
 */
export class UpdateAnnouncementBannerRequest extends Message<UpdateAnnouncementBannerRequest> {
  /**
   * @generated from field: akuity.aims.v1.Banner banner = 1;
   */
  banner?: Banner;

  /**
   * @generated from field: optional bool delete = 2;
   */
  delete?: boolean;

  /**
   * @generated from field: akuity.aims.v1.Audit audit = 3;
   */
  audit?: Audit;

  constructor(data?: PartialMessage<UpdateAnnouncementBannerRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.UpdateAnnouncementBannerRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "banner", kind: "message", T: Banner },
    { no: 2, name: "delete", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 3, name: "audit", kind: "message", T: Audit },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAnnouncementBannerRequest {
    return new UpdateAnnouncementBannerRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAnnouncementBannerRequest {
    return new UpdateAnnouncementBannerRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAnnouncementBannerRequest {
    return new UpdateAnnouncementBannerRequest().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateAnnouncementBannerRequest | PlainMessage<UpdateAnnouncementBannerRequest> | undefined, b: UpdateAnnouncementBannerRequest | PlainMessage<UpdateAnnouncementBannerRequest> | undefined): boolean {
    return proto3.util.equals(UpdateAnnouncementBannerRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.Banner
 */
export class Banner extends Message<Banner> {
  /**
   * @generated from field: optional string title = 1;
   */
  title?: string;

  /**
   * @generated from field: string message = 2;
   */
  message = "";

  /**
   * @generated from field: optional bool closable = 3;
   */
  closable?: boolean;

  /**
   * @generated from field: optional string type = 4;
   */
  type?: string;

  /**
   * @generated from field: repeated akuity.aims.v1.Banner.BannerLink links = 5;
   */
  links: Banner_BannerLink[] = [];

  /**
   * @generated from field: optional bool paid_customers_only = 6;
   */
  paidCustomersOnly?: boolean;

  constructor(data?: PartialMessage<Banner>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.Banner";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "title", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 2, name: "message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "closable", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
    { no: 4, name: "type", kind: "scalar", T: 9 /* ScalarType.STRING */, opt: true },
    { no: 5, name: "links", kind: "message", T: Banner_BannerLink, repeated: true },
    { no: 6, name: "paid_customers_only", kind: "scalar", T: 8 /* ScalarType.BOOL */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Banner {
    return new Banner().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Banner {
    return new Banner().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Banner {
    return new Banner().fromJsonString(jsonString, options);
  }

  static equals(a: Banner | PlainMessage<Banner> | undefined, b: Banner | PlainMessage<Banner> | undefined): boolean {
    return proto3.util.equals(Banner, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.Banner.BannerLink
 */
export class Banner_BannerLink extends Message<Banner_BannerLink> {
  /**
   * @generated from field: string name = 1;
   */
  name = "";

  /**
   * @generated from field: string url = 2;
   */
  url = "";

  constructor(data?: PartialMessage<Banner_BannerLink>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.Banner.BannerLink";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "url", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Banner_BannerLink {
    return new Banner_BannerLink().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Banner_BannerLink {
    return new Banner_BannerLink().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Banner_BannerLink {
    return new Banner_BannerLink().fromJsonString(jsonString, options);
  }

  static equals(a: Banner_BannerLink | PlainMessage<Banner_BannerLink> | undefined, b: Banner_BannerLink | PlainMessage<Banner_BannerLink> | undefined): boolean {
    return proto3.util.equals(Banner_BannerLink, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.aims.v1.UpdateAnnouncementBannerResponse
 */
export class UpdateAnnouncementBannerResponse extends Message<UpdateAnnouncementBannerResponse> {
  constructor(data?: PartialMessage<UpdateAnnouncementBannerResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.UpdateAnnouncementBannerResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateAnnouncementBannerResponse {
    return new UpdateAnnouncementBannerResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateAnnouncementBannerResponse {
    return new UpdateAnnouncementBannerResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateAnnouncementBannerResponse {
    return new UpdateAnnouncementBannerResponse().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateAnnouncementBannerResponse | PlainMessage<UpdateAnnouncementBannerResponse> | undefined, b: UpdateAnnouncementBannerResponse | PlainMessage<UpdateAnnouncementBannerResponse> | undefined): boolean {
    return proto3.util.equals(UpdateAnnouncementBannerResponse, a, b);
  }
}

/**
 * explicitly empty 
 *
 * @generated from message akuity.aims.v1.GetAnnouncementBannerRequest
 */
export class GetAnnouncementBannerRequest extends Message<GetAnnouncementBannerRequest> {
  constructor(data?: PartialMessage<GetAnnouncementBannerRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetAnnouncementBannerRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAnnouncementBannerRequest {
    return new GetAnnouncementBannerRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAnnouncementBannerRequest {
    return new GetAnnouncementBannerRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAnnouncementBannerRequest {
    return new GetAnnouncementBannerRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetAnnouncementBannerRequest | PlainMessage<GetAnnouncementBannerRequest> | undefined, b: GetAnnouncementBannerRequest | PlainMessage<GetAnnouncementBannerRequest> | undefined): boolean {
    return proto3.util.equals(GetAnnouncementBannerRequest, a, b);
  }
}

/**
 * @generated from message akuity.aims.v1.GetAnnouncementBannerResponse
 */
export class GetAnnouncementBannerResponse extends Message<GetAnnouncementBannerResponse> {
  /**
   * @generated from field: akuity.aims.v1.Banner banner = 1;
   */
  banner?: Banner;

  constructor(data?: PartialMessage<GetAnnouncementBannerResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.aims.v1.GetAnnouncementBannerResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "banner", kind: "message", T: Banner },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetAnnouncementBannerResponse {
    return new GetAnnouncementBannerResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetAnnouncementBannerResponse {
    return new GetAnnouncementBannerResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetAnnouncementBannerResponse {
    return new GetAnnouncementBannerResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetAnnouncementBannerResponse | PlainMessage<GetAnnouncementBannerResponse> | undefined, b: GetAnnouncementBannerResponse | PlainMessage<GetAnnouncementBannerResponse> | undefined): boolean {
    return proto3.util.equals(GetAnnouncementBannerResponse, a, b);
  }
}

