// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file user/v1/user.proto (package akuity.user.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3, Struct, Timestamp } from "@bufbuild/protobuf";
import { EventType } from "../../types/events/v1/events_pb.js";

/**
 * @generated from enum akuity.user.v1.NotificationCategory
 */
export enum NotificationCategory {
  /**
   * @generated from enum value: NOTIFICATION_CATEGORY_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: NOTIFICATION_CATEGORY_BILLING = 1;
   */
  BILLING = 1,

  /**
   * @generated from enum value: NOTIFICATION_CATEGORY_MARKETING = 2;
   */
  MARKETING = 2,

  /**
   * @generated from enum value: NOTIFICATION_CATEGORY_USAGE_ALERTS = 3;
   */
  USAGE_ALERTS = 3,

  /**
   * @generated from enum value: NOTIFICATION_CATEGORY_PRODUCT_UPDATES = 4;
   */
  PRODUCT_UPDATES = 4,
}
// Retrieve enum metadata with: proto3.getEnumType(NotificationCategory)
proto3.util.setEnumType(NotificationCategory, "akuity.user.v1.NotificationCategory", [
  { no: 0, name: "NOTIFICATION_CATEGORY_UNSPECIFIED" },
  { no: 1, name: "NOTIFICATION_CATEGORY_BILLING" },
  { no: 2, name: "NOTIFICATION_CATEGORY_MARKETING" },
  { no: 3, name: "NOTIFICATION_CATEGORY_USAGE_ALERTS" },
  { no: 4, name: "NOTIFICATION_CATEGORY_PRODUCT_UPDATES" },
]);

/**
 * empty 
 *
 * @generated from message akuity.user.v1.GetUserRequest
 */
export class GetUserRequest extends Message<GetUserRequest> {
  constructor(data?: PartialMessage<GetUserRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.GetUserRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserRequest {
    return new GetUserRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserRequest {
    return new GetUserRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserRequest {
    return new GetUserRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetUserRequest | PlainMessage<GetUserRequest> | undefined, b: GetUserRequest | PlainMessage<GetUserRequest> | undefined): boolean {
    return proto3.util.equals(GetUserRequest, a, b);
  }
}

/**
 * @generated from message akuity.user.v1.OrganizationSummary
 */
export class OrganizationSummary extends Message<OrganizationSummary> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string name = 2;
   */
  name = "";

  /**
   * @generated from field: string role = 3;
   */
  role = "";

  /**
   * @generated from field: google.protobuf.Timestamp expiration_time = 4;
   */
  expirationTime?: Timestamp;

  /**
   * @generated from field: bool is_free_trial = 5;
   */
  isFreeTrial = false;

  /**
   * @generated from field: bool is_sso_inferred = 6;
   */
  isSsoInferred = false;

  /**
   * @generated from field: string plan = 7;
   */
  plan = "";

  constructor(data?: PartialMessage<OrganizationSummary>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.OrganizationSummary";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "role", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "expiration_time", kind: "message", T: Timestamp },
    { no: 5, name: "is_free_trial", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 6, name: "is_sso_inferred", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 7, name: "plan", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): OrganizationSummary {
    return new OrganizationSummary().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): OrganizationSummary {
    return new OrganizationSummary().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): OrganizationSummary {
    return new OrganizationSummary().fromJsonString(jsonString, options);
  }

  static equals(a: OrganizationSummary | PlainMessage<OrganizationSummary> | undefined, b: OrganizationSummary | PlainMessage<OrganizationSummary> | undefined): boolean {
    return proto3.util.equals(OrganizationSummary, a, b);
  }
}

/**
 * @generated from message akuity.user.v1.User
 */
export class User extends Message<User> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: string email = 2;
   */
  email = "";

  /**
   * @generated from field: repeated akuity.user.v1.OrganizationSummary organizations = 3;
   */
  organizations: OrganizationSummary[] = [];

  /**
   * @generated from field: repeated akuity.user.v1.OrganizationSummary invitations = 4;
   */
  invitations: OrganizationSummary[] = [];

  /**
   * @generated from field: bool unrestricted = 5;
   */
  unrestricted = false;

  /**
   * @generated from field: akuity.user.v1.UserInfo user_info = 6;
   */
  userInfo?: UserInfo;

  /**
   * @generated from field: google.protobuf.Struct ui_preferences = 7;
   */
  uiPreferences?: Struct;

  /**
   * @generated from field: bool initial_signup = 8;
   */
  initialSignup = false;

  constructor(data?: PartialMessage<User>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.User";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "email", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 3, name: "organizations", kind: "message", T: OrganizationSummary, repeated: true },
    { no: 4, name: "invitations", kind: "message", T: OrganizationSummary, repeated: true },
    { no: 5, name: "unrestricted", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 6, name: "user_info", kind: "message", T: UserInfo },
    { no: 7, name: "ui_preferences", kind: "message", T: Struct },
    { no: 8, name: "initial_signup", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): User {
    return new User().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): User {
    return new User().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): User {
    return new User().fromJsonString(jsonString, options);
  }

  static equals(a: User | PlainMessage<User> | undefined, b: User | PlainMessage<User> | undefined): boolean {
    return proto3.util.equals(User, a, b);
  }
}

/**
 * @generated from message akuity.user.v1.GetUserResponse
 */
export class GetUserResponse extends Message<GetUserResponse> {
  /**
   * @generated from field: akuity.user.v1.User user = 1;
   */
  user?: User;

  constructor(data?: PartialMessage<GetUserResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.GetUserResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "user", kind: "message", T: User },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetUserResponse {
    return new GetUserResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetUserResponse {
    return new GetUserResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetUserResponse {
    return new GetUserResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetUserResponse | PlainMessage<GetUserResponse> | undefined, b: GetUserResponse | PlainMessage<GetUserResponse> | undefined): boolean {
    return proto3.util.equals(GetUserResponse, a, b);
  }
}

/**
 * @generated from message akuity.user.v1.UserInfo
 */
export class UserInfo extends Message<UserInfo> {
  /**
   * @generated from field: string given_name = 1;
   */
  givenName = "";

  /**
   * @generated from field: string family_name = 2;
   */
  familyName = "";

  constructor(data?: PartialMessage<UserInfo>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.UserInfo";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "given_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "family_name", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UserInfo {
    return new UserInfo().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UserInfo {
    return new UserInfo().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UserInfo {
    return new UserInfo().fromJsonString(jsonString, options);
  }

  static equals(a: UserInfo | PlainMessage<UserInfo> | undefined, b: UserInfo | PlainMessage<UserInfo> | undefined): boolean {
    return proto3.util.equals(UserInfo, a, b);
  }
}

/**
 * @generated from message akuity.user.v1.UpdateUserUIPreferencesRequest
 */
export class UpdateUserUIPreferencesRequest extends Message<UpdateUserUIPreferencesRequest> {
  /**
   * @generated from field: google.protobuf.Struct ui_preferences = 1;
   */
  uiPreferences?: Struct;

  constructor(data?: PartialMessage<UpdateUserUIPreferencesRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.UpdateUserUIPreferencesRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "ui_preferences", kind: "message", T: Struct },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateUserUIPreferencesRequest {
    return new UpdateUserUIPreferencesRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateUserUIPreferencesRequest {
    return new UpdateUserUIPreferencesRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateUserUIPreferencesRequest {
    return new UpdateUserUIPreferencesRequest().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateUserUIPreferencesRequest | PlainMessage<UpdateUserUIPreferencesRequest> | undefined, b: UpdateUserUIPreferencesRequest | PlainMessage<UpdateUserUIPreferencesRequest> | undefined): boolean {
    return proto3.util.equals(UpdateUserUIPreferencesRequest, a, b);
  }
}

/**
 * empty 
 *
 * @generated from message akuity.user.v1.UpdateUserUIPreferencesResponse
 */
export class UpdateUserUIPreferencesResponse extends Message<UpdateUserUIPreferencesResponse> {
  constructor(data?: PartialMessage<UpdateUserUIPreferencesResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.UpdateUserUIPreferencesResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateUserUIPreferencesResponse {
    return new UpdateUserUIPreferencesResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateUserUIPreferencesResponse {
    return new UpdateUserUIPreferencesResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateUserUIPreferencesResponse {
    return new UpdateUserUIPreferencesResponse().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateUserUIPreferencesResponse | PlainMessage<UpdateUserUIPreferencesResponse> | undefined, b: UpdateUserUIPreferencesResponse | PlainMessage<UpdateUserUIPreferencesResponse> | undefined): boolean {
    return proto3.util.equals(UpdateUserUIPreferencesResponse, a, b);
  }
}

/**
 * empty 
 *
 * @generated from message akuity.user.v1.DeleteUserRequest
 */
export class DeleteUserRequest extends Message<DeleteUserRequest> {
  constructor(data?: PartialMessage<DeleteUserRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.DeleteUserRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteUserRequest {
    return new DeleteUserRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteUserRequest {
    return new DeleteUserRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteUserRequest {
    return new DeleteUserRequest().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteUserRequest | PlainMessage<DeleteUserRequest> | undefined, b: DeleteUserRequest | PlainMessage<DeleteUserRequest> | undefined): boolean {
    return proto3.util.equals(DeleteUserRequest, a, b);
  }
}

/**
 * empty 
 *
 * @generated from message akuity.user.v1.DeleteUserResponse
 */
export class DeleteUserResponse extends Message<DeleteUserResponse> {
  constructor(data?: PartialMessage<DeleteUserResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.DeleteUserResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): DeleteUserResponse {
    return new DeleteUserResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): DeleteUserResponse {
    return new DeleteUserResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): DeleteUserResponse {
    return new DeleteUserResponse().fromJsonString(jsonString, options);
  }

  static equals(a: DeleteUserResponse | PlainMessage<DeleteUserResponse> | undefined, b: DeleteUserResponse | PlainMessage<DeleteUserResponse> | undefined): boolean {
    return proto3.util.equals(DeleteUserResponse, a, b);
  }
}

/**
 * @generated from message akuity.user.v1.ListNotificationsRequest
 */
export class ListNotificationsRequest extends Message<ListNotificationsRequest> {
  /**
   * @generated from field: optional uint32 limit = 1;
   */
  limit?: number;

  /**
   * @generated from field: optional uint32 offset = 2;
   */
  offset?: number;

  constructor(data?: PartialMessage<ListNotificationsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.ListNotificationsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "limit", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
    { no: 2, name: "offset", kind: "scalar", T: 13 /* ScalarType.UINT32 */, opt: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListNotificationsRequest {
    return new ListNotificationsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListNotificationsRequest {
    return new ListNotificationsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListNotificationsRequest {
    return new ListNotificationsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ListNotificationsRequest | PlainMessage<ListNotificationsRequest> | undefined, b: ListNotificationsRequest | PlainMessage<ListNotificationsRequest> | undefined): boolean {
    return proto3.util.equals(ListNotificationsRequest, a, b);
  }
}

/**
 * @generated from message akuity.user.v1.ListNotificationsResponse
 */
export class ListNotificationsResponse extends Message<ListNotificationsResponse> {
  /**
   * @generated from field: repeated akuity.user.v1.Notification notifications = 1;
   */
  notifications: Notification[] = [];

  /**
   * @generated from field: uint32 count = 2;
   */
  count = 0;

  /**
   * @generated from field: uint32 unread_count = 3;
   */
  unreadCount = 0;

  constructor(data?: PartialMessage<ListNotificationsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.ListNotificationsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "notifications", kind: "message", T: Notification, repeated: true },
    { no: 2, name: "count", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
    { no: 3, name: "unread_count", kind: "scalar", T: 13 /* ScalarType.UINT32 */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ListNotificationsResponse {
    return new ListNotificationsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ListNotificationsResponse {
    return new ListNotificationsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ListNotificationsResponse {
    return new ListNotificationsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ListNotificationsResponse | PlainMessage<ListNotificationsResponse> | undefined, b: ListNotificationsResponse | PlainMessage<ListNotificationsResponse> | undefined): boolean {
    return proto3.util.equals(ListNotificationsResponse, a, b);
  }
}

/**
 * empty 
 *
 * @generated from message akuity.user.v1.WatchNotificationsRequest
 */
export class WatchNotificationsRequest extends Message<WatchNotificationsRequest> {
  constructor(data?: PartialMessage<WatchNotificationsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.WatchNotificationsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WatchNotificationsRequest {
    return new WatchNotificationsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WatchNotificationsRequest {
    return new WatchNotificationsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WatchNotificationsRequest {
    return new WatchNotificationsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: WatchNotificationsRequest | PlainMessage<WatchNotificationsRequest> | undefined, b: WatchNotificationsRequest | PlainMessage<WatchNotificationsRequest> | undefined): boolean {
    return proto3.util.equals(WatchNotificationsRequest, a, b);
  }
}

/**
 * @generated from message akuity.user.v1.WatchNotificationsResponse
 */
export class WatchNotificationsResponse extends Message<WatchNotificationsResponse> {
  /**
   * @generated from field: akuity.user.v1.Notification item = 1;
   */
  item?: Notification;

  /**
   * @generated from field: akuity.types.events.v1.EventType type = 2;
   */
  type = EventType.UNSPECIFIED;

  constructor(data?: PartialMessage<WatchNotificationsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.WatchNotificationsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "item", kind: "message", T: Notification },
    { no: 2, name: "type", kind: "enum", T: proto3.getEnumType(EventType) },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WatchNotificationsResponse {
    return new WatchNotificationsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WatchNotificationsResponse {
    return new WatchNotificationsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WatchNotificationsResponse {
    return new WatchNotificationsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: WatchNotificationsResponse | PlainMessage<WatchNotificationsResponse> | undefined, b: WatchNotificationsResponse | PlainMessage<WatchNotificationsResponse> | undefined): boolean {
    return proto3.util.equals(WatchNotificationsResponse, a, b);
  }
}

/**
 * @generated from message akuity.user.v1.Notification
 */
export class Notification extends Message<Notification> {
  /**
   * @generated from field: string id = 1;
   */
  id = "";

  /**
   * @generated from field: google.protobuf.Timestamp create_time = 2;
   */
  createTime?: Timestamp;

  /**
   * @generated from field: string title = 3;
   */
  title = "";

  /**
   * @generated from field: akuity.user.v1.NotificationCategory category = 4;
   */
  category = NotificationCategory.UNSPECIFIED;

  /**
   * @generated from field: string template = 5;
   */
  template = "";

  /**
   * @generated from field: bool is_read = 6;
   */
  isRead = false;

  /**
   * @generated from field: google.protobuf.Struct metadata = 7;
   */
  metadata?: Struct;

  constructor(data?: PartialMessage<Notification>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.Notification";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "id", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 2, name: "create_time", kind: "message", T: Timestamp },
    { no: 3, name: "title", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 4, name: "category", kind: "enum", T: proto3.getEnumType(NotificationCategory) },
    { no: 5, name: "template", kind: "scalar", T: 9 /* ScalarType.STRING */ },
    { no: 6, name: "is_read", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 7, name: "metadata", kind: "message", T: Struct },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Notification {
    return new Notification().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Notification {
    return new Notification().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Notification {
    return new Notification().fromJsonString(jsonString, options);
  }

  static equals(a: Notification | PlainMessage<Notification> | undefined, b: Notification | PlainMessage<Notification> | undefined): boolean {
    return proto3.util.equals(Notification, a, b);
  }
}

/**
 * @generated from message akuity.user.v1.ReadNotificationsRequest
 */
export class ReadNotificationsRequest extends Message<ReadNotificationsRequest> {
  /**
   * @generated from field: repeated string notification_ids = 1;
   */
  notificationIds: string[] = [];

  constructor(data?: PartialMessage<ReadNotificationsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.ReadNotificationsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "notification_ids", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReadNotificationsRequest {
    return new ReadNotificationsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReadNotificationsRequest {
    return new ReadNotificationsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReadNotificationsRequest {
    return new ReadNotificationsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ReadNotificationsRequest | PlainMessage<ReadNotificationsRequest> | undefined, b: ReadNotificationsRequest | PlainMessage<ReadNotificationsRequest> | undefined): boolean {
    return proto3.util.equals(ReadNotificationsRequest, a, b);
  }
}

/**
 * empty 
 *
 * @generated from message akuity.user.v1.ReadNotificationsResponse
 */
export class ReadNotificationsResponse extends Message<ReadNotificationsResponse> {
  constructor(data?: PartialMessage<ReadNotificationsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.ReadNotificationsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ReadNotificationsResponse {
    return new ReadNotificationsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ReadNotificationsResponse {
    return new ReadNotificationsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ReadNotificationsResponse {
    return new ReadNotificationsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ReadNotificationsResponse | PlainMessage<ReadNotificationsResponse> | undefined, b: ReadNotificationsResponse | PlainMessage<ReadNotificationsResponse> | undefined): boolean {
    return proto3.util.equals(ReadNotificationsResponse, a, b);
  }
}

/**
 * @generated from message akuity.user.v1.UnreadNotificationsRequest
 */
export class UnreadNotificationsRequest extends Message<UnreadNotificationsRequest> {
  /**
   * @generated from field: repeated string notification_ids = 1;
   */
  notificationIds: string[] = [];

  constructor(data?: PartialMessage<UnreadNotificationsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.UnreadNotificationsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "notification_ids", kind: "scalar", T: 9 /* ScalarType.STRING */, repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UnreadNotificationsRequest {
    return new UnreadNotificationsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UnreadNotificationsRequest {
    return new UnreadNotificationsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UnreadNotificationsRequest {
    return new UnreadNotificationsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: UnreadNotificationsRequest | PlainMessage<UnreadNotificationsRequest> | undefined, b: UnreadNotificationsRequest | PlainMessage<UnreadNotificationsRequest> | undefined): boolean {
    return proto3.util.equals(UnreadNotificationsRequest, a, b);
  }
}

/**
 * empty 
 *
 * @generated from message akuity.user.v1.UnreadNotificationsResponse
 */
export class UnreadNotificationsResponse extends Message<UnreadNotificationsResponse> {
  constructor(data?: PartialMessage<UnreadNotificationsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.UnreadNotificationsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UnreadNotificationsResponse {
    return new UnreadNotificationsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UnreadNotificationsResponse {
    return new UnreadNotificationsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UnreadNotificationsResponse {
    return new UnreadNotificationsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: UnreadNotificationsResponse | PlainMessage<UnreadNotificationsResponse> | undefined, b: UnreadNotificationsResponse | PlainMessage<UnreadNotificationsResponse> | undefined): boolean {
    return proto3.util.equals(UnreadNotificationsResponse, a, b);
  }
}

/**
 * empty 
 *
 * @generated from message akuity.user.v1.GetNotificationSettingsRequest
 */
export class GetNotificationSettingsRequest extends Message<GetNotificationSettingsRequest> {
  constructor(data?: PartialMessage<GetNotificationSettingsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.GetNotificationSettingsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetNotificationSettingsRequest {
    return new GetNotificationSettingsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetNotificationSettingsRequest {
    return new GetNotificationSettingsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetNotificationSettingsRequest {
    return new GetNotificationSettingsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: GetNotificationSettingsRequest | PlainMessage<GetNotificationSettingsRequest> | undefined, b: GetNotificationSettingsRequest | PlainMessage<GetNotificationSettingsRequest> | undefined): boolean {
    return proto3.util.equals(GetNotificationSettingsRequest, a, b);
  }
}

/**
 * @generated from message akuity.user.v1.GetNotificationSettingsResponse
 */
export class GetNotificationSettingsResponse extends Message<GetNotificationSettingsResponse> {
  /**
   * @generated from field: akuity.user.v1.NotificationSettings settings = 1;
   */
  settings?: NotificationSettings;

  constructor(data?: PartialMessage<GetNotificationSettingsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.GetNotificationSettingsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "settings", kind: "message", T: NotificationSettings },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): GetNotificationSettingsResponse {
    return new GetNotificationSettingsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): GetNotificationSettingsResponse {
    return new GetNotificationSettingsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): GetNotificationSettingsResponse {
    return new GetNotificationSettingsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: GetNotificationSettingsResponse | PlainMessage<GetNotificationSettingsResponse> | undefined, b: GetNotificationSettingsResponse | PlainMessage<GetNotificationSettingsResponse> | undefined): boolean {
    return proto3.util.equals(GetNotificationSettingsResponse, a, b);
  }
}

/**
 * @generated from message akuity.user.v1.UpdateNotificationSettingsRequest
 */
export class UpdateNotificationSettingsRequest extends Message<UpdateNotificationSettingsRequest> {
  /**
   * @generated from field: akuity.user.v1.NotificationSettings settings = 1;
   */
  settings?: NotificationSettings;

  constructor(data?: PartialMessage<UpdateNotificationSettingsRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.UpdateNotificationSettingsRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "settings", kind: "message", T: NotificationSettings },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateNotificationSettingsRequest {
    return new UpdateNotificationSettingsRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateNotificationSettingsRequest {
    return new UpdateNotificationSettingsRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateNotificationSettingsRequest {
    return new UpdateNotificationSettingsRequest().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateNotificationSettingsRequest | PlainMessage<UpdateNotificationSettingsRequest> | undefined, b: UpdateNotificationSettingsRequest | PlainMessage<UpdateNotificationSettingsRequest> | undefined): boolean {
    return proto3.util.equals(UpdateNotificationSettingsRequest, a, b);
  }
}

/**
 * empty 
 *
 * @generated from message akuity.user.v1.UpdateNotificationSettingsResponse
 */
export class UpdateNotificationSettingsResponse extends Message<UpdateNotificationSettingsResponse> {
  constructor(data?: PartialMessage<UpdateNotificationSettingsResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.UpdateNotificationSettingsResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): UpdateNotificationSettingsResponse {
    return new UpdateNotificationSettingsResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): UpdateNotificationSettingsResponse {
    return new UpdateNotificationSettingsResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): UpdateNotificationSettingsResponse {
    return new UpdateNotificationSettingsResponse().fromJsonString(jsonString, options);
  }

  static equals(a: UpdateNotificationSettingsResponse | PlainMessage<UpdateNotificationSettingsResponse> | undefined, b: UpdateNotificationSettingsResponse | PlainMessage<UpdateNotificationSettingsResponse> | undefined): boolean {
    return proto3.util.equals(UpdateNotificationSettingsResponse, a, b);
  }
}

/**
 * @generated from message akuity.user.v1.NotificationSettings
 */
export class NotificationSettings extends Message<NotificationSettings> {
  /**
   * @generated from field: akuity.user.v1.WebNotificationConfig web = 1;
   */
  web?: WebNotificationConfig;

  /**
   * @generated from field: akuity.user.v1.EmailNotificationConfig email = 2;
   */
  email?: EmailNotificationConfig;

  constructor(data?: PartialMessage<NotificationSettings>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.NotificationSettings";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "web", kind: "message", T: WebNotificationConfig },
    { no: 2, name: "email", kind: "message", T: EmailNotificationConfig },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): NotificationSettings {
    return new NotificationSettings().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): NotificationSettings {
    return new NotificationSettings().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): NotificationSettings {
    return new NotificationSettings().fromJsonString(jsonString, options);
  }

  static equals(a: NotificationSettings | PlainMessage<NotificationSettings> | undefined, b: NotificationSettings | PlainMessage<NotificationSettings> | undefined): boolean {
    return proto3.util.equals(NotificationSettings, a, b);
  }
}

/**
 * @generated from message akuity.user.v1.WebNotificationConfig
 */
export class WebNotificationConfig extends Message<WebNotificationConfig> {
  /**
   * @generated from field: bool disabled = 1;
   */
  disabled = false;

  /**
   * @generated from field: repeated akuity.user.v1.NotificationCategory disabled_categories = 2;
   */
  disabledCategories: NotificationCategory[] = [];

  constructor(data?: PartialMessage<WebNotificationConfig>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.WebNotificationConfig";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "disabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "disabled_categories", kind: "enum", T: proto3.getEnumType(NotificationCategory), repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): WebNotificationConfig {
    return new WebNotificationConfig().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): WebNotificationConfig {
    return new WebNotificationConfig().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): WebNotificationConfig {
    return new WebNotificationConfig().fromJsonString(jsonString, options);
  }

  static equals(a: WebNotificationConfig | PlainMessage<WebNotificationConfig> | undefined, b: WebNotificationConfig | PlainMessage<WebNotificationConfig> | undefined): boolean {
    return proto3.util.equals(WebNotificationConfig, a, b);
  }
}

/**
 * @generated from message akuity.user.v1.EmailNotificationConfig
 */
export class EmailNotificationConfig extends Message<EmailNotificationConfig> {
  /**
   * @generated from field: bool disabled = 1;
   */
  disabled = false;

  /**
   * @generated from field: repeated akuity.user.v1.NotificationCategory disabled_categories = 2;
   */
  disabledCategories: NotificationCategory[] = [];

  constructor(data?: PartialMessage<EmailNotificationConfig>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.EmailNotificationConfig";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "disabled", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "disabled_categories", kind: "enum", T: proto3.getEnumType(NotificationCategory), repeated: true },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): EmailNotificationConfig {
    return new EmailNotificationConfig().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): EmailNotificationConfig {
    return new EmailNotificationConfig().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): EmailNotificationConfig {
    return new EmailNotificationConfig().fromJsonString(jsonString, options);
  }

  static equals(a: EmailNotificationConfig | PlainMessage<EmailNotificationConfig> | undefined, b: EmailNotificationConfig | PlainMessage<EmailNotificationConfig> | undefined): boolean {
    return proto3.util.equals(EmailNotificationConfig, a, b);
  }
}

/**
 * Explicitly empty 
 *
 * @generated from message akuity.user.v1.ResetPasswordRequest
 */
export class ResetPasswordRequest extends Message<ResetPasswordRequest> {
  constructor(data?: PartialMessage<ResetPasswordRequest>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.ResetPasswordRequest";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ResetPasswordRequest {
    return new ResetPasswordRequest().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ResetPasswordRequest {
    return new ResetPasswordRequest().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ResetPasswordRequest {
    return new ResetPasswordRequest().fromJsonString(jsonString, options);
  }

  static equals(a: ResetPasswordRequest | PlainMessage<ResetPasswordRequest> | undefined, b: ResetPasswordRequest | PlainMessage<ResetPasswordRequest> | undefined): boolean {
    return proto3.util.equals(ResetPasswordRequest, a, b);
  }
}

/**
 * Explicitly empty 
 *
 * @generated from message akuity.user.v1.ResetPasswordResponse
 */
export class ResetPasswordResponse extends Message<ResetPasswordResponse> {
  constructor(data?: PartialMessage<ResetPasswordResponse>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.user.v1.ResetPasswordResponse";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): ResetPasswordResponse {
    return new ResetPasswordResponse().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): ResetPasswordResponse {
    return new ResetPasswordResponse().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): ResetPasswordResponse {
    return new ResetPasswordResponse().fromJsonString(jsonString, options);
  }

  static equals(a: ResetPasswordResponse | PlainMessage<ResetPasswordResponse> | undefined, b: ResetPasswordResponse | PlainMessage<ResetPasswordResponse> | undefined): boolean {
    return proto3.util.equals(ResetPasswordResponse, a, b);
  }
}

