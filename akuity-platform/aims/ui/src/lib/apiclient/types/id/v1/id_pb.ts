// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file types/id/v1/id.proto (package akuity.types.id.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum akuity.types.id.v1.Type
 */
export enum Type {
  /**
   * @generated from enum value: UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: ID = 0;
   */
  ID = 0,

  /**
   * @generated from enum value: NAME = 1;
   */
  NAME = 1,
}
// Retrieve enum metadata with: proto3.getEnumType(Type)
proto3.util.setEnumType(Type, "akuity.types.id.v1.Type", [
  { no: 0, name: "UNSPECIFIED" },
  { no: 0, name: "ID" },
  { no: 1, name: "NAM<PERSON>" },
]);

