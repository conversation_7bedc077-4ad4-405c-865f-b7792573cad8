// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file types/misc/v1/misc.proto (package akuity.types.misc.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum akuity.types.misc.v1.GroupByInterval
 */
export enum GroupByInterval {
  /**
   * @generated from enum value: GROUP_BY_INTERVAL_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: GROUP_BY_INTERVAL_MINUTE = 1;
   */
  MINUTE = 1,

  /**
   * @generated from enum value: GROUP_BY_INTERVAL_HOUR = 2;
   */
  HOUR = 2,

  /**
   * @generated from enum value: GROUP_BY_INTERVAL_DAY = 3;
   */
  DAY = 3,

  /**
   * @generated from enum value: GROUP_BY_INTERVAL_WEEK = 4;
   */
  WEEK = 4,

  /**
   * @generated from enum value: GROUP_BY_INTERVAL_MONTH = 5;
   */
  MONTH = 5,

  /**
   * @generated from enum value: GROUP_BY_INTERVAL_YEAR = 6;
   */
  YEAR = 6,
}
// Retrieve enum metadata with: proto3.getEnumType(GroupByInterval)
proto3.util.setEnumType(GroupByInterval, "akuity.types.misc.v1.GroupByInterval", [
  { no: 0, name: "GROUP_BY_INTERVAL_UNSPECIFIED" },
  { no: 1, name: "GROUP_BY_INTERVAL_MINUTE" },
  { no: 2, name: "GROUP_BY_INTERVAL_HOUR" },
  { no: 3, name: "GROUP_BY_INTERVAL_DAY" },
  { no: 4, name: "GROUP_BY_INTERVAL_WEEK" },
  { no: 5, name: "GROUP_BY_INTERVAL_MONTH" },
  { no: 6, name: "GROUP_BY_INTERVAL_YEAR" },
]);

