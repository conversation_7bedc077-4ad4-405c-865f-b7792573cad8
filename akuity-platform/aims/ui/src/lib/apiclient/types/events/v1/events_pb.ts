// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file types/events/v1/events.proto (package akuity.types.events.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import { proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum akuity.types.events.v1.EventType
 */
export enum EventType {
  /**
   * @generated from enum value: EVENT_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: EVENT_TYPE_ADDED = 1;
   */
  ADDED = 1,

  /**
   * @generated from enum value: EVENT_TYPE_MODIFIED = 2;
   */
  MODIFIED = 2,

  /**
   * @generated from enum value: EVENT_TYPE_DELETED = 3;
   */
  DELETED = 3,
}
// Retrieve enum metadata with: proto3.getEnumType(EventType)
proto3.util.setEnumType(EventType, "akuity.types.events.v1.EventType", [
  { no: 0, name: "EVENT_TYPE_UNSPECIFIED" },
  { no: 1, name: "EVENT_TYPE_ADDED" },
  { no: 2, name: "EVENT_TYPE_MODIFIED" },
  { no: 3, name: "EVENT_TYPE_DELETED" },
]);

