// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file types/status/certificate/v1/certificate.proto (package akuity.types.status.certificate.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from message akuity.types.status.certificate.v1.Status
 */
export class Status extends Message<Status> {
  /**
   * @generated from field: bool is_cname_set = 1;
   */
  isCnameSet = false;

  /**
   * @generated from field: bool is_issued = 2;
   */
  isIssued = false;

  /**
   * @generated from field: string message = 3;
   */
  message = "";

  constructor(data?: PartialMessage<Status>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.types.status.certificate.v1.Status";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "is_cname_set", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 2, name: "is_issued", kind: "scalar", T: 8 /* ScalarType.BOOL */ },
    { no: 3, name: "message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Status {
    return new Status().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Status {
    return new Status().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Status {
    return new Status().fromJsonString(jsonString, options);
  }

  static equals(a: Status | PlainMessage<Status> | undefined, b: Status | PlainMessage<Status> | undefined): boolean {
    return proto3.util.equals(Status, a, b);
  }
}

