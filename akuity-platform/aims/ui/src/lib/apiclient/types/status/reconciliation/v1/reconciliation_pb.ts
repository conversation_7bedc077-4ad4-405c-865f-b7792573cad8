// @generated by protoc-gen-es v1.2.0 with parameter "target=ts"
// @generated from file types/status/reconciliation/v1/reconciliation.proto (package akuity.types.status.reconciliation.v1, syntax proto3)
/* eslint-disable */
// @ts-nocheck

import type { BinaryReadOptions, FieldList, JsonReadOptions, JsonValue, PartialMessage, PlainMessage } from "@bufbuild/protobuf";
import { Message, proto3 } from "@bufbuild/protobuf";

/**
 * @generated from enum akuity.types.status.reconciliation.v1.AgentUpdateStatus
 */
export enum AgentUpdateStatus {
  /**
   * @generated from enum value: AGENT_UPDATE_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: AGENT_UPDATE_STATUS_UPDATED = 1;
   */
  UPDATED = 1,

  /**
   * @generated from enum value: AGENT_UPDATE_STATUS_IN_PROGRESS = 2;
   */
  IN_PROGRESS = 2,

  /**
   * @generated from enum value: AGENT_UPDATE_STATUS_DELAYED = 3;
   */
  DELAYED = 3,
}
// Retrieve enum metadata with: proto3.getEnumType(AgentUpdateStatus)
proto3.util.setEnumType(AgentUpdateStatus, "akuity.types.status.reconciliation.v1.AgentUpdateStatus", [
  { no: 0, name: "AGENT_UPDATE_STATUS_UNSPECIFIED" },
  { no: 1, name: "AGENT_UPDATE_STATUS_UPDATED" },
  { no: 2, name: "AGENT_UPDATE_STATUS_IN_PROGRESS" },
  { no: 3, name: "AGENT_UPDATE_STATUS_DELAYED" },
]);

/**
 * @generated from enum akuity.types.status.reconciliation.v1.StatusCode
 */
export enum StatusCode {
  /**
   * @generated from enum value: STATUS_CODE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: STATUS_CODE_SUCCESSFUL = 1;
   */
  SUCCESSFUL = 1,

  /**
   * @generated from enum value: STATUS_CODE_PROGRESSING = 2;
   */
  PROGRESSING = 2,

  /**
   * @generated from enum value: STATUS_CODE_FAILED = 3;
   */
  FAILED = 3,
}
// Retrieve enum metadata with: proto3.getEnumType(StatusCode)
proto3.util.setEnumType(StatusCode, "akuity.types.status.reconciliation.v1.StatusCode", [
  { no: 0, name: "STATUS_CODE_UNSPECIFIED" },
  { no: 1, name: "STATUS_CODE_SUCCESSFUL" },
  { no: 2, name: "STATUS_CODE_PROGRESSING" },
  { no: 3, name: "STATUS_CODE_FAILED" },
]);

/**
 * @generated from message akuity.types.status.reconciliation.v1.Status
 */
export class Status extends Message<Status> {
  /**
   * @generated from field: akuity.types.status.reconciliation.v1.StatusCode code = 1;
   */
  code = StatusCode.UNSPECIFIED;

  /**
   * @generated from field: string message = 2;
   */
  message = "";

  constructor(data?: PartialMessage<Status>) {
    super();
    proto3.util.initPartial(data, this);
  }

  static readonly runtime: typeof proto3 = proto3;
  static readonly typeName = "akuity.types.status.reconciliation.v1.Status";
  static readonly fields: FieldList = proto3.util.newFieldList(() => [
    { no: 1, name: "code", kind: "enum", T: proto3.getEnumType(StatusCode) },
    { no: 2, name: "message", kind: "scalar", T: 9 /* ScalarType.STRING */ },
  ]);

  static fromBinary(bytes: Uint8Array, options?: Partial<BinaryReadOptions>): Status {
    return new Status().fromBinary(bytes, options);
  }

  static fromJson(jsonValue: JsonValue, options?: Partial<JsonReadOptions>): Status {
    return new Status().fromJson(jsonValue, options);
  }

  static fromJsonString(jsonString: string, options?: Partial<JsonReadOptions>): Status {
    return new Status().fromJsonString(jsonString, options);
  }

  static equals(a: Status | PlainMessage<Status> | undefined, b: Status | PlainMessage<Status> | undefined): boolean {
    return proto3.util.equals(Status, a, b);
  }
}

