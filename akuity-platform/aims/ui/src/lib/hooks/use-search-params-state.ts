// TREAT YOUR SEARCH PARAMETERS AS A STATE

import { useMemo, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  z,
  ZodArray,
  ZodCatch,
  ZodObject,
  ZodRawShape,
  ZodRecord,
  ZodType,
  ZodDate,
  ZodBoolean,
  ZodEnum,
  ZodNativeEnum,
  ZodFirstPartyTypeKind
} from 'zod';

import { smallObjectDeepCompare } from '@/utils';

// Type for applied filters
type AppliedFilter<T> = {
  key: keyof T;
  condition: boolean;
  value: string[];
  onClear: () => void;
};

const PAGINATION_FIELDS = ['limit', 'offset'];

// Helper functions to determine Zod types with proper type casting
const getInnerZodType = (zodType: ZodType): ZodType => {
  let current = zodType;

  while (true) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const def = current._def as any;

    // Handle ZodPipeline (from .pipe())
    // ZodPipelineDef has 'out' for the output schema.
    if (def.typeName === ZodFirstPartyTypeKind.ZodPipeline) {
      current = def.out;
      continue;
    }

    // Handle .default(), .optional(), .nullable(), .catch(), .readonly(), .branded()
    // Also handles z.coerce.sometype() because the schema returned by z.coerce.sometype()
    // (e.g., z.coerce.number()) has an _def.innerType pointing to the target ZodType (e.g., ZodNumber).
    if (def.innerType && def.innerType instanceof z.ZodType) {
      current = def.innerType;
      continue;
    }

    // Handle other ZodEffects (e.g., .transform(), .refine())
    // For these, .schema usually points to the input schema of the effect.
    // This should come after specific checks like ZodPipeline and innerType wrappers.
    // ZodDefault and ZodCatch also have 'schema' pointing to the schema before default/catch.
    if (def.schema && def.schema instanceof z.ZodType) {
      current = def.schema;
      continue;
    }

    // If none of the above, we assume it's a base type or a type we don't unwrap further.
    break;
  }
  return current;
};

// Schema-aware formatter for applied filter values
const createSchemaAwareFormatter = <T extends ZodObject<ZodRawShape>>(schema: T) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return (key: keyof z.infer<T>, value: any): string[] => {
    const zodType = schema.shape[key as string];

    const innerType = getInnerZodType(zodType);

    // Handle based on Zod type first, then fallback to runtime type
    if (innerType instanceof ZodBoolean) {
      return value ? ['Yes'] : ['No'];
    }

    if (innerType instanceof ZodArray) {
      if (!Array.isArray(value) || value.length === 0) return [];

      const arrayElementType = getInnerZodType(innerType.element);

      const isEnumArray =
        arrayElementType instanceof ZodEnum || arrayElementType instanceof ZodNativeEnum;

      return value.map((v) => {
        if (isEnumArray && typeof v === 'number') {
          try {
            return String(v);
          } catch {
            return String(v);
          }
        }

        if (typeof v === 'object' && v !== null) {
          return JSON.stringify(v);
        }

        return String(v);
      });
    }

    if (innerType instanceof ZodDate) {
      if (value instanceof Date) {
        return [value.toLocaleDateString()];
      }
      try {
        const date = new Date(value);
        return [date.toLocaleDateString()];
      } catch {
        return [String(value)];
      }
    }

    if (innerType instanceof ZodObject || innerType instanceof ZodRecord) {
      if (typeof value === 'object') {
        return [JSON.stringify(value)];
      }
    }

    if (innerType instanceof ZodEnum || innerType instanceof ZodNativeEnum) {
      // For enums, we might want to provide human-readable labels
      // This could be extended with a mapping function if needed
      return [String(value)];
    }

    // For string, number, etc.
    return [String(value)];
  };
};

export const useSearchParamsState = <T extends ZodObject<ZodRawShape>>(schema: T) => {
  type StateType = z.infer<T>;

  const [search, setSearch] = useSearchParams();

  const defaultState = useMemo(() => schema.parse({}), [schema]);

  const formatAppliedFilterValue = useMemo(() => createSchemaAwareFormatter(schema), [schema]);

  const state: StateType = useMemo(() => {
    const localState = {} as z.infer<T>;

    for (const getter of Object.keys(schema.shape)) {
      const fieldSchema = schema.shape[getter];

      // Use the comprehensive getInnerZodType function to get the base type
      const baseType = getInnerZodType(fieldSchema);

      const isArray = baseType instanceof ZodArray;
      const shouldTransformToJson = baseType instanceof ZodObject || baseType instanceof ZodRecord;

      let transformedStateValue;
      if (isArray) {
        transformedStateValue = fieldSchema.safeParse(search.getAll(getter)).data;
      } else {
        let value = search.get(getter);
        if (shouldTransformToJson && value) {
          try {
            value = JSON.parse(value);
          } catch {
            // If JSON parsing fails, keep the original value
          }
        }

        transformedStateValue = fieldSchema.safeParse(value).data;
      }

      if (transformedStateValue !== undefined) {
        // @ts-expect-error getter is key of zod schema object and thats correct
        localState[getter] = transformedStateValue;
      }
    }

    return localState;
  }, [search, schema]);

  // Determine if any state values are different from their default values
  const hasActiveState = useMemo(
    () => !smallObjectDeepCompare(state, defaultState),
    [state, defaultState]
  );

  // Basic function to update search state with new values
  // Automatically handles pagination reset when non-pagination filters change
  const setSearchState = useCallback(
    (nextState: Partial<StateType>) =>
      setSearch(
        (prevSearch) => {
          const newSearch = new URLSearchParams(prevSearch);
          const stateKeys = Object.keys(nextState);
          const isPaginationOnly =
            stateKeys.length > 0 && stateKeys.every((key) => PAGINATION_FIELDS.includes(key));

          const finalState: Partial<StateType> = { ...nextState };

          // Only reset pagination if the schema has these fields and this isn't a pagination-only update
          if (!isPaginationOnly && stateKeys.length > 0) {
            PAGINATION_FIELDS.forEach((key) => {
              if (key in defaultState) {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                (finalState as Record<string, any>)[key] = (defaultState as Record<string, any>)[
                  key
                ];
              }
            });
          }

          // we will rebuild the value of "key"

          // delete keys first
          for (const [key, _value] of Object.entries(finalState)) {
            const typeSafeValue = _value as StateType[keyof StateType];

            newSearch.delete(key);

            let urlSafeValue: string;

            if (typeSafeValue === null || typeSafeValue === undefined) {
              // null || undefined is remove key operation
              continue;
            }

            // Skip empty objects
            if (
              typeof typeSafeValue === 'object' &&
              !((typeSafeValue as unknown) instanceof Date) &&
              !Array.isArray(typeSafeValue) &&
              Object.keys(typeSafeValue).length === 0
            ) {
              continue;
            }

            // Skip empty strings
            if (typeof typeSafeValue === 'string' && typeSafeValue === '') {
              continue;
            }

            // this doesn't support array of objects yet...
            // when there is requirement for array of objects
            // we simply want to perform zod instance check on this key in zod schema
            type UnsupportedArrayOfObjects = string[];

            if (Array.isArray(typeSafeValue)) {
              for (const arrValue of typeSafeValue as UnsupportedArrayOfObjects) {
                newSearch.append(key, arrValue);
              }
              continue;
            }

            if ((typeSafeValue as unknown) instanceof Date) {
              urlSafeValue = (typeSafeValue as Date).toISOString();
            } else if (typeof typeSafeValue === 'object') {
              urlSafeValue = JSON.stringify(typeSafeValue);
            } else {
              urlSafeValue = String(typeSafeValue);
            }

            if (typeof urlSafeValue === 'string') {
              newSearch.set(key, urlSafeValue);
              continue;
            }
          }

          return newSearch;
        },
        { preventScrollReset: true }
      ),
    [setSearch, defaultState]
  );

  const removeKeysFromSearch = useCallback(
    (keys: Array<keyof StateType>) =>
      setSearch(
        (prevSearch) => {
          const existingSearch = new URLSearchParams(prevSearch);
          for (const k of keys) {
            existingSearch.delete(k as string);
          }
          return existingSearch;
        },
        { preventScrollReset: true }
      ),
    [setSearch]
  );

  // Simplified schema-aware URL builder
  // When overrides are provided, they take precedence over current URL-backed state
  // TODO: For advanced use cases, additional customization can be handled outside this function
  const buildURLSearchParams = useCallback(
    (overrides?: Partial<StateType>) => {
      const searchParams = new URLSearchParams();

      const source = (overrides ? { ...state, ...overrides } : state) as Partial<StateType>;

      Object.entries(source).forEach(([key, value]) => {
        const paramKey = `filters.${key}`;

        // Handle arrays using value
        if (Array.isArray(value)) {
          if (value.length > 0) {
            value.forEach((item) => {
              searchParams.append(paramKey, String(item));
            });
          }
        } else {
          searchParams.set(paramKey, String(value));
        }
      });

      return searchParams;
    },
    [state, schema]
  );

  // Schema-aware applied filters
  const appliedFilters = useMemo<AppliedFilter<StateType>[]>(() => {
    const filters: AppliedFilter<StateType>[] = [];

    Object.keys(state).forEach((key) => {
      if (PAGINATION_FIELDS.includes(key)) {
        return;
      }

      const keyAsStateKey = key as keyof StateType;
      const currentValue = state[keyAsStateKey];
      const defaultValue = defaultState[key];

      // Use the helper for clean comparison
      const isDifferent = !smallObjectDeepCompare(currentValue, defaultValue);

      if (isDifferent) {
        filters.push({
          key: keyAsStateKey,
          condition: true,
          value: formatAppliedFilterValue(keyAsStateKey, currentValue),
          onClear: () => removeKeysFromSearch([keyAsStateKey])
        });
      }
    });

    return filters;
  }, [state, defaultState, removeKeysFromSearch, formatAppliedFilterValue]);

  return {
    state,
    setSearchState,
    removeKeysFromSearch,
    hasActiveState,
    defaultState,
    buildURLSearchParams,
    appliedFilters
  };
};

// URL gives us type string
// coerce turns that string to whatever type we need for example - Number("10")
// now when the value is undefined or nullish it will default to what coerce outputs NOT WHAT WE EXPECT IN .catch block - Number(null) = 0
// this is solved in zod itself - https://zod.dev/?id=you-can-use-pipe-to-fix-common-issues-with-zcoerce
// but this can be re-used in abstracted manner
// you would only require if your zod schema definition starts with coerce - for example z.coerce.number(), z.coerce.boolean()
export const withDefaults = <T extends ZodType>(type: T, def: ZodCatch<T>['_type']) =>
  z.string().pipe(type).catch(def);
