import { PlainMessage } from '@bufbuild/protobuf';
import { useMutation, useQuery } from '@tanstack/react-query';

import {
  UpdateAnnouncementBannerRequest,
  UpdateAnnouncementBannerResponse,
  Banner,
  Audit
} from '../../lib/apiclient/aims/v1/aims_pb';
import { apiFetch } from '../../utils/api-fetch';

export function useUpdateAnnouncementBanner() {
  return useMutation<
    UpdateAnnouncementBannerResponse,
    Error,
    {
      banner: PlainMessage<Banner>;
      audit: PlainMessage<Audit>;
    }
  >({
    mutationFn: async (body) => {
      const res = await apiFetch<UpdateAnnouncementBannerResponse>(`v1/aims/banner`, {
        method: 'POST',
        body: new UpdateAnnouncementBannerRequest(body).toJsonString(),
        ignoreJSONReadFromResponse: true
      });
      return res;
    }
  });
}

export function useDeleteAnnouncementBanner() {
  return useMutation<
    UpdateAnnouncementBannerResponse,
    Error,
    {
      delete: boolean;
      audit: PlainMessage<Audit>;
    }
  >({
    mutationFn: async (body) => {
      const res = await apiFetch<UpdateAnnouncementBannerResponse>(`v1/aims/banner`, {
        method: 'POST',
        body: new UpdateAnnouncementBannerRequest(body).toJsonString(),
        ignoreJSONReadFromResponse: true
      });
      return res;
    }
  });
}

export function useGetAnnouncementBanner() {
  return useQuery({
    queryKey: ['announcement-banner'],
    queryFn: async () => {
      const res = await apiFetch<{ banner: PlainMessage<Banner> }>(`v1/aims/announcement`, {
        method: 'GET'
      });
      return res;
    }
  });
}
