import { useQuery } from '@tanstack/react-query';

import { useInternalAuditLogsFilters } from '@/internal-audit-logs/use-audit-logs-filters';
import { GetInternalAuditLogsResponse } from '@/lib/apiclient/aims/v1/aims_pb';

import { queryKeys } from '../../constants/query-keys';
import { apiFetch } from '../../utils/api-fetch';

export function useListInternalAuditLogs() {
  const { filters, buildURLSearchParams } = useInternalAuditLogsFilters();

  return useQuery<GetInternalAuditLogsResponse>({
    queryKey: queryKeys.internalAuditLogs.list(filters).queryKey,
    queryFn: () => {
      const queryParams = buildURLSearchParams();

      return apiFetch<GetInternalAuditLogsResponse>(
        `v1/aims/internal-audit-logs?${queryParams.toString()}`
      );
    },

    placeholderData: (prev: GetInternalAuditLogsResponse | undefined) => prev
  });
}
