import { useState } from 'react';

type usePaginazationTypes = {
  defaultLimit?: number;
};

export const usePagination = (opts?: usePaginazationTypes) => {
  const [limit, setLimit] = useState(opts?.defaultLimit || 10);
  const [offset, setOffset] = useState(0);
  const currentPage = offset / limit + 1;

  const setPage = (page: number) => {
    setOffset(limit * (page - 1));
  };

  const prevPage = () => {
    if (currentPage === 1) {
      return;
    }

    setOffset(limit * (currentPage - 2));
  };

  const nextPage = () => {
    setOffset(limit * currentPage);
  };

  const onPageSizeChange = (newSize: number) => setLimit(newSize);

  return {
    limit,
    offset,
    setPage,
    onPageSizeChange,
    prevPage,
    nextPage,
    currentPage,
    pageSize: limit
  };
};
