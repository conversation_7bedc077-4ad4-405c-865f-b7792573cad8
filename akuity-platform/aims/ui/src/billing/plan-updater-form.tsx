import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { Button, notification } from 'antd';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { useUpdateOrganizationPlan } from '@/hook/api';
import { AuditForm } from '@/lib/components/shared/audit-form';
import { zodAuditSchema } from '@/utils';

const schema = z.object({
  audit: zodAuditSchema
});

type FormType = z.infer<typeof schema>;

export const PlanUpdaterForm = (props: { orgId: string; newPlan: string }) => {
  const { control, handleSubmit } = useForm<FormType>({
    resolver: zodResolver(schema),
    defaultValues: {
      audit: {
        actor: '',
        reason: ''
      }
    }
  });
  const updatePlanMutation = useUpdateOrganizationPlan();

  const onSubmit = handleSubmit(({ audit }) =>
    updatePlanMutation.mutate(
      {
        organizationId: props.orgId,
        plan: props.newPlan,
        audit: {
          actor: audit.actor,
          reason: audit.reason
        }
      },
      {
        onSuccess: () => {
          notification.success({ message: 'Successfully updated plan' });
        }
      }
    )
  );

  return (
    <>
      <AuditForm control={control} />
      <Button onClick={onSubmit} loading={updatePlanMutation.isPending} type='primary'>
        Update Plan
      </Button>
    </>
  );
};
