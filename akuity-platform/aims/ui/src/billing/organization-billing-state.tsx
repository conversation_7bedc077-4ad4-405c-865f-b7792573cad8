import { PlainMessage } from '@bufbuild/protobuf';
import JsonView from '@uiw/react-json-view';
import { Descriptions } from 'antd';

import { BasicOrganization, GetFeatureGatesResponse } from '@/lib/apiclient/aims/v1/aims_pb';

export const OrganizationBillingState = (props: {
  organization: BasicOrganization;
  featureGates: PlainMessage<GetFeatureGatesResponse>;
}) => {
  const organizationPlan = props.organization.plan;

  return (
    <Descriptions
      bordered
      column={1}
      items={[
        {
          label: 'Current Plan',
          children: (
            <>
              {organizationPlan} {props.organization.billingDetails?.manual && '+ Custom'}
            </>
          )
        },
        {
          label: 'Manual',
          children: props.organization?.billingDetails?.manual ? 'Yes' : 'No'
        },
        {
          label: 'Final features (Plan + Custom set via AIMS or direct Database)',
          children: <JsonView value={props.featureGates.featureGates} collapsed />
        },
        {
          label: 'Final quotas (Plan + Custom set via AIMS or direct Database)',
          children: <JsonView value={props.organization.quota} collapsed />
        }
      ]}
    />
  );
};
