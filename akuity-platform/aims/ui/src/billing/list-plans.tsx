import { faLink } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import JsonView from '@uiw/react-json-view';
import { Card, Col, Row, Tag } from 'antd';

import { Plan } from '@/lib/apiclient/aims/v1/aims_pb';
import { StripeButton } from '@/lib/components/stripe-button';
import { getStripePriceDashboard } from '@/organizations/utils';

export const ListPlans = (props: { plans: Plan[] }) => {
  return (
    <Row wrap gutter={24}>
      {props.plans.map((plan) => (
        <Col key={plan.name} span={8} className='mb-5'>
          <Card
            title={plan.name}
            key={plan.name}
            extra={
              <>
                {plan.default && <Tag color='blue'>Default</Tag>}
                {plan.productId && plan.productId !== '_' && (
                  <StripeButton
                    href={getStripePriceDashboard(plan.productId)}
                    target='_blank'
                    icon={<FontAwesomeIcon icon={faLink} />}
                  >
                    Stripe Price Dashboard
                  </StripeButton>
                )}
              </>
            }
          >
            <JsonView value={{ features: plan.features, value: plan.quotas }} collapsed={1} />
          </Card>
        </Col>
      ))}
    </Row>
  );
};
