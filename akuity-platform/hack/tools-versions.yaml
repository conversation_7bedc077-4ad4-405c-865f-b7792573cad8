# $ make tools-update
---
# https://hub.docker.com/r/liquibase/liquibase/tags
# Make sure the version is available at https://quay.io/repository/akuity/liquibase?tab=tags
# If it's not - https://github.com/akuityio/agent/actions/workflows/retag-images.yaml
liquibase: 4.29

# https://github.com/volatiletech/sqlboiler/releases
sqlboiler: 4.16.2

# https://hub.docker.com/_/postgres/tags
postgres: 17.2

# https://github.com/golangci/golangci-lint/releases
# https://hub.docker.com/r/golangci/golangci-lint/tags
golint: 2.3.1

# https://hub.docker.com/_/golang/tags
golang: 1.24.5

# https://hub.docker.com/r/bufbuild/buf/tags
bufbuild: 1.41.0

# https://hub.docker.com/r/rancher/k3s/tags
k3s: 1.31.3-k3s1

# https://hub.docker.com/r/nvidia/cuda/tags
cuda: 12.6.1-base-ubuntu24.04

# https://github.com/kubernetes-sigs/kustomize/releases
kustomize: 5.4.3

# https://github.com/carvel-dev/ytt/releases
# Make sure to update checksums in hack/download-ytt-ci.sh
ytt: 0.50.0

# https://pkg.go.dev/sigs.k8s.io/controller-tools/cmd/controller-gen
controller_gen: 0.16.2

# https://hub.docker.com/_/debian/tags
debian: 12.11-slim
