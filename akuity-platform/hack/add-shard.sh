#!/bin/bash

set -euxo pipefail

SHARD=${1:-'us1'}
USE_ORBSTACK=${USE_ORBSTACK:-'false'}

export KUBECONFIG=kubeconfig.akuity-$SHARD
k3d cluster create --wait akuity-$SHARD \
    --k3s-arg '--disable=traefik@server:*' \
    -p "443@loadbalancer" \
    --image rancher/k3s:v1.31.3-k3s1
SHARD_PORT=$(docker inspect k3d-akuity-$SHARD-serverlb | jq -r '.[0].NetworkSettings.Ports | to_entries[] | select(.key == "443/tcp") | .value[] | select(.HostIp == "0.0.0.0") | .HostPort')

k3d image import "us-docker.pkg.dev/akuity/akp/akuity-platform:latest" -c akuity-$SHARD

until helm template ./charts/akuity-platform \
  --values ./charts/akuity-platform/test/values-dev.yaml \
  --set image.password=$DOCKER_PASSWORD \
  --set image.repository=us-docker.pkg.dev/akuity/akp/akuity-platform \
  --set image.tag=latest \
  --set platformController.shard=$SHARD \
  --set sso.dex.enabled=false \
  --set sso.oidc.insecureSkipTLSVerify=true \
  --set database.postgres.enabled=false \
  --namespace akuity-platform \
  | kubectl apply -f - ; do sleep 1; echo waiting kubectl apply; done
retVal=$?

# We need to wait CoreDNS to start running then patch the ConfigMap
while true; do
  COREDNS_STATUS=$(kubectl get pod -n kube-system -l k8s-app=kube-dns -o jsonpath="{.items[0].status.phase}" 2>/dev/null || echo "NotFound")
  if [ "$COREDNS_STATUS" == "Running" ]; then
    break
  else
    echo "CoreDNS Pod is not running yet. Waiting..."; sleep 5
  fi
done

mkdir -p dist
cat > dist/temp-patch.yaml << EOL
data:
  Corefile: |
$(kubectl -n kube-system get cm coredns -o json | jq -r '.data.Corefile' | grep -v 'file /etc/coredns/akuity.db akuity-platform' | sed 's/forward/file \/etc\/coredns\/akuity.db akuity-platform\n    forward/g' | sed 's/^/    /')
EOL

kubectl -n kube-system patch cm coredns --type strategic --patch-file dist/temp-patch.yaml

# Then we add a patch which only adds the mappings for akuity-platform
kubectl -n kube-system patch cm coredns --type strategic --patch-file hack/dev/custom-coredns-config.yaml

rm dist/temp-patch.yaml

kubectl -n kube-system patch deployment coredns --type='json' -p='[{"op":"add","path":"/spec/template/spec/volumes/0/configMap/items/-","value":{"key":"akuity.db", "path": "akuity.db"}}]'

kubectl -n akuity-platform apply -f hack/dev/postgres-service-shard.yaml

# Add the proxy for the shard - refer to PR #6750
if [[ "$USE_ORBSTACK" == "false" ]]; then
export KUBECONFIG=kubeconfig.akuity-platform
export KUBECONTEXT=k3d-akuity-platform
export EXTERNAL_HOST=host.k3d.internal
else
export KUBECONFIG=~/.kube/config
export KUBECONTEXT=orbstack
export EXTERNAL_HOST=host.orb.internal
fi

cat ./hack/dev/shard-proxy.yaml | SHARD=$SHARD SHARD_PORT=$SHARD_PORT EXTERNAL_HOST=$EXTERNAL_HOST envsubst '$SHARD $SHARD_PORT $EXTERNAL_HOST'  | kubectl --context $KUBECONTEXT apply -f -
TRAEFIK_ARGS=$(kubectl --context $KUBECONTEXT get deployment traefik -n traefik-external -o jsonpath='{.spec.template.spec.containers[0].args}')
if [[ "$TRAEFIK_ARGS" != *"--providers.kubernetescrd.allowExternalNameServices=true"* ]]; then
  kubectl --context $KUBECONTEXT patch deployment traefik -n traefik-external --type json -p '[{"op": "add", "path": "/spec/template/spec/containers/0/args/-", "value": "--providers.kubernetescrd.allowExternalNameServices=true"}]'
fi
