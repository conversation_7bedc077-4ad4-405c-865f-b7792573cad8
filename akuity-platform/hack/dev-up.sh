#!/bin/bash

set -euxo pipefail

if ! grep -q "portal-server.akuity-platform" /etc/hosts; then
    echo "WARNING: portal-server.akuity-platform has not been set in /etc/hosts!  To fix that: echo '127.0.0.1       portal-server.akuity-platform' | sudo tee -a /etc/hosts > /dev/null"
fi

if ! grep -q "dex.akuity-platform" /etc/hosts; then
    echo "WARNING: dex.akuity-platform has not been set in /etc/hosts! To fix that: echo '127.0.0.1       dex.akuity-platform' | sudo tee -a  /etc/hosts > /dev/null"
fi

if ! grep -q "host.k3d.internal" /etc/hosts; then
    echo "WARNING: host.k3d.internal has not been set in /etc/hosts! To fix that: echo '0.0.0.0       host.k3d.internal' | sudo tee -a  /etc/hosts > /dev/null"
fi

REF=${REF:-'main'}

if [[ "$USE_ORBSTACK" == "false" ]]; then
export KUBECONFIG=kubeconfig.akuity-platform
set +e
k3d cluster create akuity-platform \
  --k3s-arg '--kubelet-arg=eviction-hard=imagefs.available<1%,nodefs.available<1%@agent:*' \
  --k3s-arg '--kubelet-arg=eviction-minimum-reclaim=imagefs.available=1%,nodefs.available=1%@agent:*' \
  --k3s-arg '--kubelet-arg=eviction-hard=imagefs.available<1%,nodefs.available<1%@server:0' \
  --k3s-arg '--kubelet-arg=eviction-minimum-reclaim=imagefs.available=1%,nodefs.available=1%@server:0' \
  --k3s-arg '--disable=traefik@server:*'\
  -p "80:80@loadbalancer" \
  -p "443:443@loadbalancer" \
  -p "5432:5432@loadbalancer" \
  --image rancher/k3s:v1.31.3-k3s1
fi

set -e
TAG=latest IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform make image
if [[ "$USE_ORBSTACK" == "false" ]]; then
k3d image import "us-docker.pkg.dev/akuity/akp/akuity-platform:latest" -c "${DEV_CLUSTER}"
fi

until helm template ./charts/akuity-platform \
  --values ./charts/akuity-platform/test/values-dev.yaml \
  --set image.password="$DOCKER_PASSWORD" \
  --set image.repository=us-docker.pkg.dev/akuity/akp/akuity-platform \
  --set image.tag=latest \
  --namespace akuity-platform \
  | kubectl apply -f - ; do sleep 1; echo waiting kubectl apply; done
retVal=$?

set -eo pipefail

kubectl get ns "${DEV_NAMESPACE}" > /dev/null 2>&1 || kubectl create ns "${DEV_NAMESPACE}"

kubectl -n akuity-platform apply -f hack/dev/postgres-service-lb.yaml

kubectl wait --timeout=600s deployment -n akuity-platform portal-server --for condition=Available=True

# We need to wait CoreDNS to start running then patch the ConfigMap
while true; do
  COREDNS_STATUS=$(kubectl get pod -n kube-system -l k8s-app=kube-dns -o jsonpath="{.items[0].status.phase}" 2>/dev/null || echo "NotFound")
  if [ "$COREDNS_STATUS" == "Running" ]; then
    break
  else
    echo "CoreDNS Pod is not running yet. Waiting..."; sleep 5
  fi
done

mkdir -p dist
cat > dist/temp-patch.yaml << EOL
data:
  Corefile: |
$(kubectl -n kube-system get cm coredns -o json | jq -r '.data.Corefile' | grep -v 'file /etc/coredns/akuity.db akuity-platform' | sed 's/forward/file \/etc\/coredns\/akuity.db akuity-platform\n    forward/g' | sed 's/^/    /')
EOL

kubectl -n kube-system patch cm coredns --type strategic --patch-file dist/temp-patch.yaml

# Then we add a patch which only adds the mappings for akuity-platform
kubectl -n kube-system patch cm coredns --type strategic --patch-file hack/dev/custom-coredns-config.yaml

rm dist/temp-patch.yaml

kubectl -n kube-system patch deployment coredns --type='json' -p='[{"op":"add","path":"/spec/template/spec/volumes/0/configMap/items/-","value":{"key":"akuity.db", "path": "akuity.db"}}]'

# Need /etc/host mappings for the following:
# 127.0.0.1 portal-server.akuity-platform
# 127.0.0.1 dex.akuity-platform
# 0.0.0.0 host.k3d.internal
# kubectl port-forward svc/portal-server 9090:9090 -n akuity-platform &
# open http://portal-server.akuity-platform:9090

hack/add-customer.sh
