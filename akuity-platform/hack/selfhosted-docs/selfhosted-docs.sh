#!/bin/bash

set -xe

versionlte() {
    [  "$1" = "`echo -e "$1\n$2" | sort -V | head -n1`" ]
}

changelog() {
    RELEASE_NOTES=""
    ARGOCD_AK_LIST=""
    ARGOCD_OSS_LIST=""
    KARGO_AK_LIST=""
    KARGO_OSS_LIST=""
    AGENT_VERS=""
    for ITEM in $(gh release list --exclude-drafts --exclude-pre-releases --limit 10000 | tail -n +1 | awk '{print $1}')
    do
        RELEASE_NOTES+="### ${ITEM}
$(gh release view ${ITEM} -R **************:akuityio/akuity-platform.git --json body -q .body | \
            grep -v -e '* chore' | \
            grep -v -e 'Full Changelog' | \
            sed 's/</\\</g' | \
            sed 's/>/\\>/g' | \
            sed 's/by @.*$//g' | \
            sed 's/^#/###&/')

"
        # Agent + Argo CD Versions
        if versionlte "0.11.0" ${ITEM:1}; then
            VERS=$(docker run --rm -t us-docker.pkg.dev/akuity/akp-sh/akuity-platform:${ITEM:1} sh -c "akputil agent supported-versions | sed -n '/^[0-9]\+\.[0-9]\+\.[0-9]\+$/p' | paste -sd ',' -" | sed "s/[^,]*/ \`&\`/g" | sed 's/..$//')
            AGENT_VERS+="|\`${ITEM}\`|${VERS}\`|
"
            ARGOCD_VERS=$(docker run --rm -t us-docker.pkg.dev/akuity/akp-sh/akuity-platform:${ITEM:1} akputil agent supported-argo-versions | grep '"version":')
            ARGOCD_AK_VERS=$(echo "${ARGOCD_VERS}"  | grep -v 'latest' | grep -Po '\d+.\d+.\d+-ak.\d+' | sort -Vr | xargs printf -- '`v%s`' | sed 's/``/`, `/g')
            ARGOCD_AK_LIST+="|\`${ITEM}\`|${ARGOCD_AK_VERS}|
"
            ARGOCD_OSS_VERS=$(echo "${ARGOCD_VERS}" | grep -v '\-ak\|latest' | grep -Po '\d+.\d+.\d+' | sort -Vr | xargs printf -- '`v%s`' | sed 's/``/`, `/g')
            ARGOCD_OSS_LIST+="|\`${ITEM}\`|${ARGOCD_OSS_VERS}|
"
        fi

        # Kargo Versions
        if versionlte "0.24.0" ${ITEM:1}; then
            KARGO_VERS=$(go run ./cmd/akputil/main.go agent supported-kargo-versions | grep '"version":' )
            KARGO_VERS=$(docker run --rm -t us-docker.pkg.dev/akuity/akp-sh/akuity-platform:${ITEM:1} akputil agent supported-kargo-versions | grep '"version":' )
            KARGO_AK_VERS=$(echo "${KARGO_VERS}"  | grep -v 'latest\|unstable' | grep -Po '\d+.\d+.\d+-ak.\d+' | sort -Vr | xargs printf -- '`v%s`' | sed 's/``/`, `/g')
            KARGO_AK_LIST+="|\`${ITEM}\`|${KARGO_AK_VERS}|
"
            KARGO_OSS_VERS=$(echo "${KARGO_VERS}" | grep -v '\-ak\|latest\|unstable' | grep -Po '\d+.\d+.\d+' | sort -Vr | xargs printf -- '`v%s`' | sed 's/``/`, `/g')
            KARGO_OSS_LIST+="|\`${ITEM}\`|${KARGO_OSS_VERS}|
"
        fi

    done

cat >${1}/index.mdx <<EOL
---
description: Information on the latest updates and additions to the Self-Hosted Akuity Platform
title: Self-Hosted Changelog
---

Information on the latest updates and additions to the Self-Hosted Akuity Platform.

## Versions
${RELEASE_NOTES}
EOL


    cat >${1}/20-argocd-compatibility.md <<EOL
---
description: Akuity Platform Self-Hosted Argo CD Compatibility Table
title: Akuity Argo CD Compatibility
sidebar_label: Argo CD Compatibility
---

## Akuity Patched Argo CD Versions
|Akuity Platform Version|Argo CD Versions|
|-|-|
${ARGOCD_AK_LIST}

## Open Source Argo CD Versions
|Akuity Platform Version|Argo CD Versions|
|-|-|
${ARGOCD_OSS_LIST}
EOL

    cat >${1}/30-kargo-compatibility.md <<EOL
---
description: Akuity Platform Self-Hosted Kargo Compatibility Table
title: Akuity Kargo Compatibility
sidebar_label: Kargo Compatibility
---

## Akuity Patched Kargo Versions
|Akuity Platform Version|Kargo Versions|
|-|-|
${KARGO_AK_LIST}

## Open Source Kargo Versions
|Akuity Platform Version|Kargo Versions|
|-|-|
${KARGO_OSS_LIST}
EOL

    cat >${1}/10-agent-compatibility.md <<EOL
---
description: Akuity Platform Self-Hosted Agent Compatibility Table
title: Akuity Agent Compatibility
sidebar_label: Agent Compatibility
---

|Akuity Platform Version|Agent Versions|
|-|-|
${AGENT_VERS}
EOL

}

chart-docs() {
    readme-generator --values "${PWD}/charts/akuity-platform/values.yaml" --readme "${PWD}/charts/akuity-platform/README.md" --config "${PWD}/hack/selfhosted-docs/readme-generator-config.json"
    sed -i "/<!-- README.md -->/q" $1
    cat charts/akuity-platform/README.md >> $1
}

case ${1} in
changelog)
    changelog ${2}
    ;;
chart-docs)
    chart-docs ${2}
    ;;
*)
    exit 1
    ;;
esac
