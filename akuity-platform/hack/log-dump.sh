#!/bin/bash

LOG_DIR="/tmp/logs"
EXCLUDED_SECRETS=("akuity-pullsecrets" "platform-image-pullsecret")

# Function to create directory if it doesn't exist
create_directory() {
    mkdir -p "$1"
}

create_directory "$LOG_DIR"

decode_secret() {
    NAMESPACE=$1
    SECRET_NAME=$2

    SECRET_KEYS=$(kubectl get secret "$SECRET_NAME" -n "$NAMESPACE" -o jsonpath='{.data}' | jq -r 'keys_unsorted[]')

    for key in $SECRET_KEYS; do
        # This handles case where secret name contains '.' e.g: .dockerconfigjson or kubeconfig.yaml
        safe_key=$(echo "$key" | sed 's/\./\\./g')
        SECRET_VALUE=$(kubectl get secret "$SECRET_NAME" -n "$NAMESPACE" -o "jsonpath={.data['$safe_key']}" | base64 --decode 2>/dev/null)
        if [ -n "$SECRET_VALUE" ]; then
            echo "$key: $SECRET_VALUE"
        else
            echo "Info: No value found for key $key in secret $SECRET_NAME in namespace $NAMESPACE. Skipping..." >&2
        fi
    done
}

fetch_logs() {
    NAMESPACE=$1
    TIMESTAMP=$(date "+%Y%m%d%H%M%S")
    echo "Processing logs for namespace: $NAMESPACE"

    create_directory "$LOG_DIR/configmaps"
    create_directory "$LOG_DIR/secrets"
    create_directory "$LOG_DIR/deployments"
    create_directory "$LOG_DIR/pods"
    create_directory "$LOG_DIR/events"

    CONFIGMAPS=$(kubectl get configmaps -n "$NAMESPACE" -o jsonpath='{.items[*].metadata.name}')
    for configmap in $CONFIGMAPS; do
        kubectl get configmap "$configmap" -n "$NAMESPACE" -o jsonpath='{.data}' > "$LOG_DIR/configmaps/configmap-$NAMESPACE-$configmap-$TIMESTAMP.yaml.log"
    done

    SECRETS=$(kubectl get secrets -n "$NAMESPACE" -o jsonpath='{.items[?(@.type!="kubernetes.io/service-account-token")].metadata.name}')
    for secret in $SECRETS; do
        if [[ " ${EXCLUDED_SECRETS[@]} " =~ " $secret " ]]; then
            echo "Skipping processing of secret $secret in namespace $NAMESPACE."
        else
            SECRET_DATA=$(kubectl get secret "$secret" -n "$NAMESPACE" -o jsonpath='{.data}' 2>/dev/null)
            if [ -n "$SECRET_DATA" ]; then
                DECODED_DATA=$(decode_secret "$NAMESPACE" "$secret")
                if [ -n "$DECODED_DATA" ]; then
                    echo "$DECODED_DATA" > "$LOG_DIR/secrets/secret-$NAMESPACE-$secret-$TIMESTAMP.log"
                fi
            fi
        fi
    done

    DEPLOYMENTS=$(kubectl get deployments -n "$NAMESPACE" -o jsonpath='{.items[*].metadata.name}')
    for deployment in $DEPLOYMENTS; do
        kubectl logs -n "$NAMESPACE" deployment/"$deployment" > "$LOG_DIR/deployments/deployment-$NAMESPACE-$deployment-$TIMESTAMP.log"
    done

    PODS=$(kubectl get pods -n "$NAMESPACE" -o jsonpath='{.items[*].metadata.name}')
    for pod in $PODS; do
        kubectl logs -n "$NAMESPACE" "$pod" > "$LOG_DIR/pods/pod-$NAMESPACE-$pod-$TIMESTAMP.log"
    done

    kubectl get events -n "$NAMESPACE" > "$LOG_DIR/events/events-$NAMESPACE-$TIMESTAMP.log"

    echo "Logs for namespace $NAMESPACE dumped successfully to $LOG_DIR at $TIMESTAMP"
}

for ns in $(kubectl get ns -o=name | cut -d'/' -f2); do
    fetch_logs "$ns"
done
