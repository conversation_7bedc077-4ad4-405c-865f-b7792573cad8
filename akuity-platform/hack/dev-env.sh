#!/bin/bash

set -euo pipefail

source "$(dirname "$0")/db-connection.sh"

if [[ "${DEV_PORTAL_COMPONENTS}" = "true" ]]; then
  bash -c "ytt --data-value namespace='${DEV_NAMESPACE}' -f manifests/schema.yaml -f manifests/values.yaml -f manifests/apps" | kubectl apply -f -
elif [ "${DEV_PORTAL_COMPONENTS}" = "false" ]; then
  bash -c "ytt --data-value namespace='${DEV_NAMESPACE}' -f manifests/schema.yaml -f manifests/values.yaml -f manifests/apps/dev -f manifests/apps/overlays" | kubectl apply -f -
else
  k3d image import "akuity-platform:latest" -c "${DEV_CLUSTER}"
  bash -c "ytt --data-value namespace='${DEV_NAMESPACE}' -f manifests/schema.yaml -f manifests/values.yaml -f manifests/values-local-images.yaml -f manifests/apps" | kubectl apply -f -
fi

kubectl get deploy -n "${DEV_NAMESPACE}"
kubectl get pods   -n "${DEV_NAMESPACE}"
