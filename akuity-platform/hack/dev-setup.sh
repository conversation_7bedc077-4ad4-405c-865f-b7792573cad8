#!/bin/bash

if [ -z "${DOCKER_USERNAME}" ] || [ -z "${DOCKER_PASSWORD}" ]; then
    if [ -z "$(kubectl get secret akuity-pullsecrets -n "${DEV_NAMESPACE}")" ]; then
      echo "Please export DOCKER_USERNAME and DOCKER_PASSWORD"
      exit 0
    fi
fi

set -euo pipefail

kubectl get ns "${DEV_NAMESPACE}" > /dev/null 2>&1 || kubectl create ns "${DEV_NAMESPACE}"

CFG_docker__server=https://us-docker.pkg.dev \
CFG_docker__username=${DOCKER_USERNAME} \
CFG_docker__password=${DOCKER_PASSWORD} \
ytt \
  -f manifests/schema.yaml \
  -f manifests/values.yaml \
  -f manifests/setup \
  --data-value namespace="${DEV_NAMESPACE}" \
  --data-values-env CFG | kubectl apply -f -


# install dragonfly operator for local dev
kubectl apply -f https://raw.githubusercontent.com/dragonflydb/dragonfly-operator/refs/tags/v1.2.1/manifests/dragonfly-operator.yaml
echo "Waiting for dragonfly-operator to be available..."
kubectl wait --for=condition=Available deployment/dragonfly-operator-controller-manager -n dragonfly-operator-system --timeout=300s