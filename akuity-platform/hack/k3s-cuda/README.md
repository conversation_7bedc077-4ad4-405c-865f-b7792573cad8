# CUDA-Enabled k3s Docker Image with NVIDIA GPU Support

Reference: https://k3d.io/v5.7.3/usage/advanced/cuda/#dockerfile

This directory provides a special Dockerfile for building a CUDA-enabled k3s image. This custom Dockerfile is based on the official k3s image, with added support for NVIDIA Container Toolkit and the NVIDIA device plugin, enabling Kubernetes to schedule and manage GPU resources. By using this k3s Dockerfile, we can create a CUDA-enabled user cluster by running `CUDA=true ./hack/add-customer.sh`. For more information, please refer to the `add-customer.sh` script.
