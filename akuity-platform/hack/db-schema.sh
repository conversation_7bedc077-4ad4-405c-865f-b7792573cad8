#!/bin/bash
LIQUIBASE_IMAGE="${LIQUIBASE_IMAGE:-quay.io/akuity/liquibase:4.29}"
set -euo pipefail

rm -rf vendor

if [[ -z "${DB_HOST-}" ]]; then
  source "$(dirname "$0")/db-connection.sh"
fi

ytt --data-value namespace="${DEV_NAMESPACE}" -f manifests/schema.yaml -f manifests/values.yaml -f manifests/apps/overlays -f manifests/postgres | kubectl apply -f -

set -x
kubectl wait --for condition=available deployment --all -n "${DEV_NAMESPACE}" --timeout=60s
kubectl wait --for=condition=Ready     pod        --all -n "${DEV_NAMESPACE}" --timeout=60s
set +x

OVERRIDES=$(cat <<-END
{
  "kind": "Pod",
  "apiVersion": "v1",
  "metadata": {
    "name": "init-db"
  },
  "spec": {
    "imagePullSecrets": [
      {
        "name": "akuity-pullsecrets"
      }
    ]
  }
}
END
)

if [[ ${DEV_DB_SCHEMA} = "create" ]]; then
  set -x
  (kubectl get pods init-db -n "${DEV_NAMESPACE}" > /dev/null 2>&1 && kubectl delete pod init-db -n "${DEV_NAMESPACE}" || true) &&
    go run github.com/akuityio/akuity-platform/hack/resources schema |
      kubectl run init-db --overrides "$OVERRIDES" --image=postgres:17.2 -n "${DEV_NAMESPACE}" --rm --command psql -i -- "$DB_URL" -f -;
  set +x
fi

if [[ ${DEV_DB_SCHEMA} = "upgrade" ]]; then
  set -x
  (kubectl get pods init-db -n "${DEV_NAMESPACE}" > /dev/null 2>&1 && kubectl delete pod init-db -n "${DEV_NAMESPACE}" || true) &&
  go run github.com/akuityio/akuity-platform/hack/resources changelogs-tar |
    kubectl run init-db --overrides "$OVERRIDES" -n "${DEV_NAMESPACE}" \
      --rm --command \
      sh -i --image=${LIQUIBASE_IMAGE} -- \
      -c "cat > /tmp/changelog.tar && cd /tmp && tar -xf ./changelog.tar && docker-entrypoint.sh --url=jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME} --username=${DB_USER} --password=${DB_PASS} --classpath=/tmp --changeLogFile=changelog-root.yaml --log-format=TEXT update"
  set +x
fi

set -x
kubectl rollout status deployment/postgres     -n "${DEV_NAMESPACE}"
kubectl get deploy -n "${DEV_NAMESPACE}"
kubectl get pods   -n "${DEV_NAMESPACE}"
kubectl wait --for=delete pod/init-db -n "${DEV_NAMESPACE}" --timeout=60s
set +x
