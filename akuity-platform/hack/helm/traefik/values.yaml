# https://github.com/traefik/traefik-helm-chart/blob/master/traefik/values.yaml
ingressRoute:
  dashboard:
    enabled: false

# Traefik 2.9 now uses TLS v1.2 by default
tlsOptions:
  default:
    minVersion: VersionTLS12
    cipherSuites:
      - TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256
      - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
      - TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305
      - TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305

# This is handled in our chart
# tlsStore:
#   default:
#     defaultCertificate:
#       secretName: akuity-platform-tls
#     certificates:
#       - secretName: akuity-platform-cd-tls
#       - secretName: akuity-platform-cdsvcs-tls

service:
  annotations:
    # Instructs the AWS Load Balancer Controller to reconcile this Service instead of the in-tree AWS cloud provider
    # https://kubernetes-sigs.github.io/aws-load-balancer-controller/v2.4/guide/service/annotations/#legacy-cloud-provider
    service.beta.kubernetes.io/aws-load-balancer-type: external
    # Route traffic directly to the pod IP (as opposed to all ec2 instances)
    service.beta.kubernetes.io/aws-load-balancer-nlb-target-type: ip
    # Preserve client IP to support Traefik's IP allowlisting
    service.beta.kubernetes.io/aws-load-balancer-target-group-attributes: preserve_client_ip.enabled=true
    # This is a public facing load balancer
    service.beta.kubernetes.io/aws-load-balancer-scheme: internet-facing
    # Send TCP traffic to backend instead of SSL
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: tcp
    # NOTE: Some documents/posts suggest to use the following annotation:
    # service.beta.kubernetes.io/aws-load-balancer-proxy-protocol: '*'
    # However, base on our testing, this causes ERR_SSL_PROTOCOL_ERROR and should not be used.

  spec:
    # Our NLB is configured to use Pod IP targetting (as opposed to instance targetting), so we can
    # leverage `externalTrafficPolicy: Local` effectively. We also need to set this in order to
    # preserves source IP. See:
    # https://www.asykim.com/blog/deep-dive-into-kubernetes-external-traffic-policies
    externalTrafficPolicy: Local

ingressClass:
  enabled: true
  isDefaultClass: false

providers:
  kubernetesIngress:
    # This configuration adds the `--providers.kubernetesingress.ingressendpoint.publishedservice=<namespace>/<service>`
    # CLI flag to traefik startup. It allows traefik to update ingress.status with hostname of the
    # load balancer for external-dns to work properly.
    publishedService:
      enabled: true
    ingressClass: traefik-external

  kubernetesCRD:
    allowCrossNamespace: true

ports:
  web:
    # We run Traefik behind an NLB. NLBs are layer 4 load balancers, not L7 (like classic ELB / ALB)
    # so NLBs cannot respond with L7 HTTP redirects to 443. When an unencrypted HTTP request is
    # received by an NLB, the request will flow as-is to the backend. This option will instruct
    # traefik to respond with a 301 redirect to websecure port when it receives the request:
    # https://docs.traefik.io/routing/entrypoints/#redirection
    redirections:
      entryPoint:
        to: websecure
        scheme: https

  websecure:
    # For HTTPS traffic, we have two choices:
    #    * Terminate TLS at the NLB load balancer (and leverage AWS ACM for certificates)
    #    * Terminate TLS at Ingress (and serve our own certificate e.g. from Let's Encrypt)
    # If TLS is terminated at the NLB, we must:
    #    1. Set 'tls.enabled=false' so that Traefik will not attempt to decrypt unencrypted traffic
    #       passed from the NLB load balancer.
    #    2. Annotate the traefik service with the ACM certificate ARN
    # If TLS is terminated at Ingress, we must:
    #    1. Create a TLSStore named 'default' in Traefik namespace
    #    2. Set 'tls.enabled=true' to indicate Traefik should serve TLS
    #    3. Ensure no ACM certificate annotation exists on the Traefik Service
    # https://doc.traefik.io/traefik/routing/entrypoints/#tls
    # https://doc.traefik.io/traefik/routing/routers/#domains
    # Our current architecture is that we must terminate TLS at Traefik Ingress. This is because
    # websocket connections (required by chisel) do not seem to upgrade properly when TLS is
    # terminated at NLB.
    tls:
      enabled: true
logs:
  general:
    format: json

# Unsets defaults metrics reporting and new version check
globalArguments: []

# # https://doc.traefik.io/traefik/reference/static-configuration/cli/
additionalArguments:
  # skip tls verify and allow traefik talk to k3s proxy pod using HTTPs
  # https://github.com/akuityio/agent/pull/660
  - --serversTransport.insecureSkipVerify=true
# - --accesslog=true"
# - --accesslog.format=json
# - --accesslog.fields.headers.defaultmode=keep
# - --accesslog.fields.headers.names.X-Forwarded-For=keep
