apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: traefik-external

helmCharts:
- name: traefik
  # Ensure CRDs match the version used!
  # https://helm.traefik.io/traefik/index.yaml => "traefik:"
  version: 35.0.1
  repo: https://helm.traefik.io/traefik
  releaseName: traefik
  includeCRDs: false
  namespace: traefik-external
  valuesFile: values.yaml
  # Ensure CRDs match the version used!
  # https://github.com/traefik/traefik-helm-chart/blob/master/traefik-crds/values.yaml
- name: traefik-crds
  version: 1.6.0
  repo: https://helm.traefik.io/traefik
  valuesInline:
    traefik: true
    gatewayAPI: false
    hub: false
    deleteOnUninstall: true
patches:
- target:
    kind: CustomResourceDefinition
  patch: |-
    - op: remove
      path: /metadata/annotations/app.kubernetes.io~1managed-by
- target:
    labelSelector: app.kubernetes.io/name=traefik
    labelSelector: app.kubernetes.io/managed-by=Helm
  patch: |-
    - op: remove
      path: /metadata/labels
- target:
    kind: IngressClass
    name: traefik
  patch: |-
    - op: replace
      path: /metadata/name
      value: traefik-external
- target:
    kind: Deployment
    name: traefik
  patch: |-
    - op: add
      path: /spec/template/spec/topologySpreadConstraints
      value: null
