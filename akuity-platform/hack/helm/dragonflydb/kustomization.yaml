apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: dragonfly-operator-system

resources:
    # https://github.com/dragonflydb/dragonfly-operator
  - https://raw.githubusercontent.com/dragonflydb/dragonfly-operator/refs/tags/v1.2.1/manifests/dragonfly-operator.yaml

patches:
- patch: |-
    - op: replace
      path: /spec/template/spec/containers/0/resources
      value: kube-rbac-proxy-replace-me
  target:
    version: v1
    kind: Deployment
    name: dragonfly-operator-controller-manager
- patch: |-
    - op: replace
      path: /spec/template/spec/containers/1/resources
      value: manager-replace-me
  target:
    version: v1
    kind: Deployment
    name: dragonfly-operator-controller-manager