#!/bin/bash

export DB_HOST=postgres.akuity-platform.svc.cluster.local
export DB_PORT=5432
export DB_NAME=postgres
export DB_USER=postgres
export DB_PASS=postgres
export DB_URL="postgresql://${DB_USER}:${DB_PASS}@${DB_HOST}:${DB_PORT}/${DB_NAME}"
export PORTAL_DB_CONNECTION="user=${DB_USER} dbname=${DB_NAME} host=${DB_HOST} port=${DB_PORT} sslmode=disable password=${DB_PASS}"
export PORTAL_RO_DB_CONNECTION="user=${DB_USER} dbname=${DB_NAME} host=${DB_HOST} port=${DB_PORT} sslmode=disable password=${DB_PASS}"
