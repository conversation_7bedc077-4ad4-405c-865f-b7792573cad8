# Buf
# https://hub.docker.com/r/bufbuild/buf/tags
FROM bufbuild/buf:1.41.0 AS buf

# golangci-lint
# https://hub.docker.com/r/golangci/golangci-lint/tags
FROM golangci/golangci-lint:v2.3.1 AS golangci-lint

# Terraform
FROM hashicorp/terraform:1.12 AS terraform

# Platform (Non UI)
# https://hub.docker.com/_/golang/tags
FROM golang:1.24.5-bullseye AS platform

COPY --from=buf /usr/local/bin/buf /usr/local/bin/buf
COPY --from=golangci-lint /usr/bin/golangci-lint /usr/local/bin/golangci-lint
COPY --from=terraform /bin/terraform /usr/local/bin/terraform

RUN go install github.com/akuity/grpc-gateway-client/protoc-gen-grpc-gateway-client@latest
# https://pkg.go.dev/sigs.k8s.io/controller-tools/cmd/controller-gen
RUN go install sigs.k8s.io/controller-tools/cmd/controller-gen@v0.16.2

# Install the patch command (used in the terraform generation step)
RUN apt-get update && \
    apt-get install -y patch && \
    rm -rf /var/lib/apt/lists/*

# UI
# https://hub.docker.com/_/node/tags
FROM node:22.3.0-bullseye AS ui

RUN corepack enable && \
  corepack prepare pnpm@9.3.0 --activate
RUN npm install -g @bitnami/readme-generator-for-helm@2.7.0