#!/bin/bash

# Define environment variables with defaults
AKPUTIL_BIN=${AKPUTIL_BIN:-"./akputil"}
CONTAINER_NAME=${CONTAINER_NAME:-"argocd-application-controller"}

# Function to display usage
show_usage() {
  echo "Usage: $0 --instance-id <instance_id> --keyword <search_keyword> [options]"
  echo ""
  echo "Required options:"
  echo "  --instance-id, -i    Instance ID to check"
  echo "  --keyword, -k        Keyword to search for in logs"
  echo ""
  echo "Optional options:"
  echo "  --resume-from, -r    Resume checking from this cluster name"
  echo "  --help, -h           Show this help message"
  echo ""
  echo "Environment variables:"
  echo "  AKPUTIL_BIN          Path to akputil binary (default: ./akputil)"
  echo "  CONTAINER_NAME       Container name to check logs from (default: argocd-application-controller)"
  exit 1
}

set -o pipefail

# Parse command line arguments
INSTANCE_ID=""
SEARCH_KEYWORD=""
RESUME_CLUSTER=""
SINCE=""

while [[ $# -gt 0 ]]; do
  case $1 in
    --instance-id|-i)
      INSTANCE_ID="$2"
      shift 2
      ;;
    --keyword|-k)
      SEARCH_KEYWORD="$2"
      shift 2
      ;;
    --resume-from|-r)
      RESUME_CLUSTER="$2"
      shift 2
      ;;
    --since|-s)
      SINCE="$2"
      shift 2
      ;;
    --help|-h)
      show_usage
      ;;
    *)
      echo "Unknown option: $1"
      show_usage
      ;;
  esac
done

# Check if required arguments are provided
if [ -z "$INSTANCE_ID" ]; then
  echo "Error: Instance ID is required"
  show_usage
fi

if [ -z "$SEARCH_KEYWORD" ]; then
  echo "Error: Search keyword is required"
  show_usage
fi

if [ "$SINCE" ]; then
  TIME_SINCE="--since=$SINCE"
fi

AFFECTED_CLUSTERS=()
FAILED_CLUSTERS=()
SKIP_MODE=false

if [ -n "$RESUME_CLUSTER" ]; then
  SKIP_MODE=true
  echo "Will resume from cluster: $RESUME_CLUSTER"
fi

# Function to check if a command succeeded
check_command() {
  if [ $? -ne 0 ]; then
    echo "Error: $1 failed"
    # Record the current cluster as failed if we're in the loop
    if [ ! -z "$CLUSTER" ]; then
      # Check if this cluster is already in the failed clusters array
      local already_failed=false
      for failed in "${FAILED_CLUSTERS[@]}"; do
        if [ "$failed" = "$CLUSTER" ]; then
          already_failed=true
          break
        fi
      done
      if [ "$already_failed" = false ]; then
        FAILED_CLUSTERS+=("$CLUSTER: $1")
      fi
    fi
    return 1
  fi
  return 0
}

# Function to kill port-forward process when done with a cluster
cleanup_port_forward() {
  if [ ! -z "$PORT_FORWARD_PID" ]; then
    echo "Stopping port-forward process (PID: $PORT_FORWARD_PID)..."
    kill $PORT_FORWARD_PID 2>/dev/null
    wait $PORT_FORWARD_PID 2>/dev/null
    PORT_FORWARD_PID=""
  fi
  # Also clean up temporary files
  if [ -f "$PORT_FORWARD_OUTPUT_FILE" ]; then
    rm -f "$PORT_FORWARD_OUTPUT_FILE"
  fi
}

# Trap to ensure clean exit
trap cleanup_port_forward EXIT INT TERM

# Main execution
echo "====== Starting Cluster Log Check Script ======"
echo "Instance ID: $INSTANCE_ID"
echo "Search keyword: '$SEARCH_KEYWORD'"
if [ "$SKIP_MODE" = true ]; then
  echo "Resuming from cluster: $RESUME_CLUSTER"
fi
echo ""

# Get all cluster names
echo "Getting all cluster names..."
CLUSTER_NAMES=$($AKPUTIL_BIN instance cluster-names --instance-id $INSTANCE_ID 2>&1 | grep -v "PLATFORM_SENTRY_DSN" | tr ',' ' ')
if [ $? -ne 0 ] || [ -z "$CLUSTER_NAMES" ]; then
  echo "Fatal error: Cannot proceed without cluster names. Exiting."
  exit 1
fi

echo "Found clusters: $CLUSTER_NAMES"
echo ""

# Verify resume cluster exists if specified
if [ "$SKIP_MODE" = true ]; then
  FOUND_RESUME_CLUSTER=false
  for CLUSTER in $CLUSTER_NAMES; do
    if [ "$CLUSTER" = "$RESUME_CLUSTER" ]; then
      FOUND_RESUME_CLUSTER=true
      break
    fi
  done
  
  if [ "$FOUND_RESUME_CLUSTER" = false ]; then
    echo "Error: Resume cluster '$RESUME_CLUSTER' not found in the list of clusters"
    exit 1
  fi
fi

# Process each cluster
TOTAL_CLUSTERS=$(echo $CLUSTER_NAMES | wc -w)
PROCESSED_CLUSTERS=0
SKIPPED_CLUSTERS=0

# Create temporary directory for port-forward output
TEMP_DIR="/tmp/cluster-checker-$$"
mkdir -p "$TEMP_DIR"
if ! check_command "Creating temporary directory"; then
  echo "Warning: Failed to create temporary directory. Trying to continue..."
fi

for CLUSTER in $CLUSTER_NAMES; do
  PROCESSED_CLUSTERS=$((PROCESSED_CLUSTERS + 1))
  
  # Skip clusters until we reach the resume point
  if [ "$SKIP_MODE" = true ]; then
    if [ "$CLUSTER" != "$RESUME_CLUSTER" ]; then
      SKIPPED_CLUSTERS=$((SKIPPED_CLUSTERS + 1))
      echo "Skipping cluster $CLUSTER ($PROCESSED_CLUSTERS of $TOTAL_CLUSTERS)"
      continue
    else
      echo "Resuming from cluster $CLUSTER ($PROCESSED_CLUSTERS of $TOTAL_CLUSTERS)"
      SKIP_MODE=false  # Turn off skip mode once we've reached the resume point
    fi
  fi
  
  echo "====== Processing cluster: $CLUSTER ($PROCESSED_CLUSTERS of $TOTAL_CLUSTERS) ======"
  
  # Get controller pod name
  echo "Getting controller pod name for $CLUSTER..."
  POD_NAME=$($AKPUTIL_BIN cluster debug controller-pod-name --cluster-name $CLUSTER --instance-id $INSTANCE_ID 2>&1 | grep -v "PLATFORM_SENTRY_DSN")
  if [ $? -ne 0 ] || [ -z "$POD_NAME" ]; then
    echo "Error: Could not get controller pod name for $CLUSTER. Skipping to next cluster."
    FAILED_CLUSTERS+=("$CLUSTER: Failed to get controller pod name")
    continue
  fi
  echo "Controller pod: $POD_NAME"
  
  # Create temporary file for port-forward output
  PORT_FORWARD_OUTPUT_FILE="$TEMP_DIR/port_forward_${CLUSTER}.log"
  
  # Start port-forward in background and capture output
  echo "Starting port-forward for $CLUSTER..."
  $AKPUTIL_BIN cluster port-forward --cluster-name $CLUSTER --instance-id $INSTANCE_ID > "$PORT_FORWARD_OUTPUT_FILE" 2>&1 &
  PORT_FORWARD_STATUS=$?
  PORT_FORWARD_PID=$!
  
  # If port-forward command failed to start
  if [ $PORT_FORWARD_STATUS -ne 0 ]; then
    echo "Error: Failed to start port-forward for $CLUSTER"
    cat "$PORT_FORWARD_OUTPUT_FILE"
    FAILED_CLUSTERS+=("$CLUSTER: Failed to start port-forward")
    continue
  fi
  
  # Wait for port-forward to establish connection or fail
  echo "Waiting for port-forward to connect..."
  MAX_WAIT=30  # Maximum wait time in seconds
  START_TIME=$(date +%s)
  PORT_FORWARD_READY=false
  
  while true; do
    # Check if port-forward process is still running
    if ! ps -p $PORT_FORWARD_PID > /dev/null; then
      echo "Error: Port-forward process exited unexpectedly"
      cat "$PORT_FORWARD_OUTPUT_FILE"
      FAILED_CLUSTERS+=("$CLUSTER: Port-forward process exited unexpectedly")
      break
    fi
    
    # Check for successful port-forward message
    if grep -q "Forwarding from" "$PORT_FORWARD_OUTPUT_FILE"; then
      echo "Port-forward successfully established:"
      grep "Forwarding from" "$PORT_FORWARD_OUTPUT_FILE"
      PORT_FORWARD_READY=true
      break
    fi
    
    # Check for timeout
    CURRENT_TIME=$(date +%s)
    ELAPSED_TIME=$((CURRENT_TIME - START_TIME))
    if [ $ELAPSED_TIME -ge $MAX_WAIT ]; then
      echo "Warning: Port-forward timed out after $MAX_WAIT seconds"
      echo "Port-forward output:"
      cat "$PORT_FORWARD_OUTPUT_FILE"
      FAILED_CLUSTERS+=("$CLUSTER: Port-forward timed out")
      break
    fi
    
    # Wait a bit before checking again
    sleep 1
  done
  
  # Only proceed if port-forward is ready
  if [ "$PORT_FORWARD_READY" = true ]; then
    # Check logs for the keyword
    echo "Checking logs for keyword '$SEARCH_KEYWORD' in container '$CONTAINER_NAME'..."
    LOG_CMD_OUTPUT=$(KUBECONFIG=/tmp/akpconfig kubectl logs $TIME_SINCE $POD_NAME $CONTAINER_NAME)
    LOG_CMD_STATUS=$?
    
    if [ $LOG_CMD_STATUS -ne 0 ]; then
      echo "Warning: Error getting logs from container $CONTAINER_NAME in pod $POD_NAME"
      FAILED_CLUSTERS+=("$CLUSTER: Failed to get logs")
    else
      LOG_CMD_OUTPUT=$(echo "$LOG_CMD_OUTPUT" | grep -i "$SEARCH_KEYWORD")
      # Display results and add to affected clusters if keyword found
      if [ -z "$LOG_CMD_OUTPUT" ]; then
        echo "No occurrences of '$SEARCH_KEYWORD' found in logs"
      else
        echo "Found occurrences of '$SEARCH_KEYWORD':"
        echo "$LOG_CMD_OUTPUT"
        AFFECTED_CLUSTERS+=("$CLUSTER")
      fi
    fi
  else
    echo "Error: Could not establish port-forward connection for $CLUSTER. Skipping log check."
    # If not already recorded as failed, add it now
    if [[ ! " ${FAILED_CLUSTERS[*]} " =~ " ${CLUSTER}: " ]]; then
      FAILED_CLUSTERS+=("$CLUSTER: Could not establish port-forward connection")
    fi
  fi
  
  # Clean up port-forward process
  cleanup_port_forward
  
  echo "====== Completed processing cluster: $CLUSTER ======"
  echo ""
done

# Clean up temporary directory
rm -rf "$TEMP_DIR" || echo "Warning: Failed to clean up temporary directory"

# Print summary
echo "====== SUMMARY ======"
echo "Total clusters: $TOTAL_CLUSTERS"
echo "Processed clusters: $((PROCESSED_CLUSTERS - SKIPPED_CLUSTERS))"
if [ $SKIPPED_CLUSTERS -gt 0 ]; then
  echo "Skipped clusters: $SKIPPED_CLUSTERS"
fi
echo "Clusters with '$SEARCH_KEYWORD' found: ${#AFFECTED_CLUSTERS[@]}"
echo "Failed clusters: ${#FAILED_CLUSTERS[@]}"

if [ ${#AFFECTED_CLUSTERS[@]} -gt 0 ]; then
  echo "Affected clusters:"
  for CLUSTER in "${AFFECTED_CLUSTERS[@]}"; do
    echo "- $CLUSTER"
  done
  
  # Save affected clusters to a file
  TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
  FILENAME="affected_clusters_${TIMESTAMP}.txt"
  echo "Saving affected clusters to '$FILENAME'"
  printf "%s\n" "${AFFECTED_CLUSTERS[@]}" > "$FILENAME"
else
  echo "No clusters found with the keyword '$SEARCH_KEYWORD'"
fi

if [ ${#FAILED_CLUSTERS[@]} -gt 0 ]; then
  echo "Failed clusters (with reasons):"
  for FAILED in "${FAILED_CLUSTERS[@]}"; do
    echo "- $FAILED"
  done
  
  # Save failed clusters to a file
  TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
  FILENAME="failed_clusters_${TIMESTAMP}.txt"
  echo "Saving failed clusters to '$FILENAME'"
  printf "%s\n" "${FAILED_CLUSTERS[@]}" > "$FILENAME"
fi

echo "All clusters processed. Check the summary for results."
