---
apiVersion: v1
kind: Namespace
metadata:
  name: shard-proxy
---
apiVersion: v1
kind: Service
metadata:
  namespace: shard-proxy
  name: k3d-host
spec:
  type: ExternalName
  externalName: ${EXTERNAL_HOST}
---
apiVersion: traefik.io/v1alpha1
kind: IngressRouteTCP
metadata:
  namespace: shard-proxy
  name: ${SHARD}-proxy
spec:
  entryPoints:
    - websecure
  tls:
    passthrough: true
  routes:
    - match: HostSNI(`${SHARD}.portal-server.akuity-platform`)
      services:
        - name: k3d-host
          port: ${SHARD_PORT}
          tls: true
    - match: HostSNIRegexp(`^.+\.${SHARD}\.portal-server\.akuity-platform$`)
      services:
        - name: k3d-host
          port: ${SHARD_PORT}
          tls: true
