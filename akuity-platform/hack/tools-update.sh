#!/bin/bash

set -euo pipefail

if ! sed --version 2>&1 | grep -q "GNU sed"; then
  echo "Error: GNU sed is not installed. You can install it using the following command:"
  echo
  echo "brew install gnu-sed"
  echo 'export PATH="/opt/homebrew/opt/gnu-sed/libexec/gnubin:$PATH"'
  echo
  echo "This script runs under bash, if you are using zsh make sure you add the path"
  echo "to your ~/.bashrc"
  echo
  exit 1
fi

function version() {
  grep -v '#' './hack/tools-versions.yaml' | grep "$1" | awk '{print $2}'
}

function replace() {
  local -r pattern="$1"
  shift
  for file in "$@"; do
    sed -E -i "$pattern" "$file"
  done
}

version_pattern='[0-9\.]+'
liquibase_version=$(version liquibase)
sqlboiler_version=$(version sqlboiler)
postgres_version=$(version postgres)
golint_version=$(version golint)
go_version=$(version golang)
bufbuild_version=$(version bufbuild)
k3s_version=$(version k3s)
cuda_version=$(version cuda)
kustomize_version=$(version kustomize)
ytt_version=$(version ytt)
controller_gen_version=$(version controller_gen)
debian_version=$(version debian)

# liquibase

replace "s/liquibase:${version_pattern}/liquibase:${liquibase_version}/g" Makefile
replace "s/liquibase:${version_pattern}/liquibase:${liquibase_version}/g" models/Makefile
replace "s/liquibase:${version_pattern}/liquibase:${liquibase_version}/g" hack/*.sh
replace "s/liquibase:${version_pattern}/liquibase:${liquibase_version}/g" .github/workflows/*.yml

# sqlboiler

replace "s/SQLBOILER_VERSION=v${version_pattern}/SQLBOILER_VERSION=v${sqlboiler_version}/g" models/Makefile

# postgres

replace "s/postgres:${version_pattern}/postgres:${postgres_version}/g" models/Makefile
replace "s/postgres:${version_pattern}/postgres:${postgres_version}/g" manifests/postgres/postgres.yaml
replace "s/postgres:${version_pattern}/postgres:${postgres_version}/g" hack/db-schema.sh

# golint

replace "s/golangci-lint:v${version_pattern}/golangci-lint:v${golint_version}/g" hack/Dockerfile
replace "/golangci\/golangci-lint-action/,/args/s/version: v${version_pattern}/version: v${golint_version}/g" .github/workflows/*.yml

# golang

replace "s/golang:${version_pattern}/golang:${go_version}/" models/Makefile
replace "s/golang:${version_pattern}/golang:${go_version}/" Dockerfile
replace "s/golang:${version_pattern}/golang:${go_version}/" hack/Dockerfile
replace "s/go: \"${version_pattern}\"/go: \"${go_version}\"/" .golangci.yaml
replace "s/go-version: ${version_pattern}/go-version: ${go_version}/" .github/workflows/*.yml
replace "s/go ${version_pattern}/go ${go_version}/" go.mod

# bufbuild

replace "s/bufbuild\/buf:${version_pattern}/bufbuild\/buf:${bufbuild_version}/" hack/Dockerfile
replace "/bufbuild\/buf-setup-action/,/github_token/s/version: ${version_pattern}/version: ${bufbuild_version}/g" .github/workflows/*.yml

# k3s

replace "s/rancher\/k3s:v${version_pattern}-k3s1/rancher\/k3s:v${k3s_version}/" docs/*.md
replace "s/rancher\/k3s:v${version_pattern}-k3s1/rancher\/k3s:v${k3s_version}/" hack/*.sh
replace "s/rancher\/k3s:v${version_pattern}-k3s1/rancher\/k3s:v${k3s_version}/" hack/k3s-cuda/Dockerfile
replace "/debianmaster\/actions-k3s/,/- name:/s/version: v${version_pattern}-k3s1/version: v${k3s_version}/g" .github/workflows/*.yml

# cuda

replace "s/nvidia\/cuda:${version_pattern}-base-ubuntu${version_pattern}/nvidia\/cuda:${cuda_version}/" hack/*.sh
replace "s/nvidia\/cuda:${version_pattern}-base-ubuntu${version_pattern}/nvidia\/cuda:${cuda_version}/" hack/k3s-cuda/Dockerfile

# kustomize

replace "s/download-kustomize.sh ${version_pattern}/download-kustomize.sh ${kustomize_version}/" Dockerfile

# ytt

replace "s/download-ytt-ci.sh ${version_pattern}/download-ytt-ci.sh ${ytt_version}/" Dockerfile
replace "s/download-ytt-ci.sh ${version_pattern}/download-ytt-ci.sh ${ytt_version}/" .github/workflows/*.yml

# controller-gen

replace "s/controller-gen@v${version_pattern}/controller-gen@v${controller_gen_version}/" Makefile
replace "s/controller-gen@v${version_pattern}/controller-gen@v${controller_gen_version}/" hack/Dockerfile

# debian

replace "s/debian:${version_pattern}-slim/debian:${debian_version}/" Dockerfile
replace "s/debian:${version_pattern}-slim/debian:${debian_version}/" Makefile

git status
