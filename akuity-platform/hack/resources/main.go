package main

import (
	"archive/tar"
	"embed"
	"fmt"
	"io"
	"os"
	"path/filepath"

	"github.com/akuityio/akuity-platform/models/liquibase"
	"github.com/akuityio/akuity-platform/models/sql"
)

func tarDir(fileSystem embed.FS, root string, w *tar.Writer) error {
	entries, err := fileSystem.ReadDir(root)
	if err != nil {
		return err
	}
	for _, entry := range entries {
		info, err := entry.Info()
		if err != nil {
			return err
		}
		entryPath := filepath.Join(root, entry.Name())
		header, err := tar.FileInfoHeader(info, entryPath)
		header.Mode = 0x755
		if err != nil {
			return err
		}
		if err := w.Write<PERSON>eader(header); err != nil {
			return err
		}

		if entry.IsDir() {
			if err := tarDir(fileSystem, entryPath, w); err != nil {
				return err
			}
		} else {
			data, err := fileSystem.ReadFile(entryPath)
			if err != nil {
				return err
			}
			if _, err := w.Write(data); err != nil {
				return err
			}
		}
	}
	return nil
}

func process(out io.Writer, resourceType string) error {
	switch resourceType {
	case "schema":
		_, err := fmt.Fprint(out, sql.SchemaSQL)
		return err
	case "changelogs-tar":
		w := tar.NewWriter(out)
		if err := tarDir(liquibase.Changelogs, ".", w); err != nil {
			return err
		}
		return w.Flush()
	default:
		return fmt.Errorf("unknown resource type: %s", resourceType)
	}
}

func main() {
	resourceType := "schema"
	if len(os.Args) > 1 {
		resourceType = os.Args[1]
	}
	if err := process(os.Stdout, resourceType); err != nil {
		_, _ = os.Stderr.WriteString(err.Error())
		os.Exit(1)
	}
}
