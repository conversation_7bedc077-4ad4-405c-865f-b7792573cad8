/*
Copyright 2023 Akuity, Inc.
*/

package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	AnnotationCMPEnabled = "akuity.io/enabled"
	AnnotationCMPImage   = "akuity.io/image"
)

//+kubebuilder:object:root=true
//+kubebuilder:subresource:status

// ConfigManagementPlugin is the Schema for the ConfigManagementPlugin API
type ConfigManagementPlugin struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec    PluginSpec `json:"spec,omitempty"`
}

//+kubebuilder:object:root=true

// ConfigManagementPluginList contains a list of ConfigManagementPlugin
type ConfigManagementPluginList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []ConfigManagementPlugin `json:"items"`
}

func init() {
	SchemeBuilder.Register(&ConfigManagementPlugin{}, &ConfigManagementPluginList{})
}
