package genkubetypes

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"

	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

// If this test fails, a new Cluster field has been added to the argocdv1.Cluster model.
// If this field should be exposed to user, update the ClusterSpec with the new field in cluster_types.go.tmpl,
// regenerate Cluster CRD, update the ApplyInstance and ExportInstance handlers, and bump this number accordingly.
func TestNoNewClusterFields(t *testing.T) {
	assert.Equal(t, reflect.TypeOf(argocdv1.Cluster{}).NumField(), 17)
}

// If this test fails, a new Instance field has been added to the argocdv1.Instance model.
// If this field should be exposed to user, update the ArgoCDSpec with the new field in argocdinstance_types.go.tmpl,
// regenerate ArgoCD CRD, update the ApplyInstance and ExportInstance handlers, and bump this number accordingly.
func TestNoNewInstanceFields(t *testing.T) {
	assert.Equal(t, reflect.TypeOf(argocdv1.Instance{}).NumField(), 24)
}

// If this test fails, a new ConfigManagementPlugin field has been added to the argocdv1.ConfigManagementPlugin model.
// If this field should be exposed to user, update the ConfigManagementPluginSpec with the new field in ConfigManagementPlugin_types.go.tmpl,
// regenerate ConfigManagementPlugin CRD, update the ApplyInstance and ExportInstance handlers, and bump this number accordingly.
func TestNoNewConfigManagementPluginFields(t *testing.T) {
	assert.Equal(t, reflect.TypeOf(argocdv1.ConfigManagementPlugin{}).NumField(), 7)
	assert.Equal(t, reflect.TypeOf(argocdv1.PluginSpec{}).NumField(), 9)
}

// If this test fails, a new KargoAgent field has been added to the argocdv1.KargoAgent model.
// If this field should be exposed to user, update the KargoAgentSpec with the new field in kargoagent_types.go.tmpl,
// regenerate KargoAgent CRD, update the ApplyKargoInstance and ExportKargoInstance handlers, and bump this number accordingly.
func TestNoNewKargoAgentFields(t *testing.T) {
	assert.Equal(t, reflect.TypeOf(kargov1.KargoAgent{}).NumField(), 14)
}

// If this test fails, a new KargoInstance field has been added to the argocdv1.KargoInstance model.
// If this field should be exposed to user, update the KargoSpec with the new field in kargoinstance_types.go.tmpl,
// regenerate Kargo CRD, update the ApplyKargoInstance and ExportKargoInstance handlers, and bump this number accordingly.
func TestNoNewKargoInstanceFields(t *testing.T) {
	assert.Equal(t, reflect.TypeOf(kargov1.KargoInstance{}).NumField(), 25)
}
