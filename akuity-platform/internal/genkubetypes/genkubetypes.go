package genkubetypes

import (
	"bytes"
	"embed"
	"fmt"
	"go/ast"
	"go/parser"
	"go/printer"
	"go/token"
	"os"
	"strings"

	"github.com/fatih/structtag"
	"github.com/go-logr/logr"

	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
)

var (
	//go:embed *.go.tmpl
	types embed.FS

	// defaultIgnoredFieldNames are the fields we ignore when discovering a struct. Currently these are
	// the names of autogenerated gRPC fields which cannot be supported by controller-gen features
	// like DeepCopy()
	defaultIgnoredFieldNames = map[string]bool{
		"state":         true,
		"sizeCache":     true,
		"unknownFields": true,
	}

	typeGenConfigs = []typeGenConfig{
		{
			sourceFile:       "./pkg/api/gen/argocd/v1/argocd.pb.go",
			templateFileName: "argocdinstance_types.go.tmpl",
			structsToDiscover: map[string]bool{
				"InstanceSpec": false,
			},
			destFileFolder: "./api/akuity/v1alpha1/",
		},
		{
			sourceFile:       "./pkg/api/gen/argocd/v1/argocd.pb.go",
			templateFileName: "cluster_types.go.tmpl",
			structsToDiscover: map[string]bool{
				"ClusterData": false,
				// Skip "ClusterSize" and hardcode it as string in the `cluster_types.go.tmpl` so that user can use "small", "medium", and "large" to config the size.
				"ClusterSize":       true,
				"DirectClusterType": true,
			},
			destFileFolder: "./api/akuity/v1alpha1/",
			ignoredFieldNames: map[string]bool{
				// Skip "Labels" and "Annotations" and reuse the fields from ObjectMeta of generated CRD.
				"Labels":          true,
				"Annotations":     true,
				"NamespaceScoped": true,
				"Namespace":       true,
			},
		},
		{
			sourceFile:       "./pkg/api/gen/argocd/v1/argocd.pb.go",
			templateFileName: "configmanagementplugin_types.go.tmpl",
			structsToDiscover: map[string]bool{
				"PluginSpec": false,
			},
			destFileFolder: "./api/argocd/v1alpha1/",
		},
		{
			sourceFile:       "./pkg/api/gen/kargo/v1/kargo.pb.go",
			templateFileName: "kargoinstance_types.go.tmpl",
			structsToDiscover: map[string]bool{
				"KargoInstanceSpec": false,
			},
			destFileFolder: "./api/akuity/v1alpha1/",
		},
		{
			sourceFile:       "./pkg/api/gen/kargo/v1/kargo.pb.go",
			templateFileName: "kargoagent_types.go.tmpl",
			structsToDiscover: map[string]bool{
				"KargoAgentData": false,
				// skip "KargoInstanceSize" and hardcode it as string in the `kargoinstance_types.go.tmpl` so that user can use "small", "medium", and "large" to config the size.
				"KargoAgentSize": true,
			},
			destFileFolder: "./api/akuity/v1alpha1/",
			ignoredFieldNames: map[string]bool{
				"Labels":      true,
				"Annotations": true,
				"Namespace":   true,
			},
		},
	}

	log logr.Logger
)

func init() {
	var err error
	log, err = logging.NewLogger()
	cli.CheckErr(err)
}

func CodegenTypes() {
	for _, c := range typeGenConfigs {
		c.discoverStructs(c.sourceFile)
		typesFileOut := c.codegenStructs(c.sourceFile)
		destFile := c.destFileFolder + strings.TrimSuffix(c.templateFileName, ".tmpl")
		err := os.WriteFile(destFile, typesFileOut.Bytes(), 0o600)
		cli.CheckErr(err)
	}
}

type typeGenConfig struct {
	sourceFile       string
	templateFileName string
	// structsToDiscover is a map of struct names to discover from the source file.
	// A boolean value of true indicates it was already discovered/processed. It is initialized
	// with InstanceSpec, but the map will be built up as nested structs are discovered.
	structsToDiscover map[string]bool
	ignoredFieldNames map[string]bool
	destFileFolder    string
}

// discoverStructs will discover/recurse a struct for additional nested structs to discover/codegen
func (t *typeGenConfig) discoverStructs(fileName string) {
	fset := token.NewFileSet()
	file, err := parser.ParseFile(fset, fileName, nil, 0)
	cli.CheckErr(err)

	for t.stillMoreStructsToDiscover() {
		ast.Inspect(file, func(n ast.Node) bool {
			typeSpec, ok := n.(*ast.TypeSpec)
			if !ok {
				return true
			}
			structType, ok := typeSpec.Type.(*ast.StructType)
			if !ok {
				return true
			}
			structName := typeSpec.Name.String()
			alreadyDiscovered, shouldDiscover := t.structsToDiscover[structName]
			if alreadyDiscovered || !shouldDiscover {
				return true
			}
			log.Info("Discovering struct", "struct", structName)
			for _, field := range structType.Fields.List {
				t.discoverExpr(getFieldName(field), field.Type)
			}
			t.structsToDiscover[structName] = true
			return true
		})
	}
}

// stillMoreStructsToDiscover returns whether or not there are still structs to discover
func (t *typeGenConfig) stillMoreStructsToDiscover() bool {
	for _, ok := range t.structsToDiscover {
		if !ok {
			return true
		}
	}
	return false
}

// discoverExpr recurses down a struct field to see if there are nested structs for discovery
func (t *typeGenConfig) discoverExpr(fieldName string, exp ast.Expr) {
	if t.ignoredFieldNames[fieldName] || defaultIgnoredFieldNames[fieldName] {
		return
	}
	log := log.WithValues("name", fieldName)
	switch f := exp.(type) {
	case *ast.Ident:
		log.V(9).Info("  discovering ident", "type", f.Name)
		t.enqueueForDiscovery(f.Name)
	case *ast.ArrayType:
		elt, _ := resolvePointer(f.Elt)
		arrayTypeName := fmt.Sprintf("%s", elt)
		t.enqueueForDiscovery(arrayTypeName)
	case *ast.StructType:
		log.Info("  discovering struct", "struct", f)
		panic("struct type not yet supported")
	case *ast.MapType:
		log.Info("  discovering map", "map", f)
		keyPtr, _ := resolvePointer(f.Key)
		keyName := fmt.Sprintf("%s", keyPtr)
		t.enqueueForDiscovery(keyName)
		valPtr, _ := resolvePointer(f.Value)
		valName := fmt.Sprintf("%s", valPtr)
		t.enqueueForDiscovery(valName)
	case *ast.StarExpr:
		ptr, _ := resolvePointer(exp)
		t.discoverExpr(fieldName, ptr)
	case *ast.SelectorExpr:
		// this is for handling the structpb.Struct type, which we do not need to perform further discovery
		// but we will panic if we determine unknown ast.SelectorExprs.
		ident := f.X.(*ast.Ident)
		typeName := fmt.Sprintf("%s.%s", ident.Name, f.Sel.Name)
		switch typeName {
		case "structpb.Struct":
			// do nothing
		default:
			panic(fmt.Sprintf("introduction of unknown ast.SelectorExpr field '%s' of type: %s", fieldName, typeName))
		}
	default:
		panic(fmt.Sprintf("introduction of unknown field '%s' of type: %s", fieldName, exp))
	}
}

// enqueueForDiscovery adds a struct name for discovery, unless it has already been discovered
func (t *typeGenConfig) enqueueForDiscovery(structName string) {
	switch structName {
	// We only want to enqueue structs for discovery, not primitive types
	case "string", "bool", "byte", "rune",
		"int", "int8", "int16", "int32", "int64",
		"uint", "uint8", "uint16", "uint32", "uint64",
		"float32", "float64", "complex64", "complex128":
		return
	}
	if _, ok := t.structsToDiscover[structName]; !ok {
		log.Info("  enqueued for discovery", "struct", structName)
		t.structsToDiscover[structName] = false
	}
}

// codegenStructs copies structs from the source file auto-generated file to a new k8s types.go
func (t *typeGenConfig) codegenStructs(fileName string) *bytes.Buffer {
	fset := token.NewFileSet()
	file, err := parser.ParseFile(fset, fileName, nil, 0)
	cli.CheckErr(err)

	fsetOut := token.NewFileSet()
	buf := new(bytes.Buffer)

	tmplBytes, err := types.ReadFile(t.templateFileName)
	cli.CheckErr(err)
	fmt.Fprint(buf, "// This is an auto-generated file. DO NOT EDIT\n")
	fmt.Fprint(buf, string(tmplBytes))
	fmt.Fprint(buf, "\n")

	ast.Inspect(file, func(n ast.Node) bool {
		typeSpec, ok := n.(*ast.TypeSpec)
		if !ok {
			return true
		}
		structType, ok := typeSpec.Type.(*ast.StructType)
		if !ok {
			return true
		}
		structName := typeSpec.Name.String()
		_, shouldDiscover := t.structsToDiscover[structName]
		if !shouldDiscover {
			return true
		}
		log.Info("Code generating struct", "struct", structName)

		var newFieldsList []*ast.Field
		for _, field := range structType.Fields.List {
			fieldName := getFieldName(field)
			if t.ignoredFieldNames[fieldName] || defaultIgnoredFieldNames[fieldName] {
				continue
			}
			rewriteFieldTag(field.Tag)
			if rawExtension := structpbStructToRawExtension(fieldName, field); rawExtension != nil {
				field.Type = rawExtension
			}
			newFieldsList = append(newFieldsList, field)
		}
		structType.Fields.List = newFieldsList

		_, err = fmt.Fprint(buf, "type ")
		cli.CheckErr(err)
		err = printer.Fprint(buf, fsetOut, n)
		cli.CheckErr(err)
		_, err = fmt.Fprint(buf, "\n\n")
		cli.CheckErr(err)
		return true
	})

	return buf
}

// getFieldName is a helper to return the field name of a struct
func getFieldName(field *ast.Field) string {
	if len(field.Names) != 1 {
		panic(fmt.Sprintf("  found field with multiple names %s", field.Names))
	}
	return field.Names[0].String()
}

// resolvePointer returns the underlying type of a field
func resolvePointer(exp ast.Expr) (ast.Expr, bool) {
	ptr, ok := exp.(*ast.StarExpr)
	if !ok {
		return exp, false
	}
	return ptr.X, true
}

// structpbStructToRawExtension returns a new ast.Expr of type runtime.RawExtension if the field is of type structpb.Struct
func structpbStructToRawExtension(fieldName string, field *ast.Field) ast.Expr {
	// special case to translate structpb.Struct to runtime.RawExtension
	if fieldName != "Kustomization" {
		return nil
	}
	expr, _ := resolvePointer(field.Type)
	f, ok := expr.(*ast.SelectorExpr)
	if !ok {
		return nil
	}
	ident := f.X.(*ast.Ident)
	typeName := fmt.Sprintf("%s.%s", ident.Name, f.Sel.Name)
	if typeName == "structpb.Struct" {
		ident.Name = "runtime"
		f.Sel.Name = "RawExtension"
	} else {
		panic(fmt.Sprintf("I dont know how to convert for field %s of type %s", fieldName, typeName))
	}
	return expr
}

// rewriteFieldTag rewrites a struct field tag to our preferred K8s compliant tag using camelCase
// For example, this:
//
//	protobuf:"bytes,1,rep,name=ip_allow_list,json=ipAllowList,proto3" json:"ip_allow_list,omitempty"
//
// becomes this:
//
//	protobuf:"bytes,1,rep,name=ipAllowList,proto3" json:"ipAllowList,omitempty"``
func rewriteFieldTag(tag *ast.BasicLit) {
	if tag == nil {
		return
	}
	// get rid of ` characters so structtag can parse it
	tagValue := strings.TrimPrefix(tag.Value, "`")
	tagValue = strings.TrimSuffix(tagValue, "`")
	tags, err := structtag.Parse(tagValue)
	cli.CheckErr(err)

	protobufTag, err := tags.Get("protobuf")
	cli.CheckErr(err)
	camelCaseName := camelCaseProtobufTag(protobufTag)
	// This removes the `protobuf:`, `protobuf_key:`, and `protobuf_val` tags completely to have cleaner spec.
	// If we want to include back `protobuf:`, `protobuf_key:`, and `protobuf_val` in the struct tag, remove the following lines.
	tags.Delete("protobuf")
	tags.Delete("protobuf_key")
	tags.Delete("protobuf_val")

	jsonTag, err := tags.Get("json")
	cli.CheckErr(err)
	jsonTag.Name = camelCaseName

	tag.Value = fmt.Sprintf("`%s`", tags.String())
}

// camelCaseProtobufTag will rewrite a protobuf tag to use the camelCase version of the name,
// and also removes the json tag. Returns the camelCase name.
// This:
//
//	`protobuf:"bytes,1,rep,name=ip_allow_list,json=ipAllowList,proto3"`
//
// becomes this:
//
//	`protobuf:"bytes,1,rep,name=ipAllowList,proto3"`
func camelCaseProtobufTag(protobufTag *structtag.Tag) string {
	var (
		protobufName  string
		camelCaseName string
	)
	var newOptions []string
	for _, opt := range protobufTag.Options {
		if strings.HasPrefix(opt, "json=") {
			camelCaseName = strings.TrimPrefix(opt, "json=")
		} else if strings.HasPrefix(opt, "name=") {
			protobufName = strings.TrimPrefix(opt, "name=")
		} else {
			newOptions = append(newOptions, opt)
		}
	}
	if camelCaseName == "" {
		camelCaseName = protobufName
	}

	newOptions = append(newOptions, "name="+camelCaseName)
	protobufTag.Options = newOptions
	return camelCaseName
}
