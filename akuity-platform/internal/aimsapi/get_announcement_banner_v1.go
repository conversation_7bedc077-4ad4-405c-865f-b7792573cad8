package aimsapi

import (
	"context"
	"encoding/json"
	"fmt"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/utils/aims"
)

func (s *AimsV1Server) GetAnnouncementBanner(_ context.Context, _ *aimsv1.GetAnnouncementBannerRequest) (*aimsv1.GetAnnouncementBannerResponse, error) {
	cm, err := aims.RetrieveConfigMap(s.cmInformer, config.PortalServerCmName)
	if err != nil {
		return nil, err
	}

	announcementBannerRaw, ok := cm.Data[config.AnnouncementBannerKey]
	if !ok || announcementBannerRaw == "" {
		return &aimsv1.GetAnnouncementBannerResponse{
			Banner: nil,
		}, nil
	}

	var announcementBanner aimsv1.Banner
	if err := json.Unmarshal([]byte(announcementBannerRaw), &announcementBanner); err != nil {
		return nil, err
	}

	if announcementBanner.Message == "" {
		return nil, fmt.Errorf("'message' is missing in ANNOUNCEMENT_BANNER")
	}

	var links []*aimsv1.Banner_BannerLink

	for _, link := range announcementBanner.Links {
		links = append(links, &aimsv1.Banner_BannerLink{
			Name: link.Name,
			Url:  link.Url,
		})
	}

	banner := &aimsv1.Banner{
		Title:             announcementBanner.Title,
		Message:           announcementBanner.Message,
		Closable:          announcementBanner.Closable,
		Type:              announcementBanner.Type,
		Links:             links,
		PaidCustomersOnly: announcementBanner.PaidCustomersOnly,
	}

	return &aimsv1.GetAnnouncementBannerResponse{
		Banner: banner,
	}, nil
}
