package aimsapi

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/services/customroles"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func (s *AimsV1Server) ListOrganizationCustomRoles(ctx context.Context, req *aimsv1.ListOrganizationCustomRolesRequest) (*aimsv1.ListOrganizationCustomRolesResponse, error) {
	if req.GetOrganizationId() == "" {
		return nil, status.Error(codes.InvalidArgument, "organization_id is required")
	}

	limit := req.GetLimit()
	offset := req.GetOffset()
	if limit == 0 {
		limit = 100
	}

	repoSet := client.NewRepoSet(s.db)
	crs := customroles.New(repoSet)

	roles, err := crs.ListOrganizationCustomRoles(ctx, req.OrganizationId, limit, offset)
	if err != nil {
		return nil, err
	}

	noWorkspace := ""
	count, err := crs.CountCustomRole(ctx, req.OrganizationId, noWorkspace)
	if err != nil {
		return nil, err
	}
	return &aimsv1.ListOrganizationCustomRolesResponse{
		CustomRoles: newCustomRoles(roles),
		TotalCount:  count,
	}, nil
}

func newCustomRoles(rr []*models.CustomRole) []*aimsv1.CustomRole {
	v1Roles := make([]*aimsv1.CustomRole, 0, len(rr))

	for _, r := range rr {
		v1Roles = append(v1Roles, newCustomRole(r))
	}
	return v1Roles
}

func newCustomRole(cr *models.CustomRole) *aimsv1.CustomRole {
	return &aimsv1.CustomRole{
		Id:          cr.ID,
		Name:        cr.Name,
		Description: cr.Description.String,
		Policy:      cr.Policy.String,
	}
}
