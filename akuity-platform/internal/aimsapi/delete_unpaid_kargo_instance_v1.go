package aimsapi

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func (s *AimsV1Server) DeleteUnpaidKargoInstance(ctx context.Context, req *aimsv1.DeleteUnpaidInstanceRequest) (*aimsv1.DeleteUnpaidInstanceResponse, error) {
	audit := req.GetAudit()

	if err := aimsutil.ValidateAuditPayload(audit); err != nil {
		return nil, err
	}

	id := req.GetInstanceId()

	if id == "" {
		return nil, status.Error(codes.InvalidArgument, "kargo instance id required")
	}

	data := InternalKargoInstanceData{}

	err := models.NewQuery(
		qm.Select(internalKargoInstanceDataColumns()...),
		qm.From(models.TableNames.KargoInstance),
		qm.InnerJoin("organization on organization.id = kargo_instance.organization_owner"),
		qm.Where("kargo_instance.id = ?", id),
	).Bind(ctx, s.db, &data)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, status.Error(codes.NotFound, fmt.Sprintf("kargo id %s not found", id))
		}
		return nil, err
	}

	orgStatus, err := data.Organization.GetOrgStatus()
	if err != nil {
		return nil, err
	}

	if orgStatus.BillingUpdating {
		return nil, status.Error(codes.Aborted, "failed to delete instance. we found that this organization has billing state updating")
	}

	if orgStatus.State == models.PaidCustomer {
		return nil, status.Error(codes.Aborted, "cannot delete instance of a paid customer")
	}

	repoSet := client.NewRepoSet(s.db)

	modelsInstance := data.KargoInstance
	modelsInstance.DeletionTimestamp = null.TimeFrom(time.Now())

	newCtx := s.aimsAuditSvc.SetActor(ctx, audit)

	return &aimsv1.DeleteUnpaidInstanceResponse{}, repoSet.KargoInstances().Update(newCtx, &modelsInstance, models.KargoInstanceColumns.DeletionTimestamp)
}
