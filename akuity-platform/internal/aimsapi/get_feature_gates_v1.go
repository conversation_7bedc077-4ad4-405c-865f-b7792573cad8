package aimsapi

import (
	"context"

	"google.golang.org/protobuf/reflect/protoreflect"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	featuresv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/features/v1"
)

func (s *AimsV1Server) GetFeatureGates(ctx context.Context, req *aimsv1.GetFeatureGatesRequest) (*aimsv1.GetFeatureGatesResponse, error) {
	orgID := req.GetId()
	featureStatuses := s.featureSvc.GetFeatureStatuses(ctx, &orgID)

	orgFeatureGates := &featuresv1.OrganizationFeatureGates{}

	orgGatesProtoReflect := orgFeatureGates.ProtoReflect()
	orgGatesFields := orgGatesProtoReflect.Descriptor().Fields()

	statusProtoReflect := featureStatuses.ProtoReflect()
	statusFields := statusProtoReflect.Descriptor().Fields()

	for i := 0; i < orgGatesFields.Len(); i++ {
		targetField := orgGatesFields.Get(i)
		targetFieldName := string(targetField.Name())

		sourceField := statusFields.ByName(protoreflect.Name(targetFieldName))
		if sourceField == nil {
			continue
		}

		if targetFieldName == "shards" {
			if sourceField.Kind() == protoreflect.StringKind && sourceField.IsList() {
				sourceList := statusProtoReflect.Get(sourceField).List()
				if sourceList.Len() > 0 {
					orgGatesProtoReflect.Set(targetField, statusProtoReflect.Get(sourceField))
				}
			}
			continue
		}

		if targetField.Kind() == protoreflect.BoolKind && sourceField.Kind() == protoreflect.EnumKind {
			sourceEnumValue := featuresv1.FeatureStatus(statusProtoReflect.Get(sourceField).Enum())
			isEnabled := sourceEnumValue == featuresv1.FeatureStatus_FEATURE_STATUS_ENABLED

			orgGatesProtoReflect.Set(targetField, protoreflect.ValueOfBool(isEnabled))
		}
	}

	systemFeatureStatuses := s.featureSvc.GetFeatureStatuses(ctx, nil)
	systemFeatureGates := &featuresv1.SystemFeatureGates{}

	sysGatesProtoReflect := systemFeatureGates.ProtoReflect()
	sysGatesFields := sysGatesProtoReflect.Descriptor().Fields()

	sysStatusProtoReflect := systemFeatureStatuses.ProtoReflect()
	sysStatusFields := sysStatusProtoReflect.Descriptor().Fields()

	for i := 0; i < sysGatesFields.Len(); i++ {
		targetField := sysGatesFields.Get(i)
		targetFieldName := string(targetField.Name())

		sourceField := sysStatusFields.ByName(protoreflect.Name(targetFieldName))
		if sourceField == nil {
			continue
		}

		if targetField.Kind() == protoreflect.BoolKind && sourceField.Kind() == protoreflect.EnumKind {
			sourceEnumValue := featuresv1.FeatureStatus(sysStatusProtoReflect.Get(sourceField).Enum())
			isEnabled := sourceEnumValue == featuresv1.FeatureStatus_FEATURE_STATUS_ENABLED

			sysGatesProtoReflect.Set(targetField, protoreflect.ValueOfBool(isEnabled))
		}
	}

	return &aimsv1.GetFeatureGatesResponse{
		FeatureGates:       orgFeatureGates,
		SystemFeatureGates: systemFeatureGates,
	}, nil
}
