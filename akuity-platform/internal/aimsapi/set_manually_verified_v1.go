package aimsapi

import (
	"context"
	"encoding/json"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/utils/aims"
)

func (s *AimsV1Server) SetManuallyVerified(ctx context.Context, req *aimsv1.SetManuallyVerifiedRequest) (*aimsv1.SetManuallyVerifiedResponse, error) {
	audit := req.GetAudit()
	if err := aimsutil.ValidateAuditPayload(audit); err != nil {
		return nil, err
	}

	cm, err := aims.RetrieveConfigMap(s.cmInformer, config.InternalCmName)
	if err != nil {
		return nil, err
	}

	orgMap, err := aims.ParseVerifiedOrganizationMap(cm)
	if err != nil {
		return nil, err
	}

	if orgMap == nil {
		orgMap = map[string]bool{}
	}

	orgMap[req.OrganizationId] = req.Verified

	encoded, err := json.Marshal(orgMap)
	if err != nil {
		return nil, err
	}

	cm.Data[config.VerifiedOrganizationMapKey] = string(encoded)

	if _, err := s.kubeclient.CoreV1().ConfigMaps(config.InternalCmNamespace).Update(ctx, cm, metav1.UpdateOptions{}); err != nil {
		return nil, err
	}

	verified := "false"
	if req.GetVerified() {
		verified = "true"
	}

	return nil, s.aimsAuditSvc.AuditOrganizationPatch(ctx, req.GetOrganizationId(), audit, []byte(`{ "manually_verified": `+verified+` }`))
}
