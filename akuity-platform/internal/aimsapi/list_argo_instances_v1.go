package aimsapi

import (
	"context"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
	"github.com/akuityio/akuity-platform/models/models"
)

func (s *AimsV1Server) ListArgoInstances(ctx context.Context, req *aimsv1.ListArgoInstancesRequest) (*aimsv1.ListArgoInstancesResponse, error) {
	data := []aimsutil.InternalInstanceData{}

	requestedFilters := req.GetFilter()

	columns := aimsutil.GetAllColumnsOfInstance()
	columns = append(columns, aimsutil.GetAllColumnsOfOrganization()...)
	columns = append(columns, aimsutil.GetAllColumnsOfInstanceConfig()...)
	columns = append(columns, "count(argo_cd_cluster.id) as clusters_count")

	mods := []qm.QueryMod{
		qm.Select(columns...),
		qm.LeftOuterJoin("organization on argo_cd_instance.organization_owner = organization.id"),
		qm.LeftOuterJoin("argo_cd_instance_config on argo_cd_instance_config.instance_id = argo_cd_instance.id"),
		qm.LeftOuterJoin("argo_cd_cluster on argo_cd_cluster.instance_id = argo_cd_instance.id AND argo_cd_cluster.status_agent_state -> 'status' ->> 'PriorityStatus' = 'Healthy'"),
		qm.From(models.TableNames.ArgoCDInstance),
		qm.GroupBy(models.TableNames.ArgoCDInstance + "." + models.ArgoCDInstanceColumns.ID),
		qm.GroupBy(models.TableNames.Organization + "." + models.OrganizationColumns.ID),
		qm.GroupBy(models.TableNames.ArgoCDInstanceConfig + "." + models.ArgoCDInstanceConfigColumns.InstanceID),
		qm.OrderBy(models.TableNames.ArgoCDInstance + "." + models.ArgoCDInstanceColumns.CreationTimestamp + " DESC"),
	}

	if requestedFilters.GetPaid() {
		mods = append(mods, qm.Where("organization.org_status->>'trial' = ?", false))
	} else if requestedFilters.GetUnpaid() {
		mods = append(mods, qm.Where("organization.org_status->>'trial' = ?", true))
	}

	if orgID := requestedFilters.GetOrganizationId(); orgID != "" {
		mods = append(mods, qm.Where("organization.id = ?", orgID))
	}

	err := models.NewQuery(mods...).Bind(ctx, s.db, &data)
	if err != nil {
		return nil, err
	}

	instances := []*aimsv1.InternalInstance{}
	for _, payload := range data {
		instance, err := aimsutil.NewArgoCDInstanceV1(payload, s.cfg.InstanceProgressingDeadline)
		if err != nil {
			return nil, err
		}
		instances = append(instances, instance)
	}

	return &aimsv1.ListArgoInstancesResponse{
		Instances: instances,
	}, nil
}
