package aimsapi

import (
	"context"
	"encoding/json"
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/utils/aims"
)

func (s *AimsV1Server) UpdateAnnouncementBanner(ctx context.Context, req *aimsv1.UpdateAnnouncementBannerRequest) (*aimsv1.UpdateAnnouncementBannerResponse, error) {
	audit := req.GetAudit()
	if err := aimsutil.ValidateAuditPayload(audit); err != nil {
		return nil, err
	}

	var encoded []byte

	cm, err := aims.RetrieveConfigMap(s.cmInformer, config.PortalServerCmName)
	if err != nil {
		return nil, err
	}

	if !req.GetDelete() {
		banner := req.GetBanner()

		if banner == nil {
			return nil, fmt.Errorf("'banner' field cannot be empty")
		}

		if banner.GetMessage() == "" {
			return nil, fmt.Errorf("'message' field cannot be empty")
		}

		var links []*aimsv1.Banner_BannerLink

		for _, link := range banner.GetLinks() {
			links = append(links, &aimsv1.Banner_BannerLink{
				Name: link.GetName(),
				Url:  link.GetUrl(),
			})
		}

		announcementBanner := &aimsv1.Banner{
			Type:              banner.Type,
			Closable:          banner.Closable,
			Title:             banner.Title,
			Message:           banner.GetMessage(),
			Links:             links,
			PaidCustomersOnly: banner.PaidCustomersOnly,
		}

		encoded, err = json.Marshal(announcementBanner)
		if err != nil {
			return nil, err
		}
	}

	cm.Data[config.AnnouncementBannerKey] = string(encoded)

	if _, err := s.kubeclient.CoreV1().ConfigMaps(config.InternalCmNamespace).Update(ctx, cm, metav1.UpdateOptions{}); err != nil {
		return nil, err
	}

	return &aimsv1.UpdateAnnouncementBannerResponse{}, s.aimsAuditSvc.AuditAkuityPlatformPatch(ctx, "announcement-banner", audit, encoded)
}
