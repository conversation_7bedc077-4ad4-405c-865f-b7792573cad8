package aimsapi

import (
	"context"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/models/models"
)

func (s *AimsV1Server) ListTeams(ctx context.Context, req *aimsv1.ListTeamsRequest) (*aimsv1.ListTeamsResponse, error) {
	if req.GetOrganizationId() == "" {
		return nil, status.Error(codes.InvalidArgument, "organization_id is required")
	}

	limit := 10
	offset := 0
	if req.Limit != nil && *req.Limit > 0 {
		limit = int(*req.Limit)
	}
	if req.Offset != nil {
		offset = int(*req.Offset)
	}

	countQuery := models.Teams(
		qm.Where("organization_id = ?", req.GetOrganizationId()),
	)
	totalCount, err := countQuery.Count(ctx, s.db)
	if err != nil {
		return nil, err
	}

	var data []struct {
		ID                string      `boil:"id"`
		Name              string      `boil:"name"`
		Description       null.String `boil:"description"`
		CreationTimestamp null.Time   `boil:"creation_timestamp"`
		MemberCount       int64       `boil:"member_count"`
	}

	err = models.NewQuery(
		qm.Select(
			"team.id",
			"team.name",
			"team.description",
			"team.creation_timestamp",
			"(SELECT COUNT(*) FROM team_user WHERE team_user.team_id = team.id) AS member_count", // Subquery for count
		),
		qm.From(models.TableNames.Team),
		qm.Where("team.organization_id = ?", req.GetOrganizationId()),
		qm.OrderBy("team.creation_timestamp DESC"),
		qm.Limit(limit),
		qm.Offset(offset),
	).Bind(ctx, s.db, &data)
	if err != nil {
		return nil, err
	}

	var apiTeams []*aimsv1.Team
	for _, row := range data {
		apiTeams = append(apiTeams, &aimsv1.Team{
			Name:        row.Name,
			Description: row.Description.String,
			CreateTime:  timestamppb.New(row.CreationTimestamp.Time),
			MemberCount: row.MemberCount,
		})
	}

	return &aimsv1.ListTeamsResponse{
		Teams: apiTeams,
		Count: uint32(totalCount),
	}, nil
}
