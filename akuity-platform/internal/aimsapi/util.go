package aimsapi

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"strings"
	"time"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/utils/aims"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	stripepkg "github.com/akuityio/akuity-platform/pkg/billing/stripe"
)

type InternalOrganizationUserData struct {
	AkuityUser   models.AkuityUser   `boil:"akuity_user,bind"`
	Organization models.Organization `boil:"organization,bind"`
}

func internalOrganizationUserDataColumns() []string {
	return []string{
		"organization.id", "organization.name", "organization.creation_timestamp", "organization.org_status",
		"organization.max_instances", "organization.max_clusters", "organization.max_applications", "organization.feature_gates", "organization.plan", "organization.max_kargo_projects",
		"organization.max_kargo_instances", "organization.max_kargo_agents", "organization.max_workspaces",
	}
}

func CanAIMSDeleteOrganization(org *aimsv1.BasicOrganization) bool {
	// hard check when it must not be deleted
	if org.ManuallyVerified {
		return false
	}

	// safe check when it can be deleted when billed
	if org.StripeData != nil && org.StripeData.Stale {
		return true
	}

	// customer is deleted in stripe
	// in this case "stale" metadata couldn't be used so this is the only option
	if org.Inactive || !org.Billed {
		return true
	}

	return false
}

func (s *AimsV1Server) NewBasicOrg(ctx context.Context, org models.Organization) (*aimsv1.BasicOrganization, error) {
	data := []InternalOrganizationUserData{}

	err := models.NewQuery(
		qm.Select("akuity_user.email"),
		qm.From(models.TableNames.Organization),
		qm.InnerJoin("organization_user on organization_user.organization_id = organization.id"),
		qm.InnerJoin("akuity_user on akuity_user.id = organization_user.user_id"),
		qm.Where("organization.id = ?", org.ID),
	).Bind(ctx, s.db, &data)
	if err != nil {
		return nil, err
	}

	emailArray := make([]string, len(data))
	for i, user := range data {
		emailArray[i] = user.AkuityUser.Email
	}

	billing, err := models.Billings(qm.Where("organization_id = ?", org.ID)).One(ctx, s.db)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	billingDetails := &organizationv1.BillingDetails{}
	inactive := false

	if billing != nil {
		billingDetails.CustomerId = billing.CustomerID
		billingDetails.Manual = billing.Manual.Bool

		billingMetadata, err := billing.GetMetadata()
		if err != nil {
			return nil, err
		}

		billingDetails.Metadata = &organizationv1.BillingMetadata{
			Name: billingMetadata.Name,
		}

		inactive = billing.Inactive.Bool
	}

	instances, err := models.ArgoCDInstances(qm.Where("organization_owner = ?", org.ID)).Count(ctx, s.db)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	manuallyVerified := false
	config, err := aims.RetrieveInternalConfig(s.cmInformer)
	// if issues retreiving config, no need to fail, just log
	if err != nil {
		s.log.Error(err, "failed to retreive internal config")
	} else {
		manuallyVerified = config.VerifiedOrganizationMap[org.ID]
	}

	billed := billing != nil && !errors.Is(err, sql.ErrNoRows)

	return &aimsv1.BasicOrganization{
		Id:                org.ID,
		Name:              org.Name,
		Emails:            emailArray,
		Billed:            billed,
		NumInstances:      uint64(instances),
		ManuallyVerified:  manuallyVerified,
		Plan:              org.Plan.String,
		BillingDetails:    billingDetails,
		CreationTimestamp: timestamppb.New(org.CreationTimestamp),
		Inactive:          inactive,
	}, nil
}

// NewOrg is NewBasicOrg + Quota + Usage + Status info
func (s *AimsV1Server) NewOrg(ctx context.Context, org *models.Organization) (*aimsv1.BasicOrganization, error) {
	basicOrg, err := s.NewBasicOrg(ctx, *org)
	if err != nil {
		return nil, err
	}

	usage, err := s.featureSvc.GetOrgUsages(ctx, org.GetID())
	if err != nil {
		return nil, err
	}

	basicOrg.Usage = usage

	// Adding new addOns object with eks count
	clustersWithEKSAddon, err := models.ArgoCDClusters(
		qm.InnerJoin("argo_cd_instance aci ON aci.id = argo_cd_cluster.instance_id"),
		qm.Where("aci.organization_owner = ?", org.ID),
		qm.Where("argo_cd_cluster.spec->>'eksAddonEnabled' = 'true'"),
	).Count(ctx, s.db)
	if err != nil {
		return nil, err
	}

	basicOrg.Misc = map[string]int32{
		"eksClusters": int32(clustersWithEKSAddon),
	}

	orgStatus, err := org.GetOrgStatus()
	if err != nil {
		return nil, err
	}

	if orgStatus != nil {
		basicOrg.Status = &organizationv1.OrganizationStatus{
			Trial:           orgStatus.Trial,
			Expiry:          uint64(orgStatus.ExpiryTime),
			State:           string(orgStatus.State),
			BillingUpdating: orgStatus.BillingUpdating,
		}
	}

	quota, err := s.featureSvc.GetOrgQuotas(ctx, org.GetID())
	if err != nil {
		return nil, err
	}

	basicOrg.Quota = quota

	if basicOrg.BillingDetails != nil {
		// stripe things
		stripeCfg, err := config.NewStripeConfig()
		if err != nil {
			return nil, err
		}

		provider := stripepkg.NewStripeProvider(stripeCfg.Key, client.NewRepoSet(s.db))

		customer, err := provider.GetCustomerDetails(ctx, basicOrg.BillingDetails.CustomerId)
		if err != nil {
			if !strings.Contains(err.Error(), stripepkg.ErrNoStripeCustomer) {
				return nil, err
			}
			// ignore error if customer is not found in stripe
		}

		basicOrg.StripeData = &aimsv1.StripeData{}

		if customer != nil && customer.SubscriptionStatus != nil {
			basicOrg.StripeData.SubscriptionEndTime = timestamppb.New(customer.SubscriptionStatus.EndTime)
			basicOrg.BillingDetails.HasActiveSubscription = true
		}

		if customer != nil {
			basicOrg.StripeData.Stale = customer.Stale
		}
	}

	basicOrg.CanDelete = CanAIMSDeleteOrganization(basicOrg)

	return basicOrg, nil
}

// for joining tables - organization and kargo_instance
type InternalKargoInstanceData struct {
	Organization  models.Organization  `boil:"organization,bind"`
	KargoInstance models.KargoInstance `boil:"kargo_instance,bind"`
	Version       string               `boil:"config_version"`
}

// select the required columns, we have put every value manually
// sorry, cannot give organization.* and kargo_instance.* due to sqlboiler join limitation
func internalKargoInstanceDataColumns() []string {
	return []string{
		"organization.id", "organization.name", "organization.org_status",
		"kargo_instance.id", "kargo_instance.name", "kargo_instance.shard", "kargo_instance.creation_timestamp", "kargo_instance.deletion_timestamp",
		"kargo_instance.status_health", "kargo_instance.status_conditions", "kargo_instance.status_info",
		"kargo_instance.generation", "kargo_instance.status_observed_generation", "kargo_instance.workspace_id",
		"kargo_instance.status_hostname", "kargo_instance.status_recent_processed_event_info", "kargo_instance.description",
		"kargo_instance.organization_owner",
	}
}

func configVersionColumn() string {
	return "kargo_instance_config.version AS config_version"
}

func convertNullJSONToStruct(in null.JSON) (*structpb.Struct, error) {
	if !in.Valid {
		return nil, nil
	}
	var m map[string]interface{}
	if err := json.Unmarshal(in.JSON, &m); err != nil {
		return nil, err
	}
	return structpb.NewStruct(m)
}

func toRPCKargoInstance(kargo models.KargoInstance, instanceProgressingDeadline time.Duration) (*kargov1.KargoInstance, error) {
	status, err := kargo.GetStatus()
	if err != nil {
		return nil, err
	}

	reconciliationStatus, err := kargo.GetReconciliationStatus(instanceProgressingDeadline)
	if err != nil {
		return nil, err
	}

	var deleteTime *timestamppb.Timestamp
	if !kargo.DeletionTimestamp.IsZero() {
		deleteTime = timestamppb.New(kargo.DeletionTimestamp.Time)
	}

	return &kargov1.KargoInstance{
		Id:                   kargo.ID,
		Name:                 kargo.Name,
		Description:          kargo.Description.String,
		HealthStatus:         misc.NewHealthStatusV1(status.Health),
		ReconciliationStatus: misc.NewReconciliationStatusV1(reconciliationStatus),
		Generation:           uint32(kargo.Generation),
		DeleteTime:           deleteTime,
		// update it as you need - make sure you update the selector (ie. internalKargoInstanceDataColumns()) in join query you make
	}, nil
}

func toRPCKargoInstanceOrganization(org models.Organization) (*aimsv1.BasicOrganization, error) {
	status, err := org.GetOrgStatus()
	if err != nil {
		return nil, err
	}

	return &aimsv1.BasicOrganization{
		Id:     org.ID,
		Name:   org.Name,
		Billed: status.State == models.PaidCustomer,
	}, nil
}

// Notification events
func GetCustomNotificationPayload(metadata *structpb.Struct) (*models.EventMetadata, error) {
	metadataMap := metadata.AsMap()

	html, ok := metadataMap["body"]
	if !ok || html == "" {
		return nil, status.Error(codes.InvalidArgument, "expected body in metadata")
	}

	title, ok := metadataMap["title"]
	if !ok || title == "" {
		return nil, status.Error(codes.InvalidArgument, "expected title in metadata")
	}

	subject, ok := metadataMap["subject"]
	if !ok || subject == "" {
		return nil, status.Error(codes.InvalidArgument, "expected subject in metadata")
	}

	return &models.EventMetadata{
		CustomEvent: &models.CustomEvent{
			Title:   title.(string),
			Body:    html.(string),
			Subject: subject.(string),
		},
	}, nil
}

func GetNewFeatureNotificationPayload(metadata *structpb.Struct) (*models.EventMetadata, error) {
	metadataMap := metadata.AsMap()

	feature, ok := metadataMap["feature"]
	if !ok || feature == "" {
		return nil, status.Error(codes.InvalidArgument, "expected feature in metadata")
	}

	return &models.EventMetadata{
		NewFeature: &models.NewFeature{
			Feature: feature.(string),
		},
	}, nil
}
