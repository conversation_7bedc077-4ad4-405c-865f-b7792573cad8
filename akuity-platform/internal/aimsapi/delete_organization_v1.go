package aimsapi

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
	"github.com/akuityio/akuity-platform/internal/auth0"
	internalconfig "github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/validator"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func (s *AimsV1Server) DeleteOrganization(ctx context.Context, req *aimsv1.DeleteOrganizationRequest) (*aimsv1.DeleteOrganizationResponse, error) {
	audit := req.GetAudit()

	if err := aimsutil.ValidateAuditPayload(audit); err != nil {
		return nil, err
	}

	org, err := models.Organizations(models.OrganizationWhere.ID.EQ(req.OrganizationId)).One(ctx, s.db)
	if err != nil {
		return nil, err
	}

	basicOrg, err := s.NewOrg(ctx, org)
	if err != nil {
		return nil, err
	}

	if !basicOrg.CanDelete {
		return nil, status.Error(codes.InvalidArgument, "cannot delete organization. its either manually verified or not inactive or does not have 'stale' assigned in stripe metadata")
	}

	txDB, txBeginner := database.WithTxBeginner(s.db)
	reposet := client.NewRepoSet(txDB)

	auth0Client, err := auth0.New(ctx, s.cfg.Auth0)
	if err != nil {
		return nil, err
	}

	v := validator.New()
	os := organizations.New(txDB, txBeginner, auth0Client, s.billingProviders, reposet, internalconfig.PortalServerConfig{
		DomainSuffix: s.cfg.DomainSuffix,
	}, v)

	newCtx := s.aimsAuditSvc.SetActor(ctx, audit)

	if err := os.Delete(newCtx, basicOrg.Id); err != nil {
		if organizations.IsFailedPreconditionError(err) {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
		return nil, err
	}

	return &aimsv1.DeleteOrganizationResponse{}, nil
}
