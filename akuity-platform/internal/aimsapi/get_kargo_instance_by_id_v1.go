package aimsapi

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func (s *AimsV1Server) GetKargoInstanceById(ctx context.Context, req *aimsv1.GetKargoInstanceByIdRequest) (*aimsv1.GetKargoInstanceByIdResponse, error) {
	id := req.GetInstanceId()
	if id == "" {
		return nil, status.Error(codes.InvalidArgument, "instance_id required")
	}

	data := InternalKargoInstanceData{}
	err := models.NewQuery(
		qm.Select(internalKargoInstanceDataColumns()...),
		qm.From(models.TableNames.KargoInstance),
		qm.InnerJoin("organization on organization.id = kargo_instance.organization_owner"),
		qm.Where("kargo_instance.id = ?", id),
	).Bind(ctx, s.db, &data)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, status.Error(codes.NotFound, fmt.Sprintf("kargo instance %s not found", id))
		}
		return nil, err
	}

	workspace, err := models.Workspaces(
		qm.Select("id", "name"),
		qm.Where("id = ?", data.KargoInstance.WorkspaceID),
	).One(ctx, s.db)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	var apiWorkspace *aimsv1.Workspace
	if workspace != nil {
		apiWorkspace = &aimsv1.Workspace{
			Id:   workspace.ID,
			Name: workspace.Name,
		}
	}

	instance, err := toRPCKargoInstance(data.KargoInstance, s.cfg.InstanceProgressingDeadline)
	if err != nil {
		return nil, err
	}

	rs := client.NewRepoSet(s.db)
	instanceConfig, err := rs.KargoInstanceConfigs().GetByID(ctx, instance.Id)
	if err != nil {
		return nil, err
	}

	if instanceConfig.Subdomain.Valid {
		instance.Subdomain = instanceConfig.Subdomain.String
	}
	if instanceConfig.Version.Valid {
		instance.Version = instanceConfig.Version.String
	}
	if instanceConfig.FQDN.Valid {
		instance.Fqdn = instanceConfig.FQDN.String
	}

	org, err := toRPCKargoInstanceOrganization(data.Organization)
	if err != nil {
		return nil, err
	}

	statusInfo := make(map[string]interface{})
	if data.KargoInstance.StatusObservedGeneration.Valid {
		statusInfo["observedGeneration"] = data.KargoInstance.StatusObservedGeneration.Int
	}
	if data.KargoInstance.StatusHostname.Valid && data.KargoInstance.StatusHostname.String != "" {
		statusInfo["hostname"] = data.KargoInstance.StatusHostname.String
	}
	if data.KargoInstance.StatusConditions.Valid {
		var conditions interface{}
		if err := json.Unmarshal(data.KargoInstance.StatusConditions.JSON, &conditions); err == nil {
			statusInfo["conditions"] = conditions
		}
	}
	if data.KargoInstance.StatusRecentProcessedEventInfo.Valid {
		var eventInfo interface{}
		if err := json.Unmarshal(data.KargoInstance.StatusRecentProcessedEventInfo.JSON, &eventInfo); err == nil {
			statusInfo["recentProcessedEventInfo"] = eventInfo
		}
	}
	if data.KargoInstance.Shard != "" {
		statusInfo["shard"] = data.KargoInstance.Shard
	}
	if data.KargoInstance.Description.Valid && data.KargoInstance.Description.String != "" {
		statusInfo["description"] = data.KargoInstance.Description.String
	}

	var statusStruct *structpb.Struct
	if len(statusInfo) > 0 {
		var err error
		statusStruct, err = structpb.NewStruct(statusInfo)
		if err != nil {
			return nil, err
		}
	}

	promotions, err := rs.KargoPromotions(
		qm.Where("instance_id = ?", instance.Id),
		qm.OrderBy("start_time DESC"),
		qm.Limit(50),
	).ListAll(ctx)
	if err != nil {
		return nil, err
	}

	var promotionStructs []*structpb.Struct
	for _, promotion := range promotions {
		promotionInfo := map[string]interface{}{
			"id":            promotion.ID,
			"projectName":   promotion.ProjectName,
			"stageName":     promotion.StageName,
			"promotionName": promotion.PromotionName,
			"startTime":     promotion.StartTime.Format(time.RFC3339),
			"endTime":       promotion.EndTime.Format(time.RFC3339),
			"resultPhase":   promotion.ResultPhase,
			"resultMessage": promotion.ResultMessage,
		}

		if promotion.Details.Valid {
			var details interface{}
			if err := json.Unmarshal(promotion.Details.JSON, &details); err == nil {
				promotionInfo["details"] = details
			}
		}

		if promotionStruct, err := structpb.NewStruct(promotionInfo); err == nil {
			promotionStructs = append(promotionStructs, promotionStruct)
		}
	}

	var instanceSpecsStruct *structpb.Struct
	if instanceConfig.Spec.Valid {
		var specData map[string]interface{}
		if err := json.Unmarshal(instanceConfig.Spec.JSON, &specData); err == nil {
			// Pass through the entire spec data generically
			if len(specData) > 0 {
				if extStruct, err := structpb.NewStruct(specData); err == nil {
					instanceSpecsStruct = extStruct
				}
			}
		}
	}

	var oidcConfigStruct *structpb.Struct
	if oidcConfig, err := instanceConfig.GetOidcConfig(); err == nil {
		if oidcConfig.Enabled || oidcConfig.DexEnabled || oidcConfig.DexConfig != "" || oidcConfig.IssuerURL != "" {
			if oidcConfigJSON, err := json.Marshal(oidcConfig); err == nil {
				var oidcConfigMap map[string]interface{}
				if err := json.Unmarshal(oidcConfigJSON, &oidcConfigMap); err == nil {
					if oidcConfigMap["dex_encrypted_config_secrets"] != nil && oidcConfigMap["dex_encrypted_config_secrets"] != "" {
						oidcConfigMap["dex_encrypted_config_secrets"] = "***"
					}
					if oidcConfigMap["dex_encrypted_config"] != nil && oidcConfigMap["dex_encrypted_config"] != "" {
						oidcConfigMap["dex_encrypted_config"] = "***"
					}

					if oidcConfig.DexConfig != "" {
						oidcConfigMap["dex_config"] = oidcConfig.DexConfig
					}

					if oidcStruct, err := structpb.NewStruct(oidcConfigMap); err == nil {
						oidcConfigStruct = oidcStruct
					}
				}
			}
		}
	}

	var controllerConfigStruct *structpb.Struct
	if instanceConfig.ControllerCM.Valid {
		if controllerStruct, err := convertNullJSONToStruct(instanceConfig.ControllerCM); err == nil {
			controllerConfigStruct = controllerStruct
		}
	}

	var webhookConfigStruct *structpb.Struct
	if instanceConfig.WebhookCM.Valid {
		if webhookStruct, err := convertNullJSONToStruct(instanceConfig.WebhookCM); err == nil {
			webhookConfigStruct = webhookStruct
		}
	}

	var apiCmStruct *structpb.Struct
	if instanceConfig.APICM.Valid {
		if apiStruct, err := convertNullJSONToStruct(instanceConfig.APICM); err == nil {
			apiCmStruct = apiStruct
		}
	}

	var apiSecretStruct *structpb.Struct
	if apiSecret, err := instanceConfig.GetAPISecret(); err == nil {
		if apiSecret.AdminAccountPasswordHash != "" {
			censoredAPISecret := map[string]interface{}{
				"admin_account_password_hash": "***",
			}
			if secretStruct, err := structpb.NewStruct(censoredAPISecret); err == nil {
				apiSecretStruct = secretStruct
			}
		}
	}

	var miscellaneousSecretsStruct *structpb.Struct
	if miscSecrets, err := instanceConfig.GetMiscellaneousSecrets(true); err == nil {
		if miscSecrets.DatadogRolloutsSecret != nil || miscSecrets.NewRelicRolloutsSecret != nil || miscSecrets.InfluxDbRolloutsSecret != nil {
			if miscSecretsJSON, err := json.Marshal(miscSecrets); err == nil {
				var miscSecretsMap map[string]interface{}
				if err := json.Unmarshal(miscSecretsJSON, &miscSecretsMap); err == nil {
					if miscStruct, err := structpb.NewStruct(miscSecretsMap); err == nil {
						miscellaneousSecretsStruct = miscStruct
					}
				}
			}
		}
	}

	var certificateStatusStruct *structpb.Struct
	if data.KargoInstance.StatusConditions.Valid {
		var conditions map[string]interface{}
		if err := json.Unmarshal(data.KargoInstance.StatusConditions.JSON, &conditions); err == nil {
			if certStatus, exists := conditions["certificateStatus"]; exists {
				if certStatusMap, ok := certStatus.(map[string]interface{}); ok {
					certificateStatusStruct, _ = structpb.NewStruct(certStatusMap)
				}
			}
		}
	}

	return &aimsv1.GetKargoInstanceByIdResponse{
		Instance: &aimsv1.InternalKargoInstance{
			Instance:             instance,
			Organization:         org,
			Workspace:            apiWorkspace,
			CreationTimestamp:    timestamppb.New(data.KargoInstance.CreationTimestamp),
			StatusInfo:           statusStruct,
			Promotions:           promotionStructs,
			OidcConfig:           oidcConfigStruct,
			ControllerConfig:     controllerConfigStruct,
			WebhookConfig:        webhookConfigStruct,
			InstanceSpecs:        instanceSpecsStruct,
			ApiCm:                apiCmStruct,
			ApiSecret:            apiSecretStruct,
			MiscellaneousSecrets: miscellaneousSecretsStruct,
			CertificateStatus:    certificateStatusStruct,
		},
	}, nil
}
