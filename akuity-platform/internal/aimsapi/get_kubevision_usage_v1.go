package aimsapi

import (
	"context"
	"time"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/client"
)

func (s *AimsV1Server) GetKubeVisionUsage(ctx context.Context, req *aimsv1.GetKubeVisionUsageRequest) (*aimsv1.GetKubeVisionUsageResponse, error) {
	repoSet := client.NewRepoSet(s.db)
	resSvc := k8sresource.NewServiceWithOptions(repoSet, s.db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)))

	startTime := time.Time{}
	if req.GetStartTime() != nil {
		startTime = req.GetStartTime().AsTime()
	}
	endTime := time.Now()
	if req.GetEndTime() != nil {
		endTime = req.GetEndTime().AsTime()
	}

	usage, err := resSvc.GetKubeVisionUsage(ctx, startTime, endTime)
	if err != nil {
		return nil, err
	}
	return &aimsv1.GetKubeVisionUsageResponse{Usage: usage}, nil
}
