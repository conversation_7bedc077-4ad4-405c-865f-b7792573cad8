package aimsutil

import (
	"reflect"
	"strings"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	"github.com/akuityio/akuity-platform/models/models"
	statusmodel "github.com/akuityio/akuity-platform/models/util/status"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

type InternalInstanceData struct {
	Instance       models.ArgoCDInstance       `boil:"argo_cd_instance,bind"`
	Organization   models.Organization         `boil:"organization,bind"`
	InstanceConfig models.ArgoCDInstanceConfig `boil:"argo_cd_instance_config,bind"`
	ClustersCount  int                         `boil:"clusters_count"`
}

func NewArgoCDInstanceV1(internalInstance InternalInstanceData,
	instanceProgressingDeadline time.Duration,
) (*aimsv1.InternalInstance, error) {
	spec, err := internalInstance.InstanceConfig.GetSpec()
	if err != nil {
		return nil, err
	}

	modelsConfigMap, err := internalInstance.InstanceConfig.GetArgoCDConfigMap()
	if err != nil {
		return nil, err
	}

	configMap := argocdutil.NewArgoCDConfigMapV1(modelsConfigMap)
	modelsRbacConfigMap, err := internalInstance.InstanceConfig.GetArgoCDRbacConfigMap()
	if err != nil {
		return nil, err
	}
	rbacConfigMap := argocdutil.NewArgoCDRBACConfigMapV1(modelsRbacConfigMap)

	ipAllowList, _ := types.MapSlice(spec.IpAllowlist, func(in models.IpAllowlistEntry) (*argocdv1.IPAllowListEntry, error) {
		return &argocdv1.IPAllowListEntry{
			Ip:          in.Ip,
			Description: in.Description,
		}, nil
	})
	hostAliases, _ := types.MapSlice(spec.HostAliases, func(in models.HostAliases) (*argocdv1.HostAliases, error) {
		return &argocdv1.HostAliases{
			Ip:        in.Ip,
			Hostnames: in.Hostnames,
		}, nil
	})
	extensions, _ := types.MapSlice(spec.Extensions, func(in models.ArgoCDExtensionInstallEntry) (*argocdv1.ArgoCDExtensionInstallEntry, error) {
		return &argocdv1.ArgoCDExtensionInstallEntry{
			Id:      in.ID,
			Version: in.Version,
		}, nil
	})

	instanceStatus, err := internalInstance.Instance.GetStatus()
	if err != nil {
		return nil, err
	}

	if instanceStatus.Health.Code == statusmodel.HealthStatusCodeUnknown {
		instanceStatus.Health.Message = "failed to get tenant status"
	}

	reconciliationStatus, err := internalInstance.Instance.GetReconciliationStatus(instanceProgressingDeadline)
	if err != nil {
		return nil, err
	}

	argoCDSecret, err := internalInstance.InstanceConfig.GetArgocdSecret()
	if err != nil {
		return nil, err
	}
	for k := range argoCDSecret {
		if instances.IsManagedArgoCDSecretKey(k) {
			delete(argoCDSecret, k)
		} else {
			argoCDSecret[k] = ""
		}
	}

	var deleteTime *timestamppb.Timestamp
	if !internalInstance.Instance.DeletionTimestamp.IsZero() {
		deleteTime = timestamppb.New(internalInstance.Instance.DeletionTimestamp.Time)
	}

	delegate := argocdutil.NewRepoServerDelegate(spec.RepoServerDelegate)
	imageUpdaterDelegate := argocdutil.NewImageUpdaterDelegate(spec.ImageUpdaterDelegate)
	appSetDelegate := argocdutil.NewAppSetDelegate(spec.AppSetDelegate)

	kustomization, err := structpb.NewStruct(spec.ClusterCustomizationDefaults.Kustomization)
	if err != nil {
		return nil, err
	}

	processedEventInfo, err := internalInstance.Instance.GetRecentProcessedEventInfo()
	if err != nil {
		return nil, err
	}

	agentPermissionsRules, _ := types.MapSlice(spec.AgentPermissionsRules, func(in models.AgentPermissionsRule) (*argocdv1.AgentPermissionsRule, error) {
		return &argocdv1.AgentPermissionsRule{
			ApiGroups: in.APIGroups,
			Resources: in.Resources,
			Verbs:     in.Verbs,
		}, nil
	})

	inst := &argocdv1.Instance{
		Id:                     internalInstance.Instance.ID,
		Shard:                  internalInstance.Instance.Shard,
		Name:                   internalInstance.Instance.Name,
		Hostname:               internalInstance.Instance.StatusHostname.String,
		ClusterCount:           uint32(internalInstance.ClustersCount),
		Secrets:                argoCDSecret,
		Generation:             uint32(internalInstance.Instance.Generation),
		RecentProcessedEventId: uint32(processedEventInfo.EventId),
		HealthStatus:           argocdutil.NewInstanceHealthStatusV1(instanceStatus),
		ReconciliationStatus:   argocdutil.NewReconciliationStatusV1(reconciliationStatus),
		DeleteTime:             deleteTime,
		OwnerOrganizationName:  internalInstance.Organization.Name,
		Description:            internalInstance.Instance.Description.String,
		Version:                internalInstance.InstanceConfig.Version.String,
		Spec: &argocdv1.InstanceSpec{
			IpAllowList:                  ipAllowList,
			HostAliases:                  hostAliases,
			Subdomain:                    internalInstance.InstanceConfig.Subdomain,
			Fqdn:                         internalInstance.InstanceConfig.FQDN.String,
			DeclarativeManagementEnabled: spec.DeclarativeManagementEnabled,
			Extensions:                   extensions,
			ClusterCustomizationDefaults: &argocdv1.ClusterCustomization{
				Kustomization:       kustomization,
				AutoUpgradeDisabled: spec.ClusterCustomizationDefaults.AutoUpgradeDisabled,
				AppReplication:      spec.ClusterCustomizationDefaults.AppReplication,
				RedisTunneling:      spec.ClusterCustomizationDefaults.RedisTunneling,
			},
			ImageUpdaterEnabled:         internalInstance.InstanceConfig.ArgocdImageUpdaterEnable.Bool,
			BackendIpAllowListEnabled:   spec.BackendIpAllowlistEnabled,
			AuditExtensionEnabled:       spec.AuditExtensionEnabled,
			SyncHistoryExtensionEnabled: spec.SyncHistoryExtensionEnabled,
			AssistantExtensionEnabled:   spec.AssistantExtensionEnabled,
			CrossplaneExtension:         argocdutil.NewCrossplaneExtensionV1(spec.CrossplaneExtension),
			RepoServerDelegate:          delegate,
			ImageUpdaterDelegate:        imageUpdaterDelegate,
			AppSetDelegate:              appSetDelegate,
			AppsetPolicy: &argocdv1.AppsetPolicy{
				Policy:         spec.AppsetPolicy.Policy,
				OverridePolicy: spec.AppsetPolicy.PolicyOverrideEnabled,
			},
			AgentPermissionsRules: agentPermissionsRules,
			ImageUpdaterVersion:   spec.ImageUpdaterVersion,
			KubeVisionConfig:      argocdutil.NewKubeVisionConfig(spec.KubeVisionConfig),
		},
		Config:     configMap,
		RbacConfig: rbacConfigMap,
		Info: &argocdv1.InstanceInfo{
			ApplicationsStatus: argocdutil.NewApplicationsStatusV1(instanceStatus),
		},
		WorkspaceId: internalInstance.Instance.WorkspaceID.String,
	}

	orgStatus, err := internalInstance.Organization.GetOrgStatus()
	if err != nil {
		return nil, err
	}

	notificationConfig, err := internalInstance.InstanceConfig.GetArgoCDNotificationsConfigMap()
	if err != nil {
		return nil, err
	}

	processedInfoStr := string(internalInstance.Instance.StatusRecentProcessedEventInfo.JSON)
	return &aimsv1.InternalInstance{
		Instance:            inst,
		CreateTime:          timestamppb.New(internalInstance.Instance.CreationTimestamp),
		OrgId:               internalInstance.Organization.ID,
		Expired:             orgStatus.ExpiryTime < time.Now().Unix(),
		ConnectedClusters:   uint32(internalInstance.ClustersCount),
		StatusProcessedInfo: &processedInfoStr,
		NotificationConfig: &aimsv1.NotificationConfig{
			Config: notificationConfig,
		},
	}, nil
}

func GetAllColumnsOfInstance() []string {
	cols := []string{}

	values := reflect.ValueOf(models.ArgoCDInstanceColumns)

	for i := 0; i < values.NumField(); i++ {
		fieldValue := values.Field(i).Interface().(string)
		cols = append(cols, models.TableNames.ArgoCDInstance+"."+fieldValue)
	}

	return cols
}

func GetAllColumnsOfOrganization() []string {
	cols := []string{}

	values := reflect.ValueOf(models.OrganizationColumns)

	for i := 0; i < values.NumField(); i++ {
		fieldValue := values.Field(i).Interface().(string)
		cols = append(cols, models.TableNames.Organization+"."+fieldValue)
	}

	return cols
}

func GetAllColumnsOfInstanceConfig() []string {
	cols := []string{}

	values := reflect.ValueOf(models.ArgoCDInstanceConfigColumns)

	for i := 0; i < values.NumField(); i++ {
		fieldValue := values.Field(i).Interface().(string)
		cols = append(cols, models.TableNames.ArgoCDInstanceConfig+"."+fieldValue)
	}

	return cols
}

func ValidateAuditPayload(audit *aimsv1.Audit) error {
	if audit == nil {
		return status.Error(codes.InvalidArgument, "must provide audit details")
	}

	emailSegments := strings.Split(audit.Actor, "@")
	if len(emailSegments) != 2 {
		return status.Errorf(codes.InvalidArgument, "invalid email: %q", audit.Actor)
	}

	if audit.Reason == "" {
		return status.Error(codes.InvalidArgument, "reason must not be empty")
	}

	return nil
}
