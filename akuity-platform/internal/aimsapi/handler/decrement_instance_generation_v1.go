package handler

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
	instanceutil "github.com/akuityio/akuity-platform/internal/akputil/instance"
	aimsservice "github.com/akuityio/akuity-platform/internal/services/aims"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/models"
)

type DecrementInstanceGenerationHandlerFunc func(ctx context.Context, req *aimsv1.DecrementInstanceGenerationRequest) (*aimsv1.DecrementInstanceGenerationResponse, error)

func DecrementInstanceGenerationV1(db *sql.DB, aimsAuditSvc aimsservice.AimsAuditService) DecrementInstanceGenerationHandlerFunc {
	return func(ctx context.Context, req *aimsv1.DecrementInstanceGenerationRequest) (*aimsv1.DecrementInstanceGenerationResponse, error) {
		audit := req.GetAudit()

		if err := aimsutil.ValidateAuditPayload(audit); err != nil {
			return nil, err
		}

		id := req.GetInstanceId()

		instanceQuery := instanceutil.DecrementArgoInstanceObservedGeneration
		clustersQuery := instanceutil.DecrementArgoClustersObservedGeneration

		txCtx, cancel := context.WithCancel(ctx)
		defer cancel()

		txDB, txBeginner := database.WithTxBeginner(db)
		tx, err := txBeginner.Begin(txCtx)
		if err != nil {
			return nil, err
		}

		iq := models.NewQuery(qm.SQL(instanceQuery, id))
		res, err := iq.Exec(txDB)
		if err != nil {
			return nil, err
		}

		affected, err := res.RowsAffected()
		if err != nil {
			return nil, err
		}

		if affected == 0 {
			return nil, fmt.Errorf("instance %s not found", id)
		}

		q := models.NewQuery(qm.SQL(clustersQuery, id))
		if _, err := q.Exec(txDB); err != nil {
			return nil, err
		}

		if err := tx.Commit(); err != nil {
			return nil, err
		}

		return &aimsv1.DecrementInstanceGenerationResponse{}, aimsAuditSvc.AuditDecrementArgoCDInstanceGeneration(ctx, id, audit)
	}
}
