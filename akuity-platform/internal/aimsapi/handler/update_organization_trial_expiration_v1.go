package handler

import (
	"context"
	"database/sql"

	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
	aimsservice "github.com/akuityio/akuity-platform/internal/services/aims"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

type UpdateOrganizationTrialExpirationHandlerFunc func(ctx context.Context, req *aimsv1.UpdateOrganizationTrialExpirationRequest) (*aimsv1.UpdateOrganizationTrialExpirationResponse, error)

func UpdateOrganizationTrialExpirationV1(db *sql.DB) UpdateOrganizationTrialExpirationHandlerFunc {
	rs := client.NewRepoSet(db)
	aimsAuditSvc := aimsservice.NewAimsAuditService(rs)
	return func(ctx context.Context, req *aimsv1.UpdateOrganizationTrialExpirationRequest) (*aimsv1.UpdateOrganizationTrialExpirationResponse, error) {
		audit := req.GetAudit()
		if err := aimsutil.ValidateAuditPayload(audit); err != nil {
			return nil, err
		}
		newCtx := aimsAuditSvc.SetActor(ctx, audit)
		org, err := models.Organizations(qm.Where("id = ?", req.GetOrganizationId())).One(newCtx, db)
		if err != nil {
			return nil, err
		}
		status, err := org.GetOrgStatus()
		if err != nil {
			return nil, err
		}
		if err := org.SetOrgStatus(models.OrgStatus{
			Trial:                true,
			ExpiryTime:           int64(req.GetTrialExpiration()),
			LastEventProcessedAt: status.LastEventProcessedAt,
		}); err != nil {
			return nil, err
		}

		if _, err := org.Update(newCtx, db, boil.Infer()); err != nil {
			return nil, err
		}
		return &aimsv1.UpdateOrganizationTrialExpirationResponse{}, nil
	}
}
