package handler

import (
	"context"
	"database/sql"
	"errors"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/models/models"
)

type ListUnbilledOrganizationsHandlerFunc func(ctx context.Context, req *aimsv1.ListUnbilledOrganizationsRequest) (*aimsv1.ListUnbilledOrganizationsResponse, error)

func ListUnbilledOrganizationsV1(
	db *sql.DB,
	domainSuffix string,
) ListUnbilledOrganizationsHandlerFunc {
	return func(ctx context.Context, req *aimsv1.ListUnbilledOrganizationsRequest) (*aimsv1.ListUnbilledOrganizationsResponse, error) {
		orgs, err := models.Organizations().All(ctx, db)
		if err != nil {
			return nil, err
		}

		var basicOrgs []*aimsv1.BasicOrganization
		for _, org := range orgs {
			billing, err := models.Billings(qm.Where("organization_id = ?", org.ID)).One(ctx, db)
			if err != nil && !errors.Is(err, sql.ErrNoRows) {
				return nil, err
			}
			basicOrg := &aimsv1.BasicOrganization{
				Id:   org.ID,
				Name: org.Name,
			}
			if billing == nil || errors.Is(err, sql.ErrNoRows) {
				basicOrg.Billed = false
			}
			basicOrgs = append(basicOrgs, basicOrg)
		}

		return &aimsv1.ListUnbilledOrganizationsResponse{
			Organizations: basicOrgs,
		}, nil
	}
}
