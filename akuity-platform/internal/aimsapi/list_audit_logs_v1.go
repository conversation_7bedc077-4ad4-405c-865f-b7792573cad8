package aimsapi

import (
	"context"
	"fmt"
	"time"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/portalapi/organization"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *AimsV1Server) ListAuditLogs(ctx context.Context, req *aimsv1.ListAuditLogsRequest) (*aimsv1.ListAuditLogsResponse, error) {
	if req.GetOrganizationId() == "" {
		return nil, status.Error(codes.InvalidArgument, "organization id missing")
	}

	filters := req.GetFilters()

	objects := organization.CreateAuditLogObjectsFromFilter(filters)

	f := organizations.AuditLogFilter{
		ActorID:   filters.ActorId,
		Action:    filters.Action,
		ActorType: filters.ActorType,
		Objects:   objects,
	}

	var err error
	if filters.StartTime != nil {
		if f.StartTime, err = time.Parse(time.RFC3339, *filters.StartTime); err != nil {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
	}

	if filters.EndTime != nil {
		if f.EndTime, err = time.Parse(time.RFC3339, *filters.EndTime); err != nil {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
	}

	// Set limit and offset with default values if not provided
	limit := 50
	if filters.Limit != nil && *filters.Limit > 0 {
		limit = int(*filters.Limit)
	}

	offset := 0
	if filters.Offset != nil {
		offset = int(*filters.Offset)
	}

	mods := append([]qm.QueryMod{
		models.AuditLogWhere.OrganizationID.EQ(null.StringFrom(req.GetOrganizationId())),
		models.AuditLogWhere.IsDeleted.EQ(false),
	}, f.GetMods()...)

	countQuery := models.AuditLogs(mods...)
	totalCount, err := countQuery.Count(ctx, s.db)
	if err != nil {
		return nil, err
	}

	logItems, err := models.AuditLogs(
		append(mods,
			qm.OrderBy(fmt.Sprintf("%s desc", models.AuditLogColumns.Timestamp)),
			qm.Limit(limit),
			qm.Offset(offset),
		)...).All(ctx, s.db)
	if err != nil {
		return nil, err
	}

	items := make([]*organizationv1.AuditLog, 0)
	for _, auditLog := range logItems {
		auditLogV1, err := organization.MapAuditLogToRPCEntity(auditLog)
		if err != nil {
			return nil, err
		}
		items = append(items, auditLogV1)
	}
	return &aimsv1.ListAuditLogsResponse{
		AuditLogs: items,
		Count:     uint32(totalCount),
	}, nil
}
