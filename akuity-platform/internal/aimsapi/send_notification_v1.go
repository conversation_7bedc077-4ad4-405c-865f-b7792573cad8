package aimsapi

import (
	"context"
	"fmt"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	aimsutil "github.com/akuityio/akuity-platform/internal/aimsapi/util"
	"github.com/akuityio/akuity-platform/internal/services/notifications"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func (s *AimsV1Server) SendNotification(ctx context.Context, req *aimsv1.SendNotificationRequest) (*aimsv1.SendNotificationResponse, error) {
	audit := req.GetAudit()

	if err := aimsutil.ValidateAuditPayload(audit); err != nil {
		return nil, err
	}

	newCtx := s.aimsAuditSvc.SetActor(ctx, audit)

	rs := client.NewRepoSet(s.db)

	event := &models.Event{}

	metadata := req.GetMetadata()
	if metadata == nil {
		return nil, status.Error(codes.InvalidArgument, "metadata is required for notification")
	}

	switch req.GetCategory() {
	case aimsv1.NotificationCategory_NOTIFICATION_CATEGORY_CUSTOM:
		eventMetadata, err := GetCustomNotificationPayload(metadata)
		if err != nil {
			return nil, err
		}
		event.EventType = null.StringFrom(models.EventTypeCustom)
		err = event.SetMetadata(*eventMetadata)
		if err != nil {
			return nil, err
		}
	case aimsv1.NotificationCategory_NOTIFICATION_CATEGORY_NEW_FEATURE:
		eventMetadata, err := GetNewFeatureNotificationPayload(metadata)
		if err != nil {
			return nil, err
		}

		event.EventType = null.StringFrom(models.EventTypeNewFeature)
		err = event.SetMetadata(*eventMetadata)
		if err != nil {
			return nil, err
		}
	default:
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("unknown category %s", req.GetCategory()))
	}

	if req.GetTestMode() {
		if req.GetOrganizationId() == "" {
			return nil, status.Error(codes.InvalidArgument, "you cannot send notification to all organization in test mode")
		}

		mods := []qm.QueryMod{
			qm.From(models.TableNames.AkuityUser),
			qm.InnerJoin("organization_user on organization_user.user_id = akuity_user.id and organization_user.organization_id = ?", req.GetOrganizationId()),
			qm.InnerJoin("organization on organization.id = organization_user.organization_id"),
			qm.Where("akuity_user.email = ?", audit.Actor),
		}

		countQuery := models.NewQuery(mods...)
		queries.SetCount(countQuery)
		var count int64
		err := countQuery.QueryRowContext(ctx, s.db).Scan(&count)
		if err != nil || count == 0 {
			return nil, status.Error(codes.PermissionDenied, "you cannot send test notification to an organization you are not member of")
		}

		event.OrganizationID = null.StringFrom(req.GetOrganizationId())
	}

	if !req.GetTestMode() && req.GetOrganizationId() != "" {
		return nil, status.Error(codes.InvalidArgument, "you cannot send notification to individual organization in non-test mode")
	}

	err, eventCreated := notifications.CreateEventWithDeDuplication(newCtx, rs, event)

	if err != nil {
		return nil, err
	}

	if !eventCreated {
		return nil, status.Error(codes.InvalidArgument, "notification was not sent")
	}

	return &aimsv1.SendNotificationResponse{}, nil
}
