package aimsapi

import (
	"context"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/models/models"
)

func (s *AimsV1Server) GetOrganization(ctx context.Context, req *aimsv1.GetOrganizationRequest) (*aimsv1.GetOrganizationResponse, error) {
	org, err := models.Organizations(models.OrganizationWhere.ID.EQ(req.OrganizationId)).One(ctx, s.db)
	if err != nil {
		return nil, err
	}

	basicOrg, err := s.NewOrg(ctx, org)
	if err != nil {
		return nil, err
	}

	return &aimsv1.GetOrganizationResponse{
		Organization: basicOrg,
	}, nil
}
