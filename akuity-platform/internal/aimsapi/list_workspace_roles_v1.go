package aimsapi

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/services/customroles"
	"github.com/akuityio/akuity-platform/models/client"
)

func (s *AimsV1Server) ListWorkspaceCustomRoles(ctx context.Context, req *aimsv1.ListWorkspaceCustomRolesRequest) (*aimsv1.ListWorkspaceCustomRolesResponse, error) {
	if req.GetOrganizationId() == "" {
		return nil, status.Error(codes.InvalidArgument, "organization_id is required")
	}
	if req.GetWorkspaceId() == "" {
		return nil, status.Error(codes.InvalidArgument, "workspace_id is required")
	}

	limit := req.GetLimit()
	offset := req.GetOffset()
	if limit == 0 {
		limit = 100
	}

	repoSet := client.NewRepoSet(s.db)
	crs := customroles.New(repoSet)

	roles, err := crs.ListWorkspaceCustomRoles(ctx, req.OrganizationId, req.WorkspaceId, limit, offset)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to list custom roles: %v", err)
	}

	count, err := crs.CountCustomRole(ctx, req.OrganizationId, req.WorkspaceId)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to count custom roles: %v", err)
	}

	resp := &aimsv1.ListWorkspaceCustomRolesResponse{
		CustomRoles: make([]*aimsv1.CustomRole, len(roles)),
		TotalCount:  count,
		WorkspaceId: req.GetWorkspaceId(),
	}

	for i, role := range roles {
		resp.CustomRoles[i] = &aimsv1.CustomRole{
			Id:          role.ID,
			Name:        role.Name,
			Description: role.Description.String,
			Policy:      role.Policy.String,
		}
	}

	return resp, nil
}
