package aimsapi

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	aimsv1 "github.com/akuityio/akuity-platform/aims/pkg/api/gen/aims/v1"
	"github.com/akuityio/akuity-platform/internal/services/teams"
)

func (s *AimsV1Server) ListTeamMembers(ctx context.Context, req *aimsv1.ListTeamMembersRequest) (*aimsv1.ListTeamMembersResponse, error) {
	if req.GetOrganizationId() == "" {
		return nil, status.Error(codes.InvalidArgument, "organization_id is required")
	}
	if req.GetTeamName() == "" {
		return nil, status.Error(codes.InvalidArgument, "team_name is required")
	}

	limit := 10
	offset := 0
	if req.Limit != nil && *req.Limit > 0 {
		limit = int(*req.Limit)
	}
	if req.Offset != nil {
		offset = int(*req.Offset)
	}

	teamSvc := teams.NewService(s.db)
	team, err := teamSvc.GetTeam(ctx, req.GetOrganizationId(), req.GetTeamName())
	if err != nil {
		return nil, status.Errorf(codes.NotFound, "team not found: %v", err)
	}

	members, err := teamSvc.ListTeamMembers(ctx, team.ID, limit, offset)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to fetch team members: %v", err)
	}

	memberCount, err := teamSvc.CountTeamMembers(ctx, team.ID)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to count team members: %v", err)
	}

	var apiMembers []*aimsv1.TeamMember
	for _, member := range members {
		apiMembers = append(apiMembers, &aimsv1.TeamMember{
			Id:    member.ID,
			Email: member.Email,
		})
	}

	return &aimsv1.ListTeamMembersResponse{
		TeamMembers: apiMembers,
		Count:       int64(memberCount),
	}, nil
}
