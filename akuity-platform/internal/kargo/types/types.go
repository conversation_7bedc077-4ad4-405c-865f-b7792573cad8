package types

import (
	apiextensionsv1 "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type Project struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	Status            ProjectStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// ProjectStatus describes a Project's current status.
type ProjectStatus struct {
	// Stats contains a summary of the collective state of a Project's
	// constituent resources.
	Stats *ProjectStats `json:"stats,omitempty" protobuf:"bytes,4,opt,name=stats"`
}

// ProjectStats contains a summary of the collective state of a Project's
// constituent resources.
type ProjectStats struct {
	// Warehouses contains a summary of the collective state of the Project's
	// Warehouses.
	Warehouses WarehouseStats `json:"warehouses,omitempty" protobuf:"bytes,1,opt,name=warehouses"`
	// Stages contains a summary of the collective state of the Project's Stages.
	Stages StageStats `json:"stages,omitempty" protobuf:"bytes,2,opt,name=stages"`
}

// WarehouseStats contains a summary of the collective state of a Project's
// Warehouses.
type WarehouseStats struct {
	// Count contains the total number of Warehouses in the Project.
	Count int64 `json:"count,omitempty" protobuf:"varint,2,opt,name=count"`
	// Health contains a summary of the collective health of a Project's
	// Warehouses.
	Health HealthStats `json:"health,omitempty" protobuf:"bytes,1,opt,name=health"`
}

// StageStats contains a summary of the collective state of a Project's
// Stages.
type StageStats struct {
	// Count contains the total number of Stages in the Project.
	Count int64 `json:"count,omitempty" protobuf:"varint,2,opt,name=count"`
	// Health contains a summary of the collective health of a Project's Stages.
	Health HealthStats `json:"health,omitempty" protobuf:"bytes,1,opt,name=health"`
}

// HealthStats contains a summary of the collective health of some resource
// type.
type HealthStats struct {
	// Healthy contains the number of resources that are explicitly healthy.
	Healthy int64 `json:"healthy,omitempty" protobuf:"varint,1,opt,name=healthy"`
}

type Stage struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata" protobuf:"bytes,1,opt,name=metadata"`
	// Spec describes sources of Freight used by the Stage and how to incorporate
	// Freight into the Stage.
	Spec StageSpec `json:"spec" protobuf:"bytes,2,opt,name=spec"`
	// Status describes the Stage's current and recent Freight, health, and more.
	Status StageStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// StageStatus describes a Stages's current and recent Freight, health, and
// more.
type StageStatus struct {
	// Conditions contains the last observations of the Stage's current
	// state.
	// +patchMergeKey=type
	// +patchStrategy=merge
	// +listType=map
	// +listMapKey=type
	Conditions []metav1.Condition `json:"conditions,omitempty" patchMergeKey:"type" patchStrategy:"merge" protobuf:"bytes,13,rep,name=conditions"`
	// LastHandledRefresh holds the value of the most recent AnnotationKeyRefresh
	// annotation that was handled by the controller. This field can be used to
	// determine whether the request to refresh the resource has been handled.
	// +optional
	LastHandledRefresh string `json:"lastHandledRefresh,omitempty" protobuf:"bytes,11,opt,name=lastHandledRefresh"`
	// FreightHistory is a list of recent Freight selections that were deployed
	// to the Stage. By default, the last ten Freight selections are stored.
	// The first item in the list is the most recent Freight selection and
	// currently deployed to the Stage, subsequent items are older selections.
	FreightHistory FreightHistory `json:"freightHistory,omitempty" protobuf:"bytes,4,rep,name=freightHistory" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// FreightSummary is human-readable text maintained by the controller that
	// summarizes what Freight is currently deployed to the Stage. For Stages that
	// request a single piece of Freight AND the request has been fulfilled, this
	// field will simply contain the name of the Freight. For Stages that request
	// a single piece of Freight AND the request has NOT been fulfilled, or for
	// Stages that request multiple pieces of Freight, this field will contain a
	// summary of fulfilled/requested Freight. The existence of this field is a
	// workaround for kubectl limitations so that this complex but valuable
	// information can be displayed in a column in response to `kubectl get
	// stages`.
	FreightSummary string `json:"freightSummary,omitempty" protobuf:"bytes,12,opt,name=freightSummary"`
	// Health is the Stage's last observed health.
	Health *Health `json:"health,omitempty" protobuf:"bytes,8,opt,name=health"`
	// ObservedGeneration represents the .metadata.generation that this Stage
	// status was reconciled against.
	ObservedGeneration int64 `json:"observedGeneration,omitempty" protobuf:"varint,6,opt,name=observedGeneration"`
	// CurrentPromotion is a reference to the currently Running promotion.
	CurrentPromotion *PromotionReference `json:"currentPromotion,omitempty" protobuf:"bytes,7,opt,name=currentPromotion"`
	// LastPromotion is a reference to the last completed promotion.
	LastPromotion *PromotionReference `json:"lastPromotion,omitempty" protobuf:"bytes,10,opt,name=lastPromotion"`
}

// PromotionReference contains the relevant information about a Promotion
// as observed by a Stage.
type PromotionReference struct {
	// Name is the name of the Promotion.
	Name string `json:"name" protobuf:"bytes,1,opt,name=name"`
	// Freight is the freight being promoted.
	Freight *FreightReference `json:"freight,omitempty" protobuf:"bytes,2,opt,name=freight"`
	// Status is the (optional) status of the Promotion.
	Status *PromotionStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
	// FinishedAt is the time at which the Promotion was completed.
	FinishedAt *metav1.Time `json:"finishedAt,omitempty" protobuf:"bytes,4,opt,name=finishedAt"`
}

// Health describes the health of a Stage.
type Health struct {
	// Status describes the health of the Stage.
	Status HealthState `json:"status,omitempty" protobuf:"bytes,1,opt,name=status"`
	// Issues clarifies why a Stage in any state other than Healthy is in that
	// state. This field will always be the empty when a Stage is Healthy.
	Issues []string `json:"issues,omitempty" protobuf:"bytes,2,rep,name=issues"`
	// Config is the opaque configuration of all health checks performed on this
	// Stage.
	Config *apiextensionsv1.JSON `json:"config,omitempty" protobuf:"bytes,4,opt,name=config"`
	// Output is the opaque output of all health checks performed on this Stage.
	Output *apiextensionsv1.JSON `json:"output,omitempty" protobuf:"bytes,5,opt,name=output"`
}

type HealthState string

// FreightHistory is a linear list of FreightCollection items. The list is
// ordered by the time at which the FreightCollection was recorded, with the
// most recent (current) FreightCollection at the top of the list.
type FreightHistory []*FreightCollection

type StageSpec struct {
	// RequestedFreight expresses the Stage's need for certain pieces of Freight,
	// each having originated from a particular Warehouse. This list must be
	// non-empty. In the common case, a Stage will request Freight having
	// originated from just one specific Warehouse. In advanced cases, requesting
	// Freight from multiple Warehouses provides a method of advancing new
	// artifacts of different types through parallel pipelines at different
	// speeds. This can be useful, for instance, if a Stage is home to multiple
	// microservices that are independently versioned.
	RequestedFreight []FreightRequest `json:"requestedFreight" protobuf:"bytes,5,rep,name=requestedFreight"`
}

// FreightRequest expresses a Stage's need for Freight having originated from a
// particular Warehouse.
type FreightRequest struct {
	// Origin specifies from where the requested Freight must have originated.
	// This is a required field.
	Origin FreightOrigin `json:"origin" protobuf:"bytes,1,opt,name=origin"`
	// Sources describes where the requested Freight may be obtained from. This is
	// a required field.
	Sources FreightSources `json:"sources" protobuf:"bytes,2,opt,name=sources"`
}

// FreightOrigin describes a kind of Freight in terms of where it may have
// originated.
type FreightOrigin struct {
	// Name is the name of the resource of the kind indicated by the Kind field
	// from which Freight may originate.
	Name string `json:"name" protobuf:"bytes,2,opt,name=name"`
}

type FreightSources struct {
	// Direct indicates the requested Freight may be obtained directly from the
	// Warehouse from which it originated. If this field's value is false, then
	// the value of the Stages field must be non-empty. i.e. Between the two
	// fields, at least one source must be specified.
	Direct bool `json:"direct,omitempty" protobuf:"varint,1,opt,name=direct"`
	// Stages identifies other "upstream" Stages as potential sources of the
	// requested Freight. If this field's value is empty, then the value of the
	// Direct field must be true. i.e. Between the two fields, at least on source
	// must be specified.
	Stages []string `json:"stages,omitempty" protobuf:"bytes,2,rep,name=stages"`
}

type Warehouse struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata" protobuf:"bytes,1,opt,name=metadata"`
}

type Freight struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata" protobuf:"bytes,1,opt,name=metadata"`
	// Alias is a human-friendly alias for a piece of Freight. This is an optional
	// field. A defaulting webhook will sync this field with the value of the
	// kargo.akuity.io/alias label. When the alias label is not present or differs
	// from the value of this field, the defaulting webhook will set the label to
	// the value of this field. If the alias label is present and this field is
	// empty, the defaulting webhook will set the value of this field to the value
	// of the alias label. If this field is empty and the alias label is not
	// present, the defaulting webhook will choose an available alias and assign
	// it to both the field and label.
	Alias string `json:"alias,omitempty" protobuf:"bytes,7,opt,name=alias"`
	// Origin describes a kind of Freight in terms of its origin.
	//
	// +kubebuilder:validation:Required
	Origin FreightOrigin `json:"origin,omitempty" protobuf:"bytes,9,opt,name=origin"`
	// Commits describes specific Git repository commits.
	Commits []GitCommit `json:"commits,omitempty" protobuf:"bytes,3,rep,name=commits"`
	// Images describes specific versions of specific container images.
	Images []Image `json:"images,omitempty" protobuf:"bytes,4,rep,name=images"`
	// Charts describes specific versions of specific Helm charts.
	Charts []Chart `json:"charts,omitempty" protobuf:"bytes,5,rep,name=charts"`
	// Status describes the current status of this Freight.
	Status FreightStatus `json:"status,omitempty" protobuf:"bytes,6,opt,name=status"`
}

// FreightStatus describes a piece of Freight's most recently observed state.
type FreightStatus struct {
	// CurrentlyIn describes the Stages in which this Freight is currently in use.
	CurrentlyIn map[string]CurrentStage `json:"currentlyIn,omitempty" protobuf:"bytes,3,rep,name=currentlyIn" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// VerifiedIn describes the Stages in which this Freight has been verified
	// through promotion and subsequent health checks.
	VerifiedIn map[string]VerifiedStage `json:"verifiedIn,omitempty" protobuf:"bytes,1,rep,name=verifiedIn" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// ApprovedFor describes the Stages for which this Freight has been approved
	// preemptively/manually by a user. This is useful for hotfixes, where one
	// might wish to promote a piece of Freight to a given Stage without
	// transiting the entire pipeline.
	ApprovedFor map[string]ApprovedStage `json:"approvedFor,omitempty" protobuf:"bytes,2,rep,name=approvedFor" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Metadata is a map of arbitrary metadata associated with the Freight.
	// This is useful for storing additional information about the Freight
	// or Promotion that can be shared across steps or stages.
	Metadata map[string]apiextensionsv1.JSON `json:"metadata,omitempty" protobuf:"bytes,4,rep,name=metadata" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
}

// CurrentStage reflects a Stage's current use of Freight.
type CurrentStage struct {
	// Since is the time at which the Stage most recently started using the
	// Freight. This can be used to calculate how long the Freight has been in use
	// by the Stage.
	Since *metav1.Time `json:"since,omitempty" protobuf:"bytes,1,opt,name=since"`
}

// VerifiedStage describes a Stage in which Freight has been verified.
type VerifiedStage struct {
	// VerifiedAt is the time at which the Freight was verified in the Stage.
	VerifiedAt *metav1.Time `json:"verifiedAt,omitempty" protobuf:"bytes,1,opt,name=verifiedAt"`
}

// ApprovedStage describes a Stage for which Freight has been (manually)
// approved.
type ApprovedStage struct {
	// ApprovedAt is the time at which the Freight was approved for the Stage.
	ApprovedAt *metav1.Time `json:"approvedAt,omitempty" protobuf:"bytes,1,opt,name=approvedAt"`
}

type Promotion struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata" protobuf:"bytes,1,opt,name=metadata"`
	// Spec describes the desired transition of a specific Stage into a specific
	// Freight.
	Spec PromotionSpec `json:"spec" protobuf:"bytes,2,opt,name=spec"`
	// Status describes the current state of the transition represented by this
	// Promotion.
	Status PromotionStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}

// PromotionStatus describes the current state of the transition represented by
// a Promotion.
type PromotionStatus struct {
	// LastHandledRefresh holds the value of the most recent AnnotationKeyRefresh
	// annotation that was handled by the controller. This field can be used to
	// determine whether the request to refresh the resource has been handled.
	// +optional
	LastHandledRefresh string `json:"lastHandledRefresh,omitempty" protobuf:"bytes,4,opt,name=lastHandledRefresh"`
	// Phase describes where the Promotion currently is in its lifecycle.
	Phase PromotionPhase `json:"phase,omitempty" protobuf:"bytes,1,opt,name=phase"`
	// Message is a display message about the promotion, including any errors
	// preventing the Promotion controller from executing this Promotion.
	// i.e. If the Phase field has a value of Failed, this field can be expected
	// to explain why.
	Message string `json:"message,omitempty" protobuf:"bytes,2,opt,name=message"`
	// Freight is the detail of the piece of freight that was referenced by this promotion.
	Freight *FreightReference `json:"freight,omitempty" protobuf:"bytes,5,opt,name=freight"`
	// FreightCollection contains the details of the piece of Freight referenced
	// by this Promotion as well as any additional Freight that is carried over
	// from the target Stage's current state.
	FreightCollection *FreightCollection `json:"freightCollection,omitempty" protobuf:"bytes,7,opt,name=freightCollection"`
	// HealthChecks contains the health check directives to be executed after
	// the Promotion has completed.
	HealthChecks []HealthCheckStep `json:"healthChecks,omitempty" protobuf:"bytes,8,rep,name=healthChecks"`
	// FinishedAt is the time when the promotion was completed.
	FinishedAt *metav1.Time `json:"finishedAt,omitempty" protobuf:"bytes,6,opt,name=finishedAt"`
	// CurrentStep is the index of the current promotion step being executed. This
	// permits steps that have already run successfully to be skipped on
	// subsequent reconciliations attempts.
	CurrentStep int64 `json:"currentStep,omitempty" protobuf:"varint,9,opt,name=currentStep"`
	// StepExecutionMetadata tracks metadata pertaining to the execution
	// of individual promotion steps.
	StepExecutionMetadata StepExecutionMetadataList `json:"stepExecutionMetadata,omitempty" protobuf:"bytes,11,rep,name=stepExecutionMetadata"`
	// State stores the state of the promotion process between reconciliation
	// attempts.
	State *apiextensionsv1.JSON `json:"state,omitempty" protobuf:"bytes,10,opt,name=state"`
}

// StepExecutionMetadataList is a list of StepExecutionMetadata.
type StepExecutionMetadataList []StepExecutionMetadata

// StepExecutionMetadata tracks metadata pertaining to the execution of
// a promotion step.
type StepExecutionMetadata struct {
	// Alias is the alias of the step.
	Alias string `json:"alias,omitempty" protobuf:"bytes,1,opt,name=alias"`
	// StartedAt is the time at which the first attempt to execute the step
	// began.
	StartedAt *metav1.Time `json:"startedAt,omitempty" protobuf:"bytes,2,opt,name=startedAt"`
	// FinishedAt is the time at which the final attempt to execute the step
	// completed.
	FinishedAt *metav1.Time `json:"finishedAt,omitempty" protobuf:"bytes,3,opt,name=finishedAt"`
	// ErrorCount tracks consecutive failed attempts to execute the step.
	ErrorCount uint32 `json:"errorCount,omitempty" protobuf:"varint,4,opt,name=errorCount"`
	// Status is the high-level outcome of the step.
	Status PromotionStepStatus `json:"status,omitempty" protobuf:"bytes,5,opt,name=status"`
	// Message is a display message about the step, including any errors.
	Message string `json:"message,omitempty" protobuf:"bytes,6,opt,name=message"`
	// ContinueOnError is a boolean value that, if set to true, will cause the
	// Promotion to continue executing the next step even if this step fails. It
	// also will not permit this failure to impact the overall status of the
	// Promotion.
	ContinueOnError bool `json:"continueOnError,omitempty" protobuf:"varint,7,opt,name=continueOnError"`
}

type PromotionStepStatus string

// HealthCheckStep describes a health check directive which can be executed by
// a Stage to verify the health of a Promotion result.
type HealthCheckStep struct {
	// Uses identifies a runner that can execute this step.
	//
	// +kubebuilder:validation:MinLength=1
	Uses string `json:"uses" protobuf:"bytes,1,opt,name=uses"`

	// Config is the configuration for the directive.
	Config *apiextensionsv1.JSON `json:"config,omitempty" protobuf:"bytes,2,opt,name=config"`
}

type PromotionPhase string

// FreightReference is a simplified representation of a piece of Freight -- not
// a root resource type.
type FreightReference struct {
	// Name is a system-assigned identifier derived deterministically from
	// the contents of the Freight. I.e., two pieces of Freight can be compared
	// for equality by comparing their Names.
	Name string `json:"name,omitempty" protobuf:"bytes,1,opt,name=name"`
	// Origin describes a kind of Freight in terms of its origin.
	Origin FreightOrigin `json:"origin,omitempty" protobuf:"bytes,8,opt,name=origin"`
	// Commits describes specific Git repository commits.
	Commits []GitCommit `json:"commits,omitempty" protobuf:"bytes,2,rep,name=commits"`
	// Images describes specific versions of specific container images.
	Images []Image `json:"images,omitempty" protobuf:"bytes,3,rep,name=images"`
	// Charts describes specific versions of specific Helm charts.
	Charts []Chart `json:"charts,omitempty" protobuf:"bytes,4,rep,name=charts"`
}

// GitCommit describes a specific commit from a specific Git repository.
type GitCommit struct {
	// RepoURL is the URL of a Git repository.
	RepoURL string `json:"repoURL,omitempty" protobuf:"bytes,1,opt,name=repoURL"`
	// ID is the ID of a specific commit in the Git repository specified by
	// RepoURL.
	ID string `json:"id,omitempty" protobuf:"bytes,2,opt,name=id"`
	// Branch denotes the branch of the repository where this commit was found.
	Branch string `json:"branch,omitempty" protobuf:"bytes,3,opt,name=branch"`
	// Tag denotes a tag in the repository that matched selection criteria and
	// resolved to this commit.
	Tag string `json:"tag,omitempty" protobuf:"bytes,4,opt,name=tag"`
	// Message is the message associated with the commit. At present, this only
	// contains the first line (subject) of the commit message.
	Message string `json:"message,omitempty" protobuf:"bytes,6,opt,name=message"`
	// Author is the author of the commit.
	Author string `json:"author,omitempty" protobuf:"bytes,7,opt,name=author"`
	// Committer is the person who committed the commit.
	Committer string `json:"committer,omitempty" protobuf:"bytes,8,opt,name=committer"`
}

// Image describes a specific version of a container image.
type Image struct {
	// RepoURL describes the repository in which the image can be found.
	RepoURL string `json:"repoURL,omitempty" protobuf:"bytes,1,opt,name=repoURL"`
	// GitRepoURL specifies the URL of a Git repository that contains the source
	// code for the image repository referenced by the RepoURL field if Kargo was
	// able to infer it.
	//
	// Deprecated: Use OCI annotations instead. Will be removed in v1.7.0.
	GitRepoURL string `json:"gitRepoURL,omitempty" protobuf:"bytes,2,opt,name=gitRepoURL"`
	// Tag identifies a specific version of the image in the repository specified
	// by RepoURL.
	Tag string `json:"tag,omitempty" protobuf:"bytes,3,opt,name=tag"`
	// Digest identifies a specific version of the image in the repository
	// specified by RepoURL. This is a more precise identifier than Tag.
	Digest string `json:"digest,omitempty" protobuf:"bytes,4,opt,name=digest"`
	// Annotations is a map of arbitrary metadata for the image.
	Annotations map[string]string `json:"annotations,omitempty" protobuf:"bytes,5,rep,name=annotations" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
}

// Chart describes a specific version of a Helm chart.
type Chart struct {
	// RepoURL specifies the URL of a Helm chart repository. Classic chart
	// repositories (using HTTP/S) can contain differently named charts. When this
	// field points to such a repository, the Name field will specify the name of
	// the chart within the repository. In the case of a repository within an OCI
	// registry, the URL implicitly points to a specific chart and the Name field
	// will be empty.
	RepoURL string `json:"repoURL,omitempty" protobuf:"bytes,1,opt,name=repoURL"`
	// Name specifies the name of the chart.
	Name string `json:"name,omitempty" protobuf:"bytes,2,opt,name=name"`
	// Version specifies a particular version of the chart.
	Version string `json:"version,omitempty" protobuf:"bytes,3,opt,name=version"`
}

// FreightCollection is a collection of FreightReferences, each of which
// represents a piece of Freight that has been selected for deployment to a
// Stage.
type FreightCollection struct {
	// ID is a unique and deterministically calculated identifier for the
	// FreightCollection. It is updated on each use of the UpdateOrPush method.
	ID string `json:"id" protobuf:"bytes,3,opt,name=id"`
	// Freight is a map of FreightReference objects, indexed by their Warehouse
	// origin.
	Freight map[string]FreightReference `json:"items,omitempty" protobuf:"bytes,1,rep,name=items" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// VerificationHistory is a stack of recent VerificationInfo. By default,
	// the last ten VerificationInfo are stored.
	VerificationHistory VerificationInfoStack `json:"verificationHistory,omitempty" protobuf:"bytes,2,rep,name=verificationHistory"`
}

type VerificationInfoStack []VerificationInfo

// VerificationInfo contains the details of an instance of a Verification
// process.
type VerificationInfo struct {
	// ID is the identifier of the Verification process.
	//
	// +kubebuilder:validation:Required
	ID string `json:"id" protobuf:"bytes,4,opt,name=id"`
	// Actor is the name of the entity that initiated or aborted the
	// Verification process.
	Actor string `json:"actor,omitempty" protobuf:"bytes,7,opt,name=actor"`
	// StartTime is the time at which the Verification process was started.
	//
	// +kubebuilder:validation:Required
	StartTime *metav1.Time `json:"startTime" protobuf:"bytes,5,opt,name=startTime"`
	// Phase describes the current phase of the Verification process. Generally,
	// this will be a reflection of the underlying AnalysisRun's phase, however,
	// there are exceptions to this, such as in the case where an AnalysisRun
	// cannot be launched successfully.
	//
	// +kubebuilder:validation:Required
	Phase VerificationPhase `json:"phase" protobuf:"bytes,1,opt,name=phase"`
	// Message may contain additional information about why the verification
	// process is in its current phase.
	Message string `json:"message,omitempty" protobuf:"bytes,2,opt,name=message"`
	// FinishTime is the time at which the Verification process finished.
	FinishTime *metav1.Time `json:"finishTime,omitempty" protobuf:"bytes,6,opt,name=finishTime"`
}

type VerificationPhase string

// PromotionSpec describes the desired transition of a specific Stage into a
// specific Freight.
type PromotionSpec struct {
	// Stage specifies the name of the Stage to which this Promotion
	// applies. The Stage referenced by this field MUST be in the same
	// namespace as the Promotion.
	Stage string `json:"stage" protobuf:"bytes,1,opt,name=stage"`
	// Freight specifies the piece of Freight to be promoted into the Stage
	// referenced by the Stage field.
	Freight string `json:"freight" protobuf:"bytes,2,opt,name=freight"`
}

type Images map[string]TagMap

type TagMap struct {
	Tags map[string]ImageStageMap `json:"tags"`
}

type ImageStageMap struct {
	Stages map[string]int32 `json:"stages"`
}

type QueryFreightResult struct {
	Groups map[string]FreightList `json:"groups"`
}

type FreightList struct {
	Freight []Freight `json:"freight"`
}

// ResourceRef represents a reference to a Kargo resource
type ResourceRef struct {
	Group     string `json:"group,omitempty" protobuf:"bytes,1,opt,name=group"`
	Version   string `json:"version,omitempty" protobuf:"bytes,2,opt,name=version"`
	Kind      string `json:"kind,omitempty" protobuf:"bytes,3,opt,name=kind"`
	Name      string `json:"name,omitempty" protobuf:"bytes,4,opt,name=name"`
	Namespace string `json:"namespace,omitempty" protobuf:"bytes,5,opt,name=namespace"`
	UID       string `json:"uid,omitempty" protobuf:"bytes,6,opt,name=uid"`
}

// InfoItem contains key-value information about a resource
type InfoItem struct {
	Name  string `json:"name,omitempty" protobuf:"bytes,1,opt,name=name"`
	Value string `json:"value,omitempty" protobuf:"bytes,2,opt,name=value"`
}

// HealthStatus represents the health of a resource
type HealthStatus struct {
	Status  string `json:"status,omitempty" protobuf:"bytes,1,opt,name=status"`
	Message string `json:"message,omitempty" protobuf:"bytes,2,opt,name=message"`
}

// ResourceNode represents a resource in the tree with its parent relationships
type ResourceNode struct {
	ResourceRef `json:",inline" protobuf:"bytes,1,opt,name=resourceRef"`
	ParentRefs  []ResourceRef `json:"parentRefs,omitempty" protobuf:"bytes,2,opt,name=parentRefs"`
	Info        []InfoItem    `json:"info,omitempty" protobuf:"bytes,3,opt,name=info"`
	CreatedAt   *metav1.Time  `json:"createdAt,omitempty" protobuf:"bytes,5,opt,name=createdAt"`
}

// ResourceTree represents the hierarchical relationship between Kargo resources
type ResourceTree struct {
	Nodes []ResourceNode `json:"nodes,omitempty" protobuf:"bytes,1,rep,name=nodes"`
}

func SetGVK(obj any) {
	switch t := obj.(type) {
	case *Project:
		t.APIVersion = "kargo.akuity.io/v1alpha1"
		t.Kind = "Project"
	case *Stage:
		t.APIVersion = "kargo.akuity.io/v1alpha1"
		t.Kind = "Stage"
	case *Warehouse:
		t.APIVersion = "kargo.akuity.io/v1alpha1"
		t.Kind = "Warehouse"
	case *Freight:
		t.APIVersion = "kargo.akuity.io/v1alpha1"
		t.Kind = "Freight"
	case *Promotion:
		t.APIVersion = "kargo.akuity.io/v1alpha1"
		t.Kind = "Promotion"
	}
}
