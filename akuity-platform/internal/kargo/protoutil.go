package kargo

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/fullstorydev/grpcurl"
	"github.com/go-logr/logr"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protodesc"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/reflect/protoregistry"
	"google.golang.org/protobuf/types/descriptorpb"
	"google.golang.org/protobuf/types/dynamicpb"

	_ "embed"
)

type KargoProtoUtil struct {
	Files             *protoregistry.Files
	Types             *dynamicpb.Types
	descriptorSource  grpcurl.DescriptorSource
	serviceDescriptor protoreflect.ServiceDescriptor
	logger            logr.Logger
}

//go:embed kargo.protoset
var KargoProtoset []byte

type GrpcConnectionGetter func() (*grpc.ClientConn, func(), error)

func NewKargoProtoUtil(logger logr.Logger, _ any) (*KargoProtoUtil, error) {
	source, sd, files, err := getKargoDescriptors()
	if err != nil {
		return nil, fmt.Errorf("failed to get Kargo descriptors: %v", err)
	}

	types := dynamicpb.NewTypes(files)

	return &KargoProtoUtil{
		Files:             files,
		Types:             types,
		descriptorSource:  source,
		serviceDescriptor: sd,
		logger:            logger,
	}, nil
}

func (p *KargoProtoUtil) FindMethodByName(name string) protoreflect.MethodDescriptor {
	return p.serviceDescriptor.Methods().ByName(protoreflect.Name(name))
}

func (p *KargoProtoUtil) GetRequestFormater(format grpcurl.Format, in io.Reader, opts grpcurl.FormatOptions) (grpcurl.RequestParser, grpcurl.Formatter, error) {
	return grpcurl.RequestParserAndFormatter(format, p.descriptorSource, in, opts)
}

func (p *KargoProtoUtil) CallGRPC(ctx context.Context, methodDescriptor protoreflect.MethodDescriptor, request proto.Message, connectionGetter GrpcConnectionGetter, token string) (proto.Message, error) {
	clientConnection, closer, err := connectionGetter()
	if err != nil {
		return nil, err
	}
	defer closer()

	if token != "" {
		md := metadata.New(map[string]string{
			"authorization": "Bearer " + token,
		})
		ctx = metadata.NewOutgoingContext(ctx, md)
	}

	// Create response message using dynamicpb
	resp := dynamicpb.NewMessage(methodDescriptor.Output())

	methodName := fmt.Sprintf("/%s/%s", methodDescriptor.Parent().FullName(), methodDescriptor.Name())
	err = clientConnection.Invoke(ctx, methodName, request, resp)
	if err != nil {
		return nil, fmt.Errorf("failed to invoke %s: %w", methodName, err)
	}

	return resp, nil
}

func (p *KargoProtoUtil) NewMessage(messageDesc protoreflect.MessageDescriptor) *dynamicpb.Message {
	return dynamicpb.NewMessage(messageDesc)
}

// getKargoDescriptors returns gRPC descriptors from the embedded Kargo protoset.
func getKargoDescriptors() (grpcurl.DescriptorSource, protoreflect.ServiceDescriptor, *protoregistry.Files, error) {
	var fs descriptorpb.FileDescriptorSet

	err := proto.Unmarshal(KargoProtoset, &fs)
	if err != nil {
		return nil, nil, nil, err
	}

	// Create Files registry from FileDescriptorSet
	files := &protoregistry.Files{}
	for _, fdProto := range fs.File {
		fd, err := protodesc.NewFile(fdProto, files)
		if err != nil {
			return nil, nil, nil, fmt.Errorf("failed to create file descriptor: %v", err)
		}
		err = files.RegisterFile(fd)
		if err != nil {
			return nil, nil, nil, fmt.Errorf("failed to register file descriptor: %v", err)
		}
	}

	source, err := grpcurl.DescriptorSourceFromFileDescriptorSet(&fs)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to create descriptor source: %v", err)
	}

	services, err := source.ListServices()
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to list services: %v", err)
	}

	var targetService string
	for _, svc := range services {
		if svc == "akuity.io.kargo.service.v1alpha1.KargoService" {
			targetService = svc
			break
		}
	}
	if targetService == "" {
		return nil, nil, nil, fmt.Errorf("KargoService not found in services: %v", services)
	}

	// Find the service descriptor in the Files registry
	var serviceDescriptor protoreflect.ServiceDescriptor
	files.RangeFiles(func(fd protoreflect.FileDescriptor) bool {
		services := fd.Services()
		for i := 0; i < services.Len(); i++ {
			svc := services.Get(i)
			if string(svc.FullName()) == targetService {
				serviceDescriptor = svc
				return false // stop iteration
			}
		}
		return true // continue iteration
	})

	if serviceDescriptor == nil {
		return nil, nil, nil, fmt.Errorf("target server does not expose service %q", targetService)
	}

	return source, serviceDescriptor, files, nil
}

func convertValue(value, target any) any {
	if value == nil {
		return nil
	}
	if target == nil {
		return value
	}

	targetType := reflect.TypeOf(target)
	nextTarget := target
	if targetType.Kind() == reflect.Ptr {
		targetType = targetType.Elem()
		nextTarget = reflect.Zero(targetType).Interface()
	}
	switch {
	case targetType.Kind() == reflect.Struct:
		return convertObject(value.(map[string]any), nextTarget)
	case targetType.Kind() == reflect.Slice:
		return convertArray(value.([]any), nextTarget)
	case targetType.Kind() == reflect.Map:
		return convertMap(value.(map[string]any), nextTarget)
	default:
		return convertScalar(value, target)
	}
}

func convertObject(obj map[string]any, target any) any {
	if seconds, hasSeconds := obj["seconds"]; hasSeconds {
		if nanos, hasNanos := obj["nanos"]; hasNanos {
			return convertTimestamp(seconds, nanos)
		}
	}

	// For regular objects, convert each field recursively
	result := make(map[string]any)
	processedFields := make(map[string]bool)

	targetType := reflect.TypeOf(target)
	// If the target is a pointer, get the element type
	if targetType != nil && targetType.Kind() == reflect.Ptr {
		targetType = targetType.Elem()
	}
	// If the target is a struct, convert each field recursively
	if targetType.Kind() == reflect.Struct {
		for i := 0; i < targetType.NumField(); i++ {
			field := targetType.Field(i)
			jsonTag := field.Tag.Get("json")

			if jsonTag == ",inline" || jsonTag == "-" || jsonTag == "" && field.Anonymous {
				continue
			}

			jsonName, options := parseJSONTag(jsonTag)
			if jsonName == "" || jsonName == "-" {
				continue
			}

			containOmitEmpty := false
			for _, s := range options {
				if s == "omitempty" {
					containOmitEmpty = true
				}
			}
			if containOmitEmpty && obj[jsonName] == nil {
				continue
			}

			if fieldValue, exists := obj[jsonName]; exists {
				var fieldTarget any
				if field.Type.Kind() == reflect.Ptr {
					fieldTarget = reflect.New(field.Type.Elem()).Interface()
				} else {
					fieldTarget = reflect.Zero(field.Type).Interface()
				}
				result[jsonName] = convertValue(fieldValue, fieldTarget)
				processedFields[jsonName] = true
			}
		}

		for key, value := range obj {
			if !processedFields[key] {
				result[key] = convertValue(value, nil)
			}
		}
	} else {
		// For non-struct types, convert each field without a specific target type
		for key, value := range obj {
			result[key] = convertValue(value, nil)
		}
	}

	return result
}

func parseJSONTag(tag string) (string, []string) {
	if tag == "" {
		return "", nil
	}

	parts := strings.Split(tag, ",")
	if len(parts) == 0 {
		return "", nil
	}

	name := parts[0]
	options := parts[1:]

	return name, options
}

func convertArray(arr []any, target any) []any {
	result := make([]any, len(arr))

	targetType := reflect.TypeOf(target)
	var elemType reflect.Type
	if targetType != nil && targetType.Kind() == reflect.Slice {
		elemType = targetType.Elem()
	}

	for i, item := range arr {
		if elemType != nil {
			var elemTarget any
			if elemType.Kind() == reflect.Ptr {
				elemTarget = reflect.New(elemType.Elem()).Interface()
			} else {
				elemTarget = reflect.Zero(elemType).Interface()
			}
			result[i] = convertValue(item, elemTarget)
		} else {
			result[i] = convertValue(item, nil)
		}
	}

	return result
}

func convertMap(inputMap map[string]any, target any) any {
	result := make(map[string]any)

	targetType := reflect.TypeOf(target)
	var valueType reflect.Type
	if targetType != nil && targetType.Kind() == reflect.Map {
		valueType = targetType.Elem()
	}

	for key, value := range inputMap {
		if valueType != nil {
			var valueTarget any
			if valueType.Kind() == reflect.Ptr {
				valueTarget = reflect.New(valueType.Elem()).Interface()
			} else {
				valueTarget = reflect.Zero(valueType).Interface()
			}
			result[key] = convertValue(value, valueTarget)
		} else {
			result[key] = convertValue(value, nil)
		}
	}

	return result
}

func convertScalar(value, target any) any {
	targetType := reflect.TypeOf(target)
	if targetType != nil && targetType.Kind() == reflect.Ptr {
		targetType = targetType.Elem()
	}
	if targetType == nil {
		if str, ok := value.(string); ok {
			if num, err := strconv.ParseInt(str, 10, 64); err == nil {
				return num
			}
			if num, err := strconv.ParseFloat(str, 64); err == nil {
				return num
			}
			if b, err := strconv.ParseBool(str); err == nil {
				return b
			}
		}
		return value
	}

	switch targetType.Kind() {
	case reflect.String:
		return fmt.Sprintf("%v", value)
	case reflect.Int, reflect.Int32, reflect.Int64:
		if str, ok := value.(string); ok {
			if num, err := strconv.ParseInt(str, 10, 64); err == nil {
				switch targetType.Kind() {
				case reflect.Int:
					return int(num)
				case reflect.Int32:
					return int32(num)
				case reflect.Int64:
					return num
				}
			}
		}
		if f, ok := value.(float64); ok {
			switch targetType.Kind() {
			case reflect.Int:
				return int(f)
			case reflect.Int32:
				return int32(f)
			case reflect.Int64:
				return int64(f)
			}
		}
		return value
	case reflect.Uint, reflect.Uint32, reflect.Uint64:
		if str, ok := value.(string); ok {
			if num, err := strconv.ParseUint(str, 10, 64); err == nil {
				switch targetType.Kind() {
				case reflect.Uint:
					return uint(num)
				case reflect.Uint32:
					return uint32(num)
				case reflect.Uint64:
					return num
				}
			}
		}
		if f, ok := value.(float64); ok {
			switch targetType.Kind() {
			case reflect.Uint:
				return uint(f)
			case reflect.Uint32:
				return uint32(f)
			case reflect.Uint64:
				return uint64(f)
			}
		}
		return value
	case reflect.Float32, reflect.Float64:
		if str, ok := value.(string); ok {
			if num, err := strconv.ParseFloat(str, 64); err == nil {
				if targetType.Kind() == reflect.Float32 {
					return float32(num)
				}
				return num
			}
		}
		return value
	case reflect.Bool:
		if str, ok := value.(string); ok {
			if b, err := strconv.ParseBool(str); err == nil {
				return b
			}
		}
		return value
	default:
		return value
	}
}

func convertTimestamp(seconds, nanos any) string {
	var sec int64
	var nsec int64

	switch s := seconds.(type) {
	case float64:
		sec = int64(s)
	case int64:
		sec = s
	case int:
		sec = int64(s)
	case string:
		if parsed, err := strconv.ParseInt(s, 10, 64); err == nil {
			sec = parsed
		}
	}

	switch n := nanos.(type) {
	case float64:
		nsec = int64(n)
	case int64:
		nsec = n
	case int:
		nsec = int64(n)
	case string:
		if parsed, err := strconv.ParseInt(n, 10, 64); err == nil {
			nsec = parsed
		}
	}

	timestamp := time.Unix(sec, nsec)
	return timestamp.Format(time.RFC3339)
}

func unmarshalProtobufResponse(respBytes []byte, target any) error {
	var data any
	if err := json.Unmarshal(respBytes, &data); err != nil {
		return fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	convertedData := convertValue(data, target)

	convertedBytes, err := json.Marshal(convertedData)
	if err != nil {
		return fmt.Errorf("failed to marshal converted JSON: %w", err)
	}

	if err := json.Unmarshal(convertedBytes, target); err != nil {
		return fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return nil
}
