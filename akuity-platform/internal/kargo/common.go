package kargo

const (
	EventReasonPromotionCreated             = "PromotionCreated"
	EventReasonPromotionSucceeded           = "PromotionSucceeded"
	EventReasonPromotionFailed              = "PromotionFailed"
	EventReasonPromotionErrored             = "PromotionErrored"
	EventReasonFreightApproved              = "FreightApproved"
	EventReasonFreightVerificationPrefix    = "FreightVerification"
	EventReasonFreightVerificationSucceeded = "FreightVerificationSucceeded"

	AnnotationKeyEventActor                  = "event.kargo.akuity.io/actor"
	AnnotationKeyEventProject                = "event.kargo.akuity.io/project"
	AnnotationKeyEventPromotionName          = "event.kargo.akuity.io/promotion-name"
	AnnotationKeyEventPromotionCreateTime    = "event.kargo.akuity.io/promotion-create-time"
	AnnotationKeyEventFreightAlias           = "event.kargo.akuity.io/freight-alias"
	AnnotationKeyEventFreightName            = "event.kargo.akuity.io/freight-name"
	AnnotationKeyEventFreightCreateTime      = "event.kargo.akuity.io/freight-create-time"
	AnnotationKeyEventFreightCommits         = "event.kargo.akuity.io/freight-commits"
	AnnotationKeyEventFreightImages          = "event.kargo.akuity.io/freight-images"
	AnnotationKeyEventFreightCharts          = "event.kargo.akuity.io/freight-charts"
	AnnotationKeyEventStageName              = "event.kargo.akuity.io/stage-name"
	AnnotationKeyEventAnalysisRunName        = "event.kargo.akuity.io/analysis-run-name"
	AnnotationKeyEventVerificationPending    = "event.kargo.akuity.io/verification-pending"
	AnnotationKeyEventVerificationStartTime  = "event.kargo.akuity.io/verification-start-time"
	AnnotationKeyEventVerificationFinishTime = "event.kargo.akuity.io/verification-finish-time"
	AnnotationKeyEventApplications           = "event.kargo.akuity.io/applications"

	PromotionKind = "Promotion"
	FreightKind   = "Freight"
	APIVersion    = "kargo.akuity.io/v1alpha1"

	EventActorControllerPrefix = "controller:"

	KargoConfigMapName = "kargo-cm"
	KargoSecretName    = "kargo-secret"
)
