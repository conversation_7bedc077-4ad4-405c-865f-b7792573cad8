package kargo

import (
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/akuityio/akuity-platform/internal/kargo/types"
)

func TestUnmarshalProtobufResponse_QueryFreightResult(t *testing.T) {
	// This is the actual JSON response from the Kargo API
	responseJSON := `{"groups":{"":{"freight":[{"metadata":{"name":"62729c498a5381507ab519c79871d1844ee583b9", "generateName":"", "namespace":"kargo-demo", "selfLink":"", "uid":"de956972-70e4-4794-93f0-59234e758f51", "resourceVersion":"14009", "generation":"1", "creationTimestamp":{"seconds":"1748940578", "nanos":0}, "labels":{"kargo.akuity.io/alias":"your-mite"}, "managedFields":[{"manager":"kargo", "operation":"Update", "apiVersion":"kargo.akuity.io/v1alpha1", "time":{"seconds":"1748940578", "nanos":0}, "fieldsType":"FieldsV1", "fieldsV1":{"Raw":"eyJmOmNvbW1pdHMiOnt9LCJmOm9yaWdpbiI6eyIuIjp7fSwiZjpraW5kIjp7fSwiZjpuYW1lIjp7fX19"}, "subresource":""}]}, "alias":"your-mite", "origin":{"kind":"Warehouse", "name":"test"}, "commits":[{"repoURL":"https://github.com/hanxiaop/kargo-demo", "id":"1941a1e213fda1913ce399c7948f44a597a97502", "branch":"", "tag":"", "message":"Update service.yaml", "author":"Xiaopeng Han <<EMAIL>>", "committer":"GitHub <<EMAIL>>"}], "status":{}}]}}}`

	response := types.QueryFreightResult{
		Groups: make(map[string]types.FreightList),
	}

	err := unmarshalProtobufResponse([]byte(responseJSON), &response)
	require.NoError(t, err, "unmarshalProtobufResponse should not return an error")
}
