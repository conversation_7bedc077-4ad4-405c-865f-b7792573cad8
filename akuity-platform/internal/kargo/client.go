package kargo

import (
	"context"
	"fmt"

	"github.com/go-logr/logr"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/reflect/protoreflect"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"github.com/akuityio/akuity-platform/internal/kargo/types"
)

var protoUtil *KargoProtoUtil

func init() {
	if val, err := NewKargoProtoUtil(logr.Discard(), nil); err != nil {
		panic(err)
	} else {
		protoUtil = val
	}
}

type RepoCredentials struct {
	Username string
	Password string
}

type Client struct {
	baseURL      string
	token        string
	k8sClientset kubernetes.Interface
	instanceID   string
}

func NewClient(token, baseURL, instanceID string, k8sClientset kubernetes.Interface) *Client {
	return &Client{
		baseURL:      baseURL,
		token:        token,
		instanceID:   instanceID,
		k8sClientset: k8sClientset,
	}
}

func (c *Client) getGrpcConnection() (*grpc.ClientConn, func(), error) {
	conn, err := grpc.NewClient(c.baseURL, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return nil, nil, err
	}
	return conn, func() { conn.Close() }, nil
}

func (c *Client) ListProjects(ctx context.Context) ([]types.Project, error) {
	methodDesc := protoUtil.FindMethodByName("ListProjects")
	if methodDesc == nil {
		return nil, fmt.Errorf("ListProjects method not found")
	}

	reqMsg := protoUtil.NewMessage(methodDesc.Input())

	resp, err := protoUtil.CallGRPC(ctx, methodDesc, reqMsg, c.getGrpcConnection, c.token)
	if err != nil {
		return nil, fmt.Errorf("failed to call ListProjects: %w", err)
	}

	respBytes, err := protojson.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	var response struct {
		Projects []types.Project `json:"projects"`
	}

	if err := unmarshalProtobufResponse(respBytes, &response); err != nil {
		return nil, err
	}

	for i := range response.Projects {
		types.SetGVK(&response.Projects[i])
	}

	return response.Projects, nil
}

func (c *Client) GetProject(ctx context.Context, projectName string) (*types.Project, error) {
	methodDesc := protoUtil.FindMethodByName("GetProject")
	if methodDesc == nil {
		return nil, fmt.Errorf("GetProject method not found")
	}

	reqMsg := protoUtil.NewMessage(methodDesc.Input())
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("name"), protoreflect.ValueOfString(projectName))

	resp, err := protoUtil.CallGRPC(ctx, methodDesc, reqMsg, c.getGrpcConnection, c.token)
	if err != nil {
		return nil, fmt.Errorf("failed to call GetProject: %w", err)
	}

	respBytes, err := protojson.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	var response struct {
		Project types.Project `json:"project"`
	}

	if err := unmarshalProtobufResponse(respBytes, &response); err != nil {
		return nil, err
	}

	types.SetGVK(&response.Project)

	return &response.Project, nil
}

func (c *Client) ListStages(ctx context.Context, projectName string) ([]types.Stage, error) {
	methodDesc := protoUtil.FindMethodByName("ListStages")
	if methodDesc == nil {
		return nil, fmt.Errorf("ListStages method not found")
	}

	reqMsg := protoUtil.NewMessage(methodDesc.Input())
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("project"), protoreflect.ValueOfString(projectName))

	resp, err := protoUtil.CallGRPC(ctx, methodDesc, reqMsg, c.getGrpcConnection, c.token)
	if err != nil {
		return nil, fmt.Errorf("failed to call ListStages: %w", err)
	}

	respBytes, err := protojson.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	var response struct {
		Stages []types.Stage `json:"stages"`
	}

	if err := unmarshalProtobufResponse(respBytes, &response); err != nil {
		return nil, err
	}

	for i := range response.Stages {
		types.SetGVK(&response.Stages[i])
	}

	return response.Stages, nil
}

func (c *Client) GetStage(ctx context.Context, projectName, stageName string) (*types.Stage, error) {
	methodDesc := protoUtil.FindMethodByName("GetStage")
	if methodDesc == nil {
		return nil, fmt.Errorf("GetStage method not found")
	}

	reqMsg := protoUtil.NewMessage(methodDesc.Input())
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("project"), protoreflect.ValueOfString(projectName))
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("name"), protoreflect.ValueOfString(stageName))

	resp, err := protoUtil.CallGRPC(ctx, methodDesc, reqMsg, c.getGrpcConnection, c.token)
	if err != nil {
		return nil, fmt.Errorf("failed to call GetStage: %w", err)
	}

	respBytes, err := protojson.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	var response struct {
		Stage types.Stage `json:"stage"`
	}

	if err := unmarshalProtobufResponse(respBytes, &response); err != nil {
		return nil, err
	}

	types.SetGVK(&response.Stage)

	return &response.Stage, nil
}

func (c *Client) ListWarehouses(ctx context.Context, projectName string) ([]types.Warehouse, error) {
	methodDesc := protoUtil.FindMethodByName("ListWarehouses")
	if methodDesc == nil {
		return nil, fmt.Errorf("ListWarehouses method not found")
	}

	reqMsg := protoUtil.NewMessage(methodDesc.Input())
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("project"), protoreflect.ValueOfString(projectName))

	resp, err := protoUtil.CallGRPC(ctx, methodDesc, reqMsg, c.getGrpcConnection, c.token)
	if err != nil {
		return nil, fmt.Errorf("failed to call ListWarehouses: %w", err)
	}

	respBytes, err := protojson.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	var response struct {
		Warehouses []types.Warehouse `json:"warehouses"`
	}

	if err := unmarshalProtobufResponse(respBytes, &response); err != nil {
		return nil, err
	}

	for i := range response.Warehouses {
		types.SetGVK(&response.Warehouses[i])
	}

	return response.Warehouses, nil
}

func (c *Client) GetWarehouse(ctx context.Context, projectName, warehouseName string) (*types.Warehouse, error) {
	methodDesc := protoUtil.FindMethodByName("GetWarehouse")
	if methodDesc == nil {
		return nil, fmt.Errorf("GetWarehouse method not found")
	}

	reqMsg := protoUtil.NewMessage(methodDesc.Input())
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("project"), protoreflect.ValueOfString(projectName))
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("name"), protoreflect.ValueOfString(warehouseName))

	resp, err := protoUtil.CallGRPC(ctx, methodDesc, reqMsg, c.getGrpcConnection, c.token)
	if err != nil {
		return nil, fmt.Errorf("failed to call GetWarehouse: %w", err)
	}

	respBytes, err := protojson.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	var response struct {
		Warehouse types.Warehouse `json:"warehouse"`
	}

	if err := unmarshalProtobufResponse(respBytes, &response); err != nil {
		return nil, err
	}

	types.SetGVK(&response.Warehouse)

	return &response.Warehouse, nil
}

func (c *Client) ListImages(ctx context.Context, projectName string) (types.Images, error) {
	methodDesc := protoUtil.FindMethodByName("ListImages")
	if methodDesc == nil {
		return nil, fmt.Errorf("ListImages method not found")
	}

	reqMsg := protoUtil.NewMessage(methodDesc.Input())
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("project"), protoreflect.ValueOfString(projectName))

	resp, err := protoUtil.CallGRPC(ctx, methodDesc, reqMsg, c.getGrpcConnection, c.token)
	if err != nil {
		return nil, fmt.Errorf("failed to call ListImages: %w", err)
	}

	respBytes, err := protojson.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	var response struct {
		Images types.Images `json:"images"`
	}

	if err := unmarshalProtobufResponse(respBytes, &response); err != nil {
		return nil, err
	}

	return response.Images, nil
}

func (c *Client) QueryFreight(ctx context.Context, projectName, stage string, reverse bool, origins []string) (*types.QueryFreightResult, error) {
	methodDesc := protoUtil.FindMethodByName("QueryFreight")
	if methodDesc == nil {
		return nil, fmt.Errorf("QueryFreight method not found")
	}

	reqMsg := protoUtil.NewMessage(methodDesc.Input())
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("project"), protoreflect.ValueOfString(projectName))
	if stage != "" {
		reqMsg.Set(reqMsg.Descriptor().Fields().ByName("stage"), protoreflect.ValueOfString(stage))
	}
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("reverse"), protoreflect.ValueOfBool(reverse))

	if len(origins) > 0 {
		originsList := reqMsg.Descriptor().Fields().ByName("origins")
		list := reqMsg.NewField(originsList).List()
		for _, origin := range origins {
			list.Append(protoreflect.ValueOfString(origin))
		}
		reqMsg.Set(originsList, protoreflect.ValueOfList(list))
	}

	resp, err := protoUtil.CallGRPC(ctx, methodDesc, reqMsg, c.getGrpcConnection, c.token)
	if err != nil {
		return nil, fmt.Errorf("failed to call QueryFreight: %w", err)
	}

	respBytes, err := protojson.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	response := types.QueryFreightResult{
		Groups: make(map[string]types.FreightList),
	}

	if err := unmarshalProtobufResponse(respBytes, &response); err != nil {
		return nil, err
	}

	for groupName, freightList := range response.Groups {
		for i := range freightList.Freight {
			types.SetGVK(&freightList.Freight[i])
		}
		response.Groups[groupName] = freightList
	}

	return &response, nil
}

func (c *Client) RefreshWarehouse(ctx context.Context, projectName, warehouseName string) (*types.Warehouse, error) {
	methodDesc := protoUtil.FindMethodByName("RefreshWarehouse")
	if methodDesc == nil {
		return nil, fmt.Errorf("RefreshWarehouse method not found")
	}

	reqMsg := protoUtil.NewMessage(methodDesc.Input())
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("project"), protoreflect.ValueOfString(projectName))
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("name"), protoreflect.ValueOfString(warehouseName))

	resp, err := protoUtil.CallGRPC(ctx, methodDesc, reqMsg, c.getGrpcConnection, c.token)
	if err != nil {
		return nil, fmt.Errorf("failed to call RefreshWarehouse: %w", err)
	}

	respBytes, err := protojson.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	var response struct {
		Warehouse types.Warehouse `json:"warehouse"`
	}

	if err := unmarshalProtobufResponse(respBytes, &response); err != nil {
		return nil, err
	}

	types.SetGVK(&response.Warehouse)

	return &response.Warehouse, nil
}

func (c *Client) PromoteToStage(ctx context.Context, projectName, stage, freight, freightAlias string) (*types.Promotion, error) {
	methodDesc := protoUtil.FindMethodByName("PromoteToStage")
	if methodDesc == nil {
		return nil, fmt.Errorf("PromoteToStage method not found")
	}

	reqMsg := protoUtil.NewMessage(methodDesc.Input())
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("project"), protoreflect.ValueOfString(projectName))
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("stage"), protoreflect.ValueOfString(stage))
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("freight"), protoreflect.ValueOfString(freight))
	if freightAlias != "" {
		reqMsg.Set(reqMsg.Descriptor().Fields().ByName("freight_alias"), protoreflect.ValueOfString(freightAlias))
	}

	resp, err := protoUtil.CallGRPC(ctx, methodDesc, reqMsg, c.getGrpcConnection, c.token)
	if err != nil {
		return nil, fmt.Errorf("failed to call PromoteToStage: %w", err)
	}

	respBytes, err := protojson.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	var response struct {
		Promotion types.Promotion `json:"promotion"`
	}

	if err := unmarshalProtobufResponse(respBytes, &response); err != nil {
		return nil, err
	}

	types.SetGVK(&response.Promotion)

	return &response.Promotion, nil
}

func (c *Client) PromoteDownstream(ctx context.Context, projectName, stage, freight, freightAlias string) ([]types.Promotion, error) {
	methodDesc := protoUtil.FindMethodByName("PromoteDownstream")
	if methodDesc == nil {
		return nil, fmt.Errorf("PromoteDownstream method not found")
	}

	reqMsg := protoUtil.NewMessage(methodDesc.Input())
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("project"), protoreflect.ValueOfString(projectName))
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("stage"), protoreflect.ValueOfString(stage))
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("freight"), protoreflect.ValueOfString(freight))
	if freightAlias != "" {
		reqMsg.Set(reqMsg.Descriptor().Fields().ByName("freight_alias"), protoreflect.ValueOfString(freightAlias))
	}

	resp, err := protoUtil.CallGRPC(ctx, methodDesc, reqMsg, c.getGrpcConnection, c.token)
	if err != nil {
		return nil, fmt.Errorf("failed to call PromoteDownstream: %w", err)
	}

	respBytes, err := protojson.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	var response struct {
		Promotions []types.Promotion `json:"promotions"`
	}

	if err := unmarshalProtobufResponse(respBytes, &response); err != nil {
		return nil, err
	}

	for i := range response.Promotions {
		types.SetGVK(&response.Promotions[i])
	}

	return response.Promotions, nil
}

func (c *Client) ListPromotions(ctx context.Context, projectName, stage string) ([]types.Promotion, error) {
	methodDesc := protoUtil.FindMethodByName("ListPromotions")
	if methodDesc == nil {
		return nil, fmt.Errorf("ListPromotions method not found")
	}

	reqMsg := protoUtil.NewMessage(methodDesc.Input())
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("project"), protoreflect.ValueOfString(projectName))
	if stage != "" {
		reqMsg.Set(reqMsg.Descriptor().Fields().ByName("stage"), protoreflect.ValueOfString(stage))
	}

	resp, err := protoUtil.CallGRPC(ctx, methodDesc, reqMsg, c.getGrpcConnection, c.token)
	if err != nil {
		return nil, fmt.Errorf("failed to call ListPromotions: %w", err)
	}

	respBytes, err := protojson.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	var response struct {
		Promotions []types.Promotion `json:"promotions"`
	}

	if err := unmarshalProtobufResponse(respBytes, &response); err != nil {
		return nil, err
	}

	for i := range response.Promotions {
		types.SetGVK(&response.Promotions[i])
	}

	return response.Promotions, nil
}

func (c *Client) GetPromotion(ctx context.Context, projectName, promotionName string) (*types.Promotion, error) {
	methodDesc := protoUtil.FindMethodByName("GetPromotion")
	if methodDesc == nil {
		return nil, fmt.Errorf("GetPromotion method not found")
	}

	reqMsg := protoUtil.NewMessage(methodDesc.Input())
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("project"), protoreflect.ValueOfString(projectName))
	reqMsg.Set(reqMsg.Descriptor().Fields().ByName("name"), protoreflect.ValueOfString(promotionName))

	resp, err := protoUtil.CallGRPC(ctx, methodDesc, reqMsg, c.getGrpcConnection, c.token)
	if err != nil {
		return nil, fmt.Errorf("failed to call GetPromotion: %w", err)
	}

	respBytes, err := protojson.Marshal(resp)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response: %w", err)
	}

	var response struct {
		Promotion types.Promotion `json:"promotion"`
	}

	if err := unmarshalProtobufResponse(respBytes, &response); err != nil {
		return nil, err
	}

	types.SetGVK(&response.Promotion)

	return &response.Promotion, nil
}

// GetResourceRelationTree returns a resource tree of the project.
// The tree is a directed graph with nodes representing resources and edges representing relationships.
//
// A visual representation of the tree is as follows:
// Project
// ├── Warehouse
// │   ├── Freight-1
// │   └── Freight-2
// ├── Stage-Test
// │   ├── Promotion-1 → Freight-1
// │   └── Promotion-2 → Freight-1
// └── Stage-Prod
//
//	└── Promotion-3 → Freight-2
func (c *Client) GetResourceRelationTree(ctx context.Context, projectName string) (*types.ResourceTree, error) {
	project, err := c.GetProject(ctx, projectName)
	if err != nil {
		return nil, fmt.Errorf("failed to get project: %w", err)
	}

	var nodes []types.ResourceNode

	projectNode := types.ResourceNode{
		ResourceRef: types.ResourceRef{
			Group:     "kargo.akuity.io",
			Version:   "v1alpha1",
			Kind:      "Project",
			Name:      project.Name,
			Namespace: project.Namespace,
			UID:       string(project.UID),
		},
		CreatedAt: &project.CreationTimestamp,
	}
	nodes = append(nodes, projectNode)

	projectRef := types.ResourceRef{
		Group:     "kargo.akuity.io",
		Version:   "v1alpha1",
		Kind:      "Project",
		Name:      project.Name,
		Namespace: project.Namespace,
		UID:       string(project.UID),
	}

	warehouses, err := c.ListWarehouses(ctx, projectName)
	if err != nil {
		return nil, fmt.Errorf("failed to list warehouses: %w", err)
	}

	wareHouseRefs := map[string]types.ResourceRef{}
	for _, warehouse := range warehouses {
		ref := types.ResourceRef{
			Group:     "kargo.akuity.io",
			Version:   "v1alpha1",
			Kind:      "Warehouse",
			Name:      warehouse.Name,
			Namespace: warehouse.Namespace,
			UID:       string(warehouse.UID),
		}
		wareHouseRefs[warehouse.Name] = ref
		node := types.ResourceNode{
			ResourceRef: ref,
			ParentRefs:  []types.ResourceRef{projectRef},
			CreatedAt:   &warehouse.CreationTimestamp,
		}
		nodes = append(nodes, node)
	}

	stages, err := c.ListStages(ctx, projectName)
	if err != nil {
		return nil, fmt.Errorf("failed to list stages: %w", err)
	}
	stageRefs := map[string]*types.ResourceRef{}
	stageNodes := map[string]*types.ResourceNode{}
	for _, stage := range stages {
		ref := types.ResourceRef{
			Group:     "kargo.akuity.io",
			Version:   "v1alpha1",
			Kind:      "Stage",
			Name:      stage.Name,
			Namespace: stage.Namespace,
			UID:       string(stage.UID),
		}
		node := types.ResourceNode{
			ResourceRef: ref,
			CreatedAt:   &stage.CreationTimestamp,
			ParentRefs:  make([]types.ResourceRef, 0),
		}
		stageRefs[stage.Name] = &ref
		stageNodes[stage.Name] = &node
	}
	for _, stage := range stages {
		for _, freight := range stage.Spec.RequestedFreight {
			warehouseRef, ok := wareHouseRefs[freight.Origin.Name]
			if ok && freight.Sources.Direct {
				stageNodes[stage.Name].ParentRefs = append(stageNodes[stage.Name].ParentRefs, warehouseRef)
			} else {
				for _, stageName := range freight.Sources.Stages {
					if _, ok := stageRefs[stageName]; !ok {
						continue
					}
					stageNodes[stage.Name].ParentRefs = append(stageNodes[stage.Name].ParentRefs, *stageRefs[stageName])
				}
			}
		}
	}
	for _, node := range stageNodes {
		nodes = append(nodes, *node)
	}

	freightResult, err := c.QueryFreight(ctx, projectName, "", false, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to query freight: %w", err)
	}

	freightNodes := map[string]*types.ResourceNode{}
	for _, group := range freightResult.Groups {
		for _, freight := range group.Freight {
			ref := types.ResourceRef{
				Group:     "kargo.akuity.io",
				Version:   "v1alpha1",
				Kind:      "Freight",
				Name:      freight.Name,
				Namespace: freight.Namespace,
			}
			node := types.ResourceNode{
				ResourceRef: ref,
				CreatedAt:   &freight.CreationTimestamp,
			}
			// add freight to warehouse
			if _, ok := wareHouseRefs[freight.Origin.Name]; ok {
				node.ParentRefs = append(node.ParentRefs, wareHouseRefs[freight.Origin.Name])
			}
			freightNodes[freight.Name] = &node
		}
	}

	promotions, err := c.ListPromotions(ctx, projectName, "")
	if err != nil {
		return nil, fmt.Errorf("failed to list promotions: %w", err)
	}
	for _, promotion := range promotions {
		ref := types.ResourceRef{
			Group:     "kargo.akuity.io",
			Version:   "v1alpha1",
			Kind:      "Promotion",
			Name:      promotion.Name,
			Namespace: promotion.Namespace,
		}

		node := types.ResourceNode{
			ResourceRef: ref,
			CreatedAt:   &promotion.CreationTimestamp,
		}

		if _, ok := freightNodes[promotion.Spec.Freight]; ok {
			freightNodes[promotion.Spec.Freight].ParentRefs = append(freightNodes[promotion.Spec.Freight].ParentRefs, ref)
		}

		if _, ok := stageNodes[promotion.Spec.Stage]; ok {
			node.ParentRefs = append(node.ParentRefs, stageNodes[promotion.Spec.Stage].ResourceRef)
		}
		nodes = append(nodes, node)
	}
	for _, node := range freightNodes {
		nodes = append(nodes, *node)
	}

	return &types.ResourceTree{
		Nodes: nodes,
	}, nil
}

func (c *Client) GetRepoCreds(ctx context.Context, projectName string) (map[string]RepoCredentials, error) {
	if c.k8sClientset == nil {
		return nil, fmt.Errorf("k8s client not available for this kargo client")
	}

	secrets, err := c.k8sClientset.CoreV1().Secrets(projectName).List(ctx, metav1.ListOptions{
		LabelSelector: "kargo.akuity.io/cred-type=git",
	})
	if err != nil {
		return nil, fmt.Errorf("failed to list git credential secrets: %w", err)
	}

	credentials := make(map[string]RepoCredentials)
	for _, secret := range secrets.Items {
		username := string(secret.Data["username"])
		repoURL := string(secret.Data["repoURL"])
		password := string(secret.Data["password"])
		if repoURL == "" || password == "" {
			continue
		}
		credentials[repoURL] = RepoCredentials{
			Username: username,
			Password: password,
		}
	}
	return credentials, nil
}
