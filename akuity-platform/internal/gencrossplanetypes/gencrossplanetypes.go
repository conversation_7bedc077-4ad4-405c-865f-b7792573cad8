package gencrossplanetypes

import (
	"bytes"
	"embed"
	"fmt"
	"go/ast"
	"go/format"
	"go/parser"
	"go/printer"
	"go/token"
	"os"
	"path/filepath"
	"strings"

	"github.com/fatih/structtag"
	"github.com/go-logr/logr"

	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
)

var (
	//go:embed *.go.tmpl
	types embed.FS

	crossplaneGenConfigs = []crossplaneGenConfig{
		{
			boolToPointer:     true,
			modelTemplateName: "argocdinstance_types.go.tmpl",
			sourceFileName:    "./api/akuity/v1alpha1/argocdinstance_types.go",
			structsToIgnore: map[string]bool{
				"ArgoCD": true,
			},
			fieldToIgnore: map[string]bool{
				"ArgoCDSpec.Shard": true,
			},
		},
		{
			boolToPointer:     true,
			modelTemplateName: "cluster_types.go.tmpl",
			sourceFileName:    "./api/akuity/v1alpha1/cluster_types.go",
			structsToIgnore: map[string]bool{
				"Cluster": true,
			},
		},
		{
			modelTemplateName: "configmanagementplugin_types.go.tmpl",
			sourceFileName:    "./api/argocd/v1alpha1/configmanagementplugin_types.go",
			structsToIgnore: map[string]bool{
				"ConfigManagementPlugin": true,
			},
		},
	}

	log logr.Logger
)

func init() {
	var err error
	log, err = logging.NewLogger()
	cli.CheckErr(err)
}

type crossplaneGenConfig struct {
	// boolToPointer indicates if we want to modify the CRD file to replace all boolean fields to boolean pointer fields.
	// For example, for argo instance CRD, the instance patch of apply endpoint needs to distinguish
	// between the boolean fields are unset or set to false.
	boolToPointer bool
	// modelTemplateName is the name of the template file for generating crossplane model.
	modelTemplateName string
	// sourceFileName is the name of the source CRD file.
	sourceFileName string
	// structsToIgnore defines which structs should be ignored during the generation of crossplane models.
	structsToIgnore map[string]bool
	// fieldToIgnore defines which fields should be ignored during the generation of crossplane models. The key is StructName.FieldName.
	fieldToIgnore map[string]bool
}

func CodegenTypes() {
	for _, c := range crossplaneGenConfigs {
		apiType := c.generateAPIFiles()
		apiDestFile := strings.Replace(c.sourceFileName, "./api", "./crossplane-gen", 1)
		err := os.WriteFile(apiDestFile, apiType, 0o600)
		cli.CheckErr(err)

		typeDestFile := "./crossplane-gen/crossplane/v1alpha1/" + filepath.Base(c.sourceFileName)
		err = os.MkdirAll(filepath.Dir(typeDestFile), 0o755)
		cli.CheckErr(err)

		typesFileOut := c.codegenStructs(c.sourceFileName)
		err = os.WriteFile(typeDestFile, typesFileOut.Bytes(), 0o600)
		cli.CheckErr(err)
	}
}

func (c *crossplaneGenConfig) generateAPIFiles() []byte {
	fset := token.NewFileSet()
	file, err := parser.ParseFile(fset, c.sourceFileName, nil, 0)
	cli.CheckErr(err)
	if c.boolToPointer {
		ast.Inspect(file, func(n ast.Node) bool {
			if x, ok := n.(*ast.Field); ok {
				// Convert the field type from boolean to boolean pointer
				if t, ok := x.Type.(*ast.Ident); ok && t.Name == "bool" {
					x.Type = &ast.StarExpr{X: x.Type}
				}
			}
			return true
		})
	}
	output := strings.Builder{}
	output.WriteString(`
// This is an auto-generated file. DO NOT EDIT
/*
Copyright 2023 Akuity, Inc.
*/
	
`)
	if err := format.Node(&output, fset, file); err != nil {
		cli.CheckErr(err)
	}
	return []byte(output.String())
}

// codegenStructs copies structs from the source file to a new crossplane model
// ArgoCD: https://github.com/akuity/provider-crossplane-akuity/blob/main/internal/types/generated/crossplane/v1alpha1/argocdinstance_types.go
// Cluster: https://github.com/akuity/provider-crossplane-akuity/blob/main/internal/types/generated/crossplane/v1alpha1/cluster_types.go
// Config Management Plugin: https://github.com/akuity/provider-crossplane-akuity/blob/main/internal/types/generated/crossplane/v1alpha1/configmanagementplugin_types.go
func (c *crossplaneGenConfig) codegenStructs(sourceFileName string) *bytes.Buffer {
	fset := token.NewFileSet()
	file, err := parser.ParseFile(fset, sourceFileName, nil, 0)
	cli.CheckErr(err)

	fsetOut := token.NewFileSet()
	buf := new(bytes.Buffer)

	tmplBytes, err := types.ReadFile(c.modelTemplateName)
	cli.CheckErr(err)
	fmt.Fprint(buf, "// This is an auto-generated file. DO NOT EDIT\n")
	fmt.Fprint(buf, string(tmplBytes))
	fmt.Fprint(buf, "\n")

	ast.Inspect(file, func(n ast.Node) bool {
		typeSpec, ok := n.(*ast.TypeSpec)
		if !ok || typeSpec.Type == nil {
			return true
		}
		structType, ok := typeSpec.Type.(*ast.StructType)
		if !ok {
			return true
		}

		structName := typeSpec.Name.String()
		if c.structsToIgnore[structName] {
			log.Info("Skipping generating struct", "struct", structName)
			return true
		}
		log.Info("Code generating struct", "struct", structName)

		var newFieldsList []*ast.Field
		for _, field := range structType.Fields.List {
			if len(field.Names) == 0 {
				continue
			}
			if c.fieldToIgnore[structName+"."+field.Names[0].Name] {
				log.Info("Skipping generating field", "struct", structName, "field", field.Names[0].Name)
				continue
			}
			newField, err := convertCrossplaneTypes(structName, field, c.boolToPointer)
			cli.CheckErr(err)
			newFieldsList = append(newFieldsList, newField)
		}
		structType.Fields.List = newFieldsList
		_, err = fmt.Fprint(buf, "// +kubebuilder:object:generate=true\n")
		cli.CheckErr(err)
		_, err = fmt.Fprint(buf, "type ")
		cli.CheckErr(err)
		err = printer.Fprint(buf, fsetOut, n)
		cli.CheckErr(err)
		_, err = fmt.Fprint(buf, "\n\n")
		cli.CheckErr(err)
		return true
	})
	return buf
}

// convertCrossplaneTypes performs some type conversion for some fields to generate Corssplane types.
func convertCrossplaneTypes(structName string, field *ast.Field, boolToPointer bool) (*ast.Field, error) {
	// Convert the field type from raw.Extension to string
	if t, ok := field.Type.(*ast.SelectorExpr); ok && t.Sel.Name == "RawExtension" {
		field.Type = &ast.Ident{Name: "string"}
		return field, nil
	}
	if boolToPointer {
		if ident, ok := field.Type.(*ast.Ident); ok && ident.Name == "bool" {
			field.Type = &ast.StarExpr{
				X: &ast.Ident{Name: "bool"},
			}
			return field, nil
		}
	}
	// Add the `omitempty` tag
	if structName == "ArgoCDSpec" && field.Names[0].Name == "Description" {
		tagValue := strings.TrimPrefix(field.Tag.Value, "`")
		tagValue = strings.TrimSuffix(tagValue, "`")
		tags, err := structtag.Parse(tagValue)
		if err != nil {
			return nil, err
		}
		jsonTag, err := tags.Get("json")
		if err != nil {
			return nil, err
		}
		if err := tags.Set(&structtag.Tag{
			Key:  "json",
			Name: fmt.Sprintf("%s,omitempty", jsonTag.Name),
		}); err != nil {
			return nil, err
		}
		field.Tag.Value = fmt.Sprintf("`%s`", tags.String())
		return field, nil
	}
	return field, nil
}
