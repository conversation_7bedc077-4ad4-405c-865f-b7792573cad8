package config

import (
	"time"

	"github.com/kelseyhightower/envconfig"

	"github.com/akuityio/akuity-platform/internal/services/features"
)

type BillingControllerConfig struct {
	// PortalDBConnection is a postgres connection string to the portal db.
	// e.g. user=masteruser dbname=postgres host=rds.us-west-2.rds.amazonaws.com port=5432 password=abc123 sslmode=require
	PortalDBConnection string `envconfig:"PORTAL_DB_CONNECTION" required:"true"`

	// DBDataKey contains key for the DB data encryption/decryption.
	DBDataKey DBDataKeyConfig

	// DBConnection contains configurations for the DB connection
	DBConnection DBConnectionConfig

	// ResyncDuration billing data re-syncing duration
	// default duration 1hr
	ResyncDuration time.Duration `envconfig:"RESYNC_DURATION" default:"1h"`

	// ReconciliationTimeout is the deadline for reconciling Argo CD billing data.
	// http://prometheus-prod.monitoring.svc/graph?g0.expr=sum%20by%20(controller)%20(histogram_quantile(1.0%2C%20rate(billing_loop_duration_seconds_bucket%7B%7D%5B1h%5D)))&g0.tab=0&g0.display_mode=lines&g0.show_exemplars=0&g0.range_input=2d
	ReconciliationTimeout time.Duration `envconfig:"RECONCILIATION_TIMEOUT" default:"10s"`

	// DomainSuffix is the DNS suffix.
	// e.g. akuity.cloud, stage.akuity.io
	DomainSuffix string `envconfig:"DOMAIN_SUFFIX" default:"akuity.cloud"`

	FeatureGatesSource features.FeatureGatesSource `envconfig:"FEATURES_SOURCE" default:"env"`
}

func NewBillingControllerConfig() (BillingControllerConfig, error) {
	var cfg BillingControllerConfig
	return cfg, envconfig.Process("", &cfg)
}
