package config

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/kelseyhightower/envconfig"
	"sigs.k8s.io/yaml"

	"github.com/akuityio/agent/pkg/client/apis/controlplane"
	kargocontrolplane "github.com/akuityio/agent/pkg/client/apis/kargo/controlplane"
)

const (
	// DefaultPortalServerPort is the default port that the portal server listens on
	DefaultPortalServerPort = 9090

	// DefaultPortalAPIPort is the default port that the portal-api listens on
	DefaultPortalAPIPort = 9091

	// DefaultControllerMetricsPort is the default port that the platform controller metrics server listens on
	DefaultControllerMetricsPort = 9500

	// DefaultPortalMetricsPort is the default port that the portal server metrics server listens on
	DefaultPortalMetricsPort = 9501

	// DefaultBillingControllerMetricsPort is the default port that the billing controller metrics server listens on
	DefaultBillingControllerMetricsPort = 9502

	// DefaultNotificationControllerMetricsPort is the default port that the notification controller metrics server listens on
	DefaultNotificationControllerMetricsPort = 9505

	// DefaultAimsMetricsPort is the default port that the AIMS metrics server listens on
	DefaultAimsMetricsPort = 9503

	// AkputilMetricsPort is the port akputil metrics server listens on
	AkputilMetricsPort = 9504

	// DefaultExtensionsServerPort is the default port that the extensions server listens on
	DefaultExtensionsServerPort = 9092
	// DefaultExtensionsAPIPort is the default port that the grpc extensions api listens on
	DefaultExtensionsAPIPort = 9093

	// DefaultAIMSServerPort is the default port that the AIMS server listens on
	DefaultAIMSServerPort = 9095

	// DefaultAIMSServerPort is the default port that the AIMS server listens on
	DefaultAIMSAPIPort = 9096

	// DefaultAgentAPIPort is the default port that agent-api listens on
	DefaultAgentAPIPort = 9097

	// DefaultAddonControllerMetricsPort is the default port that the notification controller metrics server listens on
	DefaultAddonControllerMetricsPort = 9506
)

// For self-hosted configurations only.
var (
	// IsSelfHostedReleaseBuild is a flag to indicate if the platform is self-hosted release build.
	// this flag should only be used to turn of dev features that should not
	// make into a self hosted release eg. Dev feature source which allows access to all features
	IsSelfHostedReleaseBuild = false
	// IsSelfHosted is a flag to indicate if the platform is self-hosted.
	// Flag can be set via the IS_SELF_HOSTED environment variable for testing purposes in cloud builds only
	// In the self-hosted build the flag is always true.
	IsSelfHosted = os.Getenv("IS_SELF_HOSTED") == "true"
	// SuperUsers is list of super-user emails.
	SuperUsers map[string]bool
	// OIDCGroupsMap is a map of OIDC groups to map to organization roles.
	OIDCGroupsMap map[string]string
	// OIDCTeamsMap is a map of OIDC groups to map to organization teams.
	OIDCTeamsMap map[string][]string
)

type OIDCConfig struct {
	// Issuer is the URL of the OIDC provider
	Issuer                string   `envconfig:"OIDC_ISSUER"`
	ClientID              string   `envconfig:"OIDC_CLIENT_ID"`
	ClientSecret          string   `envconfig:"OIDC_CLIENT_SECRET"`
	Scopes                []string `envconfig:"OIDC_SCOPES" default:"openid,profile,email,offline_access"`
	LogoutURL             string   `envconfig:"OIDC_LOGOUT_URL"`
	InsecureSkipTLSVerify bool     `envconfig:"OIDC_INSECURE_SKIP_TLS_VERIFY"`
}

type Auth0Config struct {
	// Domain is the domain of the Auth0 service.
	// e.g. akuity-test.us.auth0.com, auth.akuity.io
	Domain string

	// ClientID is the client id for the Auth0 service.
	// e.g. ZzQpfezpoqgsLwb9xHu6b3IvHKerPQ4K
	ClientID string

	// ClientSecret is the application's secret.
	ClientSecret string

	// CallbackURL is the url for the auth callback handler.
	// e.g. https://akuity.cloud/api/auth/callback
	CallbackURL string

	// Audience is the default audience of the token
	// e.g. https://api.akuity.cloud
	Audience string

	// ManagementAPIAudience is the audience when interacting with Auth0 Management API (https://auth0.com/docs/api/management/v2).
	// This is used by account deletion endpoint to remove user entry from Auth0.
	ManagementAPIAudience string
}

func (c Auth0Config) Enabled() bool {
	return c.Domain != "" && c.ClientID != ""
}

type PortalAuth0Config struct {
	// Domain is the domain of the Auth0 service.
	// e.g. akuity-test.us.auth0.com, auth.akuity.io
	Domain string `envconfig:"AUTH0_DOMAIN"`

	// CLIClientID is the Auth0 client id for CLI.
	// e.g. 4LgDBwNTcVgfcqaaIeyP7nFGOvOoVZ4d
	CLIClientID string `envconfig:"AUTH0_CLI_CLIENT_ID"`

	// PortalClientID is the Auth0 client id for the portal service.
	// e.g. ZzQpfezpoqgsLwb9xHu6b3IvHKerPQ4K
	PortalClientID string `envconfig:"AUTH0_CLIENT_ID"`

	// PortalClientSecret is the Auth0 client secret for the portal service.
	PortalClientSecret string `envconfig:"AUTH0_CLIENT_SECRET"`

	// CallbackURL is the url for the auth callback handler.
	// e.g. https://akuity.cloud/api/auth/callback
	CallbackURL string `envconfig:"AUTH0_CALLBACK_URL"`

	// Audience is the default audience of the token
	// e.g. https://api.akuity.cloud
	Audience string `envconfig:"AUTH0_AUDIENCE"`

	// ManagementAPIAudience is the audience when interacting with Auth0 Management API (https://auth0.com/docs/api/management/v2).
	// This is used by account deletion endpoint to remove user entry from Auth0.
	ManagementAPIAudience string `envconfig:"AUTH0_MANAGEMENT_API_AUDIENCE"`

	// EnableConnectionDomainVerification enabled the user domain verification for the connection
	// this is used to ensure a user cannot spoof the email domain of a different org/user
	EnableConnectionDomainVerification bool `envconfig:"AUTH0_ENABLE_CONNECTION_DOMAIN_VERIFICATION" default:"true"`
}

type AIConfig struct {
	OpenAIBaseURL             string        `envconfig:"OPENAI_BASE_URL"`
	OpenAIAPIKey              string        `envconfig:"OPENAI_API_KEY"`
	OpenAIMainModel           string        `envconfig:"OPENAI_MAIN_MODEL"`
	OpenAISearchModel         string        `envconfig:"OPENAI_SEARCH_MODEL"`
	OpenAILightweightModel    string        `envconfig:"OPENAI_LIGHTWEIGHT_MODEL"`
	AnthropicAPIKey           string        `envconfig:"ANTHROPIC_API_KEY"`
	Dev                       bool          `envconfig:"AI_DEV" default:"false"`
	MaxToolsPerReconciliation int           `envconfig:"AI_MAX_TOOLS_PER_RECONCILIATION" default:"4"`
	ToolCallTimeout           time.Duration `envconfig:"AI_TOOL_CALL_TIMEOUT" default:"15s"`
	RetriesPerMessage         int           `envconfig:"AI_RETRIES_PER_MESSAGE" default:"5"`
}

type MaxMindConfig struct {
	// AccountID is MaxMind Account ID: https://www.maxmind.com/en/accounts/802566/
	AccountID string `envconfig:"MAX_MIND_ACCOUNT_ID"`

	// ApiKey is MaxMind License Key: https://www.maxmind.com/en/accounts/802566/license-key
	ApiKey string `envconfig:"MAX_MIND_API_KEY"`
}

func (c PortalAuth0Config) CLI() Auth0Config {
	return Auth0Config{
		Domain:      c.Domain,
		ClientID:    c.CLIClientID,
		CallbackURL: c.CallbackURL,
		Audience:    c.Audience,
	}
}

func (c PortalAuth0Config) Portal() Auth0Config {
	auth0Config := Auth0Config{
		Domain:       c.Domain,
		ClientID:     c.PortalClientID,
		ClientSecret: c.PortalClientSecret,
		CallbackURL:  c.CallbackURL,
		Audience:     c.Audience,
	}

	if c.ManagementAPIAudience != "" {
		// For prod environment, since we use custom domain (auth.akuity.io), and due to Auth0 documentation (https://auth0.com/docs/customize/custom-domains/configure-features-to-use-custom-domains#auth0-apis),
		// we need to use the default domain, e.g., https://akuity.us.auth0.com/api/v2/ for the audience.
		auth0Config.ManagementAPIAudience = c.ManagementAPIAudience
	} else {
		// For test and stage environment, since we don't use custom domain, the audience will be https://{DOMAIN}/api/v2/, e.g., https://akuity-test.us.auth0.com/api/v2/.
		auth0Config.ManagementAPIAudience = fmt.Sprintf("https://%s/api/v2/", auth0Config.Domain)
	}
	return auth0Config
}

type DBDataKeyConfig struct {
	// DataKey is the data key used to encrypt/decrypt data from the database. This environment
	// variable is used only for development purposes. In production, the data key should be
	// retrieved via an AWS KMS decrypt call against ciphertext blob of the plaintext data key.
	// Value should be base64 encoded.
	// e.g. m8PTL8tiENXfaOPqop78ljrdaoloXn+w/HeTIWMUgO4=
	DataKey string `envconfig:"DB_DATA_KEY"`
}

type DBConnectionConfig struct {
	MaxOpen     int           `envconfig:"DB_CON_MAX_OPEN" default:"20"`
	MaxIdle     int           `envconfig:"DB_CON_MAX_IDLE" default:"10"`
	MaxLifetime time.Duration `envconfig:"DB_CON_MAX_LIFETIME" default:"10m"`
	MaxIdleTime time.Duration `envconfig:"DB_CON_MAX_IDLE_TIME" default:"5m"`
}

type SMTPConfig struct {
	Host     string `envconfig:"SMTP_HOST"`
	Port     int    `envconfig:"SMTP_PORT" default:"587"`
	User     string `envconfig:"SMTP_USER"`
	Password string `envconfig:"SMTP_PASSWORD"`

	// InviteEmail is the sender email for the invitation
	InviteEmail string `envconfig:"INVITE_EMAIL" default:"<EMAIL>"`
	// MarketplaceNotifyEmail is the email address to notify for marketplace events
	MarketplaceNotifyEmail string `envconfig:"MARKETPLACE_NOTIFY_EMAIL" default:"<EMAIL>"`
	// NotificationEmail is the sender email for the email notifications
	NotificationEmail string `envconfig:"NOTIFICATION_EMAIL" default:"<EMAIL>"`
}

// InstanceConfig holds configuration applicable when provisioning Argo CD instances
type InstanceConfig struct {
	// AgentImage overrides the agent server image when generating control-plane manifests.
	AgentServerImage string `envconfig:"AGENT_SERVER_IMAGE"`

	// AgentServerImageHost overrides the agent-server image host (e.g. us-docker.pkg.dev/akuity/akp-sh). This is ignored if AGENT_SERVER_IMAGE is set.
	AgentServerImageHost string `envconfig:"AGENT_SERVER_IMAGE_HOST"`

	// AgentImage overrides the agent image when generating cluster manifests.
	AgentImage string `envconfig:"AGENT_IMAGE"`

	// InstanceValues is a default set of values used to generate instance manifests
	InstanceValues InstanceValues `envconfig:"INSTANCE_VALUES_FILE"`

	// KargoInstanceValues is a default set of values used to generate instance manifests
	KargoInstanceValues KargoInstanceValues `envconfig:"KARGO_INSTANCE_VALUES_FILE"`

	// ArgoCDImageHost overrides the argo cd image host (e.g. quay.io/argoproj).
	ArgoCDImageHost string `envconfig:"ARGOCD_IMAGE_HOST"`

	// ArgoCDImageRepo overrides the argo cd image repository name (e.g. argocd).
	ArgoCDImageRepo string `envconfig:"ARGOCD_IMAGE_REPO"`

	// ArgoCDAppResyncIntervalSeconds holds Argo CD Application resync interval in seconds
	ArgoCDAppResyncIntervalSeconds int `envconfig:"ARGOCD_APP_RESYNC_INTERVAL_SECONDS"`

	// AgentStatusUpdateIntervalSeconds holds agent status update interval in seconds
	AgentStatusUpdateIntervalSeconds int `envconfig:"AGENT_STATUS_UPDATE_INTERVAL_SECONDS"`

	// If OpenshiftCompatibility is true (false by default) - Argo CD instances will be created in an OpenShift compatible way
	OpenshiftCompatibility bool `envconfig:"OPENSHIFT_COMPATIBILITY" default:"false"`

	// If SidecarFeatureCompatibility is true (true by default) - instances will run pgpool/pgbouncer as k8s 1.29 sidecars
	SidecarFeatureCompatibility bool `envconfig:"SIDECAR_FEATURE_COMPATIBILITY" default:"true"`

	// If IPV6OnlyCompatibility is true (false by default) - instances k3s will be created with IPV6 only compatibility
	IPV6OnlyCompatibility bool `envconfig:"IPV6_ONLY_COMPATIBILITY" default:"false"`
}

func NewInstanceConfig() (InstanceConfig, error) {
	var cfg InstanceConfig
	return cfg, envconfig.Process("", &cfg)
}

type UsageLimits struct {
	InstancesCountPerOwner  int `envconfig:"LIMIT_INSTANCES_COUNT_PER_OWNER" default:"0"`
	ClustersCountPerOwner   int `envconfig:"LIMIT_CLUSTERS_COUNT_PER_OWNER" default:"0"`
	AppsCountPerOwner       int `envconfig:"LIMIT_APPS_COUNT_PER_OWNER" default:"0"`
	WorkspacesCountPerOwner int `envconfig:"LIMIT_WORKSPACES_COUNT_PER_OWNER" default:"0"`

	KargoInstancesCountPerOwner int `envconfig:"LIMIT_KARGO_INSTANCES_COUNT_PER_OWNER" default:"0"`
	KargoAgentsCountPerOwner    int `envconfig:"LIMIT_KARGO_AGENTS_COUNT_PER_OWNER" default:"0"`
	KargoProjectsCountPerOwner  int `envconfig:"LIMIT_KARGO_PROJECTS_COUNT_PER_OWNER" default:"0"`
	KargoStageCountPerOwner     int `envconfig:"LIMIT_KARGO_STAGE_COUNT_PER_OWNER" default:"0"`

	OrgsCountPerUser int `envconfig:"LIMIT_ORGS_COUNT_PER_USER" default:"0"`

	// TotalInstancesCount is the maximum no. of argocd tenants that can be created
	// in this instance of Akuity Platform, 0 = no limit
	TotalInstancesCount int64 `envconfig:"LIMIT_TOTAL_INSTANCES_COUNT" default:"0"`

	// TotalKargoInstancesCount is the maximum no. of kargo tenants that can be created
	// in this instance of Akuity Platform, 0 = no limit
	TotalKargoInstancesCount int64 `envconfig:"LIMIT_TOTAL_KARGO_INSTANCES_COUNT" default:"0"`

	// MaxInvitationsPerBatch sets the maximum number of invitation emails which can be sent in one go. Defaults to 5.
	MaxInvitationsPerBatch int `envconfig:"LIMIT_EMAIL_INVITATION_COUNT_PER_BATCH" default:"5"`
}

// SuperUserEnv lists all superuser accounts in a self-hosted setup
type SuperUserEnv struct {
	SuperUserEmails []string `envconfig:"SUPERUSER_EMAILS" default:""`
}

// OIDCMapConfig defines OIDC groups to map to organization roles. For self-hosted configurations only.
type OIDCMapConfig struct {
	MemberGroups []string `envconfig:"GLOBAL_OIDC_MEMBER_GROUPS" default:""`
	AdminGroups  []string `envconfig:"GLOBAL_OIDC_ADMIN_GROUPS" default:""`
	OwnerGroups  []string `envconfig:"GLOBAL_OIDC_OWNER_GROUPS" default:""`
}

type Team struct {
	Name string `json:"name"`
}

type RoleTeamGroup struct {
	OIDCGroup string `json:"oidcGroup"`
	OrgRole   string `json:"orgRole"`
	Teams     []Team `json:"teams"`
}

type RoleTeamGroupConfig struct {
	RawJSON string `envconfig:"GLOBAL_ROLE_TEAM_GROUPS" default:"[]"`
}

type NameConfig struct {
	MinOrganizationNameLength int `envconfig:"MIN_ORGANIZATION_NAME_LENGTH" default:"4" json:"minOrganizationNameLength"`
	MinInstanceNameLength     int `envconfig:"MIN_INSTANCE_NAME_LENGTH" default:"3" json:"minInstanceNameLength"`
	MinClusterNameLength      int `envconfig:"MIN_CLUSTER_NAME_LENGTH" default:"3" json:"minClusterNameLength"`
	MinSubdomainLength        int `envconfig:"MIN_SUBDOMAIN_LENGTH" default:"3" json:"minSubdomainLength"`
}

type InstanceValues struct {
	Values *controlplane.DataValues
}

func (cfg *InstanceValues) Decode(value string) error {
	data, err := os.ReadFile(filepath.Clean(value))
	if err != nil {
		return err
	}
	cfg.Values = &controlplane.DataValues{}
	return yaml.Unmarshal(data, cfg.Values)
}

type KargoInstanceValues struct {
	Values *kargocontrolplane.DataValues
}

func (cfg *KargoInstanceValues) Decode(value string) error {
	data, err := os.ReadFile(filepath.Clean(value))
	if err != nil {
		return err
	}
	cfg.Values = &kargocontrolplane.DataValues{}
	return yaml.Unmarshal(data, cfg.Values)
}

type ConfigWithConnection interface {
	GetDBConnection() string
	GetTenantDBConnection() string
	ShouldOverrideSafeguard() bool
}
