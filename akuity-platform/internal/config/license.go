package config

import (
	"fmt"
	"time"

	"github.com/go-logr/logr"
	"github.com/kelseyhightower/envconfig"

	"github.com/akuityio/akuity-platform/internal/license"

	_ "embed"
)

type LicenseKeyENV struct {
	LicenseKey string `envconfig:"LICENSE_KEY" default:""`
}

//go:embed license.pem
var licensePubKey string

var (
	DefaultLicense = license.License{
		// ARGO CD Limits
		Applications: 10,
		Clusters:     1,
		Instances:    1,

		// KARGO Limits
		KargoAgents:    1,
		KargoInstances: 1,
		KargoStages:    10, // set to 10 to match app count
		// even though default limits are set kargo will be disabled by default
		KargoEnabled: false,

		Version: license.VersionV2,

		// default grace period and expiry is 0, i.e. this is valid forever
		GracePeriod:    0,
		ExpirationTime: 0,
	}
	// CurrentLicense by default is set to default license data
	CurrentLicense = DefaultLicense
)

func InitializeLicense(log logr.Logger) error {
	var licenseKey LicenseKeyENV
	if err := envconfig.Process("", &licenseKey); err != nil {
		return fmt.Errorf("failed to parse license key env variable")
	}
	if licenseKey.LicenseKey == "" {
		return nil
	}

	// validate license key
	licenseData, err := license.ValidateLicenseKey([]byte(licensePubKey), licenseKey.LicenseKey)
	if err != nil {
		log.Error(err, "failed to validate license key")
		return nil
	}
	// update CurrentLicense details
	CurrentLicense = *backwardsCompat(licenseData)

	return nil
}

func GetLicense() license.License {
	if CurrentLicense.ExpirationTime != 0 && time.Unix(CurrentLicense.ExpirationTime, 0).Add(time.Duration(CurrentLicense.GracePeriod)*time.Second).Before(time.Now()) {
		return DefaultLicense
	}
	return CurrentLicense
}

func backwardsCompat(claims *license.License) *license.License {
	if claims.Version == license.VersionV1Backwards || claims.Version == license.VersionV1 {
		claims.Version = license.VersionV1
		// v1 license does not have kargo limits included
		// so set default kargo limits
		claims.KargoAgents = DefaultLicense.KargoAgents
		claims.KargoInstances = DefaultLicense.KargoInstances
		claims.KargoStages = DefaultLicense.KargoStages
		claims.KargoEnabled = false
	}
	return claims
}
