package config

var (
	InternalCmName      = "portal-internal"
	InternalCmNamespace = "akuity-platform"
	PortalServerCmName  = "portal-server"

	VerifiedOrganizationMapKey  = "verifiedOrganizationMap"
	DisabledInstanceCreationKey = "disableFreeInstanceCreation"
	AnnouncementBannerKey       = "ANNOUNCEMENT_BANNER"
)

// InternalConfig defines the structure of the portal-internal configmap.
type InternalConfig struct {
	DisableFreeInstanceCreation bool            `json:"disableFreeInstanceCreation"`
	VerifiedOrganizationMap     map[string]bool `json:"verifiedOrganizationMap"`
}
