package config

import (
	"time"

	"github.com/kelseyhightower/envconfig"

	"github.com/akuityio/akuity-platform/internal/services/features"
)

type AimsConfig struct {
	// PortalDBConnection is a postgres connection string to the portal db.
	// e.g. user=masteruser dbname=postgres host=rds.us-west-2.rds.amazonaws.com port=5432 password=abc123 sslmode=require
	PortalDBConnection string `envconfig:"PORTAL_DB_CONNECTION"`

	// DBConnection contains configurations for the DB connection
	DBConnection DBConnectionConfig

	// DomainSuffix is the DNS suffix.
	// e.g. akuity.cloud, stage.akuity.io
	DomainSuffix string `envconfig:"DOMAIN_SUFFIX" default:"akuity.cloud"`

	// InstanceProgressingDeadline is the deadline for configuring Argo CD instance.
	InstanceProgressingDeadline time.Duration `envconfig:"INSTANCE_PROGRESSING_DEADLINE" default:"5m"`

	// ClusterProgressingDeadline is the deadline for configuring Argo CD cluster.
	ClusterProgressingDeadline time.Duration `envconfig:"CLUSTER_PROGRESSING_DEADLINE" default:"1m"`

	// DBDataKey contains key for the DB data encryption/decryption.
	DBDataKey DBDataKeyConfig

	// Auth0 contains Auth0 related configurations.
	Auth0 PortalAuth0Config

	FeatureGatesSource features.FeatureGatesSource `envconfig:"FEATURES_SOURCE" default:"env"`
}

func NewAimsConfig() (AimsConfig, error) {
	var cfg AimsConfig
	return cfg, envconfig.Process("", &cfg)
}
