package config

import (
	"time"

	"github.com/kelseyhightower/envconfig"

	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/consts"
)

type AddonControllerConfig struct {
	// PortalDBConnection - Postgres read-write connection string to the portal DB
	// e.g. user=masteruser dbname=postgres host=<host> port=5432 sslmode=require password=<password>
	PortalDBConnection string `envconfig:"PORTAL_DB_CONNECTION" required:"true"`

	// TenantDBConnection - Postgres read-write connection string to the tenants DB
	// e.g. user=masteruser dbname=postgres host=<host> port=5432 sslmode=require password=<password>
	TenantDBConnection string `envconfig:"K3S_DB_CONNECTION" required:"true"`

	FeatureGatesSource features.FeatureGatesSource `envconfig:"FEATURES_SOURCE" default:"env"`

	// DBDataKey contains key for the DB data encryption/decryption.
	DBDataKey DBDataKeyConfig

	// DBConnection contains configurations for the DB connection
	DBConnection DBConnectionConfig

	// ResyncDuration instances/clusters resyncing duration
	ResyncDuration time.Duration `envconfig:"RESYNC_DURATION" default:"5m"`

	// ReconciliationTimeout is the deadline for reconciling Argo CD instances/clusters/events and Kargo instances/agents/events.
	// http://prometheus-prod.monitoring.svc/graph?g0.expr=sum%20by%20(controller)%20(histogram_quantile(1.0%2C%20rate(reconcile_loop_duration_seconds_bucket%7B%7D%5B1h%5D)))&g0.tab=0&g0.display_mode=lines&g0.show_exemplars=0&g0.range_input=2d
	ReconciliationTimeout time.Duration `envconfig:"RECONCILIATION_TIMEOUT" default:"1m"`

	// Namespace is the namespace where the platform controller is running.
	Namespace string `envconfig:"NAMESPACE"`

	// MetricsCollectionInterval is the interval at which metrics are collected
	MetricsCollectionInterval time.Duration `envconfig:"METRICS_COLLECTION_INTERVAL" default:"30s"`

	// SkipSafeguard skips the development safeguard which prevents running the platform controller in the wrong environment by ensuring in-cluster Postgres exists
	SkipSafeguard bool `envconfig:"SKIP_SAFEGUARD"`

	// ControllerNamespace is the namespace where the controller is running
	ControllerNamespace string `envconfig:"CONTROLLER_NAMESPACE" default:"akuity-platform"`
}

func (cfg *AddonControllerConfig) GetNamespace() string {
	if cfg.Namespace != "" {
		return cfg.Namespace
	}
	return consts.AkuityPlatformNamespace
}

func (cfg *AddonControllerConfig) GetDBConnection() string {
	return cfg.PortalDBConnection
}

func (cfg *AddonControllerConfig) GetTenantDBConnection() string {
	return cfg.TenantDBConnection
}

func (cfg *AddonControllerConfig) ShouldOverrideSafeguard() bool {
	return cfg.SkipSafeguard
}

func NewAddonControllerConfig() (AddonControllerConfig, error) {
	var cfg AddonControllerConfig
	return cfg, envconfig.Process("", &cfg)
}
