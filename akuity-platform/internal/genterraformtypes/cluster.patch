diff --git a/terraform/akp/types/cluster.go b/terraform/akp/types/cluster.go
--- a/terraform/akp/types/cluster.go	(revision 7de9bc5ea168e297e8aaeb5434fe76c5620815ae)
+++ b/terraform/akp/types/cluster.go	(date 1755690894283)
@@ -36,8 +36,8 @@
 }
 
 type Resources struct {
-	Mem types.String `tfsdk:"mem"`
-	Cpu types.String `tfsdk:"cpu"`
+	Memory types.String `tfsdk:"memory"`
+	Cpu    types.String `tfsdk:"cpu"`
 }
 
 type DirectClusterSpec struct {
@@ -63,8 +63,24 @@
 type RepoServerAutoScalingConfig struct {
 	ResourceMinimum *Resources  `tfsdk:"resource_minimum"`
 	ResourceMaximum *Resources  `tfsdk:"resource_maximum"`
-	ReplicaMaximum  types.Int64 `tfsdk:"replica_maximum"`
-	ReplicaMinimum  types.Int64 `tfsdk:"replica_minimum"`
+	ReplicasMaximum types.Int64 `tfsdk:"replicas_maximum"`
+	ReplicasMinimum types.Int64 `tfsdk:"replicas_minimum"`
+}
+
+type CustomAgentSizeConfig struct {
+	ApplicationController *AppControllerCustomAgentSizeConfig `tfsdk:"application_controller"`
+	RepoServer            *RepoServerCustomAgentSizeConfig    `tfsdk:"repo_server"`
+}
+
+type AppControllerCustomAgentSizeConfig struct {
+	Memory types.String `tfsdk:"memory"`
+	Cpu    types.String `tfsdk:"cpu"`
+}
+
+type RepoServerCustomAgentSizeConfig struct {
+	Memory   types.String `tfsdk:"memory"`
+	Cpu      types.String `tfsdk:"cpu"`
+	Replicas types.Int64  `tfsdk:"replicas"`
 }
 
 type ClusterCompatibility struct {
@@ -87,7 +103,8 @@
 	EksAddonEnabled                 types.Bool                          `tfsdk:"eks_addon_enabled"`
 	ManagedClusterConfig            *ManagedClusterConfig               `tfsdk:"managed_cluster_config"`
 	MultiClusterK8SDashboardEnabled types.Bool                          `tfsdk:"multi_cluster_k8s_dashboard_enabled"`
-	AutoscalerConfig                basetypes.ObjectValue               `tfsdk:"autoscaler_config"`
+	CustomAgentSizeConfig           *CustomAgentSizeConfig              `tfsdk:"custom_agent_size_config"`
+	AutoscalerConfig                basetypes.ObjectValue               `tfsdk:"auto_agent_size_config"`
 	Project                         types.String                        `tfsdk:"project"`
 	Compatibility                   *ClusterCompatibility               `tfsdk:"compatibility"`
 	ArgocdNotificationsSettings     *ClusterArgoCDNotificationsSettings `tfsdk:"argocd_notifications_settings"`
