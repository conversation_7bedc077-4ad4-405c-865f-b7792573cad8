package genterraformtypes

import (
	"bytes"
	"embed"
	"fmt"
	"go/ast"
	"go/format"
	"go/parser"
	"go/printer"
	"go/token"
	"os"
	"path/filepath"
	"strings"
	"unicode"

	"github.com/fatih/structtag"
	"github.com/go-logr/logr"

	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
)

var (
	//go:embed *.go.tmpl
	types embed.FS

	tfGenConfigs = []tfGenConfig{
		{
			modelTemplateName: "argocd.go.tmpl",
			sourceFileName:    "./api/akuity/v1alpha1/argocdinstance_types.go",
			structToOverride: map[string]bool{
				"ArgoCD": true,
			},
			structsToIgnore: map[string]bool{
				"ArgoCDList": true,
			},
			fieldToIgnore: map[string]bool{
				"ArgoCDSpec.Shard": true,
				// NOTE(thomastaylor312): All the fields below here are fields that aren't yet
				// implemented, but I didn't want to change at the same time we changed generation.
				// In a follow up, all of these fields should be removed so they get generated and
				// implemented.
				"InstanceSpec.AkuityIntelligenceExtension":    true,
				"InstanceSpec.ApplicationSetExtension":        true,
				"InstanceSpec.CustomDeprecatedApis":           true,
				"InstanceSpec.KubeVisionConfig":               true,
				"InstanceSpec.Secrets":                        true,
				"InstanceSpec.ImageUpdaterVersion":            true,
				"InstanceSpec.Basepath":                       true,
				"InstanceSpec.AppsetProgressiveSyncsEnabled":  true,
				"InstanceSpec.AppReconciliationsRateLimiting": true,
			},
			boolToPointer: true,
			fieldToPointer: map[string]bool{
				"InstanceSpec.Fqdn": true,
			},
			fieldTypeOverrides: map[string]ast.Expr{
				"Extensions": &ast.SelectorExpr{
					X:   &ast.Ident{Name: "types"},
					Sel: &ast.Ident{Name: "List"},
				},
			},
		},
		{
			modelTemplateName: "cluster.go.tmpl",
			sourceFileName:    "./api/akuity/v1alpha1/cluster_types.go",
			structToOverride: map[string]bool{
				"Cluster": true,
			},
			structsToIgnore: map[string]bool{
				"ClusterList": true,
			},
			fieldToIgnore: map[string]bool{
				"DirectClusterSpec.Server":       true,
				"DirectClusterSpec.Organization": true,
				"DirectClusterSpec.Token":        true,
				"DirectClusterSpec.CaData":       true,
				"ClusterData.MaintenanceMode":    true,
			},
			fieldTypeOverrides: map[string]ast.Expr{
				"AutoscalerConfig": &ast.SelectorExpr{
					X:   &ast.Ident{Name: "basetypes"},
					Sel: &ast.Ident{Name: "ObjectValue"},
				},
				"ClusterType": &ast.SelectorExpr{
					X:   &ast.Ident{Name: "types"},
					Sel: &ast.Ident{Name: "String"},
				},
			},
		},
		{
			modelTemplateName: "configmanagementplugin.go.tmpl",
			sourceFileName:    "./api/argocd/v1alpha1/configmanagementplugin_types.go",
			structToOverride: map[string]bool{
				"ConfigManagementPlugin": true,
			},
			structsToIgnore: map[string]bool{
				"ConfigManagementPluginList": true,
			},
		},
		{
			modelTemplateName: "kargo.go.tmpl",
			sourceFileName:    "./api/akuity/v1alpha1/kargoinstance_types.go",
			structToOverride: map[string]bool{
				"Kargo": true,
			},
			structsToIgnore: map[string]bool{
				"KargoList": true,
				// When we publicly release, we can uncomment this
				"AkuityIntelligence": true,
			},
			fieldToIgnore: map[string]bool{
				"KargoInstanceSpec.AkuityIntelligence": true,
			},
			fieldTypeOverrides: map[string]ast.Expr{
				"AdminAccount": &ast.SelectorExpr{
					X:   &ast.Ident{Name: "types"},
					Sel: &ast.Ident{Name: "Object"},
				},
				"ViewerAccount": &ast.SelectorExpr{
					X:   &ast.Ident{Name: "types"},
					Sel: &ast.Ident{Name: "Object"},
				},
				"UserAccount": &ast.SelectorExpr{
					X:   &ast.Ident{Name: "types"},
					Sel: &ast.Ident{Name: "Object"},
				},
				"ProjectCreatorAccount": &ast.SelectorExpr{
					X:   &ast.Ident{Name: "types"},
					Sel: &ast.Ident{Name: "Object"},
				},
				"DexConfigSecret": &ast.SelectorExpr{
					X:   &ast.Ident{Name: "types"},
					Sel: &ast.Ident{Name: "Map"},
				},
			},
			boolToPointer: true,
			structToNotGenerateInModel: map[string]bool{
				"KargoPredefinedAccountClaimValue": true,
				"KargoPredefinedAccountData":       true,
				"Value":                            true,
			},
		},
		{
			modelTemplateName: "kargoagent.go.tmpl",
			sourceFileName:    "./api/akuity/v1alpha1/kargoagent_types.go",
			structToOverride: map[string]bool{
				"KargoAgent": true,
			},
			structsToIgnore: map[string]bool{
				"KargoAgentList": true,
			},
			fieldToIgnore: map[string]bool{
				// NOTE(thomastaylor312): In a follow up, all of these fields should be removed so
				// they get generated and implemented.
				"KargoAgentData.SelfManagedArgocdUrl": true,
			},
			fieldTypeOverrides: map[string]ast.Expr{
				"Size": &ast.SelectorExpr{
					X:   &ast.Ident{Name: "types"},
					Sel: &ast.Ident{Name: "String"},
				},
			},
		},
	}

	log logr.Logger
)

func init() {
	var err error
	log, err = logging.NewLogger()
	cli.CheckErr(err)
}

type tfGenConfig struct {
	// boolToPointer indicates if we want to modify the CRD file to replace all boolean fields to boolean pointer fields.
	// For example, for argo instance CRD, the instance patch of apply endpoint needs to distinguish
	// between the boolean fields are unset or set to false.
	boolToPointer bool
	// fieldToPointer defines which fields should be converted to pointer types in the original CRD.
	// The key is StructName.FieldName.
	fieldToPointer map[string]bool
	// modelTemplateName is the name of the template file for generating terraform model.
	modelTemplateName string
	// sourceFileName is the name of the source CRD file.
	sourceFileName string
	// structsToIgnore defines which structs should be ignored during the generation of terraform models.
	structsToIgnore map[string]bool
	// structToOverride defines which structs should be overridden during the generation of terraform models.
	structToOverride map[string]bool
	// fieldToIgnore defines which fields should be ignored during the generation of terraform models. The key is StructName.FieldName.
	fieldToIgnore map[string]bool
	// fieldTypeOverrides defines custom type conversions for specific fields.
	// The key is StructName.FieldName and the value is the desired terraform type.
	fieldTypeOverrides map[string]ast.Expr
	// structToNotGenerateInModel defines which structs should not be generated in the terraform model.
	structToNotGenerateInModel map[string]bool
}

func CodegenTypes() {
	for _, c := range tfGenConfigs {
		apiType := c.generateAPIFiles()
		apiDestFile := "./tf/apis/v1alpha1/" + strings.TrimSuffix(c.modelTemplateName, ".tmpl")
		err := os.MkdirAll(filepath.Dir(apiDestFile), 0o755)
		cli.CheckErr(err)
		err = os.WriteFile(apiDestFile, apiType, 0o600)
		cli.CheckErr(err)
		typesFileOut := c.codegenStructs(apiDestFile)
		destFile := "./tf/types/" + strings.TrimSuffix(c.modelTemplateName, ".tmpl")
		err = os.MkdirAll(filepath.Dir(destFile), 0o755)
		cli.CheckErr(err)
		err = os.WriteFile(destFile, typesFileOut.Bytes(), 0o600)
		cli.CheckErr(err)
	}
}

// generateAPIFiles generates the API definitions which will be used by the terraform provider from the CRD files.
// It replaces boolean fields with boolean pointer fields if necessary, and also remove a bunch of unused structs from CRD files.
// ArgoCD: https://github.com/akuity/terraform-provider-akp/blob/main/akp/apis/v1alpha1/argocd.go
// Cluster: https://github.com/akuity/terraform-provider-akp/blob/main/akp/apis/v1alpha1/cluster.go
// Config Management Plugin: https://github.com/akuity/terraform-provider-akp/blob/main/akp/apis/v1alpha1/configmanagementplugin.go
func (t *tfGenConfig) generateAPIFiles() []byte {
	fset := token.NewFileSet()
	file, err := parser.ParseFile(fset, t.sourceFileName, nil, 0)
	cli.CheckErr(err)
	var decls []ast.Decl
DeclLoop:
	for _, d := range file.Decls {
		// Remove the init function in the original CRD files.
		if fn, ok := d.(*ast.FuncDecl); ok && fn.Name.Name == "init" {
			continue
		} else if gen, ok := d.(*ast.GenDecl); ok {
			for i := range gen.Specs {
				spec := gen.Specs[i]
				// Remove the following structs since they are not used by the terraform provider.
				if ts, ok := spec.(*ast.TypeSpec); ok {
					if t.structsToIgnore[ts.Name.Name] {
						log.Info("Skipping struct: %s", ts.Name.Name)
						continue DeclLoop
					}
				}
				ts, ok := spec.(*ast.TypeSpec)
				if !ok {
					continue
				}
				st, ok := ts.Type.(*ast.StructType)
				if !ok {
					continue
				}
				// Remove the following fields since they are not used by the terraform provider.
				var filteredFields []*ast.Field
				for _, field := range st.Fields.List {
					if len(field.Names) > 0 {
						fieldName := getFieldName(field)
						ignore := fmt.Sprintf("%s.%s", ts.Name.Name, fieldName)
						if t.fieldToIgnore[ignore] {
							log.Info("Skipping field: %s", ignore)
							continue
						}

						if t.fieldToPointer[ignore] {
							log.Info("Converting field to pointer: %s", ignore)
							field.Type = &ast.StarExpr{X: field.Type}
						}
					}
					filteredFields = append(filteredFields, field)
				}
				ts.Type.(*ast.StructType).Fields.List = filteredFields
				gen.Specs[i] = ts
			}
			d = gen
		}
		decls = append(decls, d)
	}
	file.Decls = decls

	if t.boolToPointer {
		ast.Inspect(file, func(n ast.Node) bool {
			if x, ok := n.(*ast.Field); ok {
				// Convert the field type from boolean to boolean pointer
				if t, ok := x.Type.(*ast.Ident); ok && t.Name == "bool" {
					x.Type = &ast.StarExpr{X: x.Type}
				}
			}
			return true
		})
	}
	output := strings.Builder{}
	output.WriteString(`
// This is an auto-generated file. DO NOT EDIT
/*
Copyright 2023 Akuity, Inc.
*/

`)
	if err := format.Node(&output, fset, file); err != nil {
		cli.CheckErr(err)
	}
	return []byte(output.String())
}

// codegenStructs copies structs from the source file to a new terraform model
// ArgoCD: https://github.com/akuity/terraform-provider-akp/blob/main/akp/types/argocd.go
// Cluster: https://github.com/akuity/terraform-provider-akp/blob/main/akp/types/cluster.go
// Config Management Plugin: https://github.com/akuity/terraform-provider-akp/blob/main/akp/types/configmanagementplugin.go
func (t *tfGenConfig) codegenStructs(sourceFileName string) *bytes.Buffer {
	fset := token.NewFileSet()
	file, err := parser.ParseFile(fset, sourceFileName, nil, 0)
	cli.CheckErr(err)

	fsetOut := token.NewFileSet()
	buf := new(bytes.Buffer)

	tmplBytes, err := types.ReadFile(t.modelTemplateName)
	cli.CheckErr(err)
	fmt.Fprint(buf, "// This is an auto-generated file. DO NOT EDIT\n")
	fmt.Fprint(buf, string(tmplBytes))
	fmt.Fprint(buf, "\n")

	ast.Inspect(file, func(n ast.Node) bool {
		typeSpec, ok := n.(*ast.TypeSpec)
		if !ok || typeSpec.Type == nil {
			return true
		}
		structType, ok := typeSpec.Type.(*ast.StructType)
		if !ok {
			return true
		}

		structName := typeSpec.Name.String()
		if t.structToOverride[structName] {
			log.Info("Struct will be overridden by the template", "struct", structName)
			return true
		}
		if t.structToNotGenerateInModel[structName] {
			log.Info("Struct will not be generated in model", "struct", structName)
			return true
		}
		log.Info("Code generating struct", "struct", structName)

		var newFieldsList []*ast.Field
		for _, field := range structType.Fields.List {
			if len(field.Names) == 0 {
				continue
			}

			if field.Tag == nil {
				continue
			}
			fieldName := getFieldName(field)
			cli.CheckErr(addTerraformTag(field))
			field.Type = t.convertTerraformType(fieldName, field.Type)
			if objType := rawExtensionToStruct(fieldName, field); objType != nil {
				field.Type = objType
			}
			newFieldsList = append(newFieldsList, field)
		}
		structType.Fields.List = newFieldsList
		_, err = fmt.Fprint(buf, "type ")
		cli.CheckErr(err)
		err = printer.Fprint(buf, fsetOut, n)
		cli.CheckErr(err)
		_, err = fmt.Fprint(buf, "\n\n")
		cli.CheckErr(err)
		return true
	})
	return buf
}

// convertTerraformType converts the type of field to terraform framework attribute type.
func (t *tfGenConfig) convertTerraformType(fieldName string, exp ast.Expr) ast.Expr {
	if override, exists := t.fieldTypeOverrides[fieldName]; exists {
		log.Info("applying type override", "field", fieldName)
		return override
	}

	log := log.WithValues("name", fieldName)
	switch f := exp.(type) {
	case *ast.Ident:
		log.Info("converting ident", "ident", f)
		if attributeType := getTerraformAttributeType(f.Name); attributeType != nil {
			return attributeType
		}
	case *ast.ArrayType:
		log.Info("converting array", "array", f)
		elt, _ := resolvePointer(f.Elt)
		arrayTypeName := fmt.Sprintf("%s", elt)
		if attributeType := getTerraformAttributeType(arrayTypeName); attributeType != nil {
			f.Elt = attributeType
			return f
		}
	case *ast.MapType:
		log.Info("converting map", "map", f)
		// check if the map key and value are string type
		key, _ := resolvePointer(f.Key)
		value, _ := resolvePointer(f.Value)
		keyTypeName := fmt.Sprintf("%s", key)
		valueTypeName := fmt.Sprintf("%s", value)
		if keyTypeName == "string" && valueTypeName == "string" {
			return &ast.SelectorExpr{
				X:   &ast.Ident{Name: "types"},
				Sel: &ast.Ident{Name: "Map"},
			}
		}
		panic("for map type field, only map[string]string is supported")
	case *ast.StarExpr:
		log.Info("converting pointer", "pointer", f)
		ptr, ok := resolvePointer(exp)
		typeName := fmt.Sprintf("%s", ptr)
		if attributeType := getTerraformAttributeType(typeName); attributeType != nil {
			return attributeType
		}
		if ok {
			f.X = t.convertTerraformType(fieldName, ptr)
			return f
		}
		return t.convertTerraformType(fieldName, ptr)
	}
	return exp
}

// getTerraformAttributeType generates terraform framework attribute type from the go primitive types.
// https://developer.hashicorp.com/terraform/plugin/framework/handling-data/attributes#framework-attribute-types
func getTerraformAttributeType(typeName string) ast.Expr {
	switch typeName {
	case "string":
		return &ast.SelectorExpr{
			X:   &ast.Ident{Name: "types"},
			Sel: &ast.Ident{Name: "String"},
		}
	case "bool":
		return &ast.SelectorExpr{
			X:   &ast.Ident{Name: "types"},
			Sel: &ast.Ident{Name: "Bool"},
		}
	case "int", "int8", "int16", "int32", "int64",
		"uint", "uint8", "uint16", "uint32", "uint64":
		return &ast.SelectorExpr{
			X:   &ast.Ident{Name: "types"},
			Sel: &ast.Ident{Name: "Int64"},
		}
	case "float32", "float64":
		return &ast.SelectorExpr{
			X:   &ast.Ident{Name: "types"},
			Sel: &ast.Ident{Name: "Float64"},
		}
	case "ClusterSize":
		return &ast.SelectorExpr{
			X:   &ast.Ident{Name: "types"},
			Sel: &ast.Ident{Name: "String"},
		}
	case "ClusterCustomization", "AppsetPolicy":
		return &ast.SelectorExpr{
			X:   &ast.Ident{Name: "types"},
			Sel: &ast.Ident{Name: "Object"},
		}
	}
	return nil
}

func rawExtensionToStruct(fieldName string, field *ast.Field) ast.Expr {
	if fieldName != "Kustomization" {
		return nil
	}
	expr, _ := resolvePointer(field.Type)
	f, ok := expr.(*ast.SelectorExpr)
	if !ok {
		return nil
	}
	ident := f.X.(*ast.Ident)
	typeName := fmt.Sprintf("%s.%s", ident.Name, f.Sel.Name)
	if typeName == "runtime.RawExtension" {
		ident.Name = "types"
		f.Sel.Name = "String"
	} else {
		panic(fmt.Sprintf("I dont know how to convert for field %s of type %s", fieldName, typeName))
	}
	return expr
}

// addTerraformTag converts the value of json tag of a field in to lowercase snake case, and then add it with `tfsdk` tag.
func addTerraformTag(field *ast.Field) error {
	tagValue := strings.TrimPrefix(field.Tag.Value, "`")
	tagValue = strings.TrimSuffix(tagValue, "`")
	tags, err := structtag.Parse(tagValue)
	if err != nil {
		return err
	}
	jsonTag, err := tags.Get("json")
	if err != nil {
		return err
	}
	snakeCaseName := camelToSnakeCase(jsonTag.Name)
	if err := tags.Set(&structtag.Tag{
		Key:  "tfsdk",
		Name: snakeCaseName,
	}); err != nil {
		return err
	}
	tags.Delete("json")
	field.Tag.Value = fmt.Sprintf("`%s`", tags.String())
	return nil
}

// camelToSnakeCase changes the camel case input to lowercase snake case format.
func camelToSnakeCase(input string) string {
	var buf bytes.Buffer

	for i, r := range input {
		if unicode.IsUpper(r) {
			if i > 0 {
				buf.WriteByte('_')
			}
			buf.WriteRune(unicode.ToLower(r))
		} else {
			buf.WriteRune(r)
		}
	}
	return buf.String()
}

// resolvePointer returns the underlying type of a field
func resolvePointer(exp ast.Expr) (ast.Expr, bool) {
	ptr, ok := exp.(*ast.StarExpr)
	if !ok {
		return exp, false
	}
	return ptr.X, true
}

// getFieldName is a helper to return the field name of a struct
func getFieldName(field *ast.Field) string {
	if len(field.Names) != 1 {
		panic(fmt.Sprintf("  found field with multiple names %s", field.Names))
	}
	return field.Names[0].String()
}
