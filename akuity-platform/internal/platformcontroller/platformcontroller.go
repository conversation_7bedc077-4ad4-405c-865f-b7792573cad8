package platformcontroller

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"os"

	"github.com/go-logr/logr"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/spf13/cobra"
	"golang.org/x/sync/semaphore"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"

	agentClient "github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/client/apis/controlplane"
	"github.com/akuityio/agent/pkg/kube"
	"github.com/akuityio/akuity-platform/controllers/platform/integration"
	"github.com/akuityio/akuity-platform/controllers/platform/metrics"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/devcheck"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	utilsMetrics "github.com/akuityio/akuity-platform/internal/utils/metrics"
	"github.com/akuityio/akuity-platform/models/client"
)

func getToolExecCallback(cmd string, execSemaphore *semaphore.Weighted, metricsRegistry *metrics.ControllerMetricsRegistry) func(ctx context.Context) (func(), error) {
	return func(ctx context.Context) (func(), error) {
		metricsRegistry.IncExecCount(cmd)
		if err := execSemaphore.Acquire(ctx, 1); err != nil {
			return nil, fmt.Errorf("failed to acquire semaphore: %w", err)
		}
		return func() {
			execSemaphore.Release(1)
			metricsRegistry.DecExecCount(cmd)
		}, nil
	}
}

func NewPlatformControllerCommand() *cobra.Command {
	var (
		numWorkers        int
		metricsPort       int
		enableIngress     bool
		insecure          bool
		shard             string
		externalDNS       = controlplane.NewDataValuesIngressExternalDnsWithDefaults()
		portalIPs         []string
		clusterIssuerName string
		debug             bool
	)
	cmd := &cobra.Command{
		Use:   "platform-controller",
		Short: "Akuity Platform Controller",
		Run: func(cmd *cobra.Command, args []string) {
			controllerConfig, err := config.NewPlatformControllerConfig()
			cli.CheckErr(err)

			var logOpts []logging.Option
			if debug {
				logOpts = append(logOpts, logging.WithDebug())
			}
			log, err := logging.NewLogger(logOpts...)
			cli.CheckErr(err)

			// dev: license init needs to happen before feature flag init
			cli.CheckErr(config.InitializeLicense(log))

			instanceConfig, err := config.NewInstanceConfig()
			cli.CheckErr(err)

			err = database.InitializeDataKey(controllerConfig.DBDataKey)
			cli.CheckErr(err)

			ctx, cancel := context.WithCancel(context.Background())
			defer cancel()

			portalDBPool, err := database.GetDBPool(controllerConfig.PortalDBConnection, &controllerConfig.DBConnection)
			if err != nil {
				cli.CheckErr(fmt.Errorf("failed to connect to portal RW db: %w", err))
			}

			k3sDBPool, err := database.GetDBPool(controllerConfig.TenantDBConnection, &controllerConfig.DBConnection)
			if err != nil {
				cli.CheckErr(fmt.Errorf("failed to connect to k3s db: %w", err))
			}

			repoSet := client.NewRepoSet(portalDBPool.DB)

			featureSvc := features.NewService(repoSet, portalDBPool.DB, config.IsSelfHosted, config.IsSelfHostedReleaseBuild, controllerConfig.FeatureGatesSource, config.GetLicense(), features.WithLogger(&log))

			loadingRules := clientcmd.NewDefaultClientConfigLoadingRules()
			loadingRules.DefaultClientConfig = &clientcmd.DefaultClientConfig
			overrides := clientcmd.ConfigOverrides{}
			clientConfig := clientcmd.NewInteractiveDeferredLoadingClientConfig(loadingRules, &overrides, os.Stdin)
			restConfig, err := clientConfig.ClientConfig()
			cli.CheckErr(err)
			if insecure {
				restConfig.Insecure = insecure
				restConfig.CAData = nil
				restConfig.CAFile = ""
			}

			k3sReadonlyHostname := ""
			if controllerConfig.TenantDBReadonlyConnection != "" {
				roTennantDBInfo, err := database.ExtractDBInfo(controllerConfig.TenantDBReadonlyConnection)
				cli.CheckErr(err)
				k3sReadonlyHostname = roTennantDBInfo.Host
			}

			k8sClient, err := kubernetes.NewForConfig(restConfig)
			cli.CheckErr(err)

			cli.CheckErr(devcheck.Safeguard(ctx, config.ConfigWithConnection(&controllerConfig), k8sClient))

			for _, ip := range portalIPs {
				if ipAddr := net.ParseIP(ip); ipAddr == nil {
					_, _, err := net.ParseCIDR(ip)
					cli.CheckErr(err)
				}
			}

			settings := integration.ControllerSettings{
				InstanceSubDomains:        controllerConfig.InstanceSubDomains,
				OverrideDefaultDomains:    controllerConfig.OverrideDefaultDomains,
				EnableIngress:             enableIngress,
				K8SRestConfig:             restConfig,
				K8SClientSet:              k8sClient,
				PortalDBRawClient:         portalDBPool.DB,
				K3sDBRawClient:            k3sDBPool.DB,
				K3sDBConnection:           controllerConfig.TenantDBConnection,
				K3sReadonlyHostname:       k3sReadonlyHostname,
				SharedK3sDBConnectionAuth: controllerConfig.SharedTenantDBConnectionAuth,
				PortalDBConnection:        controllerConfig.PortalDBConnection,
				RepoSet:                   repoSet,
				Log:                       &log,
				InstanceConfig:            instanceConfig,
				IngressConfig:             controllerConfig.IngressConfig,
				ExternalDNS:               externalDNS,
				DomainSuffix:              controllerConfig.DomainSuffix,
				Shard:                     shard,
				PortalIPs:                 portalIPs,
				ClusterIssuerName:         clusterIssuerName,
				PortalURL:                 controllerConfig.PortalURL,
				AIConfig:                  controllerConfig.AIConfig,
				MultiClusterK8sDashboardConfig: integration.MultiClusterK8sDashboardConfig{
					ClusterInfoSyncPeriod:      controllerConfig.ClusterInfoSyncPeriod,
					ResourcesSyncPeriod:        controllerConfig.ResourcesSyncPeriod,
					EventSyncPeriod:            controllerConfig.EventSyncPeriod,
					GarbageCollectionPeriod:    controllerConfig.GarbageCollectionPeriod,
					GarbageCollectionThreshold: controllerConfig.GarbageCollectionThreshold,
					SkipTLSVerify:              insecure,
				},
			}

			logServicesEnabled(ctx, controllerConfig, settings, featureSvc, &log)

			metricsRegistry := metrics.NewControllerMetricsRegistry()
			platformController, err := integration.NewPlatformController(ctx, &log, settings, featureSvc, controllerConfig, metricsRegistry)

			if controllerConfig.ExecConcurrencyLimit > 0 {
				execSemaphore := semaphore.NewWeighted(int64(controllerConfig.ExecConcurrencyLimit))
				agentClient.SetOnYTTStream(getToolExecCallback("ytt", execSemaphore, metricsRegistry))
				agentClient.SetOnKustomize(getToolExecCallback("kustomize", execSemaphore, metricsRegistry))
				kube.SetOnKubectlCallback(getToolExecCallback("kubectl", execSemaphore, metricsRegistry))
			}
			cli.CheckErr(err)

			metricsCollectorDB := portalDBPool.DB
			if controllerConfig.PortalDBReadonlyConnection != "" {
				portalDBReadonlyPool, err := database.GetDBPool(controllerConfig.PortalDBReadonlyConnection, &controllerConfig.DBConnection)
				if err != nil {
					cli.CheckErr(fmt.Errorf("failed to connect to portal RO db: %w", err))
				}
				metricsCollectorDB = portalDBReadonlyPool.DB
			}
			metricsCollector := metrics.NewMetricsCollector(controllerConfig, metricsCollectorDB, settings.RepoSet, &log, settings.Shard)

			prometheus.DefaultRegisterer.MustRegister(metricsCollector)
			metrics.MustRegisterWorkqueueMetrics(prometheus.DefaultRegisterer)

			cli.CheckErr(platformController.Init(context.Background()))
			go func() { cli.CheckErr(platformController.Run(ctx, numWorkers)) }()
			go func() { cli.CheckErr(metricsCollector.Run(context.Background())) }()
			go func() { cli.CheckErr(utilsMetrics.NewMetricsServer(&log, "platform-controller", metricsPort)()) }()

			// Wait forever
			select {}
		},
	}
	cmd.Flags().IntVar(&numWorkers, "num-workers", 1, "The number of workers")
	cmd.Flags().IntVar(&metricsPort, "metrics-port", config.DefaultControllerMetricsPort, "The metrics server port")
	cmd.Flags().BoolVar(&enableIngress, "enable-ingress", false, "Whether to enable Ingress")
	cmd.Flags().BoolVar(&insecure, "insecure", false, "Whether to enable insecure")
	cmd.Flags().StringVar(&shard, "shard", "", "Shard name")
	cmd.Flags().StringVar(externalDNS.HealthCheckId, "external-dns-health-check-id", externalDNS.GetHealthCheckId(), "The health check id for the external DNS failover")
	cmd.Flags().StringVar(externalDNS.SetIdentifier, "external-dns-set-identifier", "usw2", "The external DNS set identifier")
	cmd.Flags().StringVar(externalDNS.AwsFailover, "external-dns-aws-failover", "PRIMARY", "The external DNS aws failover")
	cmd.Flags().StringVar(externalDNS.Target, "external-dns-target", "", "The external DNS target")
	cmd.Flags().StringSliceVar(&portalIPs, "portal-ips", nil, "Portal-Server Egress IPs")
	cmd.Flags().StringVar(&clusterIssuerName, "cluster-issuer-name", "letsencrypt-platform-prod", "The name of the LetsEncrypt ClusterIssuer for argocd custom domains")
	cmd.Flags().BoolVar(&debug, "debug", false, "Enable debug logging")

	return cmd
}

func logServicesEnabled(ctx context.Context, config config.PlatformControllerConfig, settings integration.ControllerSettings, featService features.Service, log *logr.Logger) {
	log.Info(fmt.Sprintf("Read-Only Portal DB enabled: %t", config.PortalDBReadonlyConnection != ""))
	log.Info(fmt.Sprintf("Read-Only Tenants DB enabled: %t", config.TenantDBReadonlyConnection != ""))
	log.Info(fmt.Sprintf("Shared K3s DB Connection Auth enabled: %t", config.SharedTenantDBConnectionAuth))
	log.Info(fmt.Sprintf("Config.DomainSuffix: %q", config.DomainSuffix))
	instanceConfigJson, _ := json.Marshal(settings.InstanceConfig)
	log.Info(fmt.Sprintf("Settings.InstanceConfig: %s", string(instanceConfigJson)))
	log.Info(fmt.Sprintf("Settings.EnableIngress: %v", settings.EnableIngress))
	log.Info(fmt.Sprintf("Settings.Shard: %q", settings.Shard))

	featureStatuses := featService.GetFeatureStatuses(ctx, nil)
	autoscaler := featureStatuses.GetAutoscaler().Enabled()
	k3sProxyInformers := featureStatuses.GetK3SProxyInformers().Enabled()
	agentPermissions := featureStatuses.GetAgentPermissions().Enabled()
	clusterAutoscaler := featureStatuses.GetClusterAutoscaler().Enabled()

	log.Info(fmt.Sprintf("Cluster Autoscaler enabled: %t", clusterAutoscaler))
	log.Info(fmt.Sprintf("Informer-based k3s proxy enabled: %t", k3sProxyInformers))
	log.Info(fmt.Sprintf("Agent Permissions enabled: %t", agentPermissions))
	log.Info(fmt.Sprintf("Autoscaler enabled: %t", autoscaler))
}
