package logcleaner

import (
	"context"
	"fmt"

	"github.com/go-logr/logr"
	"github.com/spf13/cobra"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	"github.com/akuityio/akuity-platform/internal/version"
	"github.com/akuityio/akuity-platform/models/client"
)

func NewLogCleanerCommand() *cobra.Command {
	var (
		debug  bool
		dryRun bool
	)
	command := &cobra.Command{
		Use:   "logs-cleaner",
		Short: "AKP stored logs cleaner",
		Run: func(cmd *cobra.Command, args []string) {
			cfg, err := config.NewPortalServerConfig()
			cli.CheckErr(err)

			var logOpts []logging.Option
			if debug {
				logOpts = append(logOpts, logging.WithDebug())
			}
			log, err := logging.NewLogger(logOpts...)
			cli.CheckErr(err)

			cli.CheckErr(start(cmd.Context(), cfg, dryRun, log))
		},
	}
	command.Flags().BoolVar(&dryRun, "dry-run", false, "Run the deletion in dry run mode")
	command.Flags().BoolVar(&debug, "debug", false, "Enable debug logging")
	return command
}

func start(
	ctx context.Context,
	cfg config.PortalServerConfig,
	dryRun bool,
	log logr.Logger,
) error {
	versionInfo := version.GetVersion()
	log.Info("Starting Akuity Platform Logs Cleaner",
		"version", versionInfo.Version,
		"build_date", versionInfo.BuildDate)

	// Set up the database connection
	portalDBPool, err := database.GetDBPool(cfg.PortalDBConnection, &cfg.DBConnection)
	if err != nil {
		return fmt.Errorf("failed to connect to portal db: %w", err)
	}
	// get logs repo set
	repoSet := client.NewRepoSet(portalDBPool.DB)

	q := repoSet.Logs(qm.Where("current_timestamp > (creation_timestamp + (ttl_seconds * INTERVAL '1 second'))"))

	count, err := q.Count(ctx)
	if err != nil {
		return fmt.Errorf("failed to count logs: %w", err)
	}
	if count > 0 {
		log.Info("Found logs to delete", "count", count)
		if dryRun {
			log.Info("Dry-run mode enabled, skipping deletion")
			return nil
		}
		log.Info("Deleting logs...")
		err := q.DeleteAll(ctx)
		if err != nil {
			return fmt.Errorf("failed to delete logs: %w", err)
		}
		log.Info("Logs deleted successfully")
	} else {
		log.Info("No logs to delete")
		return nil
	}
	return nil
}
