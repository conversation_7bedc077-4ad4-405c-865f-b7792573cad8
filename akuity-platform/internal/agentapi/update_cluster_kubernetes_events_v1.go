package agentapi

import (
	"context"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	agentv1 "github.com/akuityio/akuity-platform/agent/pkg/api/gen/agent/v1"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

func (s *AgentService) UpdateKubernetesEvents(
	ctx context.Context,
	req *agentv1.UpdateKubernetesEventsRequest,
) (*agentv1.UpdateKubernetesEventsResponse, error) {
	txDB, txBeginner := database.WithTxBeginner(s.db)
	rs := client.NewRepoSet(txDB)

	cluster, err := rs.ArgoCDClusters(qm.Load(models.ArgoCDClusterRels.Instance)).GetByID(ctx, req.GetClusterId())
	if err != nil {
		return nil, status.Error(codes.NotFound, err.Error())
	}
	if err = s.checkDashboardEnabled(ctx, cluster, rs); err != nil {
		return nil, err
	}

	resSvc := k8sresource.NewServiceWithOptions(rs, txDB, cluster.R.Instance.OrganizationOwner, k8sresource.WithLogger(logging.Extract(ctx)))

	tx, err := txBeginner.Begin(ctx)
	if err != nil {
		return nil, err
	}
	defer func() {
		_ = tx.Rollback()
	}()
	if err := resSvc.UpsertKubernetesEvents(ctx, cluster, req.GetEvents()); err != nil {
		return nil, err
	}

	if err := tx.Commit(); err != nil {
		return nil, err
	}
	return &agentv1.UpdateKubernetesEventsResponse{}, nil
}
