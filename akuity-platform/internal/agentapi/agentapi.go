package agentapi

import (
	"context"
	"database/sql"
	"fmt"
	"net"
	"net/http"

	"github.com/go-logr/logr"
	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"

	agentv1 "github.com/akuityio/akuity-platform/agent/pkg/api/gen/agent/v1"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/errors"
	akuitygrpc "github.com/akuityio/akuity-platform/internal/utils/grpc"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/auth"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/gateway"
	grpclogging "github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	modelClient "github.com/akuityio/akuity-platform/models/client"
)

type Options struct {
	BindNetwork string
	BindAddress string
}

type APIServer interface {
	Start(ctx context.Context, log *logr.Logger) error
	Proxy() (http.HandlerFunc, error)
}

func NewAPIServer(cfg config.PortalServerConfig, db *sql.DB, options Options) APIServer {
	return &apiServer{Options: options, cfg: cfg, db: db}
}

var _ APIServer = &apiServer{}

type apiServer struct {
	Options
	cfg config.PortalServerConfig
	db  *sql.DB
}

func (srv *apiServer) runAPI(log *logr.Logger, errCh chan<- error) (*grpc.Server, error) {
	l, err := net.Listen(srv.BindNetwork, srv.BindAddress)
	if err != nil {
		return nil, fmt.Errorf("agent api new listener: %w", err)
	}

	authUnaryInterceptor := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		res, err := auth.UnaryServerAgentAuthInterceptor(srv.db, srv.cfg.DomainSuffix, features.NewService(
			modelClient.NewRepoSet(srv.db), srv.db, config.IsSelfHosted, config.IsSelfHostedReleaseBuild, srv.cfg.FeatureGatesSource, config.GetLicense(), features.WithLogger(log),
		))(ctx, req, info, handler)
		return res, errors.ConvertError(err)
	}

	grpcSrv := grpc.NewServer(
		akuitygrpc.NewUnaryServerInterceptor(
			log,
			akuitygrpc.WithLoggingOptions(grpclogging.WithCodeFunc(func(err error) codes.Code {
				return errors.GetAPIStatus(err).GRPCStatus().Code()
			})),
			akuitygrpc.WithUnaryServerInterceptors(
				authUnaryInterceptor,
			),
		),
	)

	agentService := NewAgentService(log, srv.db, srv.cfg)
	agentv1.RegisterAgentServiceServer(grpcSrv, agentService)

	go func() {
		log.Info(fmt.Sprintf("agent api is listening on %s://%s", srv.BindNetwork, srv.BindAddress))
		if err := grpcSrv.Serve(l); err != nil {
			errCh <- fmt.Errorf("serve agent api: %w", err)
		}
	}()
	return grpcSrv, nil
}

func (srv *apiServer) Start(ctx context.Context, log *logr.Logger) error {
	errCh := make(chan error, 1)
	api, err := srv.runAPI(log, errCh)
	if err != nil {
		return fmt.Errorf("agent api run api: %w", err)
	}
	select {
	case <-ctx.Done():
		api.GracefulStop()
		return nil
	case err := <-errCh:
		return err
	}
}

func (srv *apiServer) Proxy() (http.HandlerFunc, error) {
	opts := []grpc.DialOption{grpc.WithTransportCredentials(insecure.NewCredentials())}
	client, err := grpc.NewClient(srv.BindAddress, opts...)
	if err != nil {
		return nil, fmt.Errorf("agent api failed to dial to %s: %w", srv.BindAddress, err)
	}

	mux := runtime.NewServeMux(gateway.InjectAgentMetadata())
	if err := agentv1.RegisterAgentServiceHandler(context.Background(), mux, client); err != nil {
		return nil, fmt.Errorf("agent api failed to register http mux: %w", err)
	}

	return mux.ServeHTTP, nil
}
