package agentapi

import (
	"context"
	"database/sql"

	"github.com/go-logr/logr"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	agentv1 "github.com/akuityio/akuity-platform/agent/pkg/api/gen/agent/v1"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	modelClient "github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

var _ agentv1.AgentServiceServer = &AgentService{}

type AgentService struct {
	agentv1.UnimplementedAgentServiceServer

	db      *sql.DB
	cfg     config.PortalServerConfig
	featSvc features.Service

	log *logr.Logger
}

func NewAgentService(log *logr.Logger, db *sql.DB, cfg config.PortalServerConfig) *AgentService {
	return &AgentService{log: log, db: db, cfg: cfg, featSvc: features.NewService(
		modelClient.NewRepoSet(db), db, config.IsSelfHosted, config.IsSelfHostedReleaseBuild, cfg.FeatureGatesSource, config.GetLicense(), features.WithLogger(log),
	)}
}

func (s *AgentService) checkDashboardEnabled(ctx context.Context, cluster *models.ArgoCDCluster, rs modelClient.RepoSet) error {
	if !s.featSvc.GetFeatureStatuses(ctx, &cluster.R.Instance.OrganizationOwner).GetMultiClusterK8SDashboard().Enabled() {
		return status.Errorf(codes.PermissionDenied, "kubevision is not enabled for organization: %s", cluster.R.Instance.OrganizationOwner)
	}
	instanceConfig, err := rs.ArgoCDInstanceConfigs().GetByID(ctx, cluster.InstanceID)
	if err != nil {
		return err
	}
	if instanceConfig == nil {
		return status.Errorf(codes.NotFound, "instance config not found for instance: %s, cluster id: %s", cluster.InstanceID, cluster.ID)
	}
	instanceSpec, err := instanceConfig.GetSpec()
	if err != nil {
		return err
	}
	if !instanceSpec.MultiClusterK8sDashboardEnabled {
		return status.Errorf(codes.PermissionDenied, "kubevision is not enabled for instance: %s, cluster id: %s", cluster.InstanceID, cluster.ID)
	}
	spec, err := cluster.GetSpec()
	if err != nil {
		return err
	}
	if !spec.MultiClusterK8SDashboardEnabled {
		return status.Errorf(codes.PermissionDenied, "kubevision is not enabled for cluster: %s", cluster.ID)
	}
	return nil
}
