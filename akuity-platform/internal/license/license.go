package license

import (
	"crypto/ecdsa"
	"crypto/elliptic"
	"crypto/rand"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

const (
	// VersionV1 is the first version of the license
	VersionV1 = "v1"
	// VersionV1Backwards is the first version of the license where version is empty
	VersionV1Backwards = ""
	// VersionV2 is the second version of the license
	VersionV2 = "v2"
)

var ValidLicenseVersions = map[string]bool{
	// v1 and "" are the same
	VersionV1Backwards: true,
	VersionV1:          true,
	// v2 license includes kargo limits
	VersionV2: true,
}

var limitKeysMap = map[string]bool{
	"applications":   true,
	"clusters":       true,
	"instances":      true,
	"kargoAgents":    true,
	"kargoInstances": true,
	//"kargoProjects":  true,
	"kargoStages": true,
}

type License struct {
	// ARGO CD Limits
	Applications uint64 `json:"applications"`
	Clusters     uint64 `json:"clusters"`
	Instances    uint64 `json:"instances"`

	// KARGO LIMITS
	KargoAgents    uint64 `json:"kargoAgents"`
	KargoInstances uint64 `json:"kargoInstances"`
	KargoStages    uint64 `json:"kargoStages"`

	// Version used for the license versioning
	Version string `json:"licenseVersion"`

	// GracePeriod to give after expiry in seconds
	GracePeriod int64 `json:"gracePeriod"`
	// ExpirationTime time in unix timestamp seconds
	ExpirationTime int64 `json:"expirationTime"`
	// IssuedFor is the customer name the license is issued for
	IssuedFor string `json:"issuedFor"`
	// Description holds the additional information about the license
	Description string `json:"description"`

	// KargoEnabled is the flag to enable kargo
	KargoEnabled bool `json:"kargoEnabled"`

	// KubeVisionEnabled is the flag to enable kube vision
	KubeVisionEnabled bool `json:"kubeVisionEnabled"`

	jwt.MapClaims `json:"-"`
}

// PrettyPrint pretty print license data
func (l *License) PrettyPrint() (string, error) {
	ldata, err := json.Marshal(l)
	if err != nil {
		return "", fmt.Errorf("failed to marshal license data: %w", err)
	}
	var lmap map[string]interface{}
	if err := json.Unmarshal(ldata, &lmap); err != nil {
		return "", fmt.Errorf("failed to unmarshal license data: %w", err)
	}
	for k, v := range lmap {
		if limitKeysMap[k] {
			val, ok := v.(float64)
			if !ok {
				return "", fmt.Errorf("failed to convert %s to float64, original type = %T", k, v)
			}
			if val == 0 {
				lmap[k] = "unlimited"
			}
		}
		if k == "expirationTime" {
			val, ok := v.(float64)
			if !ok {
				return "", fmt.Errorf("failed to convert %s to float64, original type = %T", k, v)
			}
			lmap[k] = fmt.Sprintf("%.0f (%v)", val, time.Unix(int64(val), 0).UTC().Format(time.RFC1123))
		}
		if k == "gracePeriod" {
			val, ok := v.(float64)
			if !ok {
				return "", fmt.Errorf("failed to convert %s to float64, original type = %T", k, v)
			}
			lmap[k] = fmt.Sprintf("%.0f days", val/24/3600)
		}
	}
	pretty, err := json.MarshalIndent(lmap, "", "  ")
	if err != nil {
		return "", fmt.Errorf("failed to marshal license data: %w", err)
	}
	return string(pretty), nil
}

func CreateLicense(privKeyPem []byte, licenseData *License) (string, error) {
	if licenseData.Version == "" {
		return "", fmt.Errorf("license version is required; use v2 if license is post kargo intg")
	}
	privKey, err := jwt.ParseECPrivateKeyFromPEM(privKeyPem)
	if err != nil {
		return "", err
	}
	token := jwt.NewWithClaims(
		jwt.SigningMethodES256, // specific instance of `*SigningMethodECDSA`
		licenseData,
	)
	return token.SignedString(privKey)
}

func ValidateLicenseKey(pubKeyPem []byte, licenseKey string) (*License, error) {
	pubKey, err := jwt.ParseECPublicKeyFromPEM(pubKeyPem)
	if err != nil {
		return nil, err
	}
	token, err := jwt.ParseWithClaims(licenseKey, &License{}, func(token *jwt.Token) (interface{}, error) {
		return pubKey, nil
	})
	if err != nil {
		return nil, err
	}
	if claims, ok := token.Claims.(*License); ok && token.Valid {
		return claims, nil
	} else {
		return nil, fmt.Errorf("license invalid")
	}
}

func GeneratePemFiles() ([]byte, []byte, error) {
	privKey, err := ecdsa.GenerateKey(elliptic.P256(), rand.Reader)
	if err != nil {
		return nil, nil, err
	}
	derPrivKey, err := x509.MarshalECPrivateKey(privKey)
	if err != nil {
		return nil, nil, err
	}

	privKeyBlock := &pem.Block{
		Type:  "EC PRIVATE KEY",
		Bytes: derPrivKey,
	}

	derPublicKey, err := x509.MarshalPKIXPublicKey(&privKey.PublicKey)
	if err != nil {
		return nil, nil, err
	}

	pubKeyBlock := &pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: derPublicKey,
	}

	return pem.EncodeToMemory(privKeyBlock), pem.EncodeToMemory(pubKeyBlock), nil
}
