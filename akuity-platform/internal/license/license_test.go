package license

import (
	"log"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

func TestLicense(t *testing.T) {
	privKey, pubKey, err := GeneratePemFiles()
	require.NoError(t, err)
	log.Print("privKey\n", string(privKey), "\n\npubKey\n", string(pubKey))
	licenseData := &License{
		Applications:   2,
		Clusters:       1,
		Instances:      2,
		GracePeriod:    30,
		ExpirationTime: time.Now().Add(2 * time.Minute).Unix(),
		Version:        "v2",
	}
	jwt, err := CreateLicense(privKey, licenseData)
	require.NoError(t, err)
	log.Print("\njwt ", jwt)

	_, randomPubKey, err := GeneratePemFiles()
	require.NoError(t, err)

	_, err = ValidateLicenseKey(randomPubKey, jwt)
	require.Error(t, err)

	license, err := ValidateLicenseKey(pubKey, jwt)
	require.NoError(t, err)
	require.Equal(t, *license, *licenseData)
}

func TestLicensePrettyPrint(t *testing.T) {
	licenseData := &License{
		Applications:   0,
		Clusters:       0,
		Instances:      12,
		KargoAgents:    0,
		KargoInstances: 123,
		KargoStages:    4,
		Version:        "v2",
		GracePeriod:    35 * 24 * 60 * 60,
		ExpirationTime: **********,
		IssuedFor:      "test",
		Description:    "test description",
	}

	prettyPrinted, err := licenseData.PrettyPrint()
	require.NoError(t, err)

	expected := `{
  "applications": "unlimited",
  "clusters": "unlimited",
  "description": "test description",
  "expirationTime": "********** (Sat, 24 Aug 2024 09:27:10 UTC)",
  "gracePeriod": "35 days",
  "instances": 12,
  "issuedFor": "test",
  "kargoAgents": "unlimited",
  "kargoEnabled": false,
  "kargoInstances": 123,
  "kargoStages": 4,
  "kubeVisionEnabled": false,
  "licenseVersion": "v2"
}`
	require.Equal(t, expected, prettyPrinted)
}
