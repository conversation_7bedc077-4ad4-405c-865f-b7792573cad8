WITH aggregated AS (
  SELECT
    COUNT(*) AS count,
    CASE
      WHEN COUNT(*) > 1 THEN gen_random_uuid()::text
      ELSE MIN(id)
    END AS id,
    MIN(start_time) AS first_start,
    MAX(start_time) AS last_start,
    MAX(end_time) AS last_end,
    ROUND(AVG(EXTRACT(EPOCH FROM (end_time - start_time)))) AS avg_duration,
    instance_id,
    application_name,
    result_phase,
    result_message,
    details
  FROM
    argo_cd_sync_operation
  WHERE
    start_time >= $1 AND start_time <= $2
    AND is_aggregated = FALSE
  GROUP BY
    instance_id,
    application_name,
    result_phase,
    result_message,
    details,
    (date_trunc('hour', start_time) + ((date_part('minute', start_time)::integer / $3) * $3 || ' minutes')::interval)
),
updated AS (
  UPDATE argo_cd_sync_operation
  SET is_aggregated = TRUE, last_occurred_timestamp = last_start, duration = avg_duration
  FROM aggregated
  WHERE argo_cd_sync_operation.id = aggregated.id AND aggregated.count = 1
  RETURNING argo_cd_sync_operation.*
),
inserted AS (
    INSERT INTO argo_cd_sync_operation (id, count, start_time, end_time, last_occurred_timestamp, duration, instance_id, application_name, result_phase, result_message, details, is_aggregated)
SELECT
  id,
  count,
  first_start,
  last_end,
  last_start,
  avg_duration,
  instance_id,
  application_name,
  result_phase,
  result_message,
  details,
  TRUE
FROM aggregated
WHERE count > 1
RETURNING *
)
DELETE FROM argo_cd_sync_operation
WHERE start_time >= $1
  AND start_time <= $2
  AND is_aggregated = FALSE
  AND id NOT IN (SELECT id FROM updated UNION ALL SELECT id FROM inserted);
