package aggregator

import (
	"fmt"
	"time"

	"github.com/spf13/cobra"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/cli"
)

func NewAggregatorCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use: "aggregator",
		Run: func(cmd *cobra.Command, args []string) {
			cmd.HelpFunc()(cmd, args)
		},
	}
	cmd.AddCommand(NewAuditSyncAggregatorCommand())
	return cmd
}

// NewAuditSyncAggregatorCommand - a command to aggregate duplicate Audit and Sync records into a single DB record:
// akputil aggregator aggregate-audit-sync-records - aggregate Audit Logs for the previous day.
// akputil aggregator aggregate-audit-sync-records --aggregation-window 30 - aggregate Audit Logs for the previous day with 30min aggregation window.
// akputil aggregator aggregate-audit-sync-records --start-date <startDate> --end-date <endDate> --aggregation-window 5 - aggregate Audit Logs in the period specified.
func NewAuditSyncAggregatorCommand() *cobra.Command {
	var (
		clientConfig                   clientcmd.ClientConfig
		startDate, endDate             time.Time
		startDateString, endDateString string
		aggregationWindow              int
	)
	// akuity-platform-deploy/base/charts/akuity-platform/templates/aggregator/aggregator-cronjob.yaml
	// http://prometheus-prod.monitoring.svc/graph?g0.expr=sum%20by%20(job_name)%20(kube_job_status_completion_time%7Bjob_name%3D~%22records-aggregator.%2B%22%7D%20-%20kube_job_status_start_time%7Bjob_name%3D~%22records-aggregator.%2B%22%7D)&g0.tab=0&g0.display_mode=lines&g0.show_exemplars=0&g0.range_input=1w
	cmd := &cobra.Command{
		Use:   "aggregate-audit-sync-records",
		Short: "Aggregate duplicate Audit and Sync DB records into one record",
		Run: func(cmd *cobra.Command, args []string) {
			akp := shared.NewAKPUtil(clientConfig, cmd.Context(), 5*time.Minute)
			defer func() { _ = akp.Close() }()

			if aggregationWindow < 1 || aggregationWindow > 60 {
				cli.CheckErr(fmt.Errorf("aggregation-window should be in a range of [1,60]"))
			}

			aggregator := NewRecordsAggregator(akp, aggregationWindow)

			if startDateString == "" && endDateString == "" {
				cli.CheckErr(aggregator.AggregateAuditSyncPrevDay(akp.Ctx))
				return
			}

			var err error
			startDate, err = time.Parse(time.DateOnly, startDateString)
			cli.CheckErr(err)

			endDate, err = time.Parse(time.DateOnly, endDateString)
			cli.CheckErr(err)
			cli.CheckErr(aggregator.AggregateAuditSyncStartEndPeriod(akp.Ctx, startDate, endDate))
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&startDateString, "start-date", "", fmt.Sprintf("Audit Log date to start the aggregation process from (%q format, optional)", time.DateOnly))
	cmd.Flags().StringVar(&endDateString, "end-date", "", fmt.Sprintf("Audit Log date to stop the archival aggregation at, inclusive (%q format, optional)", time.DateOnly))
	cmd.Flags().IntVar(&aggregationWindow, "aggregation-window", 5, "Audit Log aggregation window in minutes.(0 < aggregation-window <= 60")
	return cmd
}
