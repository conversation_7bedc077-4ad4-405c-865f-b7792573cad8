WITH aggregated AS (
  SELECT
    COUNT(*) AS count,
    CASE
      WHEN COUNT(*) > 1 THEN gen_random_uuid()::text
      ELSE MIN(id)
    END AS id,
    MIN(timestamp) AS first_timestamp,
    MAX(timestamp) AS last_timestamp,
    organization_id,
    actor,
    details,
    action,
    object
  FROM
    audit_log
  WHERE
    timestamp >= $1 AND timestamp <= $2
    AND is_aggregated = FALSE
    AND NOT EXISTS (
        SELECT 1 FROM audit_log_archive
        WHERE audit_log_archive.start_record = audit_log.id
        OR audit_log_archive.end_record = audit_log.id
      )
  GROUP BY
    organization_id,
    actor,
    details,
    action,
    object,
    (date_trunc('hour', timestamp) + ((date_part('minute', timestamp)::integer / $3) * $3 || ' minutes')::interval)
),
updated AS (
  UPDATE audit_log
  SET is_aggregated = TRUE, last_occurred_timestamp = first_timestamp
  FROM aggregated
  WHERE audit_log.id = aggregated.id AND aggregated.count = 1
  RETURNING audit_log.*
),
inserted AS (
INSERT INTO audit_log (id, count, timestamp, last_occurred_timestamp, organization_id, actor, details, action, object, is_aggregated)
SELECT
  id,
  count,
  first_timestamp,
  last_timestamp,
  organization_id,
  actor,
  details,
  action,
  object,
  TRUE
FROM aggregated
WHERE count > 1
RETURNING *
)
DELETE FROM audit_log
WHERE timestamp >= $1
  AND timestamp <= $2
  AND is_aggregated = FALSE
  AND id NOT IN (SELECT id FROM updated UNION ALL SELECT id FROM inserted);
