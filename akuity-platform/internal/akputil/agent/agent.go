package agent

import (
	"encoding/json"
	"fmt"

	"github.com/spf13/cobra"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/akuity-platform/internal/cli"
)

func NewAgentCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use: "agent",
		Run: func(cmd *cobra.Command, args []string) {
			cmd.HelpFunc()(cmd, args)
		},
	}
	cmd.AddCommand(newSupportedVersionsCommand())
	cmd.AddCommand(newSupportedArgoVersionsCommand())
	cmd.AddCommand(newSupportedKargoVersionsCommand())
	return cmd
}

func newSupportedVersionsCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "supported-versions",
		Short: "Prints the supported agent versions by the current platform version.",
		Run: func(cmd *cobra.Command, args []string) {
			for _, version := range client.SupportedAgentVersions {
				fmt.Println(version)
			}
		},
	}
	return cmd
}

func newSupportedArgoVersionsCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "supported-argo-versions",
		Short: "Prints the supported Argo CD versions by the current platform version.",
		Run: func(cmd *cobra.Command, args []string) {
			versions, err := client.ListArgoCDVersions()
			cli.CheckErr(err)

			data, err := json.MarshalIndent(versions, "", "  ")
			cli.CheckErr(err)
			fmt.Println(string(data))
		},
	}
	return cmd
}

func newSupportedKargoVersionsCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "supported-kargo-versions",
		Short: "Prints the supported Kargo versions by the current platform version.",
		Run: func(cmd *cobra.Command, args []string) {
			versions, err := client.ListKargoVersions()
			cli.CheckErr(err)

			data, err := json.MarshalIndent(versions, "", "  ")
			cli.CheckErr(err)
			fmt.Println(string(data))
		},
	}
	return cmd
}
