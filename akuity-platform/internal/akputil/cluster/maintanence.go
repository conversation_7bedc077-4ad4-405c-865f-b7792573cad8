package cluster

import (
	"time"

	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/internal/cli"
)

var clusterCmd = &cobra.Command{
	Use:   "cluster",
	Short: "Cluster command",
	Run: func(cmd *cobra.Command, args []string) {
		cmd.HelpFunc()(cmd, args)
	},
}

func NewClusterCommand() *cobra.Command {
	clusterCmd.AddCommand(newMaintenanceModeCommand())
	clusterCmd.AddCommand(newPortForwardCommand())
	clusterCmd.AddCommand(newArgoCDApplicationControllerCmd())
	return clusterCmd
}

// newMaintenanceModeCommand - enables or disables cluster's "maintenance mode".
// When in "maintenance mode" - we are not alerted if cluster's agent tunnel is failing
// (happens to clusters with poor connectivity or under actual maintenance)
func newMaintenanceModeCommand() *cobra.Command {
	var (
		akpUtilCluster = &AKPUtilCluster{}
		disabled       bool
	)
	cmd := &cobra.Command{
		Use:   "maintenance-mode",
		Short: "Enables or disables cluster's \"maintenance mode\" to mute the \"Degraded Clusters\" alert",
		Run: func(cmd *cobra.Command, args []string) {
			cli.CheckErr(akpUtilCluster.Init(cmd.Context(), time.Minute))
			defer func() { _ = akpUtilCluster.Close() }()
			cli.CheckErr(akpUtilCluster.SetMaintenanceMode(akpUtilCluster.Ctx, !disabled))
		},
	}
	akpUtilCluster.AddFlags(cmd.Flags())
	cmd.Flags().BoolVar(&disabled, "disabled", false, "Whether maintenance mode needs to be disabled")
	return cmd
}
