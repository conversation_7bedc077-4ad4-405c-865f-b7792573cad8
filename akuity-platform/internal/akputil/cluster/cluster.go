package cluster

import (
	"context"
	"errors"
	"time"

	"github.com/spf13/pflag"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

type AKPUtilCluster struct {
	*shared.AKPUtil

	clusterName  string
	clusterID    string
	instanceID   string
	clientConfig clientcmd.ClientConfig

	cluster *models.ArgoCDCluster
	repoSet client.RepoSet
}

func (a *AKPUtilCluster) Init(ctx context.Context, contextTimeout time.Duration) error {
	a.AKPUtil = shared.NewAKPUtil(a.clientConfig, ctx, contextTimeout)
	a.repoSet = client.NewRepoSet(a.PortalDBPool.DB)

	var err error
	if a.clusterID != "" {
		if a.cluster, err = a.repoSet.ArgoCDClusters().GetByID(a.Ctx, a.clusterID); err != nil {
			return err
		}
		a.clusterName = a.cluster.Name
		a.instanceID = a.cluster.InstanceID
	} else if a.clusterName != "" {
		if a.instanceID == "" {
			return errors.New("instance-id must be provided")
		}
		if a.cluster, err = a.repoSet.ArgoCDClusters(
			models.ArgoCDClusterWhere.InstanceID.EQ(a.instanceID),
			models.ArgoCDClusterWhere.Name.EQ(a.clusterName)).One(a.Ctx); err != nil {
			return err
		}
	} else {
		return errors.New("cluster-id or cluster-name must be provided")
	}
	return nil
}

func (a *AKPUtilCluster) AddFlags(flags *pflag.FlagSet) {
	a.clientConfig = cli.AddKubectlFlagsToSet(flags)
	flags.StringVar(&a.clusterID, "cluster-id", "", "Argo CD Cluster ID")
	flags.StringVar(&a.clusterName, "cluster-name", "", "Argo CD Cluster Name")
	flags.StringVar(&a.instanceID, "instance-id", "", "Argo CD Instance ID")
}

// SetMaintenanceMode enables or disables cluster's "maintenance mode"
func (a *AKPUtilCluster) SetMaintenanceMode(ctx context.Context, enabled bool) error {
	clusterSpec, err := a.cluster.GetSpec()
	if err != nil {
		return err
	}

	clusterSpec.MaintenanceMode = enabled

	if err := a.cluster.SetSpec(*clusterSpec); err != nil {
		return err
	}

	if err := a.repoSet.ArgoCDClusters().Update(a.Ctx, a.cluster, "spec"); err != nil {
		return err
	}

	return nil
}
