package cluster

import (
	"errors"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/internal/cli"
)

func newArgoCDApplicationControllerCmd() *cobra.Command {
	var (
		akpUtilCluster = &AKPUtilCluster{}
		run            bool
		clusterNS      string
	)
	cmd := &cobra.Command{
		Use:   "debug [COMMAND]",
		Short: "Debug commands",
		Run: func(cmd *cobra.Command, args []string) {
			var debugCommand string
			if len(args) == 0 {
				cmd.HelpFunc()(cmd, args)
				return
			}
			debugCommand = args[0]
			cli.CheckErr(akpUtilCluster.Init(cmd.Context(), time.Hour))
			defer func() { _ = akpUtilCluster.Close() }()

			status, err := akpUtilCluster.cluster.GetStatus()
			cli.CheckErr(err)

			if status.AgentState == nil || status.AgentState.Status == nil {
				cli.CheckErr(errors.New("no agent status reported"))
			}
			podName := status.AgentState.Status.SyncerPodName
			var command string

			switch debugCommand {
			case "exec":
				afterDash := []string{"env KUBECONFIG=/home/<USER>/config", "bash"}

				if cmd.ArgsLenAtDash() > -1 {
					afterDash = args[cmd.ArgsLenAtDash():]
				}

				command = fmt.Sprintf("kubectl exec -it %s -c argocd-application-controller -n %s -- %s", podName, clusterNS, strings.Join(afterDash, " "))
			case "copy-tools":
				command = fmt.Sprintf(`[ -f "/tmp/kubectl" ] || curl -L "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl" --output /tmp/kubectl && \
cat /tmp/kubectl | base64 | kubectl exec -i %s -c argocd-application-controller -n %s -- bash -c "cat - | base64 --decode > /home/<USER>/kubectl && chmod +x /home/<USER>/kubectl"`, podName, clusterNS)
			case "cluster-config":
				command = fmt.Sprintf("kubectl exec -it %s -c argocd-application-controller -n %s -- argocd admin cluster kubeconfig https://kubernetes.default.svc config", podName, clusterNS)
			case "controller-pod-name":
				fmt.Println(podName)
				return
			default:
				cli.CheckErr(errors.New("unknown debug command. Available commands: exec, copy-tools"))
			}
			_, _ = os.Stdout.WriteString("export KUBECONFIG=/tmp/akpconfig\n")
			_, _ = os.Stdout.WriteString(command + "\n")
			if run {
				execCMD := exec.Command("bash", "-c", command)
				execCMD.Env = append(os.Environ(), fmt.Sprintf("KUBECONFIG=%s", "/tmp/akpconfig"))
				execCMD.Stdin = os.Stdin
				execCMD.Stdout = os.Stdout
				execCMD.Stderr = os.Stderr
				cli.CheckErr(execCMD.Run())
			}
		},
	}
	akpUtilCluster.AddFlags(cmd.Flags())
	cmd.Flags().BoolVar(&run, "run", true, "Run the command")
	cmd.Flags().StringVar(&clusterNS, "cluster-namespace", "akuity", "Managed cluster Namespace")
	return cmd
}
