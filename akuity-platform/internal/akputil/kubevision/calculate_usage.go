package kubevision

import (
	"time"

	"github.com/spf13/cobra"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/cli"

	_ "embed"
)

//go:embed calculate_usage.sql
var calculateKubeVisionUsageQuery string

func NewCalculateKubeVisionUsageCommand() *cobra.Command {
	var clientConfig clientcmd.ClientConfig
	cmd := &cobra.Command{
		Use:   "calculate-usage",
		Short: "Calculate Akuity Intelligence usage for organizations that have Akuity Intelligence enabled",
		Run: func(cmd *cobra.Command, args []string) {
			akp := shared.NewAKPUtil(clientConfig, cmd.Context(), time.Minute)
			defer func() { _ = akp.Close() }()
			cli.CheckErr(CalculateKubeVisionUsage(akp))
		},
	}
	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	return cmd
}

func CalculateKubeVisionUsage(akp *shared.AKPUtil) error {
	result, err := akp.PortalDBPool.DB.ExecContext(akp.Ctx, calculateKubeVisionUsageQuery)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	akp.Log.Info("Rows affected", "rows", rowsAffected)
	return nil
}
