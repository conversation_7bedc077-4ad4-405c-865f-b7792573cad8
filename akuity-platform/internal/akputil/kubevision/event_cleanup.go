package kubevision

import (
	"context"
	"time"

	"github.com/spf13/cobra"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/cli"
)

var cleanupQuery = `
DELETE FROM argo_cd_cluster_k8s_event
WHERE id IN (
    SELECT id 
    FROM argo_cd_cluster_k8s_event
    WHERE timestamp < NOW() - ($1 || ' days')::INTERVAL
    LIMIT $2
);`

func NewEventCleanupCommand() *cobra.Command {
	var (
		clientConfig    clientcmd.ClientConfig
		retentionPeriod uint16
		maxDeletions    uint16
	)
	cmd := &cobra.Command{
		Use:   "event-cleanup",
		Short: "Delete events older than the configured retention period",
		Run: func(cmd *cobra.Command, args []string) {
			akp := shared.NewAKPUtil(clientConfig, cmd.Context(), time.Minute)
			defer func() { _ = akp.Close() }()
			cli.CheckErr(CleanupEvents(cmd.Context(), akp, retentionPeriod, maxDeletions))
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().Uint16Var(&retentionPeriod, "retention-period", 14, "Retention period (in days) - events older than this will be deleted")
	cmd.Flags().Uint16Var(&maxDeletions, "max-deletions", 50000, "Maximum number of deletions to perform")
	return cmd
}

func CleanupEvents(ctx context.Context, akp *shared.AKPUtil, retentionPeriod, maxDeletions uint16) error {
	var totalRowsAffected int64
	for {
		newCtx, cancel := context.WithTimeout(ctx, time.Minute*3)
		result, err := akp.PortalDBPool.DB.ExecContext(newCtx, cleanupQuery, retentionPeriod, maxDeletions)
		cancel()
		if err != nil {
			akp.Log.Info("Error cleaning up events", "error", err)
			return err
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			akp.Log.Info("Error getting rows affected", "error", err)
			return err
		}

		totalRowsAffected += rowsAffected
		akp.Log.Info("Batch deletion complete", "rows", rowsAffected, "total", totalRowsAffected)

		if rowsAffected < int64(maxDeletions) {
			break
		}
	}
	akp.Log.Info("Total Rows affected", "rows", totalRowsAffected)
	return nil
}
