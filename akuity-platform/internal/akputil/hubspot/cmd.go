package hubspot

import (
	"fmt"
	"time"

	"github.com/spf13/cobra"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/utils/consts"
)

var hubspotCmd = &cobra.Command{
	Use:   "hubspot",
	Short: "HubSpot command",
	Run: func(cmd *cobra.Command, args []string) {
		cmd.HelpFunc()(cmd, args)
	},
}

func NewHubspotCommand() *cobra.Command {
	hubspotCmd.AddCommand(NewCreateActiveFreeTrialUserTaskCommand())
	return hubspotCmd
}

func NewCreateActiveFreeTrialUserTaskCommand() *cobra.Command {
	var (
		clientConfig       clientcmd.ClientConfig
		configMapName      string
		internalUserEmails []string
	)

	// akuity-platform-deploy/env/prod/aws/akuity-platform/hubspot-cronjob/hubspot-cronjob.yaml
	// http://prometheus-prod.monitoring.svc/graph?g0.expr=sum%20by%20(job_name)%20(kube_job_status_completion_time%7Bjob_name%3D~%22hubspot-cronjob.%2B%22%7D%20-%20kube_job_status_start_time%7Bjob_name%3D~%22hubspot-cronjob.%2B%22%7D)&g0.tab=0&g0.display_mode=lines&g0.show_exemplars=0&g0.range_input=1w
	cmd := &cobra.Command{
		Use:   "create-free-trial-active-users-task",
		Short: "Creates a task in hubspot of free trial active users on Akuity Platform",
		Run: func(cmd *cobra.Command, args []string) {
			akpHubspot, err := NewAKPUtilHubspot(clientConfig, internalUserEmails, cmd.Context(), time.Minute)
			cli.CheckErr(err)
			defer func() { _ = akpHubspot.Close() }()

			persist := NewConfigMapPersistence(configMapName,
				// if the command is running first time
				// or in rare case of an empty file
				// the default timestamp in query would be (now - <duration-given-below>)
				consts.Week,
				akpHubspot.K8sClient,
			)

			timestampOfLastRun, err := persist.ReadTimestamp(akpHubspot.Ctx)
			cli.CheckErr(err)

			akpHubspot.Log.Info(fmt.Sprintf("last ran timestamp in UTC: %s", timestampOfLastRun.UTC().String()))

			latestActiveUser, err := akpHubspot.CreateFreeTrialActiveUsersTasks(timestampOfLastRun)
			cli.CheckErr(err)

			err = persist.WriteTimestamp(akpHubspot.Ctx, latestActiveUser)
			cli.CheckErr(err)
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&configMapName, "config-map", "", "Name of the ConfigMap to store")
	cmd.Flags().StringArrayVar(&internalUserEmails, "internal-user-emails", []string{"<EMAIL>"}, "List of internal user emails to exclude from the list of free trial active users")
	cli.CheckErr(cmd.MarkFlagRequired("config-map"))
	return cmd
}
