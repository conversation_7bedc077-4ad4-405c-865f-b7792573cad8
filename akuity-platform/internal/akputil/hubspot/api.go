package hubspot

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	httputil "github.com/akuityio/akuity-platform/pkg/utils/http"
)

const (
	batchReadContacts = "/crm/v3/objects/contacts/batch/read"
	batchCreateTasks  = "/crm/v3/objects/tasks/batch/create"
)

type HubspotConfig struct {
	accessToken string
	baseURL     string
}

type HubspotAPI interface {
	GetContactsByEmail([]string) ([]HubspotContactPayload, error)
	CreateTasks([]HubspotTask) error
}

func NewHubspot(accessToken string) HubspotAPI {
	return &HubspotConfig{
		accessToken: accessToken,
		baseURL:     "https://api.hubapi.com",
	}
}

func (h *HubspotConfig) SetBearer(req *http.Request, token string) {
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
}

type HubspotContactProperty struct {
	Email     string `json:"email"`
	FirstName string `json:"firstname"`
	LastName  string `json:"lastname"`
}

type HubspotContact struct {
	Properties HubspotContactProperty `json:"properties"`
}

type HubspotContactPayload struct {
	Id               string
	Email            string
	AdditionalEmails map[string]bool
}

type AddContactsToListResponse struct {
	Updated     []int `json:"updated"`
	Discarded   []int `json:"discarded"`
	InvalidVids []int `json:"invalidVids"`
}

type HubspotContactId = string

type Filter struct {
	PropertyName string `json:"propertyName"`
	Operator     string `json:"operator"`
	Value        string `json:"value"`
}

type FilterGroup struct {
	Filters []Filter `json:"filters"`
}

func (h *HubspotConfig) GetContactsByEmail(emails []string) ([]HubspotContactPayload, error) {
	if len(emails) < 1 {
		return []HubspotContactPayload{}, nil
	}

	allContacts := []HubspotContactPayload{}

	endpoint := fmt.Sprintf("%s%s", h.baseURL, batchReadContacts)

	type Input struct {
		Id string `json:"id"`
	}

	type body struct {
		IdProperty            string   `json:"idProperty"`
		Inputs                []Input  `json:"inputs"`
		PropertiesWithHistory []string `json:"propertiesWithHistory"`
	}

	reqBody := &body{
		IdProperty:            "email",
		PropertiesWithHistory: []string{"hs_additional_emails"},
	}

	inputs := []Input{}

	for _, email := range emails {
		inputs = append(inputs, Input{Id: email})
	}

	reqBody.Inputs = inputs

	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", endpoint, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, err
	}

	req.Header.Set(httputil.HeaderContentType, httputil.MIMEApplicationJSON)
	h.SetBearer(req, h.accessToken)

	client := &http.Client{}
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}

	defer func(Body io.ReadCloser) { _ = Body.Close() }(res.Body)

	resBody, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}

	type HsAdditionalEmail struct {
		Value string `json:"value"`
	}

	type PropertiesWithHistory struct {
		HsAdditionalEmails []HsAdditionalEmail `json:"hs_additional_emails"`
	}

	type Result struct {
		HubspotContact
		Id                    string `json:"id"`
		PropertiesWithHistory `json:"propertiesWithHistory"`
	}

	type Response struct {
		Results []Result `json:"results"`
	}

	respBodyResult := &Response{}

	if err := json.Unmarshal(resBody, respBodyResult); err != nil {
		return nil, err
	}

	for _, respResult := range respBodyResult.Results {
		additionalEmails := map[string]bool{}

		for _, additionalEmail := range respResult.HsAdditionalEmails {
			additionalEmails[additionalEmail.Value] = true
		}

		allContacts = append(allContacts, HubspotContactPayload{
			Id:               respResult.Id,
			Email:            respResult.Properties.Email,
			AdditionalEmails: additionalEmails,
		})
	}

	return allContacts, nil
}

type HubspotTaskProperties struct {
	DueDate int64  `json:"hs_timestamp"`
	Subject string `json:"hs_task_subject,omitempty"`
}

type HubspotTaskAssociationTo struct {
	Id string `json:"id"`
}

type HubspotTaskAssociationType struct {
	Category string `json:"associationCategory"`
	TypeId   int32  `json:"associationTypeId"`
}

type HubspotTaskAssociation struct {
	To               HubspotTaskAssociationTo     `json:"to"`
	AssociationTypes []HubspotTaskAssociationType `json:"types"`
}

type HubspotTask struct {
	Properties   HubspotTaskProperties    `json:"properties"`
	Associations []HubspotTaskAssociation `json:"associations"`
}

func (h *HubspotConfig) CreateTasks(tasks []HubspotTask) error {
	endpoint := fmt.Sprintf("%s%s", h.baseURL, batchCreateTasks)

	if len(tasks) < 1 {
		return nil
	}

	type body struct {
		Inputs []HubspotTask `json:"inputs"`
	}

	reqBody := &body{
		Inputs: tasks,
	}

	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		return err
	}

	req, err := http.NewRequest("POST", endpoint, bytes.NewBuffer(jsonBody))
	if err != nil {
		return err
	}

	req.Header.Set(httputil.HeaderContentType, httputil.MIMEApplicationJSON)
	h.SetBearer(req, h.accessToken)

	_, err = (&http.Client{}).Do(req)

	return err
}
