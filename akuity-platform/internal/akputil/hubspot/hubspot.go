package hubspot

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/kelseyhightower/envconfig"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/utils/consts"
	"github.com/akuityio/akuity-platform/models/models"
)

const freeTrialActiveUsersQuery = `
		SELECT
			akuity_user.email,
			organization.name,
			audit_log.timestamp
		FROM
			(
				SELECT
					organization_id,
					max(timestamp) as timestamp
				FROM
					audit_log
				WHERE
					action = 'created'
				AND
					object ->> 'type' = 'argocd_application'
				AND
					timestamp > $1
				GROUP BY
					organization_id
			) AS audit_log
		INNER JOIN
			organization
		ON
			organization.id = audit_log.organization_id
		AND
			org_status ->> 'trial' = 'true'
		INNER JOIN
			organization_user
		ON
			audit_log.organization_id = organization_user.organization_id
		AND
			organization_user.organization_role = 'owner'
		INNER JOIN
			akuity_user
		ON
			akuity_user.id = organization_user.user_id
		ORDER BY
			audit_log.timestamp
		DESC
	`

type HubspotEnvConfig struct {
	HubspotAccessToken string `envconfig:"HUBSPOT_ACCESS_TOKEN"`
	DBDataKey          config.DBDataKeyConfig
}

type AKPUtilHubspot struct {
	*shared.AKPUtil
	Hubspot            HubspotAPI
	internalUserEmails map[string]bool
	cfg                HubspotEnvConfig
}

func NewAKPUtilHubspot(clientConfig clientcmd.ClientConfig, internalUserEmails []string, ctx context.Context, contextTimeout time.Duration) (*AKPUtilHubspot, error) {
	hubspotCfg := HubspotEnvConfig{}
	if err := envconfig.Process("", &hubspotCfg); err != nil {
		return nil, err
	}
	akpHubspot := AKPUtilHubspot{
		AKPUtil:            shared.NewAKPUtil(clientConfig, ctx, contextTimeout),
		Hubspot:            NewHubspot(hubspotCfg.HubspotAccessToken),
		internalUserEmails: map[string]bool{},
		cfg:                hubspotCfg,
	}

	for _, email := range internalUserEmails {
		akpHubspot.internalUserEmails[email] = true
	}
	return &akpHubspot, nil
}

type FreeTrialActiveUser struct {
	Email                string    `boil:"email"`
	OrganizationName     string    `boil:"name"`
	ApplicationCreatedAt time.Time `boil:"timestamp"`
}

// provides a list of Free Trial Active Users
// an Active User is defined as "created an Argo CD application"
// since we cannot map the Argo CD user to Akuity user, we define the organization owner in which Application is created as a relevant User
// list might include all owner of the same organization
func (a *AKPUtilHubspot) ListFreeTrialActiveUsers(after time.Time) ([]FreeTrialActiveUser, error) {
	var activeUsers []FreeTrialActiveUser

	if err := models.NewQuery(qm.SQL(freeTrialActiveUsersQuery, after)).Bind(
		a.Ctx, a.PortalDBPool.DB, &activeUsers); err != nil {
		return nil, err
	}

	return activeUsers, nil
}

func (a *AKPUtilHubspot) CreateFreeTrialActiveUsersTasks(lastFetchTimestamp time.Time) (time.Time, error) {
	activeUsers, err := a.ListFreeTrialActiveUsers(lastFetchTimestamp)
	if err != nil {
		return lastFetchTimestamp, err
	}

	var nonAkuitianActiveUsers []FreeTrialActiveUser
	for _, activeUser := range activeUsers {
		if !strings.Contains(activeUser.Email, "@akuity.io") && !a.internalUserEmails[activeUser.Email] {
			nonAkuitianActiveUsers = append(nonAkuitianActiveUsers, activeUser)
		}
	}

	activeUsers = nonAkuitianActiveUsers
	latestFetchTimestamp := lastFetchTimestamp

	if len(activeUsers) > 0 {
		latestFetchTimestamp = activeUsers[0].ApplicationCreatedAt
		a.Log.Info(fmt.Sprintf("latest active user was at %s", latestFetchTimestamp.String()))
	}

	activeUserEmails := []string{}

	for _, activeUser := range activeUsers {
		activeUserEmails = append(activeUserEmails, activeUser.Email)
	}

	a.Log.Info(fmt.Sprintf("%d active users found", len(activeUsers)))

	var ids []string
	contacts, err := a.Hubspot.GetContactsByEmail(activeUserEmails)
	if err != nil {
		return lastFetchTimestamp, err
	}

	for _, contact := range contacts {
		ids = append(ids, contact.Id)
	}

	a.Log.Info(fmt.Sprintf("%d contact ids found", len(ids)))

	if len(ids) < 1 {
		return lastFetchTimestamp, nil
	}

	if len(ids) != len(activeUserEmails) {
		a.Log.Error(fmt.Errorf("expected %d contact ids but found %d", len(activeUserEmails), len(ids)), "missing contact in hubspot")
	}

	tasks := []HubspotTask{}

	tomorrow := time.Now().Add(consts.Day).UnixMilli()

	associationCommonType := HubspotTaskAssociationType{
		Category: "HUBSPOT_DEFINED",
		TypeId:   204,
	}

	for _, id := range ids {
		association := HubspotTaskAssociation{
			To: HubspotTaskAssociationTo{
				Id: id,
			},
			AssociationTypes: []HubspotTaskAssociationType{
				associationCommonType,
			},
		}
		task := HubspotTask{
			Properties: HubspotTaskProperties{
				DueDate: tomorrow,
				Subject: "Organization Created an Application",
			},
			Associations: []HubspotTaskAssociation{
				association,
			},
		}

		tasks = append(tasks, task)
	}

	if err := a.Hubspot.CreateTasks(tasks); err != nil {
		return lastFetchTimestamp, err
	}

	a.Log.Info("successfully created tasks")

	return latestFetchTimestamp, nil
}
