package instance

import (
	"context"
	"fmt"
	"time"

	"github.com/spf13/cobra"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/akuity-platform/controllers/platform/integration"
	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/util/status"
)

func NewRefreshWebhookCerts() *cobra.Command {
	var (
		clientConfig clientcmd.ClientConfig
		minCertTTL   time.Duration
		instances    []string
		all          bool
	)
	cmd := &cobra.Command{
		Use:   "refresh-certs",
		Short: "Refreshes webhook certs for given instances",
		Run: func(cmd *cobra.Command, args []string) {
			if len(instances) == 0 && !all {
				cli.CheckErr(fmt.Errorf("no instances specified, either specify instance ids or use --all to target all instances"))
				return
			}
			cli.CheckErr(refreshCerts(cmd.Context(), minCertTTL, instances, all, clientConfig, time.Hour))
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().DurationVar(&minCertTTL, "min-cert-ttl", time.Hour*24*365*2, "TTL to expiry for a cert to be refreshed, default 2 years.")
	cmd.Flags().BoolVar(&all, "all", false, "Checks all available instances")
	cmd.Flags().StringSliceVar(&instances, "instances", []string{}, "Instance ids to refresh certs for")
	return cmd
}

func refreshCerts(ctx context.Context, minCertTTL time.Duration,
	instances []string,
	all bool, clientConfig clientcmd.ClientConfig, contextTimeout time.Duration,
) error {
	akpUtil := shared.NewAKPUtil(clientConfig, ctx, contextTimeout)
	defer func() { _ = akpUtil.Close() }()

	repoSet := client.NewRepoSet(akpUtil.PortalDBPool.DB)

	if all {
		list, err := repoSet.ArgoCDInstances().ListAll(akpUtil.Ctx, "id")
		if err != nil {
			return err
		}
		instances = []string{}
		for _, instance := range list {
			instances = append(instances, instance.ID)
		}
	}

	for _, id := range instances {
		cfg, err := repoSet.ArgoCDInstanceConfigs().GetByID(akpUtil.Ctx, id)
		if err != nil {
			return err
		}
		privSpec, err := cfg.GetPrivateSpec()
		if err != nil {
			return err
		}
		// cert not set, nothing to do
		if privSpec.WebhookKey == "" || privSpec.WebhookCert == "" {
			akpUtil.Log.Info("webhook cert not set, skipping", "instance", id)
			continue
		}
		cert, err := integration.ParseCert(privSpec.WebhookCert)
		if err != nil {
			return err
		}
		// if webhook cert is expiring in 5 days, renew it
		if cert.NotAfter.Sub(cert.NotBefore) > minCertTTL {
			akpUtil.Log.Info("webhook cert not expiring, skipping", "instance", id)
			continue
		}
		webhookKey, webhookCert, err := integration.GetWebhookCert(id, false)
		if err != nil {
			return err
		}
		privSpec.WebhookKey = webhookKey
		privSpec.WebhookCert = webhookCert
		if err := cfg.SetPrivateSpec(privSpec); err != nil {
			return err
		}
		akpUtil.Log.Info("refreshing webhook cert", "instance", id)
		if err = repoSet.ArgoCDInstanceConfigs().Update(akpUtil.Ctx, cfg, "private_spec"); err != nil {
			return err
		}
		time.Sleep(time.Second * 5)
		tempCTX, cancel := context.WithTimeout(ctx, time.Minute*5)
		for {
			akpUtil.Log.Info("waiting for instance to be healthy", "instance", id)
			inst, err := repoSet.ArgoCDInstances().GetByID(akpUtil.Ctx, id)
			if err != nil {
				cancel()
				return err
			}
			stat, err := inst.GetReconciliationStatus(time.Minute * 10)
			if err != nil {
				cancel()
				return err
			}
			if stat.Code == status.ReconciliationStatusCodeSuccessful {
				break
				// if status healthy break
			}
			select {
			case <-tempCTX.Done():
				cancel()
				return fmt.Errorf("instance %s not healthy after 5 minutes", id)
			case <-time.After(time.Second * 10):
			}
		}
		cancel()

		akpUtil.Log.Info("instance healthy, restarting k3s deployment", "instance", id)
		// restart k3s and k3s proxy deployment, replicates kubectl rollout restart deployment
		deploymentsClient := akpUtil.K8sClient.AppsV1().Deployments(fmt.Sprintf("argocd-%s", id))
		data := fmt.Sprintf(`{"spec": {"template": {"metadata": {"annotations": {"kubectl.kubernetes.io/restartedAt": "%v"}}}}}`, time.Now().Format(time.RFC3339))
		if _, err := deploymentsClient.Patch(akpUtil.Ctx, "k3s", types.StrategicMergePatchType, []byte(data), v1.PatchOptions{FieldManager: "kubectl-rollout"}); err != nil {
			return err
		}
		if _, err := deploymentsClient.Patch(akpUtil.Ctx, "k3s-proxy", types.StrategicMergePatchType, []byte(data), v1.PatchOptions{FieldManager: "kubectl-rollout"}); err != nil {
			return err
		}
		akpUtil.Log.Info("deployment restarted, finished certificate refresh", "instance", id)
	}

	return nil
}
