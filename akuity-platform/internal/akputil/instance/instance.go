package instance

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/utils/ptr"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/agent/pkg/kube"
	"github.com/akuityio/akuity-platform/controllers/platform/integration/autoscaler"
	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	akpClient "github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

const (
	K3sPort = 6443
)

type AKPUtilInstance struct {
	*shared.AKPUtil
	instanceID string
}

func NewAKPUtilInstance(instanceID string, clientConfig clientcmd.ClientConfig, ctx context.Context, contextTimeout time.Duration) (*AKPUtilInstance, error) {
	akpUtil := shared.NewAKPUtil(clientConfig, ctx, contextTimeout)
	akpUtil.Log = ptr.To(akpUtil.Log.WithValues("instance_id", instanceID))
	return &AKPUtilInstance{
		AKPUtil:    akpUtil,
		instanceID: instanceID,
	}, nil
}

const (

	// BumpArgoInstanceGeneration is a query to bump generation of an Argo CD instance (forcing apply + pruning of manifests)
	BumpArgoInstanceGeneration = `
    UPDATE
      argo_cd_instance
    SET
      generation = generation + 1
    WHERE
      id = $1;
  `
	// BumpArgoClustersGeneration is a query to bump generation of all clusters of an Argo CD instance (forcing apply + pruning of manifests)
	BumpArgoClustersGeneration = `
    UPDATE
      argo_cd_cluster
    SET
      generation = generation + 1
    WHERE
      instance_id = $1;
  `

	// DecrementArgoInstanceObservedGeneration is a query to decrement observed generation of an Argo CD instance (forcing apply of manifests but no pruning)
	DecrementArgoInstanceObservedGeneration = `
    UPDATE
      argo_cd_instance
    SET
      status_observed_generation = generation - 1
    WHERE
      id = $1;
  `
	// DecrementArgoClustersObservedGeneration is a query to decrement status observed generation of all clusters of an Argo CD instance (forcing apply of manifests but no pruning)
	DecrementArgoClustersObservedGeneration = `
    UPDATE
      argo_cd_cluster
    SET
      status_observed_generation = generation - 1
    WHERE
      instance_id = $1;
  `
	// BumpKargoInstanceGeneration is a query to bump generation of an Kargo instance (forcing apply + pruning of manifests)
	BumpKargoInstanceGeneration = `
    UPDATE
      kargo_instance
    SET
      generation = generation + 1
    WHERE
      id = $1;
  `
	// BumpKargoAgentsGeneration is a query to bump generation of all agents of a Kargo instance (forcing apply + pruning of manifests)
	BumpKargoAgentsGeneration = `
    UPDATE
      kargo_agent
    SET
      generation = generation + 1
    WHERE
      instance_id = $1;
  `

	// DecrementKargoInstanceObservedGeneration is a query to decrement observed generation of a Kargo instance (forcing apply of manifests but no pruning)
	DecrementKargoInstanceObservedGeneration = `
    UPDATE
      kargo_instance
    SET
      status_observed_generation = generation - 1
    WHERE
      id = $1;
  `
	// DecrementKargoAgentsObservedGeneration is a query to decrement status observed generation of all agents of a Kargo instance (forcing apply of manifests but no pruning)
	DecrementKargoAgentsObservedGeneration = `
    UPDATE
      kargo_agent
    SET
      status_observed_generation = generation - 1
    WHERE
      instance_id = $1;
  `
)

func (a *AKPUtilInstance) UpdateGeneration(updateStatement, operationName string) error {
	res, err := models.NewQuery(qm.SQL(updateStatement, a.instanceID)).ExecContext(a.Ctx, a.PortalDBPool.DB)
	if err != nil {
		return err
	}
	rowsAffected, err := res.RowsAffected()
	if err != nil {
		return err
	}

	affectedRowsMessage := fmt.Sprintf("%s Instance '%s' %s operation affected %d row%s",
		common.If(strings.Contains(operationName, "Kargo"), "Kargo", "Argo"),
		a.instanceID, operationName, rowsAffected,
		common.If(rowsAffected == 1, "", "s"))

	isInstanceUpdate := strings.Contains(updateStatement, "argo_cd_instance") ||
		strings.Contains(updateStatement, "kargo_instance")
	if isInstanceUpdate && rowsAffected != 1 {
		return errors.New(affectedRowsMessage + " instead of 1")
	}

	a.Log.Info(affectedRowsMessage)
	return nil
}

func (a *AKPUtilInstance) BumpArgoInstanceGeneration() error {
	return a.UpdateGeneration(BumpArgoInstanceGeneration, "BumpArgoInstanceGeneration")
}

func (a *AKPUtilInstance) BumpArgoClustersGeneration() error {
	return a.UpdateGeneration(BumpArgoClustersGeneration, "BumpArgoClustersGeneration")
}

func (a *AKPUtilInstance) BumpKargoInstanceGeneration() error {
	return a.UpdateGeneration(BumpKargoInstanceGeneration, "BumpKargoInstanceGeneration")
}

func (a *AKPUtilInstance) BumpKargoAgentsGeneration() error {
	return a.UpdateGeneration(BumpKargoAgentsGeneration, "BumpKargoAgentsGeneration")
}

func (a *AKPUtilInstance) DecrementArgoInstanceObservedGeneration() error {
	return a.UpdateGeneration(DecrementArgoInstanceObservedGeneration, "DecrementArgoInstanceObservedGeneration")
}

func (a *AKPUtilInstance) DecrementArgoClustersObservedGeneration() error {
	return a.UpdateGeneration(DecrementArgoClustersObservedGeneration, "DecrementArgoClustersObservedGeneration")
}

func (a *AKPUtilInstance) DecrementKargoInstanceObservedGeneration() error {
	return a.UpdateGeneration(DecrementKargoInstanceObservedGeneration, "DecrementKargoInstanceObservedGeneration")
}

func (a *AKPUtilInstance) DecrementKargoAgentsObservedGeneration() error {
	return a.UpdateGeneration(DecrementKargoAgentsObservedGeneration, "DecrementKargoAgentsObservedGeneration")
}

// CPlaneRestConfig returns rest config of an instance's k3s control-plane accessed via port-forwarding
func (a *AKPUtilInstance) CPlaneRestConfig() (*rest.Config, error) {
	tnt, err := client.NewArgoCDTenant(a.RestConfig, *a.Log, a.instanceID)
	if err != nil {
		return nil, err
	}
	secret, err := a.K8sClient.CoreV1().Secrets(tnt.HostNamespace).Get(a.Ctx, common.K3sKubeConfigSecretName, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}
	kubeConfigBytes := secret.Data[common.K3sKubeConfigSecretKey]
	if len(kubeConfigBytes) == 0 {
		return nil, fmt.Errorf("key '%s' missing in secret", common.K3sKubeConfigSecretKey)
	}
	cplaneRestConfig, err := clientcmd.RESTConfigFromKubeConfig(kubeConfigBytes)
	if err != nil {
		return nil, err
	}
	kubectl, err := kube.NewKubectl(a.RestConfig)
	if err != nil {
		return nil, err
	}
	port, err := kubectl.PortForward(a.Ctx, K3sPort, common.ArgoCDHostNamespace(a.instanceID), nil, "app.kubernetes.io/name=k3s")
	if err != nil {
		return nil, err
	}
	if !strings.Contains(cplaneRestConfig.Host, a.instanceID) {
		// Sanity check to make sure we are connecting to the right cluster
		return nil, fmt.Errorf("hostname %q does not match instance ID %q. Something is wrong", cplaneRestConfig.Host, a.instanceID)
	}
	cplaneRestConfig.Host = fmt.Sprintf("localhost:%d", port)
	return cplaneRestConfig, nil
}

func (a *AKPUtilInstance) AddInstanceOverride(kargo bool, instanceOverrideJSON []byte) error {
	repoSet := akpClient.NewRepoSet(a.PortalDBPool.DB)

	if !kargo {
		instanceConfig, err := repoSet.ArgoCDInstanceConfigs().GetByID(a.Ctx, a.instanceID)
		if err != nil {
			return err
		}

		internalSpec, err := instanceConfig.GetInternalSpec()
		if err != nil {
			return err
		}

		instanceOverrideMap, err := autoscaler.ValidateInstanceOverride(instanceOverrideJSON)
		if err != nil {
			return err
		}

		internalSpec.Values = instanceOverrideMap

		if err := instanceConfig.SetInternalSpec(internalSpec); err != nil {
			return err
		}

		if err := repoSet.ArgoCDInstanceConfigs().Update(a.Ctx, instanceConfig, models.ArgoCDInstanceConfigColumns.InternalSpec); err != nil {
			return err
		}
	} else {
		instanceConfig, err := repoSet.KargoInstanceConfigs().GetByID(a.Ctx, a.instanceID)
		if err != nil {
			return err
		}

		internalSpec, err := instanceConfig.GetInternalSpec()
		if err != nil {
			return err
		}

		instanceOverrideMap, err := autoscaler.ValidateKargoInstanceOverride(instanceOverrideJSON)
		if err != nil {
			return err
		}

		internalSpec.Values = instanceOverrideMap

		if err := instanceConfig.SetInternalSpec(internalSpec); err != nil {
			return err
		}

		if err := repoSet.KargoInstanceConfigs().Update(a.Ctx, instanceConfig, models.KargoInstanceConfigColumns.InternalSpec); err != nil {
			return err
		}
	}
	return nil
}

func (a *AKPUtilInstance) RemoveInstanceOverride(kargo bool) error {
	repoSet := akpClient.NewRepoSet(a.PortalDBPool.DB)

	if !kargo {
		instanceConfig, err := repoSet.ArgoCDInstanceConfigs().GetByID(a.Ctx, a.instanceID)
		if err != nil {
			return err
		}

		internalSpec, err := instanceConfig.GetInternalSpec()
		if err != nil {
			return err
		}

		internalSpec.Values = nil

		if err := instanceConfig.SetInternalSpec(internalSpec); err != nil {
			return err
		}

		if err := repoSet.ArgoCDInstanceConfigs().Update(a.Ctx, instanceConfig, models.ArgoCDInstanceConfigColumns.InternalSpec); err != nil {
			return err
		}
	} else {
		instanceConfig, err := repoSet.KargoInstanceConfigs().GetByID(a.Ctx, a.instanceID)
		if err != nil {
			return err
		}

		internalSpec, err := instanceConfig.GetInternalSpec()
		if err != nil {
			return err
		}

		internalSpec.Values = nil

		if err := instanceConfig.SetInternalSpec(internalSpec); err != nil {
			return err
		}

		if err := repoSet.KargoInstanceConfigs().Update(a.Ctx, instanceConfig, models.KargoInstanceConfigColumns.InternalSpec); err != nil {
			return err
		}
	}
	return nil
}
