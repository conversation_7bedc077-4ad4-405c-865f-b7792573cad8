package instance

import (
	"fmt"

	"github.com/Masterminds/semver"
	"github.com/volatiletech/null/v8"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

type AKPUtilEnsureVersion struct {
	*shared.AKPUtil
	includeAgents bool
}

// EnsureArgoAgentServerVersion - ensures Argo CD Instance specified is using the agent-server version required (at least)
func (a *AKPUtilEnsureVersion) EnsureArgoAgentServerVersion(instanceID, agentServerVersion string, force bool) (bool, error) {
	repoSet := client.NewRepoSet(a.PortalDBPool.DB)

	instance, err := repoSet.ArgoCDInstances().GetByID(a.Ctx, instanceID)
	if err != nil {
		return false, err
	}

	status, err := instance.GetStatus()
	if err != nil {
		return false, err
	}

	agentVersion, err := semver.NewVersion(agentServerVersion)
	if err != nil {
		return false, err
	}

	if status.Info.AkuityServerVersion != "" {
		akuityServerVersion, err := semver.NewVersion(status.Info.AkuityServerVersion)
		if err == nil && akuityServerVersion.Compare(agentVersion) >= 0 && !force {
			a.Log.Info(fmt.Sprintf("Argo Instance '%s' agent-server version is up-to-date: '%s' >= '%s'",
				instanceID, status.Info.AkuityServerVersion, agentServerVersion))
			return false, nil
		}
	}

	status.Info.RequestedAkuityServerVersion = agentServerVersion
	if err := instance.SetStatus(status); err != nil {
		return false, err
	}

	instance.StatusObservedGeneration = null.IntFrom(instance.StatusObservedGeneration.Int - 1)
	if err := repoSet.ArgoCDInstances().Update(
		a.Ctx, instance, models.ArgoCDInstanceColumns.StatusObservedGeneration, models.ArgoCDInstanceColumns.StatusInfo); err != nil {
		return false, err
	}

	if a.includeAgents {
		if err := (&AKPUtilInstance{AKPUtil: a.AKPUtil, instanceID: instanceID}).DecrementArgoClustersObservedGeneration(); err != nil {
			return false, err
		}
	}

	a.Log.Info(fmt.Sprintf("Upgraded Argo Instance '%s' agent-server from '%s' to '%s'",
		instanceID, status.Info.AkuityServerVersion, agentServerVersion))

	return true, nil
}

// EnsureKargoAgentServerVersion - ensures Kargo Instance specified is using the agent-server version required (at least)
func (a *AKPUtilEnsureVersion) EnsureKargoAgentServerVersion(instanceID, agentServerVersion string, force bool) (bool, error) {
	repoSet := client.NewRepoSet(a.PortalDBPool.DB)

	instance, err := repoSet.KargoInstances().GetByID(a.Ctx, instanceID)
	if err != nil {
		return false, err
	}

	status, err := instance.GetStatus()
	if err != nil {
		return false, err
	}

	agentVersion, err := semver.NewVersion(agentServerVersion)
	if err != nil {
		return false, err
	}

	if status.Info.AkuityServerVersion != "" {
		akuityServerVersion, err := semver.NewVersion(status.Info.AkuityServerVersion)
		if err == nil && akuityServerVersion.Compare(agentVersion) >= 0 && !force {
			a.Log.Info(fmt.Sprintf("Kargo Instance '%s' agent-server version is up-to-date: '%s' >= '%s'",
				instanceID, status.Info.AkuityServerVersion, agentServerVersion))
			return false, nil
		}
	}

	status.Info.RequestedAkuityServerVersion = agentServerVersion
	if err := instance.SetStatus(status); err != nil {
		return false, err
	}

	instance.StatusObservedGeneration = null.IntFrom(instance.StatusObservedGeneration.Int - 1)
	if err := repoSet.KargoInstances().Update(
		a.Ctx, instance, models.KargoInstanceColumns.StatusObservedGeneration, models.KargoInstanceColumns.StatusInfo); err != nil {
		return false, err
	}

	if a.includeAgents {
		if err := (&AKPUtilInstance{AKPUtil: a.AKPUtil, instanceID: instanceID}).DecrementKargoAgentsObservedGeneration(); err != nil {
			return false, err
		}
	}

	a.Log.Info(fmt.Sprintf("Upgraded Kargo Instance '%s' agent-server from '%s' to '%s'",
		instanceID, status.Info.AkuityServerVersion, agentServerVersion))

	return true, nil
}
