package instance

import (
	"fmt"
	"regexp"
	"strconv"

	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

// Matches a K3s version like  "v1.25.16-k3s4" or "v1.28.11-k3s2"
var (
	k3sVersionRegex = regexp.MustCompile(`v\d+\.\d+\.\d+-k3s\d+`)
	digitsRegex     = regexp.MustCompile(`\d+`)
)

// K3sUpgrade gradually upgrades the K3s image of all Argo and Kargo instances
func (a *AKPUtilGradualUpdate) K3sUpgrade(k3sImage, k3sSchemaMigration string) error {
	repoSet := client.NewRepoSet(a.PortalDBPool.DB)

	return a.PerformUpdateOperation(fmt.Sprintf("k3s image update to '%s'", k3sImage),
		func(instance DbInstance) (bool, error) {
			if instance.IsArgo {
				return a.ArgoK3sUpgrade(repoSet, instance.ID, k3sImage, k3sSchemaMigration)
			} else {
				return a.KargoK3sUpgrade(repoSet, instance.ID, k3sImage, k3sSchemaMigration)
			}
		})
}

func (a *AKPUtilGradualUpdate) ArgoK3sUpgrade(repoSet client.RepoSet, instanceID, k3sImage, k3sSchemaMigration string) (bool, error) {
	instanceConfig, err := repoSet.ArgoCDInstanceConfigs().GetByID(a.Ctx, instanceID)
	if err != nil {
		return false, err
	}

	internalSpec, err := instanceConfig.GetInternalSpec()
	if err != nil {
		return false, err
	}

	originalK3sImage := internalSpec.K3sImage

	needUpgrades := false
	// DEV: we decided to do equality check instead of semver to ensure downgrades work as well
	if originalK3sImage != k3sImage && isValidK3sVersion(k3sImage) {
		needUpgrades = true
		internalSpec.K3sImage = k3sImage
	}
	if k3sSchemaMigration != "" && fmt.Sprintf("%v", internalSpec.K3sSchemaMigration) != k3sSchemaMigration {
		needUpgrades = true
		val, err := strconv.Atoi(k3sSchemaMigration)
		if err != nil {
			return false, err
		}
		internalSpec.K3sSchemaMigration = val
	}
	if !needUpgrades {
		a.Log.Info(fmt.Sprintf("Argo Instance '%s' K3s image is up-to-date: '%s' >= '%s' and no schema migration change",
			instanceID, originalK3sImage, k3sImage))
		return false, nil
	}
	if err := instanceConfig.SetInternalSpec(internalSpec); err != nil {
		return false, err
	}

	if err := repoSet.ArgoCDInstanceConfigs().Update(a.Ctx, instanceConfig, models.ArgoCDInstanceConfigColumns.InternalSpec); err != nil {
		return false, err
	}

	a.Log.Info(fmt.Sprintf("Upgraded Argo Instance '%s' K3s image from '%s' to '%s'",
		instanceID, originalK3sImage, k3sImage))

	return true, nil
}

func (a *AKPUtilGradualUpdate) KargoK3sUpgrade(repoSet client.RepoSet, instanceID, k3sImage, k3sSchemaMigration string) (bool, error) {
	instanceConfig, err := repoSet.KargoInstanceConfigs().GetByID(a.Ctx, instanceID)
	if err != nil {
		return false, err
	}

	internalSpec, err := instanceConfig.GetInternalSpec()
	if err != nil {
		return false, err
	}

	originalK3sImage := internalSpec.K3sImage

	needUpgrades := false
	// DEV: we decided to do equality check instead of semver to ensure downgrades work as well
	if originalK3sImage != k3sImage && isValidK3sVersion(k3sImage) {
		needUpgrades = true
		internalSpec.K3sImage = k3sImage
	}
	if k3sSchemaMigration != "" && fmt.Sprintf("%v", internalSpec.K3sSchemaMigration) != k3sSchemaMigration {
		needUpgrades = true
		val, err := strconv.Atoi(k3sSchemaMigration)
		if err != nil {
			return false, err
		}
		internalSpec.K3sSchemaMigration = val
	}
	if !needUpgrades {
		a.Log.Info(fmt.Sprintf("Argo Instance '%s' K3s image is up-to-date: '%s' >= '%s' and no schema migration change",
			instanceID, originalK3sImage, k3sImage))
		return false, nil
	}
	if err := instanceConfig.SetInternalSpec(internalSpec); err != nil {
		return false, err
	}

	if err := repoSet.KargoInstanceConfigs().Update(a.Ctx, instanceConfig, models.KargoInstanceConfigColumns.InternalSpec); err != nil {
		return false, err
	}

	a.Log.Info(fmt.Sprintf("Upgraded Kargo Instance '%s' K3s image from '%s' to '%s'",
		instanceID, originalK3sImage, k3sImage))

	return true, nil
}

// compareK3sVersions compares two K3s version like "v1.27.15-k3s2" and returns:
// -1, true, if versionA < versionB
//
//	0, true, if versionA == versionB
//	1, true, if versionA > versionB
//	_, false if unable to compare two versions (string doesn't match the K3s version)
func compareK3sVersions(versionA, versionB string) (int, bool) {
	if !isValidK3sVersion(versionA) || !isValidK3sVersion(versionB) {
		return 0, false
	}

	if versionA == versionB {
		return 0, true
	}

	versionDigitsA := digitsRegex.FindAllString(versionA, -1)
	versionDigitsB := digitsRegex.FindAllString(versionB, -1)

	if len(versionDigitsA) != len(versionDigitsB) {
		return 0, false
	}

	// Compare the numeric components of every version
	for i := range versionDigitsA {
		numberA, err := strconv.Atoi(versionDigitsA[i])
		if err != nil {
			return 0, false
		}
		numberB, err := strconv.Atoi(versionDigitsB[i])
		if err != nil {
			return 0, false
		}

		if numberA < numberB {
			return -1, true
		} else if numberA > numberB {
			return 1, true
		}
	}

	return 0, true
}

func isValidK3sVersion(k3sVersion string) bool {
	return k3sVersionRegex.MatchString(k3sVersion)
}
