package instance

import (
	"fmt"
	"time"

	"k8s.io/utils/ptr"

	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/controllers/platform/metrics"
	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/config"
)

const refreshInterval = 30 * time.Second

type AKPUtilEnsureHealthy struct {
	*shared.AKPUtil
	stabilizationPeriodMinutes uint8
	includeAgents              bool
	shard                      string
}

func (a *AKPUtilEnsureHealthy) EnsureHealthy(instanceIDs ...string) {
	var stabilizationPeriodStart *time.Time
	deadlinesConfig, err := config.NewDeadlinesConfig()
	if err != nil {
		a.Log.Error(err, "failed to read NewDeadlinesConfig")
		return
	}

	statusRefresher := metrics.NewStatusRefresher(deadlinesConfig, a.PortalDBPool.DB, a.Log, a.shard, a.Ctx)
	healthySummaryPrefix := common.If(a.includeAgents,
		"All Instances, Clusters, and Agents are Healthy",
		"All Instances are Healthy")

	// Checks if all Argo / Kargo Instances (and optionally all Argo Clusters / Kargo Agents) are Healthy
	// Returns true  to continue the loop and continue waiting for stabilization period
	// Returns false to stop the loop, when stabilization period has ended successfully or prematurely
	// (Instances and Clusters/Agents were Healthy for the duration of stabilization period or none of the Instances exist)
	checkHealth := func() bool {
		if !statusRefresher.InstancesExist(instanceIDs...) {
			a.Log.Info("Stabilization period has ended prematurely - none of the Argo or Kargo instances requested exist",
				"instanceIDs", instanceIDs)
			return false
		}

		if !a.allHealthy(statusRefresher, instanceIDs...) {
			// Resetting the stabilization period and continuing the loop
			a.Log.Info("Some Instances, Clusters or Agents are not Healthy, resetting the stabilization period")
			stabilizationPeriodStart = nil
			return true
		}

		// All Instances (and optionally all Clusters and Agents) are Healthy
		// If stabilization period is zero - we return right away

		if a.stabilizationPeriodMinutes > 0 {
			if stabilizationPeriodStart == nil {
				// A first successful execution since last period reset or start,
				// initializing the stabilization period and continuing the loop
				stabilizationPeriodStart = ptr.To(time.Now())
				a.Log.Info(fmt.Sprintf("%s, stabilization period has started", healthySummaryPrefix),
					"instanceIDs", instanceIDs,
					"minutesLeft", fmt.Sprintf("%.2f", float64(a.stabilizationPeriodMinutes)))
				return true
			}

			stabilizationPeriodMinutesPassed := time.Since(*stabilizationPeriodStart).Minutes()

			if stabilizationPeriodMinutesPassed < float64(a.stabilizationPeriodMinutes) {
				stabilizationPeriodMinutesLeft := float64(a.stabilizationPeriodMinutes) - stabilizationPeriodMinutesPassed
				a.Log.Info(fmt.Sprintf("%s, stabilization period continues", healthySummaryPrefix),
					"instanceIDs", instanceIDs,
					"minutesPassed", fmt.Sprintf("%.2f", stabilizationPeriodMinutesPassed),
					"minutesLeft", fmt.Sprintf("%.2f", stabilizationPeriodMinutesLeft))
				return true
			}
		}

		a.Log.Info(fmt.Sprintf("%s, stabilization period has ended successfully", healthySummaryPrefix))
		return false
	}

	if !checkHealth() {
		return
	}

	ticker := time.NewTicker(refreshInterval)
	defer ticker.Stop()

	for range ticker.C {
		if !checkHealth() {
			break
		}
	}
}

// allHealthy - determines if Argo / Kargo Instances requested (all if instanceIDs is empty)
// and their Clusters / Agents are Healthy
func (a *AKPUtilEnsureHealthy) allHealthy(statusRefresher *metrics.StatusRefresher, instanceIDs ...string) bool {
	argoInstancesStatuses, err := statusRefresher.RefreshArgoInstances(instanceIDs...)
	if err != nil {
		a.Log.Error(err, "failed to refresh Argo Instances")
		return false
	}

	a.Log.Info("Argo Instances refreshed", "instanceIDs", instanceIDs)

	kargoInstancesStatuses, err := statusRefresher.RefreshKargoInstances(instanceIDs...)
	if err != nil {
		a.Log.Error(err, "failed to refresh Kargo Instances")
		return false
	}

	a.Log.Info("Kargo Instances refreshed", "instanceIDs", instanceIDs)

	if len(argoInstancesStatuses) < 1 && len(kargoInstancesStatuses) < 1 {
		a.Log.Info("None of the Argo or Kargo instances specified exist",
			"instanceIDs", instanceIDs)
		return true
	}

	unhealthyArgoInstances := unhealthyInstances(argoInstancesStatuses)
	unhealthyKargoInstances := unhealthyInstances(kargoInstancesStatuses)

	if len(unhealthyArgoInstances) < 1 {
		a.Log.Info("All Argo Instances are Healthy", "instanceIDs", instanceIDs)
	} else {
		a.Log.Info("Some Argo Instances are not Healthy", "unhealthyArgoInstances", unhealthyArgoInstances)
		return false
	}

	if len(unhealthyKargoInstances) < 1 {
		a.Log.Info("All Kargo Instances are Healthy", "instanceIDs", instanceIDs)
	} else {
		a.Log.Info("Some Kargo Instances are not Healthy", "unhealthyKargoInstances", unhealthyKargoInstances)
		return false
	}

	if a.includeAgents {
		argoClustersStatuses, err := statusRefresher.RefreshArgoClusters(instanceIDs...)
		if err != nil {
			a.Log.Error(err, "failed to refresh Argo Clusters")
			return false
		}

		a.Log.Info("Argo Clusters refreshed", "instanceIDs", instanceIDs)

		kargoAgentsStatuses, err := statusRefresher.RefreshKargoAgents(instanceIDs...)
		if err != nil {
			a.Log.Error(err, "failed to refresh Kargo Agents")
			return false
		}

		a.Log.Info("Kargo Agents refreshed", "instanceIDs", instanceIDs)

		unhealthyArgoClusters := unhealthyArgoClusters(argoClustersStatuses)
		unhealthyKargoAgents := unhealthyKargoAgents(kargoAgentsStatuses)

		if len(unhealthyArgoClusters) < 1 {
			a.Log.Info("All Argo Clusters are Healthy", "instanceIDs", instanceIDs)
		} else {
			a.Log.Info("Some Argo Clusters are not Healthy", "unhealthyArgoClusters", unhealthyArgoClusters)
			return false
		}

		if len(unhealthyKargoAgents) < 1 {
			a.Log.Info("All Kargo Agents are Healthy", "instanceIDs", instanceIDs)
		} else {
			a.Log.Info("Some Kargo Agents are not Healthy", "unhealthyKargoAgents", unhealthyKargoAgents)
			return false
		}
	}

	return true
}

func unhealthyInstances(instancesStatuses map[metrics.InstanceStatus][]string) []string {
	var unhealthyInstancesIDs []string
	for instanceStatus, instancesIDs := range instancesStatuses {
		if !instanceStatus.IsHealthy() {
			unhealthyInstancesIDs = append(unhealthyInstancesIDs, instancesIDs...)
		}
	}
	return unhealthyInstancesIDs
}

func unhealthyArgoClusters(clustersStatuses map[metrics.ClusterStatus][]string) []string {
	var unhealthyClustersIDs []string
	for clusterStatus, clusterIDs := range clustersStatuses {
		if !clusterStatus.IsHealthy() {
			unhealthyClustersIDs = append(unhealthyClustersIDs, clusterIDs...)
		}
	}
	return unhealthyClustersIDs
}

func unhealthyKargoAgents(agentsStatuses map[metrics.AgentStatus][]string) []string {
	var unhealthyAgentsIDs []string
	for agentStatus, agentIDs := range agentsStatuses {
		if !agentStatus.IsHealthy() {
			unhealthyAgentsIDs = append(unhealthyAgentsIDs, agentIDs...)
		}
	}
	return unhealthyAgentsIDs
}
