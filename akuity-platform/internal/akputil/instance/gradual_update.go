package instance

import (
	"bufio"
	"fmt"
	"math"
	"os"
	"time"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/models/models"
)

// countInstancesQuery returns the current number of Argo and Kargo instances
const countInstancesQuery = `
select 
	(select count(id) from argo_cd_instance) + 
	(select count(id) from kargo_instance);
`

// listInstancesQuery returns Argo and Kargo instances sorted from the least to the most important:
// - Trial   instances are coming before non-trial ones
// - Younger instances are coming before the older ones
// So the instances returned will be youngest trial instances first, oldest non-trial instances last.
const listInstancesQuery = `
select
    *
from
    (
        (select
            instance.id as id,
            instance.name as name,
            instance.creation_timestamp as creation_timestamp,
            coalesce((org.org_status->>'trial')::boolean, false) as trial,
            true as argo,
            org.name as org_name
        from
            argo_cd_instance instance
        left outer join
            organization org
        on
            instance.organization_owner = org.id)
        
        union all
        
        (select
            instance.id as id,
            instance.name as name,
            instance.creation_timestamp as creation_timestamp,
            coalesce((org.org_status->>'trial')::boolean, false) as trial,
            false as argo,
            org.name as org_name
        from
            kargo_instance instance
        left outer join
            organization org
        on
            instance.organization_owner = org.id)
    ) as instances
order by
    instances.trial desc,
    instances.creation_timestamp desc
offset $1
limit $2;
`

// DbInstance - represents a single argo_cd_instance row
// Needs to be in Sync with gradualUpdateListInstancesQuery query result fields
type DbInstance struct {
	ID                string    `boil:"id"`
	Name              string    `boil:"name"`
	CreationTimestamp time.Time `boil:"creation_timestamp"`
	Trial             bool      `boil:"trial"`
	IsArgo            bool      `boil:"argo"`
	OrgName           string    `boil:"org_name"`
}

type AKPUtilGradualUpdate struct {
	*shared.AKPUtil
	percentage                       uint8
	skipPercentage                   uint8
	delayBetweenUpdates              time.Duration
	stabilizationPeriodMinutes       uint8
	stabilizationPeriodIncludeAgents bool
	skipConfirmation                 bool
	shard                            string
}

// PerformUpdateOperation performs a gradual "update" operation (passed as a func) on all Argo and Kargo instances.
//
// It iterates over all instances in batches of a.percentage (typically 10%) and calls the "update" operation
// passed on each instance. After each batch, it checks if any instances were updated, and if so, starts a "stabilization period".
// During "stabilization period" it waits for all updated instances (and optionally their clusters/agents) to stay Healthy for a period specified.
// After  "stabilization period" it waits an additional fixed-time delay so the team has time to respond to any firing alerts.
//
// Parameter:
//   - operationName: the name of the update operation
//   - updateOperation: a function that performs the update operation on a single DbInstance,
//     it takes a DbInstance as input and returns a bool (whether the instance was updated) and an error.
//
// Returns: an error if any error occurs during the operation or any update operation returns an error
func (a *AKPUtilGradualUpdate) PerformUpdateOperation(operationName string, updateOperation func(instance DbInstance) (bool, error)) error {
	if a.percentage < 1 {
		return fmt.Errorf("--percentage %d needs to be positive", a.percentage)
	}

	if a.percentage > 100 {
		return fmt.Errorf("--percentage %d needs to be less than or equal to 100%%", a.percentage)
	}

	var totalInstancesProcessed uint32
	var skipPercentage uint8

	startTime := time.Now()
	for skipPercentage = a.skipPercentage; skipPercentage < 100; skipPercentage += a.percentage {
		dbInstances, instancesTotalCount, err := a.listDbInstances(skipPercentage)
		if err != nil {
			return err
		}

		numberOfInstancesToProcess := uint32(len(dbInstances))

		if numberOfInstancesToProcess < 1 {
			a.Log.Info("No instances found")
			return nil
		}

		// Number of instances skipped when starting the process, using the "--skip-percentage" flag
		instancesSkippedInitially := (instancesTotalCount * uint32(a.skipPercentage)) / 100

		// Counter of the first instance processed, starting from 1
		firstInstanceCounter := 1 + totalInstancesProcessed + instancesSkippedInitially

		endPercentage := common.Min(skipPercentage+a.percentage, 100)

		// Multiple Instances: "#1 - #5, 0% - 5%"
		instancesRangeDescription := fmt.Sprintf("#%d - #%d, %d%% - %d%%",
			firstInstanceCounter, firstInstanceCounter+numberOfInstancesToProcess-1,
			skipPercentage, endPercentage)
		if numberOfInstancesToProcess == 1 {
			// Single Instance: "#1, 0% - 25%"
			instancesRangeDescription = fmt.Sprintf("#%d, %d%% - %d%%",
				firstInstanceCounter, skipPercentage, endPercentage)
		}

		// " ===> Updating 1 out of 23 instances (#14, 65% - 70%): <operation-name>"
		// " ===> Updating 5 out of 23 instances (#11 - #15, 50% - 75%): <operation-name>"
		a.Log.Info(fmt.Sprintf(" ===> Updating %d out of %d instances (%s): %s",
			numberOfInstancesToProcess, instancesTotalCount, instancesRangeDescription, operationName))

		if !a.confirmDbInstances(dbInstances, totalInstancesProcessed) {
			return nil
		}

		batchStartTime := time.Now()
		someInstancesUpdated := false

		for _, dbInstance := range dbInstances {
			a.Log.Info(fmt.Sprintf("Updating %s Instance", common.If(dbInstance.IsArgo, "Argo", "Kargo")),
				"ID", dbInstance.ID,
				"Name", dbInstance.Name,
				"Trial", dbInstance.Trial,
				"CreationTimestamp", dbInstance.CreationTimestamp,
				"OrgName", dbInstance.OrgName)

			instanceUpdated, err := updateOperation(dbInstance)
			if err != nil {
				return err
			}

			someInstancesUpdated = someInstancesUpdated || instanceUpdated
		}

		batchDurationMillis := fmt.Sprintf("%.2d", time.Since(batchStartTime).Milliseconds())
		instancesProcessed := uint32(len(dbInstances))
		totalInstancesProcessed += instancesProcessed

		if someInstancesUpdated {
			if instancesProcessed == 1 {
				a.Log.Info(fmt.Sprintf("1 Instance has been updated in %s ms, processed %d out of %d instances",
					batchDurationMillis, totalInstancesProcessed, instancesTotalCount))
			} else {
				a.Log.Info(fmt.Sprintf("%d Instances have been updated in %s ms, processed %d out of %d instances",
					instancesProcessed, batchDurationMillis, totalInstancesProcessed, instancesTotalCount))
			}

			// Running "Ensure Healthy" command - waiting for all instances to stay Healthy during the stabilization period
			if a.stabilizationPeriodMinutes > 0 {
				(&AKPUtilEnsureHealthy{
					AKPUtil:                    a.AKPUtil,
					stabilizationPeriodMinutes: a.stabilizationPeriodMinutes,
					includeAgents:              a.stabilizationPeriodIncludeAgents,
					shard:                      a.shard,
				}).EnsureHealthy(instanceIDs(dbInstances)...)
			}

			if (a.delayBetweenUpdates > 0) && ((a.percentage + skipPercentage) < 100) {
				a.Log.Info(fmt.Sprintf("Waiting for %v between the updates, will resume at %v",
					a.delayBetweenUpdates, time.Now().Add(a.delayBetweenUpdates).Format(time.TimeOnly)))
				time.Sleep(a.delayBetweenUpdates)
			}
		} else {
			a.Log.Info(fmt.Sprintf("No Instances have been updated in %s ms, processed %d out of %d instances",
				batchDurationMillis, totalInstancesProcessed, instancesTotalCount))
		}
	}

	a.Log.Info(fmt.Sprintf("%d Instances have been processed in %s minutes",
		totalInstancesProcessed, fmt.Sprintf("%.2f", time.Since(startTime).Minutes())))
	return nil
}

// listDbInstances lists Argo and Kargo instances and returns a slice of instances according to a.percentage and skipPercentage specified
func (a *AKPUtilGradualUpdate) listDbInstances(skipPercentage uint8) ([]DbInstance, uint32, error) {
	var instancesTotalCount uint32
	if err := a.PortalDBPool.DB.QueryRowContext(a.Ctx, countInstancesQuery).Scan(&instancesTotalCount); err != nil {
		return nil, 0, err
	}

	if instancesTotalCount < 1 {
		a.Log.Info("No instances found")
		return nil, 0, nil
	}

	// Number of instances to update
	instancesToUpdate := (instancesTotalCount * uint32(a.percentage)) / 100
	if instancesToUpdate < 1 {
		return nil, 0, fmt.Errorf("%d%% out of %d instances is zero instances, "+
			"a single instance to upgrade would be %d%% at least",
			a.percentage, instancesTotalCount, int(math.Ceil(100.0/float64(instancesTotalCount))))
	}

	// Number of instances to skip
	instancesToSkip := (instancesTotalCount * uint32(skipPercentage)) / 100
	if skipPercentage > 0 && instancesToSkip < 1 {
		a.Log.Info(fmt.Sprintf("%d%% out of %d instances is zero instances, "+
			"a single instance to skip would be %d%% at least",
			skipPercentage, instancesTotalCount, int(math.Ceil(100.0/float64(instancesTotalCount)))))
		return nil, 0, nil
	}

	if skipPercentage > a.percentage {
		// As 'skipPercentage' keeps growing - it may skip too many instances
		// so we base the 'instancesToSkip' on the skipPercentage / percentage ratio instead
		instancesToSkip = instancesToUpdate * uint32(skipPercentage/a.percentage)
	}

	if a.percentage+skipPercentage >= 100 {
		// When sum of 'percentage' + 'skipPercentage' is at or above 100% - we update all remaining instances
		instancesToUpdate = instancesTotalCount - instancesToSkip
	}

	var dbInstances []DbInstance
	if err := models.NewQuery(qm.SQL(listInstancesQuery, instancesToSkip, instancesToUpdate)).Bind(
		a.Ctx, a.PortalDBPool.DB, &dbInstances); err != nil {
		return nil, 0, fmt.Errorf("failed to read instances to update using 'offset' %d and 'limit' %d: %w",
			instancesToSkip, instancesToUpdate, err)
	}

	return dbInstances, instancesTotalCount, nil
}

// confirmDbInstances confirms with the human CLI operator the operation execution for instances specified
func (a *AKPUtilGradualUpdate) confirmDbInstances(dbInstances []DbInstance, totalInstancesProcessed uint32) bool {
	if len(dbInstances) < 1 {
		return false
	}

	for index, dbInstance := range dbInstances {
		// "Argo|Kargo Instance #4"
		instanceTitle := fmt.Sprintf("%s Instance #%d",
			common.If(dbInstance.IsArgo, "Argo", "Kargo"),
			totalInstancesProcessed+1+uint32(index))

		a.Log.Info(instanceTitle,
			"ID", dbInstance.ID,
			"Name", dbInstance.Name,
			"Trial", dbInstance.Trial,
			"CreationTimestamp", dbInstance.CreationTimestamp,
			"OrgName", dbInstance.OrgName)
	}

	if a.skipConfirmation {
		return true
	}

	fmt.Print("Press 'Y' to continue or anything else to stop (or use --skip-confirmation): ")
	char, _, _ := bufio.NewReader(os.Stdin).ReadRune()
	return char == 'Y'
}

func instanceIDs(dbInstances []DbInstance) []string {
	var instanceIDs []string
	for _, dbInstance := range dbInstances {
		instanceIDs = append(instanceIDs, dbInstance.ID)
	}
	return instanceIDs
}
