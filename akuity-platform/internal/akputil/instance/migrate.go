//go:build !selfhosted

package instance

import (
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cobra"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/argoproj"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
)

func init() {
	instanceCmd.AddCommand(NewAKPUtilInstanceMigrateArgoCDNamespaceCommand())
}

var (
	argocdGVRs = []schema.GroupVersionResource{
		misc.AppprojectsGVR,
		misc.ApplicationGVR,
		misc.ApplicationsetGVR,
	}

	// argoCDManagedConfigMaps are ConfigMaps which are managed via Argo CD API (and not applied to argocd namespace manually)
	argoCDManagedConfigMaps = []string{
		argoproj.ArgoCDKnownHostsConfigMapName,
		"argocd-gpg-keys-cm",
		argoproj.ArgoCDTLSCertsConfigMapName,
	}
)

func NewAKPUtilInstanceMigrateArgoCDNamespaceCommand() *cobra.Command {
	var (
		clientConfig clientcmd.ClientConfig
		instanceID   string
	)
	cmd := &cobra.Command{
		Use:   "migrate-namespace",
		Short: "Migrate instance to new Argo CD namespace",
		Run: func(cmd *cobra.Command, args []string) {
			akpUtilInstance, err := NewAKPUtilInstance(instanceID, clientConfig, cmd.Context(), 10*time.Minute)
			cli.CheckErr(err)
			defer func() { _ = akpUtilInstance.Close() }()

			err = akpUtilInstance.MigrateArgoCDNamespace()
			cli.CheckErr(err)
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&instanceID, "instance-id", "", "Argo CD Instance")
	cli.CheckErr(cmd.MarkFlagRequired("instance-id"))
	return cmd
}

// MigrateArgoCDNamespace migrates an instance's settings from the legacy argocd-xxxx namespace to
// the well-known 'argocd' namespace. This helper is throwaway code that is not needed after GA but
// may be useful to model to perform other maintenance tasks inside the k3s control plane.
// The logic could also be used for potential backup / restore capabilities.
func (a *AKPUtilInstance) MigrateArgoCDNamespace() error {
	cplaneRestConfig, err := a.CPlaneRestConfig()
	if err != nil {
		return err
	}
	cplaneDynamicClient, err := dynamic.NewForConfig(cplaneRestConfig)
	if err != nil {
		return err
	}

	legacyNamespace := common.ArgoCDHostNamespace(a.instanceID)
	if _, err := a.K8sClient.CoreV1().Namespaces().Get(a.Ctx, legacyNamespace, metav1.GetOptions{}); err != nil {
		if k8sErrors.IsNotFound(err) {
			a.Log.Info(fmt.Sprintf("Legacy argocd namespace '%s' does not exist. Nothing to do", legacyNamespace))
			return nil
		}
		return err
	}
	if _, err := a.K8sClient.CoreV1().Namespaces().Get(a.Ctx, common.K3sArgoCDNamespace, metav1.GetOptions{}); err != nil {
		if k8sErrors.IsNotFound(err) {
			return fmt.Errorf("'argocd' namespace does not exist inside k3s of instance '%s'. Please run `akputil instance bump-generation --instance-id %s`", a.instanceID, a.instanceID)
		}
		return err
	}

	if err := a.migrateSecrets(a.K8sClient); err != nil {
		return err
	}
	if err := a.migrateConfigMaps(a.K8sClient); err != nil {
		return err
	}
	if err := a.migrateDefaultProject(cplaneDynamicClient); err != nil {
		return err
	}
	for _, gvr := range argocdGVRs {
		if err := a.migrateGVR(cplaneDynamicClient, gvr); err != nil {
			return err
		}
	}
	if err := a.copyAgentUpgraderManifests(a.K8sClient); err != nil {
		return err
	}
	return nil
}

// migrateSecrets copies (but does not replace) from legacy namespace to new
func (a *AKPUtilInstance) migrateSecrets(cplaneKubeClient kubernetes.Interface) error {
	legacyNamespace := common.ArgoCDHostNamespace(a.instanceID)
	legacySecrets, err := cplaneKubeClient.CoreV1().Secrets(legacyNamespace).List(a.Ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}
	for _, s := range legacySecrets.Items {
		secretType := ""
		if s.Labels != nil {
			secretType = s.Labels["argocd.argoproj.io/secret-type"]
		}
		switch secretType {
		case "repository", "repo-creds":
			s.Namespace = common.K3sArgoCDNamespace
			s.ObjectMeta = unsetObjectMeta(s.ObjectMeta)
			s := s // https://stackoverflow.com/a/68247837/472153 - addresses "Implicit memory aliasing in for loop"
			if _, err := cplaneKubeClient.CoreV1().Secrets(common.K3sArgoCDNamespace).Create(a.Ctx, &s, metav1.CreateOptions{}); err != nil {
				if k8sErrors.IsAlreadyExists(err) {
					a.Log.Info("Secret already migrated", "secret", s.Name)
					continue
				}
				return err
			}
			a.Log.Info("Migrated secret", "secret", s.Name)
		default:
			// a.log.Info("Ignoring secret", "secret", s.Name)
		}
	}
	return nil
}

// migrateConfigMaps migrates (replaces) configmaps from
func (a *AKPUtilInstance) migrateConfigMaps(cplaneKubeClient kubernetes.Interface) error {
	legacyNamespace := common.ArgoCDHostNamespace(a.instanceID)
	for _, cmName := range argoCDManagedConfigMaps {
		oldCM, err := cplaneKubeClient.CoreV1().ConfigMaps(legacyNamespace).Get(a.Ctx, cmName, metav1.GetOptions{})
		if err != nil {
			if k8sErrors.IsNotFound(err) {
				continue
			}
			return err
		}
		newCM, err := cplaneKubeClient.CoreV1().ConfigMaps(common.K3sArgoCDNamespace).Get(a.Ctx, cmName, metav1.GetOptions{})
		if err != nil {
			return err
		}
		oldCM.ObjectMeta = transferObjectMeta(newCM.ObjectMeta, oldCM.ObjectMeta)
		oldCM.SetNamespace(common.K3sArgoCDNamespace)
		if _, err := cplaneKubeClient.CoreV1().ConfigMaps(common.K3sArgoCDNamespace).Update(a.Ctx, oldCM, metav1.UpdateOptions{}); err != nil {
			return err
		}
		a.Log.Info("ConfigMap replaced", "configmap", cmName)
	}
	return nil
}

// migrateDefaultProject performs special case migration (replacement) of default project (since it always exists)
func (a *AKPUtilInstance) migrateDefaultProject(dynamicClient dynamic.Interface) error {
	legacyNamespace := common.ArgoCDHostNamespace(a.instanceID)
	oldProj, err := dynamicClient.Resource(misc.AppprojectsGVR).Namespace(legacyNamespace).Get(a.Ctx, "default", metav1.GetOptions{})
	if err != nil {
		return err
	}
	newProj, err := dynamicClient.Resource(misc.AppprojectsGVR).Namespace(common.K3sArgoCDNamespace).Get(a.Ctx, "default", metav1.GetOptions{})
	if err != nil {
		return err
	}
	transferMetadata(newProj, oldProj)
	oldProj.SetNamespace(common.K3sArgoCDNamespace)
	if _, err := dynamicClient.Resource(misc.AppprojectsGVR).Namespace(common.K3sArgoCDNamespace).Update(a.Ctx, oldProj, metav1.UpdateOptions{}); err != nil {
		return err
	}
	a.Log.Info("AppProject 'default' replaced")
	return nil
}

// migrateGVR copies (but does not replace) application/projects/appsets from the legacy namespace to new
func (a *AKPUtilInstance) migrateGVR(dynamicClient dynamic.Interface, gvr schema.GroupVersionResource) error {
	legacyNamespace := common.ArgoCDHostNamespace(a.instanceID)
	dynIf := dynamicClient.Resource(gvr).Namespace(legacyNamespace)
	resList, err := dynIf.List(a.Ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}
	for _, un := range resList.Items {
		if un.GetName() == "default" && un.GetKind() == "AppProject" {
			// we handle this in migrateDefaultProject
			continue
		}
		un.SetNamespace(common.K3sArgoCDNamespace)
		unsetMetadata(un.Object)
		newNamespaceDynIf := dynamicClient.Resource(gvr).Namespace(common.K3sArgoCDNamespace)
		if _, err := newNamespaceDynIf.Create(a.Ctx, &un, metav1.CreateOptions{}); err != nil {
			if k8sErrors.IsAlreadyExists(err) {
				a.Log.Info("Resource already migrated", "name", un.GetName(), "kind", un.GetKind())
				continue
			}
			return err
		}
		a.Log.Info("Migrated resource", "name", un.GetName(), "kind", un.GetKind())
	}
	return nil
}

func unsetObjectMeta(obj metav1.ObjectMeta) metav1.ObjectMeta {
	obj.Generation = 0
	obj.CreationTimestamp = metav1.Time{}
	obj.ResourceVersion = ""
	obj.UID = ""
	return obj
}

func unsetMetadata(obj map[string]interface{}) {
	unstructured.RemoveNestedField(obj, "metadata", "generation")
	unstructured.RemoveNestedField(obj, "metadata", "creationTimestamp")
	unstructured.RemoveNestedField(obj, "metadata", "resourceVersion")
	unstructured.RemoveNestedField(obj, "metadata", "uid")
}

// transferObjectMeta carries over object metadata information from one object to another.
// It is used for the purposes of migration, when we need call Update() in the new namespace
// (but with the object from the old namespace).
func transferObjectMeta(from, to metav1.ObjectMeta) metav1.ObjectMeta {
	to.Generation = from.Generation
	to.CreationTimestamp = from.CreationTimestamp
	to.ResourceVersion = from.ResourceVersion
	to.UID = from.UID
	return to
}

func transferMetadata(from, to *unstructured.Unstructured) {
	to.SetGeneration(from.GetGeneration())
	to.SetCreationTimestamp(from.GetCreationTimestamp())
	to.SetResourceVersion(from.GetResourceVersion())
	to.SetUID(from.GetUID())
}

// copyAgentUpgraderManifests copies the cluster-xxxxx.data secret containing the agent install
// manifests to the legacy namespace. This will cause legacy cluster agents to to upgrade themselves
// to use new argocd namespace + credentials.
func (a *AKPUtilInstance) copyAgentUpgraderManifests(cplaneKubeClient kubernetes.Interface) error {
	legacyNamespace := common.ArgoCDHostNamespace(a.instanceID)
	newClusterData, err := cplaneKubeClient.CoreV1().Secrets(common.K3sArgoCDNamespace).List(a.Ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}
	a.Log.Info("Copying agent upgrader manifests")
	for _, s := range newClusterData.Items {
		if !strings.HasPrefix(s.Name, "cluster-") || !strings.HasSuffix(s.Name, client.ClusterSuffixDataSecretName) || s.Data == nil || len(s.Data["generation"]) == 0 {
			// Doesn't appear to be a cluster-xxxx.data secret
			// a.log.Info("Skipping secret", "name", s.Name, "namespace", s.Namespace)
			continue
		}
		legacySecret, err := cplaneKubeClient.CoreV1().Secrets(legacyNamespace).Get(a.Ctx, s.Name, metav1.GetOptions{})
		if err != nil {
			if k8sErrors.IsNotFound(err) {
				continue
			}
			return err
		}
		legacySecret.Data = s.Data
		if _, err := cplaneKubeClient.CoreV1().Secrets(legacyNamespace).Update(a.Ctx, legacySecret, metav1.UpdateOptions{}); err != nil {
			return err
		}
		a.Log.Info("Replaced old cluster data secret", "name", s.Name, "prev_generation", string(legacySecret.Data["generation"]), "generation", string(s.Data["generation"]))
	}
	return nil
}
