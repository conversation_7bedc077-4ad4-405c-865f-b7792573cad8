package instance

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"slices"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/spf13/cobra"
	"k8s.io/client-go/tools/clientcmd"

	"github.com/akuityio/agent/pkg/client/apis/controlplane"
	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/utils/consts"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
)

const (
	argoCdInstance = "Argo CD Instance"
	instanceId     = "instance-id"
)

var instanceCmd = &cobra.Command{
	Use:   "instance",
	Short: "Instance command",
	Run: func(cmd *cobra.Command, args []string) {
		cmd.HelpFunc()(cmd, args)
	},
}

func NewInstanceCommand() *cobra.Command {
	instanceCmd.AddCommand(NewGradualUpgradeCommand())
	instanceCmd.AddCommand(NewGradualReconcileCommand())
	instanceCmd.AddCommand(NewEnsureAgentVersionCommand())
	instanceCmd.AddCommand(NewEnsureHealthyCommand())
	instanceCmd.AddCommand(NewBumpGenerationCommand())
	instanceCmd.AddCommand(NewDecrementObservedGenerationCommand())
	instanceCmd.AddCommand(NewChangeOwnerCommand())
	instanceCmd.AddCommand(NewK3sUpgradeCommand())
	instanceCmd.AddCommand(NewSetOverrideCommand())
	instanceCmd.AddCommand(NewArgoCDAccountTokenCommand())
	instanceCmd.AddCommand(NewRefreshWebhookCerts())
	instanceCmd.AddCommand(NewAKPUtilInstancePgBouncerStatCommand())
	instanceCmd.AddCommand(NewGetInstanceClusterNames())
	return instanceCmd
}

func NewGetInstanceClusterNames() *cobra.Command {
	var (
		instanceID   string
		clientConfig clientcmd.ClientConfig
	)
	cmd := &cobra.Command{
		Use:   "cluster-names",
		Short: "get cluster names for the argocd/kargo instance",
		Run: func(cmd *cobra.Command, args []string) {
			akpUtil := shared.NewAKPUtil(clientConfig, cmd.Context(), time.Minute)
			defer func() { _ = akpUtil.Close() }()

			names, err := akpUtil.GetAllClusterNames(instanceID)
			cli.CheckErr(err)

			fmt.Println(strings.Join(names, ","))
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&instanceID, instanceId, "", argoCdInstance)
	cli.CheckErr(cmd.MarkFlagRequired(instanceId))
	return cmd
}

func NewGradualUpgradeCommand() *cobra.Command {
	var (
		components                 []string
		stabilizationPeriodMinutes uint8
		percentage                 uint8
		delaySeconds               uint32
		clientConfig               clientcmd.ClientConfig
		includeAgents              bool
		skipConfirmation           bool
		shard                      string
		force                      bool
		k3sSchemaVersion           string
	)
	// akuity-platform-deploy/base/akuity-platform-shared/platform-controller/instances-upgrader.yaml
	// http://prometheus-prod.monitoring.svc/graph?g0.expr=sum%20by%20(job_name)%20(kube_job_status_completion_time%7Bjob_name%3D~%22instances-upgrader.%2B%22%7D%20-%20kube_job_status_start_time%7Bjob_name%3D~%22instances-upgrader.%2B%22%7D)&g0.tab=0&g0.display_mode=lines&g0.show_exemplars=0&g0.range_input=1w
	cmd := &cobra.Command{
		Use:   "gradual-upgrade",
		Short: "Gradually upgrades ControlPlane agent-server/k3s and client agent version for all Argo and Kargo instances",
		Run: func(cmd *cobra.Command, args []string) {
			akpUtil := shared.NewAKPUtil(clientConfig, cmd.Context(), consts.Day)
			defer func() { _ = akpUtil.Close() }()
			if len(components) == 0 {
				cli.CheckErr(fmt.Errorf("must specify at least one component to upgrade"))
			}

			ensureVersion := &AKPUtilEnsureVersion{AKPUtil: akpUtil, includeAgents: includeAgents}
			agentServerVersion, err := misc.AgentServerVersion(akpUtil.K8sClient)
			cli.CheckErr(err)
			k3sImage := controlplane.NewDataValuesK3s().GetImage()

			gradualUpdater := &AKPUtilGradualUpdate{
				AKPUtil:                          akpUtil,
				percentage:                       percentage,
				delayBetweenUpdates:              time.Duration(delaySeconds) * time.Second,
				stabilizationPeriodMinutes:       stabilizationPeriodMinutes,
				stabilizationPeriodIncludeAgents: includeAgents,
				skipConfirmation:                 skipConfirmation,
				shard:                            shard,
			}
			upgradeAgentServer := slices.Contains(components, "agent-server")
			upgradeK3s := slices.Contains(components, "k3s")
			repoSet := client.NewRepoSet(akpUtil.PortalDBPool.DB)

			var descriptions []string
			if upgradeAgentServer {
				descriptions = append(descriptions, fmt.Sprintf("agent-server version update to '%s'", agentServerVersion))
			}
			if upgradeK3s {
				descriptions = append(descriptions, fmt.Sprintf("k3s image update to '%s'", k3sImage))
				if k3sSchemaVersion != "" {
					descriptions = append(descriptions, fmt.Sprintf("k3s schema version update to '%s'", k3sSchemaVersion))
				}
			}
			cli.CheckErr(gradualUpdater.PerformUpdateOperation(strings.Join(descriptions, ", "),
				func(dbInstance DbInstance) (bool, error) {
					var err error
					var serverUpgraded bool
					if upgradeAgentServer {
						if dbInstance.IsArgo {
							serverUpgraded, err = ensureVersion.EnsureArgoAgentServerVersion(dbInstance.ID, agentServerVersion, force)
						} else {
							serverUpgraded, err = ensureVersion.EnsureKargoAgentServerVersion(dbInstance.ID, agentServerVersion, force)
						}
					}
					if err != nil {
						return false, err
					}
					var k3sUpgraded bool
					if upgradeK3s {
						if dbInstance.IsArgo {
							k3sUpgraded, err = gradualUpdater.ArgoK3sUpgrade(repoSet, dbInstance.ID, k3sImage, k3sSchemaVersion)
						} else {
							k3sUpgraded, err = gradualUpdater.KargoK3sUpgrade(repoSet, dbInstance.ID, k3sImage, k3sSchemaVersion)
						}
					}
					if err != nil {
						return false, err
					}
					return serverUpgraded || k3sUpgraded, nil
				}))
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().Uint8Var(&stabilizationPeriodMinutes, "stabilization-period", 10, "Stabilization period (in minutes) for instances and agents to stay healthy")
	cmd.Flags().Uint8Var(&percentage, "percentage", 10, "Percentage of instances to upgrade in each batch, from 0 to 100")
	cmd.Flags().Uint32Var(&delaySeconds, "delay", 0, "Delay (in seconds) between batch upgrades")
	cmd.Flags().BoolVar(&includeAgents, "include-agents", false, "Whether client agent components need to be updated")
	cmd.Flags().BoolVar(&skipConfirmation, "skip-confirmation", false, "Skip update confirmation for each batch")
	cmd.Flags().StringVar(&shard, "shard", "", "Shard name")
	cmd.Flags().BoolVar(&force, "force", false, "Force regenerate instance manifests even if the version is up-to-date")
	cmd.Flags().StringSliceVar(&components, "component", []string{"agent-server"}, "Components to upgrade (agent-server, k3s)")
	cmd.Flags().StringVar(&k3sSchemaVersion, "k3s-schema-version", "2", "K3s schema version")

	return cmd
}

func NewGradualReconcileCommand() *cobra.Command {
	var (
		stabilizationPeriodMinutes             uint8
		skipPercentage, percentage             uint8
		delaySeconds                           uint32
		clientConfig                           clientcmd.ClientConfig
		includeAgents, skipConfirmation, prune bool
		shard                                  string
	)
	// akuity-platform-deploy/base/akuity-platform-shared/platform-controller/instances-upgrader.yaml
	// http://prometheus-prod.monitoring.svc/graph?g0.expr=sum%20by%20(job_name)%20(kube_job_status_completion_time%7Bjob_name%3D~%22instances-upgrader.%2B%22%7D%20-%20kube_job_status_start_time%7Bjob_name%3D~%22instances-upgrader.%2B%22%7D)&g0.tab=0&g0.display_mode=lines&g0.show_exemplars=0&g0.range_input=1w
	cmd := &cobra.Command{
		Use:   "gradual-reconcile",
		Short: "Gradually reconciles Argo and Kargo instances by either 1) incrementing a generation or 2) decrementing an observed generation",
		Run: func(cmd *cobra.Command, args []string) {
			akpUtil := shared.NewAKPUtil(clientConfig, cmd.Context(), consts.Day)
			defer func() { _ = akpUtil.Close() }()

			gradualUpdater := &AKPUtilGradualUpdate{
				AKPUtil:                          akpUtil,
				percentage:                       percentage,
				skipPercentage:                   skipPercentage,
				delayBetweenUpdates:              time.Duration(delaySeconds) * time.Second,
				stabilizationPeriodMinutes:       stabilizationPeriodMinutes,
				stabilizationPeriodIncludeAgents: includeAgents,
				skipConfirmation:                 skipConfirmation,
				shard:                            shard,
			}

			cli.CheckErr(gradualUpdater.PerformUpdateOperation("reconciliation",
				func(dbInstance DbInstance) (bool, error) {
					akputilInstance := &AKPUtilInstance{AKPUtil: akpUtil, instanceID: dbInstance.ID}

					if prune {
						if dbInstance.IsArgo {
							if err := akputilInstance.BumpArgoInstanceGeneration(); err != nil {
								return false, err
							}
							if includeAgents {
								if err := akputilInstance.BumpArgoClustersGeneration(); err != nil {
									return false, err
								}
							}
						} else {
							if err := akputilInstance.BumpKargoInstanceGeneration(); err != nil {
								return false, err
							}
							if includeAgents {
								if err := akputilInstance.BumpKargoAgentsGeneration(); err != nil {
									return false, err
								}
							}
						}
					} else {
						if dbInstance.IsArgo {
							if err := akputilInstance.DecrementArgoInstanceObservedGeneration(); err != nil {
								return false, err
							}
							if includeAgents {
								if err := akputilInstance.DecrementArgoClustersObservedGeneration(); err != nil {
									return false, err
								}
							}
						} else {
							if err := akputilInstance.DecrementKargoInstanceObservedGeneration(); err != nil {
								return false, err
							}
							if includeAgents {
								if err := akputilInstance.DecrementKargoAgentsObservedGeneration(); err != nil {
									return false, err
								}
							}
						}
					}

					return true, nil
				}))
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().Uint8Var(&stabilizationPeriodMinutes, "stabilization-period", 10, "Stabilization period (in minutes) for instances and agents to stay healthy")
	cmd.Flags().Uint8Var(&skipPercentage, "skip-percentage", 0, "Percentage of instances to skip before starting the operation")
	cmd.Flags().Uint8Var(&percentage, "percentage", 10, "Percentage of instances to upgrade in each batch, from 0 to 100")
	cmd.Flags().Uint32Var(&delaySeconds, "delay", 0, "Delay (in seconds) between batch upgrades")
	cmd.Flags().BoolVar(&includeAgents, "include-agents", false, "Whether client agent components need to be updated")
	cmd.Flags().BoolVar(&skipConfirmation, "skip-confirmation", false, "Skip update confirmation for each batch")
	cmd.Flags().BoolVar(&prune, "prune", false, "Whether resources pruning needs to be enabled: True = bump-generation, False = decrement-observed-generation")
	cmd.Flags().StringVar(&shard, "shard", "", "Shard name")
	return cmd
}

func NewEnsureAgentVersionCommand() *cobra.Command {
	var (
		instanceID         string
		agentServerVersion string
		clientConfig       clientcmd.ClientConfig
		includeAgents      bool
	)
	cmd := &cobra.Command{
		Use:   "ensure-agent-version",
		Short: "Ensures the server and the client agent components are running the latest version",
		Run: func(cmd *cobra.Command, args []string) {
			akpUtil := shared.NewAKPUtil(clientConfig, cmd.Context(), time.Minute)
			defer func() { _ = akpUtil.Close() }()

			if agentServerVersion == "" {
				var err error
				agentServerVersion, err = misc.AgentServerVersion(akpUtil.K8sClient)
				cli.CheckErr(err)
			}

			isArgoInstance, err := akpUtil.IsArgoInstance(instanceID)
			cli.CheckErr(err)

			ensureVersion := &AKPUtilEnsureVersion{AKPUtil: akpUtil, includeAgents: includeAgents}
			if isArgoInstance {
				_, err := ensureVersion.EnsureArgoAgentServerVersion(instanceID, agentServerVersion, false)
				cli.CheckErr(err)
			} else {
				_, err := ensureVersion.EnsureKargoAgentServerVersion(instanceID, agentServerVersion, false)
				cli.CheckErr(err)
			}
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&instanceID, instanceId, "", argoCdInstance)
	cmd.Flags().StringVar(&agentServerVersion, "agent-version", "", argoCdInstance)
	cmd.Flags().BoolVar(&includeAgents, "include-agents", false, "Whether client agent components need to be updated")
	cli.CheckErr(cmd.MarkFlagRequired(instanceId))
	return cmd
}

func NewEnsureHealthyCommand() *cobra.Command {
	var (
		clientConfig               clientcmd.ClientConfig
		stabilizationPeriodMinutes uint8
		includeAgents              bool
		instanceIDs                []string
		shard                      string
	)
	cmd := &cobra.Command{
		Use:   "ensure-healthy",
		Short: "Ensures the server and the client agent components are healthy during the stabilization period specified",
		Run: func(cmd *cobra.Command, args []string) {
			akpUtil := shared.NewAKPUtil(clientConfig, cmd.Context(), consts.Day)
			defer func() { _ = akpUtil.Close() }()

			(&AKPUtilEnsureHealthy{
				AKPUtil:                    akpUtil,
				stabilizationPeriodMinutes: stabilizationPeriodMinutes,
				includeAgents:              includeAgents,
				shard:                      shard,
			}).EnsureHealthy(instanceIDs...)
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().Uint8Var(&stabilizationPeriodMinutes, "stabilization-period", 10, "Stabilization period (in minutes) for instances and agents to stay healthy")
	cmd.Flags().BoolVar(&includeAgents, "include-agents", false, "Whether client agent components need to be checked")
	cmd.Flags().StringSliceVar(&instanceIDs, "instance-ids", []string{}, "Instance IDs, comma-separated")
	cmd.Flags().StringVar(&shard, "shard", "", "Shard name")
	return cmd
}

func NewBumpGenerationCommand() *cobra.Command {
	var (
		clientConfig clientcmd.ClientConfig
		instanceID   string
	)
	cmd := &cobra.Command{
		Use:   "bump-generation",
		Short: "Bump generation of Argo or Kargo instance and its clusters or agents to trigger re-apply + pruning",
		Run: func(cmd *cobra.Command, args []string) {
			akpUtilInstance, err := NewAKPUtilInstance(instanceID, clientConfig, cmd.Context(), time.Minute)
			cli.CheckErr(err)
			defer func() { _ = akpUtilInstance.Close() }()

			isArgoInstance, err := akpUtilInstance.IsArgoInstance(akpUtilInstance.instanceID)
			cli.CheckErr(err)

			if isArgoInstance {
				cli.CheckErr(akpUtilInstance.BumpArgoInstanceGeneration())
				cli.CheckErr(akpUtilInstance.BumpArgoClustersGeneration())
			} else {
				cli.CheckErr(akpUtilInstance.BumpKargoInstanceGeneration())
				cli.CheckErr(akpUtilInstance.BumpKargoAgentsGeneration())
			}
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&instanceID, instanceId, "", argoCdInstance)
	cli.CheckErr(cmd.MarkFlagRequired(instanceId))
	return cmd
}

func NewDecrementObservedGenerationCommand() *cobra.Command {
	var (
		clientConfig clientcmd.ClientConfig
		instanceID   string
	)
	cmd := &cobra.Command{
		Use:   "decrement-observed-generation",
		Short: "Decrement observed generation of Argo or Kargo instance and its clusters or agents to trigger re-apply but without pruning",
		Run: func(cmd *cobra.Command, args []string) {
			akpUtilInstance, err := NewAKPUtilInstance(instanceID, clientConfig, cmd.Context(), time.Minute)
			cli.CheckErr(err)
			defer func() { _ = akpUtilInstance.Close() }()

			isArgoInstance, err := akpUtilInstance.IsArgoInstance(akpUtilInstance.instanceID)
			cli.CheckErr(err)

			if isArgoInstance {
				cli.CheckErr(akpUtilInstance.DecrementArgoInstanceObservedGeneration())
				cli.CheckErr(akpUtilInstance.DecrementArgoClustersObservedGeneration())
			} else {
				cli.CheckErr(akpUtilInstance.DecrementKargoInstanceObservedGeneration())
				cli.CheckErr(akpUtilInstance.DecrementKargoAgentsObservedGeneration())
			}
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&instanceID, instanceId, "", argoCdInstance)
	cli.CheckErr(cmd.MarkFlagRequired(instanceId))
	return cmd
}

func NewChangeOwnerCommand() *cobra.Command {
	var (
		orgName      string
		clientConfig clientcmd.ClientConfig
		instanceID   string
	)
	cmd := &cobra.Command{
		Use:   "change-owner",
		Short: "Change Argo CD instance owner",
		Run: func(cmd *cobra.Command, args []string) {
			akpUtilInstance, err := NewAKPUtilInstance(instanceID, clientConfig, cmd.Context(), time.Minute)
			cli.CheckErr(err)
			defer func() { _ = akpUtilInstance.Close() }()

			repoSet := client.NewRepoSet(akpUtilInstance.PortalDBPool.DB)

			instance, err := repoSet.ArgoCDInstances().GetByID(akpUtilInstance.Ctx, instanceID)
			cli.CheckErr(err)

			org, err := repoSet.Organizations().Filter(models.OrganizationWhere.Name.EQ(orgName)).One(akpUtilInstance.Ctx)
			cli.CheckErr(err)
			instance.OrganizationOwner = org.ID

			println("Instance owner changed")
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&instanceID, instanceId, "", argoCdInstance)
	cmd.Flags().StringVar(&orgName, "organization", "", "Owner organization name")
	cli.CheckErr(cmd.MarkFlagRequired(instanceId))
	cli.CheckErr(cmd.MarkFlagRequired("organization"))
	return cmd
}

func NewK3sUpgradeCommand() *cobra.Command {
	var (
		clientConfig               clientcmd.ClientConfig
		stabilizationPeriodMinutes uint8
		percentage                 uint8
		delaySeconds               uint32
		skipConfirmation           bool
		shard                      string
		k3sSchemaMigration         string
	)
	cmd := &cobra.Command{
		Use:   "k3s-upgrade image",
		Short: "Upgrade k3s",
		Run: func(cmd *cobra.Command, args []string) {
			if len(args) != 1 {
				cmd.HelpFunc()(cmd, args)
				os.Exit(1)
			}
			k3sImage := args[0]

			// regctl image digest quay.io/akuity/rancher/k3s:v1.25.16-k3s4
			// brew install regclient
			// pnpm install -g regctl
			// https://github.com/regclient/regclient/releases
			if output, err := exec.Command("regctl", "image", "digest", k3sImage).CombinedOutput(); err != nil {
				//nolint:staticcheck
				cli.CheckErr(fmt.Errorf("failed to check %q digest:\n---------------\n%s\n---------------\n",
					k3sImage, strings.TrimSpace(string(output))))
			}

			akpUtil := shared.NewAKPUtil(clientConfig, cmd.Context(), consts.Day)
			defer func() { _ = akpUtil.Close() }()

			gradualUpdater := &AKPUtilGradualUpdate{
				AKPUtil:                          akpUtil,
				percentage:                       percentage,
				delayBetweenUpdates:              time.Duration(delaySeconds) * time.Second,
				stabilizationPeriodMinutes:       stabilizationPeriodMinutes,
				stabilizationPeriodIncludeAgents: true,
				skipConfirmation:                 skipConfirmation,
				shard:                            shard,
			}

			cli.CheckErr(gradualUpdater.K3sUpgrade(k3sImage, k3sSchemaMigration))
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().Uint8Var(&percentage, "percentage", 10, "Percentage of instances to upgrade in each batch, from 0 to 100")
	cmd.Flags().Uint8Var(&stabilizationPeriodMinutes, "stabilization-period", 10, "Stabilization period (in minutes) for instances and agents to stay healthy")
	cmd.Flags().Uint32Var(&delaySeconds, "delay", 3600, "Delay (in seconds) between batch upgrades")
	cmd.Flags().BoolVar(&skipConfirmation, "skip-confirmation", false, "Skip update confirmation for each batch")
	cmd.Flags().StringVar(&shard, "shard", "", "Shard name")
	cmd.Flags().StringVar(&k3sSchemaMigration, "k3s-schema-migration", "2", "K3s schema migration version")

	return cmd
}

func NewSetOverrideCommand() *cobra.Command {
	var (
		clientConfig    clientcmd.ClientConfig
		instanceID      string
		overrideFile    string
		removeOverrides bool
		kargo           bool
	)
	cmd := &cobra.Command{
		Use:   "set-override",
		Short: "Override Argo CD instance values that are used to generate manifests, file is a JSON Map of controlplane.DataValues type",
		Run: func(cmd *cobra.Command, args []string) {
			if overrideFile == "" && !removeOverrides {
				cli.CheckErr(fmt.Errorf("must specify either --file or set --remove-overrides to true"))
			}

			akpUtilInstance, err := NewAKPUtilInstance(instanceID, clientConfig, cmd.Context(), time.Minute)
			cli.CheckErr(err)
			defer func() { _ = akpUtilInstance.Close() }()

			if removeOverrides {
				cli.CheckErr(akpUtilInstance.RemoveInstanceOverride(kargo))
				fmt.Printf("Override removed from Instance '%s'\n", instanceID)
			} else {
				fileBytes, err := os.ReadFile(filepath.Clean(overrideFile))
				cli.CheckErr(err)
				cli.CheckErr(akpUtilInstance.AddInstanceOverride(kargo, fileBytes))
				fmt.Printf("Override applied to Instance '%s'\n", instanceID)
			}
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&instanceID, instanceId, "", argoCdInstance)
	cmd.Flags().StringVarP(&overrideFile, "file", "f", "", "Override file")
	cmd.Flags().BoolVar(&removeOverrides, "remove", false, "Remove overrides")
	cmd.Flags().BoolVar(&kargo, "kargo", false, "Instance is kargo instance")
	cli.CheckErr(cmd.MarkFlagRequired(instanceId))
	return cmd
}

func NewArgoCDAccountTokenCommand() *cobra.Command {
	var (
		clientConfig clientcmd.ClientConfig
		instanceID   string
		capability   string
	)
	cmd := &cobra.Command{
		Use:   "argocd-account-token ACCOUNT",
		Short: "Generates Argo CD account token",
		Run: func(cmd *cobra.Command, args []string) {
			if len(args) != 1 {
				cmd.HelpFunc()(cmd, args)
				os.Exit(1)
			}
			account := args[0]

			akpUtilInstance, err := NewAKPUtilInstance(instanceID, clientConfig, cmd.Context(), time.Minute)
			cli.CheckErr(err)
			defer func() { _ = akpUtilInstance.Close() }()

			repoSet := client.NewRepoSet(akpUtilInstance.PortalDBPool.DB)

			instanceConfig, err := repoSet.ArgoCDInstanceConfigs().GetByID(akpUtilInstance.Ctx, instanceID)
			cli.CheckErr(err)

			internalSpec, err := instanceConfig.GetPrivateSpec()
			cli.CheckErr(err)

			now := time.Now().UTC()
			id, err := client.NanoID(10)
			cli.CheckErr(err)
			claims := jwt.RegisteredClaims{
				IssuedAt:  jwt.NewNumericDate(now),
				Issuer:    "argocd",
				NotBefore: jwt.NewNumericDate(now),
				Subject:   account + ":" + capability,
				ID:        id,
			}
			token, err := jwt.NewWithClaims(jwt.SigningMethodHS256, claims).SignedString([]byte(internalSpec.ArgoCDServerSecretKey))
			cli.CheckErr(err)
			_, _ = fmt.Fprintln(os.Stdout, token)
		},
	}

	clientConfig = cli.AddKubectlFlagsToSet(cmd.Flags())
	cmd.Flags().StringVar(&instanceID, instanceId, "", argoCdInstance)
	cmd.Flags().StringVar(&capability, "capability", "login", "Token capability (login, apiKey)")

	cli.CheckErr(cmd.MarkFlagRequired(instanceId))
	return cmd
}
