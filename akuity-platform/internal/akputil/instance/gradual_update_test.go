package instance

import (
	"fmt"
	"testing"
)

func TestCompareK3sVersions(t *testing.T) {
	tests := []struct {
		versionA string
		versionB string
		expected int
		valid    bool
	}{
		{"v1.25.16-k3s4", "v1.25.16-k3s4", 0, true},
		{"v1.25.16-k3s1", "v1.25.16-k3s4", -1, true},
		{"v1.25.16-k3s4", "v1.27.15-k3s2", -1, true},
		{"v1.27.15-k3s3", "v1.25.16-k3s4", 1, true},
		{"v1.27.15-k3s2", "v1.27.15-k3s3", -1, true},
		{"v1.27.15-k3s3", "v1.27.15-k3s2", 1, true},
		{"v1.27.15-k3s2", "v1.27.15-k3s2", 0, true},
		{"v1.28.11-k3s2", "v1.28.11-k3s2", 0, true},
		{"v1.27.15-k3s2", "v1.27.16-k3s2", -1, true},
		{"v1.27.15-k3s2", "v1.28.11-k3s2", -1, true},
		{"v1.28.11-k3s2", "v1.27.15-k3s2", 1, true},
		{"v1.27.17-k3s2", "v1.27.16-k3s2", 1, true},
		{"", "", 0, false},
		{"invalid", "v1.27.15-k3s2", 0, false},
		{"v1.27.15-k3s2", "invalid", 0, false},
		{"v1.27.15-k3s2", "v1.27.15", 0, false},
		{"v1.27.15-k3s2", "v1.27", 0, false},
		{"v1.27.15-k3s2", "v1", 0, false},
	}

	for _, test := range tests {
		t.Run(fmt.Sprintf("'%s' vs '%s'", test.versionA, test.versionB), func(t *testing.T) {
			result, valid := compareK3sVersions(test.versionA, test.versionB)
			if result != test.expected || valid != test.valid {
				t.Errorf("compareK3sVersions(%s, %s) = (%d, %v); expected (%d, %v) instead",
					test.versionA, test.versionB, result, valid, test.expected, test.valid)
			}
		})
	}
}
