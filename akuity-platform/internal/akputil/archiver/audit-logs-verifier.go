package archiver

import (
	"database/sql"
	"fmt"
	"time"

	"github.com/akuityio/akuity-platform/internal/akputil/shared"
	"github.com/akuityio/akuity-platform/internal/utils/aws"
)

// AKPUtilAuditLogsVerifier - verifies Audit Log records journal in the "audit_log_archive" table
type AKPUtilAuditLogsVerifier struct {
	*shared.AKPUtil
}

// auditLogJournalDBRecord - single Audit Log Journal DB record in the "audit_log_archive" table
type auditLogJournalDBRecord struct {
	Id       string `boil:"id"`
	Path     string `boil:"path"`
	Checksum string `boil:"checksum"`
}

func NewAuditLogsVerifier(akputil *shared.AKPUtil) *AKPUtilAuditLogsVerifier {
	return &AKPUtilAuditLogsVerifier{AKPUtil: akputil}
}

// VerifyAuditLogRecordsJournal - verifies Audit Log journal records are recent and valid:
// * Recent = Every week from the last "staleness back in time days" is represented in the journal
// * Valid  = Every journal record refers an existing S3 object with identical checksum
func (a *AKPUtilAuditLogsVerifier) VerifyAuditLogRecordsJournal(stalenessBackInTimeDays uint16) error {
	if err := a.verifyJournalRecordsAreRecent(stalenessBackInTimeDays); err != nil {
		return err
	}

	if err := a.verifyJournalRecordsAreValid(); err != nil {
		return err
	}

	return nil
}

// verifyJournalRecordsAreRecent - verifies Audit Log journal records are not stale
func (a *AKPUtilAuditLogsVerifier) verifyJournalRecordsAreRecent(stalenessBackInTimeDays uint16) error {
	startTime := time.Now()
	startDateMonday, err := mondayBackInTime(time.Now().UTC(), stalenessBackInTimeDays)
	if err != nil {
		return err
	}

	totalRecords := 0
	for {
		rowStartTime := time.Now()
		endDateSunday := addDays(startDateMonday, 6)
		if endDateSunday.Weekday() != time.Sunday {
			return fmt.Errorf("end date %q should have fallen on Sunday", formatDate(endDateSunday))
		}

		if !endDateSunday.Before(truncateTime(startTime)) {
			// Stop checking recent records when reaching the current week, which isn't archived yet
			break
		}

		periodRecords := 0
		if err := a.PortalDBPool.DB.QueryRowContext(a.Ctx, "select count(id) from audit_log_archive where start_date = $1 and end_date = $2",
			startDateMonday, endDateSunday).Scan(&periodRecords); err != nil {
			return err
		}

		if periodRecords < 1 {
			return fmt.Errorf("no results returned for Audit Log journal records in the [%q, %q] period", startDateMonday, endDateSunday)
		}

		totalRecords += periodRecords
		a.Log.Info(fmt.Sprintf("%d Audit Log journal records", periodRecords),
			"startDate", startDateMonday,
			"endDate", endDateSunday,
			"timeMillis", time.Since(rowStartTime).Milliseconds(),
		)

		startDateMonday = addDays(startDateMonday, 7)
	}

	a.Log.Info(fmt.Sprintf("%d Audit Log journal records in the last %d days", totalRecords, stalenessBackInTimeDays),
		"timeSeconds", fmt.Sprintf("%.1f", time.Since(startTime).Seconds()))

	return nil
}

// verifyJournalRecordsAreValid - verifies all Audit Log journal records are valid
func (a *AKPUtilAuditLogsVerifier) verifyJournalRecordsAreValid() error {
	startTime := time.Now()
	totalRows := 0

	if err := a.PortalDBPool.DB.QueryRowContext(a.Ctx, "select count(id) from audit_log_archive").Scan(&totalRows); err != nil {
		return err
	}

	rows, err := a.PortalDBPool.DB.QueryContext(a.Ctx, "select id, path, checksum from audit_log_archive")
	if err != nil {
		return err
	}
	defer func(rows *sql.Rows) { _ = rows.Close() }(rows)

	rowsCounter := 0
	for rows.Next() {
		rowsCounter++
		rowStartTime := time.Now()
		row := auditLogJournalDBRecord{}
		if err := rows.Scan(&row.Id, &row.Path, &row.Checksum); err != nil {
			return err
		}

		journalRecordID := row.Id
		journalRecordS3Path := row.Path
		journalRecordChecksum := row.Checksum
		invalidRecordPrefix := fmt.Sprintf("invalid Audit Log journal record %q", journalRecordID)

		bucketName, objectKey, err := aws.SplitS3Path(journalRecordS3Path, a.Log)
		if err != nil {
			return err
		}

		s3Checksum, sizeInBytes, err := aws.S3ObjectAttributes(a.Ctx, bucketName, objectKey)
		if err != nil {
			return err
		}

		if s3Checksum == "" {
			return fmt.Errorf("%s - S3 object at %q is missing", invalidRecordPrefix, journalRecordS3Path)
		}

		if s3Checksum != journalRecordChecksum {
			return fmt.Errorf("%s - checksum %q != S3 checksum %q", invalidRecordPrefix, journalRecordChecksum, s3Checksum)
		}

		if sizeInBytes < 1 {
			return fmt.Errorf("%s - %q size is zero", invalidRecordPrefix, journalRecordS3Path)
		}

		a.Log.Info(fmt.Sprintf("Audit Log journal record is valid (record %d of %d)", rowsCounter, totalRows),
			"journalRecordID", journalRecordID,
			"s3Path", journalRecordS3Path,
			"checksum", journalRecordChecksum,
			"sizeInKBytes", fmt.Sprintf("%.2f", float64(sizeInBytes)/1024),
			"timeMillis", time.Since(rowStartTime).Milliseconds(),
		)
	}

	a.Log.Info(fmt.Sprintf("All %d Audit Log journal records are valid", rowsCounter),
		"timeSeconds", fmt.Sprintf("%.1f", time.Since(startTime).Seconds()))
	return nil
}
