package archiver

import (
	"github.com/akuityio/akuity-platform/internal/akputil/shared"
)

type AKPUtilAuditLogsDeleter struct {
	*shared.AKPUtil
}

func NewAuditLogsDeleter(akputil *shared.AKPUtil) *AKPUtilAuditLogsDeleter {
	return &AKPUtilAuditLogsDeleter{AKPUtil: akputil}
}

// SoftDeleteAuditLogs - soft deletes the Audit Log records
func (a *AKPUtilAuditLogsDeleter) SoftDeleteAuditLogs(orgId string, retentionPeriod uint16) error {
	if orgId == "" {
		return a.executeStatement(updateAuditLogsIsDeletedAllOrgs, true, retentionPeriod)
	}
	return a.executeStatement(updateAuditLogsIsDeletedSingleOrg, true, retentionPeriod, orgId)
}

// UndeleteAuditLogs - undeletes the Audit Log records
func (a *AKPUtilAuditLogsDeleter) UndeleteAuditLogs(orgId string, retentionPeriod uint16) error {
	if orgId == "" {
		return a.executeStatement(updateAuditLogsIsDeletedAllOrgs, false, retentionPeriod)
	}
	return a.executeStatement(updateAuditLogsIsDeletedSingleOrg, false, retentionPeriod, orgId)
}

// HardDeleteAuditLogs - actually deletes Audit Log records from the DB
func (a *AKPUtilAuditLogsDeleter) HardDeleteAuditLogs(orgId string, retentionPeriod uint16) error {
	if orgId == "" {
		return a.executeStatement(deleteAuditLogsAllOrgs, retentionPeriod)
	}
	return a.executeStatement(deleteAuditLogsSingleOrg, retentionPeriod, orgId)
}

// executeStatement - executes the statement specified with the arguments provided
func (a *AKPUtilAuditLogsDeleter) executeStatement(statement string, args ...any) error {
	result, err := a.PortalDBPool.DB.ExecContext(a.Ctx, statement, args...)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	a.Log.Info("Rows affected", "rows", rowsAffected)
	return nil
}
