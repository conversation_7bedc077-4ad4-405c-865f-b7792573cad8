package auth

import (
	"context"
	"net/http"
	"net/url"

	"github.com/pkg/errors"
	"golang.org/x/oauth2"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	"github.com/akuityio/akuity-platform/internal/utils/oidc"
	authv1 "github.com/akuityio/akuity-platform/pkg/api/gen/auth/v1"
	metadatautil "github.com/akuityio/akuity-platform/pkg/utils/grpc/metadata"
)

type AuthV1Server struct {
	authv1.AuthServiceServer

	ds accesscontrol.DeviceService
	ts accesscontrol.TokenService
}

func NewAuthV1Server(
	ds accesscontrol.DeviceService,
	ts accesscontrol.TokenService,
) *AuthV1Server {
	return &AuthV1Server{
		ds: ds,
		ts: ts,
	}
}

func (s *AuthV1Server) GetDeviceCode(ctx context.Context, req *authv1.GetDeviceCodeRequest) (*authv1.GetDeviceCodeResponse, error) {
	code, err := s.ds.GetCode(ctx)
	if err != nil {
		return nil, err
	}
	return &authv1.GetDeviceCodeResponse{
		DeviceCode:              code.DeviceCode,
		UserCode:                code.UserCode,
		VerificationUri:         code.VerificationURI,
		VerificationUriComplete: code.VerificationURIComplete,
		ExpiresInSeconds:        int32(code.ExpireSeconds),
		IntervalSeconds:         int32(code.IntervalSeconds),
	}, nil
}

func (s *AuthV1Server) GetDeviceToken(ctx context.Context, req *authv1.GetDeviceTokenRequest) (*authv1.GetDeviceTokenResponse, error) {
	token, err := s.ds.GetToken(ctx, req.DeviceCode)
	if err != nil {
		var aErr *accesscontrol.Auth0ErrorResponse
		if errors.As(err, &aErr) {
			switch aErr.Type {
			case accesscontrol.Auth0ErrAccessDenied:
				return nil, status.Error(codes.PermissionDenied, aErr.Description)
			case accesscontrol.Auth0ErrAuthorizationPending:
				return nil, status.Error(codes.FailedPrecondition, aErr.Description)
			case accesscontrol.Auth0ErrExpiredToken:
				return nil, status.Error(codes.Aborted, aErr.Description)
			case accesscontrol.Auth0ErrSlowDown:
				return nil, status.Error(codes.Unavailable, aErr.Description)
			}
		}
		return nil, err
	}
	return &authv1.GetDeviceTokenResponse{
		AccessToken: token.IDToken,
	}, nil
}

func (s *AuthV1Server) RefreshAccessToken(ctx context.Context, _ *authv1.RefreshAccessTokenRequest) (*authv1.RefreshAccessTokenResponse, error) {
	md, _ := metadata.FromIncomingContext(ctx)
	refreshToken, ok := metadatautil.GetRefreshToken(md)
	if !ok {
		return nil, shared.ErrUnauthenticated
	}

	token, err := s.ts.RefreshToken(ctx, refreshToken)
	if err != nil {
		// RetrieveError is the error returned when the token endpoint returns a
		// non-2XX HTTP status code or populates RFC 6749's 'error' parameter.
		// https://datatracker.ietf.org/doc/html/rfc6749#section-5.2
		var rErr *oauth2.RetrieveError
		if errors.As(err, &rErr) {
			return nil, status.Error(codes.PermissionDenied, rErr.Error())
		}
		return nil, err
	}

	header := metadata.Pairs()
	metadatautil.SetUserToken(header, token.AccessToken)
	metadatautil.SetRefreshToken(header, token.RefreshToken)
	if err := grpc.SendHeader(ctx, header); err != nil {
		return nil, err
	}
	return &authv1.RefreshAccessTokenResponse{}, nil
}

func (s *AuthV1Server) GetOIDCProviderDetails(
	ctx context.Context,
	req *authv1.GetOIDCProviderDetailsRequest,
) (*authv1.GetOIDCProviderDetailsResponse, error) {
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}

	if _, err := url.Parse(req.GetDiscoveryUrl()); err != nil {
		return nil, status.Error(codes.InvalidArgument, errors.Wrap(err, "parse url").Error())
	}

	details, err := oidc.GetProviderDetails(ctx, http.DefaultClient, req.GetDiscoveryUrl())
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}
	return &authv1.GetOIDCProviderDetailsResponse{
		Issuer:                details.Issuer,
		AuthorizationEndpoint: details.AuthorizationEndpoint,
		TokenEndpoint:         details.TokenEndpoint,
		UserinfoEndpoint:      details.UserInfoEndpoint,
		JwksUri:               details.JWKSURI,
	}, nil
}
