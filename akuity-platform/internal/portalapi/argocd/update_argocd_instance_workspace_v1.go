package argocd

import (
	"context"
	"fmt"
	"net/http"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) UpdateInstanceWorkspace(
	ctx context.Context,
	req *argocdv1.UpdateInstanceWorkspaceRequest,
) (*argocdv1.UpdateInstanceWorkspaceResponse, error) {
	instanceID := req.GetId()
	srcWorkspace := req.GetWorkspaceId()
	dstWorkspace := req.GetNewWorkspaceId()
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionDeleteWorkspaceInstances(srcWorkspace, instanceID),
		accesscontrol.NewActionCreateWorkspaceInstances(dstWorkspace),
	); err != nil {
		return nil, err
	}

	teamSvc := teams.NewService(s.db)
	workspaceSvc := workspaces.NewService(s.db, teamSvc, s.cfg.FeatureGatesSource)
	_, err := workspaceSvc.GetWorkspace(ctx, dstWorkspace)
	if err != nil {
		if instances.IsNotFoundErr(err) {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "workspace not found")
		}
		return nil, fmt.Errorf("failed to get workspace: %w", err)
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())

	inst, err := instanceSvc.GetInstanceByID(ctx, instanceID)
	if err != nil {
		return nil, err
	}
	if inst.WorkspaceID.String == dstWorkspace {
		return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "can't transfer into same workspace")
	}
	_, err = instanceSvc.UpdateInstanceMetadata(ctx, inst.ID, inst.Name, inst.Description.String, dstWorkspace)
	if err != nil {
		return nil, err
	}
	return &argocdv1.UpdateInstanceWorkspaceResponse{}, nil
}
