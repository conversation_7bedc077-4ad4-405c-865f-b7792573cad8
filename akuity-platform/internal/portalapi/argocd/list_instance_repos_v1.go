package argocd

import (
	"context"
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/argoproj"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) ListInstanceRepos(
	ctx context.Context,
	req *argocdv1.ListInstanceReposRequest,
) (*argocdv1.ListInstanceReposResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId()))
	if err != nil {
		return nil, err
	}
	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	instance, err := instanceSvc.GetInstanceByID(ctx, req.GetInstanceId())
	if err != nil {
		return nil, err
	}

	var k3sKubeClient kubernetes.Interface
	if instance.Shard == "" {
		tnt, err := client.NewArgoCDTenant(s.hostRestConfig, *s.log, req.GetInstanceId())
		if err != nil {
			return nil, err
		}
		k3sKubeClient, err = tnt.ControlPlaneKubeClientset(ctx)
		if err != nil {
			return nil, err
		}
	} else {
		privateSpec, err := instance.GetPrivateSpec()
		if err != nil {
			return nil, err
		}

		restConfig, err := privateSpec.GetRestConfig()
		if err != nil {
			return nil, err
		}
		k3sKubeClient, err = kubernetes.NewForConfig(restConfig)
		if err != nil {
			return nil, err
		}
	}
	resp, err := k3sKubeClient.CoreV1().Secrets(common.K3sArgoCDNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s=%s", argoproj.LabelKeySecretType, argoproj.LabelValueSecretTypeRepository),
	})
	if err != nil {
		return nil, err
	}
	repos := []*argocdv1.Repository{}
	for _, secret := range resp.Items {
		repo := &argocdv1.Repository{}
		if url, ok := secret.Data["url"]; ok {
			s := string(url)
			repo.Repo = &s
		}
		if project, ok := secret.Data["project"]; ok {
			s := string(project)
			repo.Project = &s
		}
		if t, ok := secret.Data["type"]; ok {
			s := string(t)
			repo.Type = &s
		}
		repos = append(repos, repo)
	}

	return &argocdv1.ListInstanceReposResponse{Repos: repos}, nil
}
