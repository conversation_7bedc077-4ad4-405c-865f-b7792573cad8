package argocd

import (
	"context"
	"net/http"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) DeleteInstanceAddon(
	ctx context.Context,
	req *argocdv1.DeleteInstanceAddonRequest,
) (*argocdv1.DeleteInstanceAddonResponse, error) {
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}
	instanceID := req.GetInstanceId()
	workspaceID := req.GetWorkspaceId()
	orgID := req.GetOrganizationId()
	action := accesscontrol.NewActionUpdateWorkspaceInstances(workspaceID, req.GetInstanceId())
	if req.GetInstanceName() != "" {
		action = accesscontrol.NewActionGetWorkspaceInstances(workspaceID, req.GetInstanceName())
		instanceSvc := s.newInstanceService(ctx, orgID)
		instance, err := instanceSvc.GetInstanceByName(ctx, req.GetInstanceName())
		if err != nil {
			if instances.IsNotFoundErr(err) {
				return nil, shared.NewPermissionDeniedErr(action)
			}
			return nil, err
		}
		workspaceID = instance.WorkspaceID.String
		instanceID = instance.InstanceID
	}

	instanceCfg, err := s.repoSet.ArgoCDInstanceConfigs().GetByID(ctx, instanceID)
	if err != nil {
		return nil, err
	}

	// only allow to delete addon if instance is running ak version > 59
	isAkp, version := misc.GetAKPVersion(instanceCfg.Version.String)
	if !isAkp || version < 59 {
		return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "addons delete supported only for AKP argocd version instances, running ak.59 or higher")
	}

	ok, err := enforcer.EnforceAction(ctx, accesscontrol.NewActionUpdateWorkspaceInstances(workspaceID, instanceID))
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, shared.NewPermissionDeniedErr(action)
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, orgID,
		accesscontrol.NewActionUpdateWorkspaceInstances(workspaceID, instanceID)); err != nil {
		return nil, err
	}

	if err := s.addonsSvc.DeleteAddon(ctx, req.GetId()); err != nil {
		return nil, err
	}
	return &argocdv1.DeleteInstanceAddonResponse{}, nil
}
