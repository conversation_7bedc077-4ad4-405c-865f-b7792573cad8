package argocd

import (
	"context"
	"fmt"
	"net/http"
	"strconv"

	"gopkg.in/yaml.v2"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func toResourceCustomization(resourceCustomization *argocdv1.ResourceCustomizationConfig) (models.ResourceCustomization, error) {
	result := models.ResourceCustomization{
		Group:             resourceCustomization.Group,
		Kind:              resourceCustomization.Kind,
		Health:            resourceCustomization.Health,
		IgnoreDifferences: resourceCustomization.IgnoreDifferences,
		Actions:           resourceCustomization.Actions,
		KnownTypeFields:   resourceCustomization.KnownTypeFields,
	}
	if resourceCustomization.UseOpenLibs != nil {
		result.UseOpenLibs = strconv.FormatBool(*resourceCustomization.UseOpenLibs)
	}
	return result, nil
}

func ValidateResourceFormat(resource *argocdv1.ResourceCustomizationConfig) error {
	if resource.IgnoreDifferences != "" {
		ignoreDiffSchema := OverrideIgnoreDiff{}
		if err := yaml.Unmarshal([]byte(resource.IgnoreDifferences), &ignoreDiffSchema); err != nil {
			return fmt.Errorf("invalid ignoreDifferences yaml for resource %s/%s", resource.Group, resource.Kind)
		}
	}

	if resource.Actions != "" {
		actionSchema := OverrideActions{}
		if err := yaml.Unmarshal([]byte(resource.Actions), &actionSchema); err != nil {
			return fmt.Errorf("invalid action yaml for resource %s/%s", resource.Group, resource.Kind)
		}
	}

	if resource.KnownTypeFields != "" {
		ktfSchema := []KnownTypeField{}
		if err := yaml.Unmarshal([]byte(resource.KnownTypeFields), &ktfSchema); err != nil {
			return fmt.Errorf("invalid knownTypeFields yaml for resource %s/%s", resource.Group, resource.Kind)
		}
	}

	return nil
}

// https://github.com/argoproj/argo-cd/blob/1aebc93ad3a0b87dea7b9d8f9fbddcd56ccc3d70/pkg/apis/application/v1alpha1/types.go#L1426
type OverrideIgnoreDiff struct {
	JSONPointers          []string `yaml:"jsonPointers"`
	JQPathExpressions     []string `yaml:"jqPathExpressions"`
	ManagedFieldsManagers []string `yaml:"managedFieldsManagers"`
}

// https://github.com/argoproj/argo-cd/blob/7327093b66274a99661eed2f1947a22c6f4d9bfb/pkg/apis/application/v1alpha1/types.go#L2306
type KnownTypeField struct {
	Field string `json:"field,omitempty" protobuf:"bytes,1,opt,name=field"`
	Type  string `json:"type,omitempty" protobuf:"bytes,2,opt,name=type"`
}

type OverrideActions struct {
	ActionDiscoveryLua string              `yaml:"discovery.lua,omitempty"`
	Definitions        []ActionDefinitions `yaml:"definitions"`
}

type ActionDefinitions struct {
	Name      string `yaml:"name"`
	ActionLua string `yaml:"action.lua"`
}

func (s *ArgoCDV1Server) UpdateInstanceResourceCustomizations(
	ctx context.Context,
	req *argocdv1.UpdateInstanceResourceCustomizationsRequest,
) (*argocdv1.UpdateInstanceResourceCustomizationsResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	resourceCustomizations := req.GetResources()
	for i, resource := range resourceCustomizations {
		if resource.Kind == "" {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "malformed resource found with no kind, no operation will be executed for this request")
		}

		if err := ValidateResourceFormat(resource); err != nil {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, err.Error())
		}

		for j := i + 1; j < len(resourceCustomizations); j++ {
			_resource := resourceCustomizations[j]

			if resource.Group == _resource.Group && resource.Kind == _resource.Kind {
				return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("duplicate resources not allowed. group: %s, kind: %s", resource.Group, resource.Kind))
			}
		}
	}

	var nonVoidResources []*argocdv1.ResourceCustomizationConfig
	for _, resource := range resourceCustomizations {
		if resource.Health != "" || resource.Actions != "" || resource.IgnoreDifferences != "" || resource.KnownTypeFields != "" || resource.UseOpenLibs != nil {
			nonVoidResources = append(nonVoidResources, resource)
		}
	}

	resourceCustomizationModel := []models.ResourceCustomization{}
	for _, resource := range nonVoidResources {
		resourceModel, _ := toResourceCustomization(resource)
		resourceCustomizationModel = append(resourceCustomizationModel, resourceModel)
	}

	if _, err := instanceSvc.UpsertResourceCustomization(ctx, req.GetId(), resourceCustomizationModel); err != nil {
		return nil, err
	}
	return &argocdv1.UpdateInstanceResourceCustomizationsResponse{}, nil
}
