package argocd

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) GetAIAssistantUsageStats(ctx context.Context, req *argocdv1.GetAIAssistantUsageStatsRequest) (*argocdv1.GetAIAssistantUsageStatsResponse, error) {
	if !s.featSvc.GetFeatureStatuses(ctx, nil).GetAiAssistantStats().Enabled() {
		return &argocdv1.GetAIAssistantUsageStatsResponse{}, nil
	}

	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	svc := s.newInstanceService(ctx, req.GetOrganizationId())

	instanceIds := req.GetInstanceId()
	if len(instanceIds) == 0 {
		instanceModels, err := svc.GetAllInstances(ctx)
		if err != nil {
			return nil, err
		}
		for _, i := range instanceModels {
			action := accesscontrol.NewActionGetWorkspaceInstances(i.WorkspaceID.String, i.GetID())
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return nil, err
			}
			if !ok {
				continue
			}
			instanceIds = append(instanceIds, i.ID)
		}
	} else {
		for _, id := range instanceIds {
			inst, err := svc.GetInstanceByID(ctx, id)
			if err != nil {
				return nil, err
			}
			action := accesscontrol.NewActionGetWorkspaceInstances(inst.WorkspaceID.String, id)
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return nil, err
			}
			if !ok {
				return nil, shared.NewPermissionDeniedErr(action)
			}
		}
	}

	stats, err := svc.GetAIAssistantUsageStats(ctx, instanceIds)
	if err != nil {
		return nil, err
	}

	return &argocdv1.GetAIAssistantUsageStatsResponse{
		TotalConversations:    uint32(stats.TotalConversations),
		ResolvedConversations: uint32(stats.ResolvedConversations),
	}, nil
}
