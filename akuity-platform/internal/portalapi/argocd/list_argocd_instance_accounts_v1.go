package argocd

import (
	"context"
	"fmt"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) ListInstanceAccounts(
	ctx context.Context,
	req *argocdv1.ListInstanceAccountsRequest,
) (*argocdv1.ListInstanceAccountsResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	instance, err := instanceSvc.GetInstanceByID(ctx, req.GetInstanceId())
	if err != nil {
		if instances.IsNotFoundErr(err) {
			return nil, shared.ErrNotFound
		}
		return nil, fmt.Errorf("get instance: %w", err)
	}

	cm, err := instance.GetArgoCDConfigMap()
	if err != nil {
		return nil, err
	}
	accounts, _ := types.MapSlice(cm.Accounts, func(in models.Account) (*argocdv1.InstanceAccount, error) {
		return mapInstanceAccountModelToRPCModel(&in), nil
	})
	return &argocdv1.ListInstanceAccountsResponse{
		Accounts: accounts,
	}, nil
}
