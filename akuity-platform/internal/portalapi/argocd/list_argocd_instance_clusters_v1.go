package argocd

import (
	"context"
	"fmt"
	"strings"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/services/permissions"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	"github.com/akuityio/akuity-platform/internal/version"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

const clusterMaxLimit = 1000

func (s *ArgoCDV1Server) ListInstanceClusters(
	ctx context.Context,
	req *argocdv1.ListInstanceClustersRequest,
) (*argocdv1.ListInstanceClustersResponse, error) {
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	action := accesscontrol.NewActionGetInstanceClusters(req.GetWorkspaceId(), req.GetInstanceId(), accesscontrol.ResourceAny)

	// first we are checking that user owns requested instance
	ok, err := enforcer.CheckResourceOwnership(ctx, permissions.ObjectWorkspaceInstances, accesscontrol.FormatWorkspaceResource(req.GetWorkspaceId(), req.GetInstanceId()))
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, shared.NewPermissionDeniedErr(action)
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())

	filters := req.GetFilter()
	if filters == nil {
		filters = &argocdv1.ClusterFilter{}
	}

	mods, err := getClusterFilterMods(filters)
	if err != nil {
		return nil, err
	}
	workspaceID := req.GetWorkspaceId()
	if workspaceID == "" {
		inst, err := instanceSvc.GetInstanceByID(ctx, req.GetInstanceId())
		if err != nil {
			return nil, err
		}
		workspaceID = inst.WorkspaceID.String
	}
	resources, err := enforcer.GetResources(action.Object, action.Verb)
	if err != nil {
		return nil, err
	}
	resources, err = shared.FilterResources(req.GetInstanceId(), workspaceID, resources...)
	if err != nil {
		return nil, err
	}

	if len(resources) == 0 {
		return nil, shared.NewPermissionDeniedErr(action)
	}

	policyMods, err := instances.ClustersWhereNameInMod(req.GetInstanceId(), resources...)
	if err != nil {
		return nil, err
	}
	mods = append(mods, policyMods...)

	totalCount, err := instanceSvc.CountInstanceClusters(ctx, req.GetInstanceId(), mods...)
	if err != nil {
		return nil, err
	}

	if filters.GetLimit() > 0 {
		limit := int(filters.GetLimit())
		if limit > clusterMaxLimit {
			limit = clusterMaxLimit
		}
		mods = append(mods, qm.Limit(limit))
	}
	if filters.GetOffset() > 0 {
		mods = append(mods, qm.Offset(int(filters.GetOffset())))
	}
	mods = append(mods, qm.OrderBy(models.ArgoCDClusterColumns.Name+" asc"))
	clusterModels, err := instanceSvc.GetInstanceClusters(ctx, req.GetInstanceId(), mods...)
	if err != nil {
		return nil, err
	}

	clusters, err := types.MapSlice(clusterModels, func(in *models.ArgoCDCluster) (*argocdv1.Cluster, error) {
		return argocdutil.NewArgoCDClusterV1(*in, s.cfg.ClusterProgressingDeadline, s.clusterAutoscalerConfig)
	})
	if err != nil {
		return nil, err
	}
	return &argocdv1.ListInstanceClustersResponse{
		Clusters:   clusters,
		TotalCount: totalCount,
	}, nil
}

func getClusterFilterMods(filter *argocdv1.ClusterFilter) ([]qm.QueryMod, error) {
	var mods []qm.QueryMod
	if filter == nil {
		return mods, nil
	}

	if nameLike := filter.GetNameLike(); nameLike != "" {
		mods = append(mods, qm.Where(models.ArgoCDClusterColumns.Name+" like ?", "%"+nameLike+"%"))
	}

	if agentStatus := filter.GetAgentStatus(); len(agentStatus) > 0 {
		var statusLit []interface{}
		queryString := ""
		for _, stat := range agentStatus {
			agentStat, err := mapRpcPhaseToTenantPhase(stat)
			if err != nil {
				return nil, err
			}
			statusLit = append(statusLit, string(*agentStat))
			queryString = fmt.Sprintf("%v?,", queryString)
		}
		mods = append(mods, qm.Where(fmt.Sprintf(`case when
        (status_agent_state -> 'status' ->> 'PriorityStatus' is null) or
        extract(epoch from interval '%d seconds' - age(now(), to_timestamp(status_agent_state ->> 'observedAt', 'YYYY-MM-DD"T"HH24:MI:SS"Z"'))) < 0
			then 'Unknown'
		else
			status_agent_state -> 'status' ->> 'PriorityStatus'
		end in (%v)`, int64(models.ClusterStatusExpirationDuration.Seconds()), strings.TrimSuffix(queryString, ",")), statusLit...))
	}

	if agentVersions := filter.GetAgentVersion(); len(agentVersions) > 0 {
		mods = append(mods, database.WhereJSONin(`status_agent_state ->> 'version'`, agentVersions))
	}

	if excludeAgentVersion := filter.GetExcludeAgentVersion(); excludeAgentVersion != "" {
		mods = append(mods, qm.Where("coalesce(spec ->> 'targetVersion', ?) != ?", version.GetLatestAgentVersion(), excludeAgentVersion))
		// ignore direct clusters when filtering for outdated agent versions
		mods = append(mods, qm.Where("coalesce(spec -> 'directClusterSpec' ->> 'type', '') = ''"))
	}

	if filter.GetOutdatedManifest() {
		mods = append(mods, qm.Where("status_agent_state->>'status' is null or status_agent_state->'status'->>'minObservedGeneration' != generation::text"))
	}

	if argocdVersions := filter.GetArgocdVersion(); len(argocdVersions) > 0 {
		mods = append(mods, database.WhereJSONin(`status_agent_state ->> 'argoCDVersion'`, argocdVersions))
	}

	if namespaces := filter.GetNamespace(); len(namespaces) > 0 {
		mods = append(mods, database.WhereJSONin("namespace", namespaces))
	}

	if filter.GetNamespaceScoped() {
		mods = append(mods, qm.Where("namespace_scoped = true"))
	}

	if labels := filter.GetLabels(); len(labels) > 0 {
		for labelKey, labelVal := range labels {
			mods = append(mods, qm.Where("spec -> 'labels' ->> ? = ?", labelKey, labelVal))
		}
	}

	if filter.GetNeedReapply() {
		mods = append(mods, qm.Where("status_agent_state is not null and (coalesce((status_agent_state ->> 'lastUserAppliedGeneration')::integer, 0) < readonly_settings_changed_generation)"))
	}

	if filter.GetExcludeDirectCluster() {
		mods = append(mods, qm.Where("spec->>'directClusterSpec' is null"))
	}

	return mods, nil
}
