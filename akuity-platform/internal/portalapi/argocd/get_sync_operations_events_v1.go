package argocd

import (
	"context"
	"fmt"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) GetSyncOperationsEvents(ctx context.Context, req *argocdv1.GetSyncOperationsEventsRequest) (*argocdv1.GetSyncOperationsEventsResponse, error) {
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	filters := req.GetFilter()
	if filters == nil {
		filters = &argocdv1.SyncOperationFilter{}
	}

	svc := s.newInstanceService(ctx, req.GetOrganizationId())

	instanceIds := filters.GetInstanceId()
	if len(instanceIds) > 0 {
		for _, id := range instanceIds {
			inst, err := svc.GetInstanceByID(ctx, id)
			if err != nil {
				return nil, err
			}
			action := accesscontrol.NewActionGetWorkspaceInstances(inst.WorkspaceID.String, inst.ID)
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return nil, err
			}
			if !ok {
				return nil, shared.NewPermissionDeniedErr(action)
			}
		}
	} else if len(filters.GetInstanceNames()) > 0 {
		for _, name := range filters.GetInstanceNames() {
			inst, err := svc.GetInstanceByName(ctx, name)
			if err != nil {
				return nil, err
			}
			action := accesscontrol.NewActionGetWorkspaceInstances(inst.WorkspaceID.String, inst.ID)
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return nil, err
			}
			if !ok {
				return nil, shared.NewPermissionDeniedErr(action)
			}
		}
	} else {
		// only org owner have access to sync operations.
		action := accesscontrol.NewActionGetOrganizationAllInstances()
		ok, err := enforcer.EnforceAction(ctx, action)
		if err != nil {
			return nil, err
		}
		if !ok {
			return nil, shared.NewPermissionDeniedErr(action)
		}
	}

	mods, err := GetSyncFilters(filters, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	// if field is specified skip real query and just return all fields
	if req.GetField() != argocdv1.SyncOperationField_SYNC_OPERATION_FIELD_UNSPECIFIED && MapField(req.GetField(), len(filters.GetInstanceNames()) > 0) != "" {
		field := MapField(req.GetField(), len(filters.GetInstanceNames()) > 0)
		// limit to 10
		mods = append(mods, qm.Limit(10))
		if req.FieldLike != "" {
			mods = append(mods, qm.Where(fmt.Sprintf("%v like ?", field), req.FieldLike))
		}
		res, err := svc.GetSyncOperationEventsField(ctx, mods, field)
		if err != nil {
			return nil, err
		}
		return &argocdv1.GetSyncOperationsEventsResponse{
			FieldResult: res,
		}, nil

	}

	count, err := svc.GetSyncOperationEventsCount(ctx, mods)
	if err != nil {
		return nil, err
	}

	// limit max result count
	maxLimit := misc.MaxPaginationLimit
	if req.Limit != nil {
		if req.GetLimit() < maxLimit {
			maxLimit = req.GetLimit()
		}
	}
	mods = append(mods, qm.Limit(int(maxLimit)))

	if req.Offset != nil {
		mods = append(mods, qm.Offset(int(req.GetOffset())))
	}

	events, err := svc.GetSyncOperationEvents(ctx, mods)
	if err != nil {
		return nil, err
	}

	eventResp := []*argocdv1.SyncOperationEvent{}

	for _, event := range events {
		e, err := MapEvent(event)
		if err != nil {
			return nil, err
		}
		eventResp = append(eventResp, e)
	}

	return &argocdv1.GetSyncOperationsEventsResponse{
		SyncOperationEvents: eventResp,
		Count:               count,
	}, nil
}

func MapEvent(modelEvent *models.ArgoCDSyncOperation) (*argocdv1.SyncOperationEvent, error) {
	if modelEvent == nil {
		return nil, nil
	}
	details, err := modelEvent.GetDetails()
	if err != nil {
		return nil, err
	}
	lastOccurredTimestamp := ""
	if !modelEvent.LastOccurredTimestamp.Time.IsZero() {
		lastOccurredTimestamp = modelEvent.LastOccurredTimestamp.Time.String()
	}
	duration := uint32(modelEvent.EndTime.Sub(modelEvent.StartTime).Seconds())
	if modelEvent.Duration.Valid {
		duration = uint32(modelEvent.Duration.Int)
	}
	event := &argocdv1.SyncOperationEvent{
		Id:              modelEvent.ID,
		InstanceId:      modelEvent.InstanceID,
		ApplicationName: modelEvent.ApplicationName,
		StartTime:       modelEvent.StartTime.String(),
		EndTime:         modelEvent.EndTime.String(),
		ResultPhase:     modelEvent.ResultPhase,
		ResultMessage:   modelEvent.ResultMessage,
		Details: &argocdv1.SyncOperationEventDetails{
			Labels:      details.Labels,
			Project:     details.Project,
			Repository:  details.Repository,
			Revision:    details.Revision,
			Prune:       details.Prune,
			DryRun:      details.DryRun,
			SyncOptions: details.SyncOptions,
			InitiatedBy: &argocdv1.OperationInitiator{
				Username:  details.InitiatedBy.Username,
				Automated: details.InitiatedBy.Automated,
			},
		},
		Count:                 uint32(modelEvent.Count),
		Duration:              duration,
		LastOccurredTimestamp: lastOccurredTimestamp,
	}
	return event, nil
}

func MapField(field argocdv1.SyncOperationField, instaceLevel bool) string {
	switch field {
	case argocdv1.SyncOperationField_SYNC_OPERATION_FIELD_APPS:
		return "sync.application_name"
	case argocdv1.SyncOperationField_SYNC_OPERATION_FIELD_PROJECTS:
		return "sync.details ->> 'project'"
	case argocdv1.SyncOperationField_SYNC_OPERATION_FIELD_INITIATORS:
		return "sync.details -> 'initiatedBy' ->> 'username'"
	case argocdv1.SyncOperationField_SYNC_OPERATION_FIELD_REPOS:
		return "sync.details ->> 'repository'"
	case argocdv1.SyncOperationField_SYNC_OPERATION_FIELD_INSTANCE_NAMES:
		if !instaceLevel {
			return "instance.name"
		}
	}
	return ""
}
