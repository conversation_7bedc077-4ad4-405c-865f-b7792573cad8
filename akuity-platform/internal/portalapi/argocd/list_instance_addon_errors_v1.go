package argocd

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) ListInstanceAddonErrors(
	ctx context.Context,
	req *argocdv1.ListInstanceAddonErrorsRequest,
) (*argocdv1.ListInstanceAddonErrorsResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId())); err != nil {
		return nil, err
	}

	limit := req.GetLimit()
	offset := req.GetOffset()
	if limit == 0 {
		limit = addonDefaultLimit
	} else if limit > addonRepoMaxLimit {
		limit = addonMaxLimit
	}

	if offset < 0 {
		offset = 0
	}

	addonErrors, count, err := s.addonsSvc.ListAddonErrors(ctx, req.GetInstanceId(), req.GetId(), int(offset), int(limit))
	if err != nil {
		return nil, err
	}

	addonList := map[string]*argocdv1.AddonErrorList{}
	for cluster, errs := range addonErrors {
		clusterErrors := make([]*argocdv1.AddonError, len(errs))
		for j, err := range errs {
			clusterErrors[j] = &argocdv1.AddonError{
				Type:  err.Type,
				Error: err.Error,
			}
		}
		addonList[cluster] = &argocdv1.AddonErrorList{Errors: clusterErrors}
	}

	return &argocdv1.ListInstanceAddonErrorsResponse{
		Errors:     addonList,
		TotalCount: count,
	}, nil
}
