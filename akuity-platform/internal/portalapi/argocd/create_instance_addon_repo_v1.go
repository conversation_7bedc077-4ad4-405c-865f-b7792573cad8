package argocd

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/addons"
	"github.com/akuityio/akuity-platform/models/models"
	utilStatus "github.com/akuityio/akuity-platform/models/util/status"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	reconciliationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/status/reconciliation/v1"
)

func (s *ArgoCDV1Server) CreateInstanceAddonRepo(
	ctx context.Context,
	req *argocdv1.CreateInstanceAddonRepoRequest,
) (*argocdv1.CreateInstanceAddonRepoResponse, error) {
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetFleetManagement().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "this feature is not available")
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId())); err != nil {
		return nil, err
	}
	spec := req.GetSpec()
	if spec == nil || spec.RepoUrl == "" {
		return nil, status.Error(codes.InvalidArgument, "repoUrl is required field")
	}

	addonModel, err := s.addonsSvc.CreateAddonRepo(ctx, req.GetOrganizationId(), req.GetInstanceId(), spec.RepoUrl, spec.Revision)
	if err != nil {
		if errors.Is(err, addons.ErrRepoUrlAlreadyExists) {
			return nil, status.Error(codes.AlreadyExists, err.Error())
		}
		return nil, err
	}

	addon, err := newAddonRepoV1(*addonModel)
	if err != nil {
		return nil, err
	}

	return &argocdv1.CreateInstanceAddonRepoResponse{AddonRepo: addon}, nil
}

func newAddonRepoV1(addonRepo models.AddonRepo) (*argocdv1.AddonRepo, error) {
	addonRepoV1 := argocdv1.AddonRepo{
		Id:             addonRepo.ID,
		OrganizationId: addonRepo.OrganizationID,
		InstanceId:     addonRepo.InstanceID,
		Generation:     uint32(addonRepo.Generation),
	}
	if !addonRepo.DeletionTimestamp.IsZero() {
		addonRepoV1.DeleteTime = timestamppb.New(addonRepo.DeletionTimestamp.Time)
	}
	spec, err := addonRepo.GetSpec()
	if err != nil {
		return nil, err
	}
	if spec != nil {
		addonRepoV1.Spec = &argocdv1.RepoSpec{
			RepoUrl:  spec.RepoURL,
			Revision: spec.Revision,
		}
	}

	st, err := addonRepo.GetStatus()
	if err != nil {
		return nil, err
	}
	if st != nil {
		generationMatch := addonRepo.Generation == st.ProcessedGeneration
		addonRepoV1.Status = &argocdv1.RepoStatus{
			LastSyncTime:         st.LastSyncTime,
			LastSyncCommit:       st.LastSyncCommit,
			AddonCount:           uint32(st.AddonCount),
			ProcessedGeneration:  uint32(st.ProcessedGeneration),
			ReconciliationStatus: getAddonReconciliationStatus(generationMatch, true, st.Conditions),
		}
	}

	return &addonRepoV1, nil
}

func getAddonReconciliationStatus(generationMatch, enabled bool, conditions utilStatus.Conditions[models.AddonConditionType]) *reconciliationv1.Status {
	progressingDeadline := time.Minute * 5
	var latestTransition time.Time
	established := true
	var messages []string
	c, ok := conditions[models.AddonConditionTypeSyncSuccessful]
	if !ok {
		established = false
		messages = append(messages, fmt.Sprintf("Condition %v is not established", models.AddonConditionTypeSyncSuccessful))
	} else if !c.Status {
		established = false
		messages = append(messages, fmt.Sprintf("%s: %s", c.Reason, c.Message))
		latestTransition = c.LastTransitionTime
	}

	c1, ok := conditions[models.AddonConditionTypeDeleteSuccessful]
	if ok && c1.Reason != "" {
		established = false
		msg := c1.Message
		if strings.Contains(c1.Message, "something went wrong") {
			msg += ". Automated deletion in git is not possible, please manually delete addon from git to clean up."
		}
		messages = append(messages, fmt.Sprintf("%s: %s. ", c1.Reason, msg))
	}

	if (established && generationMatch) || (!enabled && c1.Reason == "") {
		// return successful status if the addon/repo is established and generation matches
		// or if the addon is not enabled and delete condition is not set
		return &reconciliationv1.Status{Code: reconciliationv1.StatusCode_STATUS_CODE_SUCCESSFUL}
	}

	code := reconciliationv1.StatusCode_STATUS_CODE_PROGRESSING
	if (!latestTransition.IsZero() && time.Since(latestTransition) >= progressingDeadline) || (c.Reason != "" && c.Reason != models.AddonConditionTypeSyncSuccessfulOutOfSyncReason) || c1.Reason != "" {
		code = reconciliationv1.StatusCode_STATUS_CODE_FAILED
	}

	return &reconciliationv1.Status{
		Code:    code,
		Message: strings.Join(messages, "; "),
	}
}
