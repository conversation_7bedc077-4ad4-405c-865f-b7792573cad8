package argocd

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	instancesvc "github.com/akuityio/akuity-platform/internal/services/instances"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) ListInstances(
	ctx context.Context,
	req *argocdv1.ListInstancesRequest,
) (*argocdv1.ListInstancesResponse, error) {
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())

	instanceModels, err := instanceSvc.GetAllInstances(ctx)
	if err != nil {
		return nil, err
	}
	filtered := []*instancesvc.ArgoCDInstance{}

	for _, i := range instanceModels {
		action := accesscontrol.NewActionGetWorkspaceInstances(i.WorkspaceID.String, i.GetID())
		ok, err := enforcer.EnforceAction(ctx, action)
		if err != nil {
			return nil, err
		}
		if !ok {
			continue
		}
		filtered = append(filtered, i)
	}

	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}
	orgID := req.GetOrganizationId()
	if s.featSvc.GetFeatureStatuses(ctx, &orgID).GetWorkspaces().Enabled() && actor.Type == accesscontrol.ActorTypeUser && req.GetWorkspaceId() != "" {
		filtered = s.filterInstanceByWorkspace(filtered, req.GetWorkspaceId())
	}

	instances, err := types.MapSlice(filtered, func(in *instancesvc.ArgoCDInstance) (*argocdv1.Instance, error) {
		return argocdutil.NewArgoCDInstanceV1(*in, s.cfg.InstanceProgressingDeadline, argocdutil.IsComponentVersionSupported(in.Version.String, s.versions) != nil)
	})
	if err != nil {
		return nil, err
	}
	return &argocdv1.ListInstancesResponse{
		Instances: instances,
	}, nil
}

func (s *ArgoCDV1Server) filterInstanceByWorkspace(instances []*instancesvc.ArgoCDInstance, workspaceID string) []*instancesvc.ArgoCDInstance {
	filtered := make([]*instancesvc.ArgoCDInstance, 0, len(instances))
	for _, i := range instances {
		if i.WorkspaceID.String == workspaceID {
			filtered = append(filtered, i)
		}
	}
	return filtered
}
