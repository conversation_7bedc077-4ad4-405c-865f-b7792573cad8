package argocd

import (
	"context"

	"github.com/akuityio/agent/manifests"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) GetInstanceNotificationCatalog(
	ctx context.Context,
	req *argocdv1.GetInstanceNotificationCatalogRequest,
) (*argocdv1.GetInstanceNotificationCatalogResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	instance, err := instanceSvc.GetInstanceByID(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	catalog, err := manifests.GetNotificationsDefaultCatalog(instance.Version.String)
	if err != nil {
		return nil, err
	}

	return &argocdv1.GetInstanceNotificationCatalogResponse{
		Catalog: catalog,
	}, nil
}
