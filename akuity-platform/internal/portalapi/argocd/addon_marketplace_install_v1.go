package argocd

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
	"k8s.io/utils/ptr"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/addons"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) AddonMarketplaceInstall(ctx context.Context, req *argocdv1.AddonMarketplaceInstallRequest) (*argocdv1.AddonMarketplaceInstallResponse, error) {
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetFleetManagement().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "fleet management feature is not enabled")
	}

	_, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId()))
	if err != nil {
		return nil, err
	}

	res, err := s.addonsSvc.InstallMarketplaceAddon(ctx, req.GetOrganizationId(), req.GetInstanceId(), convertAddonMarketplaceInstallConfig(req.GetConfig()))
	if err != nil {
		return nil, err
	}
	apiRes, err := reverseAddonMarketplaceInstallModel(&addons.ListMarketplaceAddonInstallsModel{
		AddonMarketplaceInstall: *res,
	})
	if err != nil {
		return nil, err
	}
	return &argocdv1.AddonMarketplaceInstallResponse{
		AddonInstall: apiRes,
	}, nil
}

func convertAddonMarketplaceInstallConfig(config *argocdv1.AddonMarketplaceInstallConfig) models.AddonMarketplaceInstallConfig {
	return models.AddonMarketplaceInstallConfig{
		RepoURL:         config.RepoUrl,
		Revision:        config.Revision,
		AddonName:       config.AddonName,
		HelmChartConfig: convertHelmChartInstallConfig(config.HelmChartConfig),
		Type:            models.AddonType(config.Type),
		Overrides:       convertOverrides(config.Overrides),
	}
}

func convertHelmChartInstallConfig(config *argocdv1.HelmChartInstallConfig) *models.HelmChartInstallConfig {
	modelConfig := &models.HelmChartInstallConfig{
		Dependencies: convertChartDependencies(config.Dependencies),
		Description:  config.Description,
		Name:         config.Name,
		Version:      config.Version,
	}
	return modelConfig
}

func reverseAddonMarketplaceInstallModel(model *addons.ListMarketplaceAddonInstallsModel) (*argocdv1.AddonMarketplaceInstall, error) {
	config, err := model.GetConfig()
	if err != nil {
		return nil, err
	}
	stat, err := model.GetStatus()
	if err != nil {
		return nil, err
	}
	return &argocdv1.AddonMarketplaceInstall{
		Id:              model.ID,
		OrganizationId:  model.OrganizationID,
		InstanceId:      model.InstanceID,
		Config:          reverseAddonMarketplaceInstallConfig(config),
		StatusInfo:      reverseAddonMarketplaceStatusInfo(stat, model.Generation != model.StatusProcessedGeneration.Int),
		AddonFound:      model.AddonFound,
		ChecksumMatched: model.ChecksumMatched,
	}, nil
}

func reverseAddonMarketplaceStatusInfo(statusInfo *models.AddonMarketplaceStatus, processing bool) *argocdv1.AddonMarketplaceStatus {
	if statusInfo == nil {
		return &argocdv1.AddonMarketplaceStatus{}
	}
	status := &argocdv1.AddonMarketplaceStatus{
		EventList:         []*argocdv1.AddonEvent{},
		LastProcessedHash: statusInfo.LastProcessedHash,
		Processing:        processing,
	}
	for _, event := range statusInfo.EventList {
		status.EventList = append(status.EventList, &argocdv1.AddonEvent{
			Type:         event.Type,
			Message:      event.Message,
			Time:         timestamppb.New(event.Time),
			Dependencies: reverseChartDependencies(event.Dependencies),
		})
	}
	return status
}

func reverseAddonMarketplaceInstallConfig(config *models.AddonMarketplaceInstallConfig) *argocdv1.AddonMarketplaceInstallConfig {
	return &argocdv1.AddonMarketplaceInstallConfig{
		RepoUrl:         config.RepoURL,
		Revision:        config.Revision,
		AddonName:       config.AddonName,
		HelmChartConfig: reverseHelmChartInstallConfig(config.HelmChartConfig),
		Type:            string(config.Type),
		Overrides:       reverseOverrides(config.Overrides),
	}
}

func reverseOverrides(overrides *models.AddonMarketplaceInstallOverrides) *argocdv1.AddonMarketplaceInstallOverrides {
	apiOverrides := &argocdv1.AddonMarketplaceInstallOverrides{
		Envs:     []string{},
		Clusters: []string{},
	}
	if overrides == nil {
		return apiOverrides
	}
	for key := range overrides.Envs {
		apiOverrides.Envs = append(apiOverrides.Envs, key)
	}
	for key := range overrides.Clusters {
		apiOverrides.Clusters = append(apiOverrides.Clusters, key)
	}
	return apiOverrides
}

func convertOverrides(overrides *argocdv1.AddonMarketplaceInstallOverrides) *models.AddonMarketplaceInstallOverrides {
	if overrides == nil || (len(overrides.Envs) == 0 && len(overrides.Clusters) == 0) {
		return nil
	}
	modelOverrides := models.AddonMarketplaceInstallOverrides{
		Envs:     map[string]struct{}{},
		Clusters: map[string]struct{}{},
	}
	for _, key := range overrides.Envs {
		modelOverrides.Envs[key] = struct{}{}
	}
	for _, key := range overrides.Clusters {
		modelOverrides.Clusters[key] = struct{}{}
	}
	return &modelOverrides
}

func convertChartDependencies(chartDeps []*argocdv1.ChartDependency) []models.ChartDependency {
	deps := make([]models.ChartDependency, len(chartDeps))
	if len(chartDeps) == 0 {
		return nil
	}
	for i, dep := range chartDeps {
		deps[i] = models.ChartDependency{
			Name:           dep.Name,
			Version:        dep.Version,
			Repository:     dep.Repository,
			RepositoryName: ptr.Deref(dep.RepositoryName, ""),
		}
	}
	return deps
}

func reverseHelmChartInstallConfig(config *models.HelmChartInstallConfig) *argocdv1.HelmChartInstallConfig {
	conf := &argocdv1.HelmChartInstallConfig{
		Dependencies: reverseChartDependencies(config.Dependencies),
		Description:  config.Description,
		Name:         config.Name,
		Version:      config.Version,
	}
	return conf
}
