package argocd

import (
	"fmt"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/addons"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/events"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) WatchAddonMarketplaceInstalls(req *argocdv1.WatchAddonMarketplaceInstallsRequest, ws argocdv1.ArgoCDService_WatchAddonMarketplaceInstallsServer) error {
	ctx := ws.Context()

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId())); err != nil {
		return err
	}

	events, err := s.addonsSvc.WatchMarketplaceAddonInstalls(ctx, req.GetInstanceId(),
		func(e events.AddonEvent) bool { return req.InstanceId == e.InstanceID },
		func(e *addons.ListMarketplaceAddonInstallsModel) bool { return e.InstanceID == req.InstanceId })
	if err != nil {
		return fmt.Errorf("failed to watch addon repos: %w", err)
	}

	for event := range events {
		apiRes, err := reverseAddonMarketplaceInstallModel(event.Item)
		if err != nil {
			return err
		}
		if err := ws.Send(&argocdv1.WatchAddonMarketplaceInstallsResponse{
			Item: apiRes,
			Type: argocdutil.MapDBEventTypeToGRPC(event.Type),
		}); err != nil {
			return err
		}
	}
	return nil
}
