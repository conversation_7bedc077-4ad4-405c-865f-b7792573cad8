package argocd

import (
	"context"
	"fmt"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	idv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/id/v1"
	healthv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/status/health/v1"
)

func (s *ArgoCDV1Server) GetInstanceCluster(
	ctx context.Context,
	req *argocdv1.GetInstanceClusterRequest,
) (*argocdv1.GetInstanceClusterResponse, error) {
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	var getClusterFn func(context.Context, string) (*models.ArgoCDCluster, error)
	switch req.GetIdType() {
	case idv1.Type_UNSPECIFIED:
		getClusterFn = instanceSvc.GetClusterByID
	case idv1.Type_NAME:
		getClusterFn = func(ctx context.Context, id string) (*models.ArgoCDCluster, error) {
			return instanceSvc.GetClusterByName(ctx, req.GetInstanceId(), id)
		}
	default:
		return nil, status.Error(codes.InvalidArgument, "id or name should be provided")
	}

	clusterModel, err := getClusterFn(ctx, req.GetId())
	if err != nil {
		if instances.IsNotFoundErr(err) {
			// checking if user has access to ALL instances or not.
			action := accesscontrol.NewActionGetInstanceClusters(req.GetWorkspaceId(), req.GetInstanceId(), req.GetId())
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				// if user has access to ALL instance and instance not found, we are returning notfound error.
				if instances.IsNotFoundErr(err) {
					return nil, status.Errorf(codes.NotFound, "cluster %q not found", req.GetId())
				}
				return nil, err
			}
			// if user doesn't have access to ALL clusters, we are returning permission denied
			if !ok {
				return nil, shared.NewPermissionDeniedErr(action)
			}
		}
		return nil, err
	}

	action := accesscontrol.NewActionGetInstanceClusters(req.GetWorkspaceId(), clusterModel.InstanceID, clusterModel.Name)
	ok, err := enforcer.EnforceAction(ctx, action)
	if err != nil {
		return nil, err
	}
	if !ok {
		// if we found cluster but user don't have access to it, we are not returning instanceID
		return nil, status.Errorf(codes.PermissionDenied, "no permission to %s on %q %s",
			action.Verb, action.Object, req.GetId())
	}

	cluster, err := argocdutil.NewArgoCDClusterV1(*clusterModel, s.cfg.ClusterProgressingDeadline, s.clusterAutoscalerConfig)
	if err != nil {
		return nil, err
	}
	return &argocdv1.GetInstanceClusterResponse{
		Cluster: cluster,
	}, nil
}

func mapRpcPhaseToTenantPhase(p healthv1.TenantPhase) (*common.ArgoCDTenantPhase, error) {
	switch p {
	case healthv1.TenantPhase_TENANT_PHASE_HEALTHY:
		return &common.TenantPhaseHealthy, nil
	case healthv1.TenantPhase_TENANT_PHASE_PROGRESSING:
		return &common.TenantPhaseProgressing, nil
	case healthv1.TenantPhase_TENANT_PHASE_DEGRADED:
		return &common.TenantPhaseDegraded, nil
	case healthv1.TenantPhase_TENANT_PHASE_UNKNOWN:
		return &common.TenantPhaseUnknown, nil
	case healthv1.TenantPhase_TENANT_PHASE_UNSPECIFIED:
		return nil, status.Error(codes.InvalidArgument, "unspecified tenant phase")
	default:
		return nil, fmt.Errorf("invalid health phase")
	}
}
