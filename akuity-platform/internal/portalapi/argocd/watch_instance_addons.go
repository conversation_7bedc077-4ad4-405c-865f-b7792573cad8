package argocd

import (
	"fmt"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/addons"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/events"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) WatchInstanceAddons(req *argocdv1.WatchInstanceAddonsRequest, ws argocdv1.ArgoCDService_WatchInstanceAddonsServer) error {
	ctx := ws.Context()

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId())); err != nil {
		return err
	}

	// list all addon repos
	addonsList := []*addons.Addon{}
	var err error
	if req.GetAddonId() != "" {
		addon, err := s.addonsSvc.GetAddon(ctx, req.GetAddonId(), false)
		if err != nil {
			return fmt.Errorf("failed to get addon addons: %w", err)
		}
		addonsList = append(addonsList, addon)
	} else {
		filter := req.GetFilter()
		if filter == nil {
			filter = &argocdv1.AddonFilter{}
		}
		addonsList, err = s.addonsSvc.ListAddons(ctx, req.InstanceId, filter, 0, 0)
		if err != nil {
			return fmt.Errorf("failed to list addon addons: %w", err)
		}
	}

	addonRepos, err := s.addonsSvc.WatchAddons(ctx, req.InstanceId, req.GetAddonId(), addonsList, func(e events.AddonEvent) bool {
		if req.GetAddonId() != "" && e.ID != req.GetAddonId() {
			return false
		}
		return req.InstanceId == e.InstanceID
	}, func(e *addons.Addon) bool {
		return e.InstanceID == req.InstanceId
	})
	if err != nil {
		return fmt.Errorf("failed to watch addons: %w", err)
	}

	for event := range addonRepos {
		instance, err := newAddonV1(*event.Item, req.GetWorkspaceId())
		if err != nil {
			return fmt.Errorf("failed to map addon: %w", err)
		}
		if err := ws.Send(&argocdv1.WatchInstanceAddonsResponse{
			Item: instance,
			Type: argocdutil.MapDBEventTypeToGRPC(event.Type),
		}); err != nil {
			return err
		}
	}
	return nil
}
