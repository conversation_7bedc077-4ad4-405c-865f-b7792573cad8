package argocd

import (
	"context"
	"fmt"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) DeleteInstanceAccount(
	ctx context.Context,
	req *argocdv1.DeleteInstanceAccountRequest,
) (*argocdv1.DeleteInstanceAccountResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	if err := instanceSvc.DeleteAccount(ctx, req.GetInstanceId(), req.GetName()); err != nil {
		if instances.IsNotFoundErr(err) {
			return nil, shared.ErrNotFound
		}
		return nil, fmt.Errorf("delete account: %w", err)
	}
	return &argocdv1.DeleteInstanceAccountResponse{}, nil
}
