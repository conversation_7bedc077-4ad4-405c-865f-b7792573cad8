package argocd

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) GetInstanceConfigManagementPlugins(
	ctx context.Context,
	req *argocdv1.GetInstanceConfigManagementPluginsRequest,
) (*argocdv1.GetInstanceConfigManagementPluginsResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	instance, err := instanceSvc.GetInstanceByID(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	argoCDConfigManagementPlugins, err := instance.GetArgoCDConfigManagementPlugins()
	if err != nil {
		return nil, err
	}

	return &argocdv1.GetInstanceConfigManagementPluginsResponse{
		Plugins: argoCDConfigManagementPlugins.Plugins,
	}, nil
}
