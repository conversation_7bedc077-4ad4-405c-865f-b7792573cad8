package argocd

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) UpdateInstanceImageUpdaterSSHConfig(
	ctx context.Context,
	req *argocdv1.UpdateInstanceImageUpdaterSSHConfigRequest,
) (*argocdv1.UpdateInstanceImageUpdaterSSHConfigResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	if err := instanceSvc.ConfigureInstanceImageUpdaterSSHConfig(ctx, req.GetId(), req.GetConfig()); err != nil {
		return nil, err
	}
	return &argocdv1.UpdateInstanceImageUpdaterSSHConfigResponse{}, nil
}
