package argocd

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) UpdateInstanceConfigManagementPlugins(
	ctx context.Context,
	req *argocdv1.UpdateInstanceConfigManagementPluginsRequest,
) (*argocdv1.UpdateInstanceConfigManagementPluginsResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())

	configManagementPlugins := models.ConfigManagementPlugins{
		Plugins: req.Plugins,
	}
	if err := instanceSvc.ConfigureInstanceConfigManagementPlugins(ctx, req.GetId(), configManagementPlugins); err != nil {
		return nil, err
	}
	return &argocdv1.UpdateInstanceConfigManagementPluginsResponse{}, nil
}
