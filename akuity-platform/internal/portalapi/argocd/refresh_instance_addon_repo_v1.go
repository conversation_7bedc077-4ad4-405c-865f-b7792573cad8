package argocd

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) RefreshInstanceAddonRepo(
	ctx context.Context,
	req *argocdv1.RefreshInstanceAddonRepoRequest,
) (*argocdv1.RefreshInstanceAddonRepoResponse, error) {
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetFleetManagement().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "fleet management feature is not enabled")
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId())); err != nil {
		return nil, err
	}

	addonRepo, err := s.addonsSvc.RefreshAddonRepo(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	addonRepoV1, err := newAddonRepoV1(*addonRepo)
	if err != nil {
		return nil, err
	}
	return &argocdv1.RefreshInstanceAddonRepoResponse{
		AddonRepo: addonRepoV1,
	}, nil
}
