package argocd

import (
	"context"

	jsonpatch "github.com/evanphx/json-patch"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) PatchInstance(
	ctx context.Context,
	req *argocdv1.PatchInstanceRequest,
) (*argocdv1.PatchInstanceResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())

	currentInstanceModel, err := instanceSvc.GetInstanceByID(ctx, req.GetId())
	if err != nil {
		if instances.IsNotFoundErr(err) {
			return nil, status.Errorf(codes.NotFound, "instance %q not found", req.GetId())
		}
		return nil, err
	}

	currentInstance, err := argocdutil.NewArgoCDInstanceV1(*currentInstanceModel, s.cfg.InstanceProgressingDeadline, false)
	if err != nil {
		return nil, err
	}

	currentInstanceJSON, err := s.jsonMarshalOptions.Marshal(currentInstance)
	if err != nil {
		return nil, err
	}
	patchJSON, err := s.jsonMarshalOptions.Marshal(req.GetPatch())
	if err != nil {
		return nil, err
	}
	patchedJSON, err := jsonpatch.MergePatch(currentInstanceJSON, patchJSON)
	if err != nil {
		return nil, err
	}
	patchedInstance := &argocdv1.Instance{}
	if err := s.jsonUnmarshalOptions.Unmarshal(patchedJSON, patchedInstance); err != nil {
		return nil, err
	}

	if patchedInstance.GetSpec().GetFqdn() != "" {
		orgID := req.GetOrganizationId()
		if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetArgocdCustomDomain().Enabled() {
			return nil, status.Error(codes.PermissionDenied, "custom domain feature is not enabled")
		}
	}

	updatedModel, err := s.updateArgoCDInstanceV1(ctx, instanceSvc, req.GetId(), patchedInstance)
	if err != nil {
		return nil, err
	}
	updated, err := argocdutil.NewArgoCDInstanceV1(*updatedModel, s.cfg.InstanceProgressingDeadline, argocdutil.IsComponentVersionSupported(updatedModel.Version.String, s.versions) != nil)
	if err != nil {
		return nil, err
	}
	return &argocdv1.PatchInstanceResponse{
		Instance: updated,
	}, nil
}
