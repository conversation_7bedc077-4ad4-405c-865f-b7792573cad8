package argocd

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) ListAddonMarketplaceInstalls(ctx context.Context, req *argocdv1.ListAddonMarketplaceInstallsRequest) (*argocdv1.ListAddonMarketplaceInstallsResponse, error) {
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetFleetManagement().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "this feature is not available")
	}

	_, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId()))
	if err != nil {
		return nil, err
	}

	limit := req.GetLimit()
	offset := req.GetOffset()
	if limit == 0 {
		limit = uint32(addonDefaultLimit)
	} else if limit > uint32(addonRepoMaxLimit) {
		limit = uint32(addonMaxLimit)
	}

	res, totalCount, err := s.addonsSvc.ListMarketplaceAddonInstalls(ctx, req.GetOrganizationId(), req.GetInstanceId(), req.GetFilter(), limit, offset)
	if err != nil {
		return nil, err
	}

	resList := make([]*argocdv1.AddonMarketplaceInstall, 0, len(res))

	for _, item := range res {
		apiRes, err := reverseAddonMarketplaceInstallModel(item)
		if err != nil {
			return nil, err
		}
		resList = append(resList, apiRes)
	}

	return &argocdv1.ListAddonMarketplaceInstallsResponse{
		AddonInstalls: resList,
		TotalCount:    uint32(totalCount),
	}, nil
}
