package argocd

import (
	"context"
	"errors"

	jsonpatch "github.com/evanphx/json-patch"
	"github.com/volatiletech/null/v8"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/addons"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) PatchInstanceAddon(
	ctx context.Context,
	req *argocdv1.PatchInstanceAddonRequest,
) (*argocdv1.PatchInstanceAddonResponse, error) {
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetFleetManagement().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "fleet management feature is not enabled")
	}

	_, actor, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId()))
	if err != nil {
		return nil, err
	}

	currentAddonModel, err := s.addonsSvc.GetAddon(ctx, req.GetId(), true)
	if err != nil {
		return nil, err
	}
	currentAddonModel.StatusOperation = null.NewJSON(nil, false)
	currentAddonModel.StatusSourceUpdate = null.NewJSON(nil, false)

	currentAddon, err := newAddonV1(*currentAddonModel, req.GetWorkspaceId())
	if err != nil {
		return nil, err
	}

	currentAddonJSON, err := s.jsonMarshalOptions.Marshal(currentAddon)
	if err != nil {
		return nil, err
	}

	patchJSON, err := s.jsonMarshalOptions.Marshal(req.GetPatch())
	if err != nil {
		return nil, err
	}

	patchedJSON, err := jsonpatch.MergePatch(currentAddonJSON, patchJSON)
	if err != nil {
		return nil, err
	}

	patchedAddon := argocdv1.Addon{}
	if err := s.jsonUnmarshalOptions.Unmarshal(patchedJSON, &patchedAddon); err != nil {
		return nil, err
	}

	patchedAddonModel, err := newAddonModel(&patchedAddon, actor)
	if err != nil {
		return nil, err
	}

	addon, err := s.addonsSvc.UpdateAddon(ctx, req.GetId(), patchedAddonModel)
	if err != nil {
		if errors.Is(err, addons.ErrOperationInProgress) {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
		return nil, err
	}

	addonv1, err := newAddonV1(*addon, req.GetWorkspaceId())
	if err != nil {
		return nil, err
	}
	return &argocdv1.PatchInstanceAddonResponse{
		Addon: addonv1,
	}, nil
}
