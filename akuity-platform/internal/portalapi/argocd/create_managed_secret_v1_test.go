package argocd

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes/fake"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
)

func TestEnsureSecretIsManaged(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name           string
		secretName     string
		existingSecret *corev1.Secret
		expectedError  error
		expectSecret   bool
	}{
		{
			name:       "managed secret with proper labels",
			secretName: "managed-secret",
			existingSecret: &corev1.Secret{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "managed-secret",
					Namespace: common.K3sArgoCDNamespace,
					Labels: map[string]string{
						client.ManagedSecretLabel: "true",
					},
				},
				Data: map[string][]byte{
					"key": []byte("value"),
				},
			},
			expectedError: nil,
			expectSecret:  true,
		},
		{
			name:       "secret with no labels",
			secretName: "no-labels-secret",
			existingSecret: &corev1.Secret{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "no-labels-secret",
					Namespace: common.K3sArgoCDNamespace,
				},
				Data: map[string][]byte{
					"key": []byte("value"),
				},
			},
			expectedError: errorNotManagedSecret,
			expectSecret:  false,
		},
		{
			name:       "secret without managed label",
			secretName: "not-managed-secret",
			existingSecret: &corev1.Secret{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "not-managed-secret",
					Namespace: common.K3sArgoCDNamespace,
					Labels: map[string]string{
						"some-other-label": "value",
					},
				},
				Data: map[string][]byte{
					"key": []byte("value"),
				},
			},
			expectedError: errorNotManagedSecret,
			expectSecret:  false,
		},
		{
			name:       "secret with managed label set to false",
			secretName: "false-managed-secret",
			existingSecret: &corev1.Secret{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "false-managed-secret",
					Namespace: common.K3sArgoCDNamespace,
					Labels: map[string]string{
						client.ManagedSecretLabel: "false",
					},
				},
				Data: map[string][]byte{
					"key": []byte("value"),
				},
			},
			expectedError: errorNotManagedSecret,
			expectSecret:  false,
		},
		{
			name:       "secret with managed label set to empty string",
			secretName: "empty-managed-secret",
			existingSecret: &corev1.Secret{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "empty-managed-secret",
					Namespace: common.K3sArgoCDNamespace,
					Labels: map[string]string{
						client.ManagedSecretLabel: "",
					},
				},
				Data: map[string][]byte{
					"key": []byte("value"),
				},
			},
			expectedError: errorNotManagedSecret,
			expectSecret:  false,
		},
		{
			name:       "secret with cluster ownership label",
			secretName: "cluster-owned-secret",
			existingSecret: &corev1.Secret{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "cluster-owned-secret",
					Namespace: common.K3sArgoCDNamespace,
					Labels: map[string]string{
						client.LabelKeySecretOwnedByCluster: "cluster-1",
					},
				},
				Data: map[string][]byte{
					"key": []byte("value"),
				},
			},
			expectedError: nil,
			expectSecret:  true,
		},
		{
			name:       "secret with both managed label and cluster ownership label",
			secretName: "both-labels-secret",
			existingSecret: &corev1.Secret{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "both-labels-secret",
					Namespace: common.K3sArgoCDNamespace,
					Labels: map[string]string{
						client.ManagedSecretLabel:           "true",
						client.LabelKeySecretOwnedByCluster: "cluster-1",
					},
				},
				Data: map[string][]byte{
					"key": []byte("value"),
				},
			},
			expectedError: nil,
			expectSecret:  true,
		},
		{
			name:           "secret does not exist",
			secretName:     "non-existent-secret",
			existingSecret: nil,
			expectedError:  nil, // Will be a wrapped error from Kubernetes API
			expectSecret:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create fake client with existing secrets
			var objects []runtime.Object
			if tt.existingSecret != nil {
				objects = append(objects, tt.existingSecret)
			}
			fakeClient := fake.NewSimpleClientset(objects...)

			// Call the function under test
			result, err := ensureSecretIsManaged(ctx, fakeClient, tt.secretName)

			// Verify error expectations
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, tt.expectedError), "Expected error %v, got %v", tt.expectedError, err)
			} else if tt.secretName == "non-existent-secret" {
				// For non-existent secrets, we expect a wrapped error
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "failed to get managed secret")
			} else {
				assert.NoError(t, err)
			}

			// Verify secret return expectations
			if tt.expectSecret {
				assert.NotNil(t, result)
				assert.Equal(t, tt.secretName, result.Name)
				assert.Equal(t, common.K3sArgoCDNamespace, result.Namespace)
			} else {
				assert.Nil(t, result)
			}
		})
	}
}
