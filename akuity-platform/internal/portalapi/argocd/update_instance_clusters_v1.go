package argocd

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) UpdateInstanceClusters(
	ctx context.Context,
	req *argocdv1.UpdateInstanceClustersRequest,
) (*argocdv1.UpdateInstanceClustersResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())

	customizations := req.GetClusterCustomizations()
	multiClusterK8SDashboardEnabled := req.MultiClusterK8SDashboardEnabled
	skipped, err := instanceSvc.BatchUpdateClusters(ctx, req.GetId(), customizations, multiClusterK8SDashboardEnabled)
	if err != nil {
		return nil, err
	}
	return &argocdv1.UpdateInstanceClustersResponse{SkippedClusters: skipped}, nil
}
