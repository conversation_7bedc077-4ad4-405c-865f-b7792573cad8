package argocd

import (
	"context"
	"errors"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) DeleteManagedSecret(
	ctx context.Context,
	req *argocdv1.DeleteManagedSecretRequest,
) (*argocdv1.DeleteManagedSecretResponse, error) {
	k3sKubeClient, err := s.getKubeClientForInstance(ctx, req.GetOrganizationId(), req.GetWorkspaceId(), req.GetInstanceId())
	if err != nil {
		if instances.IsNotFoundErr(err) {
			return nil, status.Errorf(codes.NotFound, "instance %q not found", req.GetInstanceId())
		}
		return nil, err
	}

	existingSecret, err := ensureSecretIsManaged(ctx, k3sKubeClient, req.Name)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			// If the secret is not found, we can return success since it may have already been
			// deleted or never existed as a managed secret.
			return &argocdv1.DeleteManagedSecretResponse{}, nil
		} else if errors.Is(err, errorNotManagedSecret) {
			// If the secret is not managed, we should let the user know
			return nil, status.Errorf(codes.InvalidArgument, "cannot delete reserved secret %q", req.Name)
		}
		return nil, status.Errorf(codes.Internal, "failed to fetch managed secret %q: %v", req.Name, err)
	}

	// If this is a cluster owned secret, we cannot delete it
	if existingSecret.Labels[client.LabelKeySecretOwnedByCluster] != "" {
		return nil, status.Errorf(codes.InvalidArgument, "cannot delete managed secret %q: secret is managed by cluster %q", req.Name, existingSecret.Labels[client.LabelKeySecretOwnedByCluster])
	}

	// Delete the secret
	err = k3sKubeClient.CoreV1().Secrets(common.K3sArgoCDNamespace).Delete(ctx, req.Name, metav1.DeleteOptions{})
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to delete managed secret %q: %v", req.Name, err)
	}

	return &argocdv1.DeleteManagedSecretResponse{}, nil
}
