package argocd

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"

	"github.com/akuityio/akuity-platform/internal/utils/kustomize"
	"github.com/akuityio/akuity-platform/models/models"
)

func Test_NewKubeCluster(t *testing.T) {
	cluster := &models.ArgoCDCluster{
		ID:                  "123",
		Name:                "test-cluster",
		Namespace:           "test",
		AutoUpgradeDisabled: false,
		NamespaceScoped:     true,
		Description:         null.StringFrom("test description"),
	}
	kustomization := kustomize.Kustomization(map[string]interface{}{
		"apiVersion": "kustomize.config.k8s.io/v1beta1",
		"kind":       "Kustomization",
	})
	kustomization.SetImage(
		kustomize.Image{
			Name:    "imageName",
			NewName: "newImageName",
			NewTag:  "v0.1.0",
		})
	spec := models.ClusterSpec{
		Size: models.ClusterSizeSmall,
		Labels: map[string]string{
			"test-label": "test",
		},
		Annotations: map[string]string{
			"test-annotation": "test",
		},
		Kustomization:  kustomization,
		TargetVersion:  "0.3.31",
		AppReplication: true,
	}
	require.NoError(t, cluster.SetSpec(spec))

	kubeCluster, err := newKubeCluster(cluster)
	require.NoError(t, err)
	require.Equal(t, cluster.Name, kubeCluster.Name)
	require.Equal(t, cluster.Namespace, kubeCluster.Namespace)
	require.Equal(t, spec.Labels["test-label"], kubeCluster.Labels["test-label"])
	require.Equal(t, spec.Annotations["test-annotation"], kubeCluster.Annotations["test-annotation"])
	require.Equal(t, string(spec.Size), string(kubeCluster.Spec.Data.Size))
	require.Equal(t, spec.TargetVersion, kubeCluster.Spec.Data.TargetVersion)
	kustomizationMap := map[string]interface{}{}
	require.NoError(t, json.Unmarshal(kubeCluster.Spec.Data.Kustomization.Raw, &kustomizationMap))
	require.EqualValues(t, spec.Kustomization.AsMap(), kustomizationMap)
}
