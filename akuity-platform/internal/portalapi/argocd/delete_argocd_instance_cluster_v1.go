package argocd

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) DeleteInstanceCluster(
	ctx context.Context,
	req *argocdv1.DeleteInstanceClusterRequest,
) (*argocdv1.DeleteInstanceClusterResponse, error) {
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())

	// TODO: let's switch from clusterID to clusterName if it's possible.
	cluster, err := instanceSvc.GetClusterByID(ctx, req.GetId())
	if err != nil {
		if instances.IsNotFoundErr(err) {
			// checking if user has access to ALL instances or not.
			action := accesscontrol.NewActionGetInstanceClusters(req.GetWorkspaceId(), req.GetInstanceId(), req.GetId())
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				// if user has access to ALL instance and instance not found, we are returning notfound error.
				if instances.IsNotFoundErr(err) {
					return nil, status.Errorf(codes.NotFound, "cluster %q not found", req.GetId())
				}
				return nil, err
			}
			// if user doesn't have access to ALL clusters, we are returning permission denied
			if !ok {
				return nil, shared.NewPermissionDeniedErr(action)
			}
		}
		return nil, err
	}

	action := accesscontrol.NewActionDeleteInstanceClusters(req.GetWorkspaceId(), cluster.InstanceID, cluster.Name)
	ok, err := enforcer.EnforceAction(ctx, action)
	if err != nil {
		return nil, err
	}
	if !ok {
		// if we found cluster but user don't have access to it, we are not returning instanceID
		return nil, status.Errorf(codes.PermissionDenied, "no permission to %s on %q %s",
			action.Verb, action.Object, req.GetId())
	}

	if err := instanceSvc.DeleteCluster(ctx, req.GetId()); err != nil {
		if instances.IsNotFoundErr(err) {
			return nil, shared.ErrNotFound
		}
		return nil, err
	}
	return &argocdv1.DeleteInstanceClusterResponse{}, nil
}
