package argocd

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) GetInstanceAddonRepo(
	ctx context.Context,
	req *argocdv1.GetInstanceAddonRepoRequest,
) (*argocdv1.GetInstanceAddonRepoResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId())); err != nil {
		return nil, err
	}

	addonModel, err := s.addonsSvc.GetAddonRepo(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	addon, err := newAddonRepoV1(*addonModel)
	if err != nil {
		return nil, err
	}

	return &argocdv1.GetInstanceAddonRepoResponse{
		AddonRepo: addon,
	}, nil
}
