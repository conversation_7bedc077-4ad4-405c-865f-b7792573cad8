package argocd

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/Masterminds/semver"
	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) UpdateInstance(
	ctx context.Context,
	req *argocdv1.UpdateInstanceRequest,
) (*argocdv1.UpdateInstanceResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())

	inst, err := instanceSvc.GetInstanceByID(ctx, req.GetId())
	if err != nil {
		return nil, err
	}
	if req.GetInstance().WorkspaceId != "" && inst.WorkspaceID.String != req.GetInstance().WorkspaceId {
		return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "can't change workspace, please use transfer endpoint")
	}

	updatedModel, err := s.updateArgoCDInstanceV1(ctx, instanceSvc, req.GetId(), req.GetInstance())
	if err != nil {
		return nil, err
	}
	updated, err := argocdutil.NewArgoCDInstanceV1(*updatedModel, s.cfg.InstanceProgressingDeadline, argocdutil.IsComponentVersionSupported(updatedModel.Version.String, s.versions) != nil)
	if err != nil {
		return nil, err
	}
	return &argocdv1.UpdateInstanceResponse{
		Instance: updated,
	}, nil
}

func (s *ArgoCDV1Server) updateArgoCDInstanceV1(
	ctx context.Context,
	instanceSvc *instances.Service,
	id string,
	instance *argocdv1.Instance,
) (*instances.ArgoCDInstance, error) {
	return instanceSvc.UpdateInstance(ctx, id, instance.GetName(), instance.GetDescription(), instance.GetWorkspaceId(),
		func(cfg *models.ArgoCDInstanceConfig) error {
			if cfg.Version.String != instance.GetVersion() {
				if err := argocdutil.IsComponentVersionSupported(instance.GetVersion(), s.versions); err != nil {
					return err
				}
				cfg.Version = null.StringFrom(instance.GetVersion())
			}
			cfg.Basepath = instance.GetSpec().GetBasepath()
			cfg.Subdomain = instance.GetSpec().GetSubdomain()
			if instance.GetSpec().GetFqdn() != "" {
				cfg.FQDN = null.StringFrom(instance.GetSpec().GetFqdn())
			} else {
				cfg.FQDN = null.StringFromPtr(nil)
			}

			imageUpdaterEnable := false
			var specModel models.InstanceConfigSpec
			if spec := instance.GetSpec(); spec != nil {
				imageUpdaterEnable = spec.ImageUpdaterEnabled

				// Check for duplicate custom deprecated APIs
				if len(spec.CustomDeprecatedApis) != len(lo.UniqBy(spec.CustomDeprecatedApis, func(api *argocdv1.CustomDeprecatedAPI) string {
					return api.ApiVersion + ":" + api.NewApiVersion
				})) {
					return status.Errorf(codes.InvalidArgument, "duplicate custom deprecated APIs")
				}
				for _, api := range spec.CustomDeprecatedApis {
					if len(strings.Split(api.ApiVersion, "/")) != 2 || len(strings.Split(api.NewApiVersion, "/")) != 2 {
						return status.Errorf(codes.InvalidArgument, "invalid custom deprecated API version")
					}
					if api.DeprecatedInKubernetesVersion != "" && api.UnavailableInKubernetesVersion != "" {
						deprecatedInKubernetesVersion, err := semver.NewVersion(api.DeprecatedInKubernetesVersion)
						if err != nil {
							return status.Errorf(codes.InvalidArgument, "invalid deprecated in version: %s", err)
						}
						unavailableInKubernetesVersion, err := semver.NewVersion(api.UnavailableInKubernetesVersion)
						if err != nil {
							return status.Errorf(codes.InvalidArgument, "invalid unavailable in version: %s", err)
						}
						if deprecatedInKubernetesVersion.GreaterThan(unavailableInKubernetesVersion) {
							return status.Errorf(codes.InvalidArgument, "deprecated in version must be less than unavailable in version")
						}
					}
				}

				// Check for duplicate extensions
				extensionIDs := make(map[string]bool)
				for _, ext := range spec.GetExtensions() {
					if extensionIDs[ext.GetId()] {
						return status.Errorf(codes.InvalidArgument, "duplicate extension ID: %s", ext.GetId())
					}
					extensionIDs[ext.GetId()] = true
				}

				if spec.AppReconciliationsRateLimiting != nil && spec.AppReconciliationsRateLimiting.ItemRateLimiting != nil && spec.AppReconciliationsRateLimiting.ItemRateLimiting.BackoffFactor < 0 {
					return status.Errorf(codes.InvalidArgument, "backoff factor can't be negative")
				}
				specModel = *newArgoCDInstanceSpecModel(spec)
			}
			cfg.ArgocdImageUpdaterEnable = null.BoolFrom(imageUpdaterEnable)
			if err := cfg.SetSpec(specModel); err != nil {
				return err
			}

			var rbacConfigMapModel *models.ArgoCDRbacConfigMap
			rbacConfigMap := instance.GetRbacConfig()
			if rbacConfigMap != nil {
				var overlayPolicies []models.OverlayPolicy
				for _, policy := range rbacConfigMap.OverlayPolicies {
					overlayPolicies = append(overlayPolicies, models.OverlayPolicy{Name: policy.Name, Policy: policy.Policy})
				}

				rbacConfigMapModel = &models.ArgoCDRbacConfigMap{
					DefaultPolicy:   rbacConfigMap.GetDefaultPolicy(),
					PolicyCSV:       rbacConfigMap.GetPolicyCsv(),
					Scopes:          rbacConfigMap.GetScopes(),
					OverlayPolicies: overlayPolicies,
				}
			}
			if err := cfg.SetArgoCDRbacConfigMap(rbacConfigMapModel); err != nil {
				return err
			}

			var configMapModel *models.ArgoCDConfigMap
			configMap := instance.GetConfig()
			if configMap != nil {
				configMapModel = newArgoCDConfigMapModel(configMap)
			}

			if configMapModel != nil {
				configMapModel.CrossplaneExtension = specModel.CrossplaneExtension
			}

			if err := cfg.SetArgoCDConfigMap(configMapModel); err != nil {
				return err
			}
			return nil
		})
}

func newArgoCDConfigMapModel(config *argocdv1.ArgoCDConfigMap) *models.ArgoCDConfigMap {
	return &models.ArgoCDConfigMap{
		StatusBadgeEnabled:            strconv.FormatBool(config.GetStatusBadge().GetEnabled()),
		StatusBadgeUrl:                config.GetStatusBadge().GetUrl(),
		GoogleAnalyticsTrackingID:     config.GetGoogleAnalytics().GetTrackingId(),
		GoogleAnalyticsAnonymizeUsers: strconv.FormatBool(config.GetGoogleAnalytics().GetAnonymizeUsers()),
		AnonymousEnabled:              strconv.FormatBool(config.GetAllowAnonymousUser()),
		BannerPermanent:               strconv.FormatBool(config.GetBanner().GetPermanent()),
		BannerContent:                 config.GetBanner().GetMessage(),
		BannerUrl:                     config.GetBanner().GetUrl(),
		ChatUrl:                       config.GetChat().GetUrl(),
		ChatMessage:                   config.GetChat().GetMessage(),
		InstanceLabelKey:              config.GetInstanceLabelKey(),
		KustomizeEnabled:              strconv.FormatBool(config.GetKustomizeSettings().GetEnabled()),
		KustomizeBuildOptions:         config.GetKustomizeSettings().GetBuildOptions(),
		HelmEnabled:                   strconv.FormatBool(config.GetHelmSettings().GetEnabled()),
		HelmValueFileSchemas:          config.GetHelmSettings().GetValueFileSchemas(),
		ResourceInclusions:            config.GetResourceSettings().GetInclusions(),
		ResourceExclusions:            config.GetResourceSettings().GetExclusions(),
		ResourceCompareOptions:        config.GetResourceSettings().GetCompareOptions(),
		UsersSessionDuration:          config.GetUsersSessionDuration(),
		OidcConfig:                    config.GetOidcConfig(),
		DexConfig:                     config.GetDexConfig(),
		ExecEnabled:                   strconv.FormatBool(config.GetWebTerminal().GetEnabled()),
		ExecShells:                    config.GetWebTerminal().GetShells(),
		ApplicationLinks:              newDeeplinkArray(config.GetDeepLinks().GetApplicationLinks()),
		ProjectLinks:                  newDeeplinkArray(config.GetDeepLinks().GetProjectLinks()),
		ResourceLinks:                 newDeeplinkArray(config.GetDeepLinks().GetResourceLinks()),
		LogsRBACEnabled:               strconv.FormatBool(config.GetLogsRbacEnabled()),
	}
}

func newDeeplink(link *argocdv1.DeepLink) *models.DeepLink {
	return &models.DeepLink{
		URL:         link.GetUrl(),
		Description: link.GetDescription(),
		Title:       link.GetTitle(),
		IconClass:   link.GetIconClass(),
		If:          link.GetIf(),
	}
}

func newDeeplinkArray(links []*argocdv1.DeepLink) []models.DeepLink {
	var deeplinks []models.DeepLink
	for _, link := range links {
		deeplinks = append(deeplinks, *newDeeplink(link))
	}
	return deeplinks
}

func newSecretManagementConfig(in *argocdv1.SecretsManagementConfig) models.SecretsManagementConfig {
	out := models.SecretsManagementConfig{}
	if in == nil {
		return out
	}

	for i := range in.Sources {
		out.Sources = append(out.Sources, models.ClusterSecretMapping{
			Clusters: newSelector(in.Sources[i].Clusters),
			Secrets:  newSelector(in.Sources[i].Secrets),
		})
	}
	for i := range in.Destinations {
		out.Destinations = append(out.Destinations, models.ClusterSecretMapping{
			Clusters: newSelector(in.Destinations[i].Clusters),
			Secrets:  newSelector(in.Destinations[i].Secrets),
		})
	}
	return out
}

func newSelector(from *argocdv1.ObjectSelector) models.ObjectSelector {
	out := models.ObjectSelector{
		MatchLabels: from.GetMatchLabels(),
	}
	for _, expr := range from.GetMatchExpressions() {
		out.MatchExpressions = append(out.MatchExpressions, v1.LabelSelectorRequirement{
			Key:      expr.GetKey(),
			Operator: v1.LabelSelectorOperator(expr.GetOperator()),
			Values:   expr.GetValues(),
		})
	}
	return out
}

func newCrossplaneExtension(ext *argocdv1.CrossplaneExtension) *models.CrossplaneExtension {
	newExt := &models.CrossplaneExtension{
		Resources: []models.CrossplaneExtensionResource{},
	}

	if ext != nil {
		for _, extRes := range ext.Resources {
			newExt.Resources = append(newExt.Resources, models.CrossplaneExtensionResource{
				Group: extRes.Group,
			})
		}
	}

	return newExt
}

func newAkuityIntelligenceExtension(ext *argocdv1.AkuityIntelligenceExtension) *models.AkuityIntelligenceExtension {
	var enabled bool
	var allowedUsernames []string
	var allowedGroups []string
	var aiSupportEngineerEnabled bool
	if ext != nil {
		enabled = ext.Enabled
		allowedUsernames = ext.AllowedUsernames
		allowedGroups = ext.AllowedGroups
		aiSupportEngineerEnabled = ext.AiSupportEngineerEnabled
	}

	return &models.AkuityIntelligenceExtension{
		Enabled:                  enabled,
		AllowedUsernames:         allowedUsernames,
		AllowedGroups:            allowedGroups,
		AISupportEngineerEnabled: aiSupportEngineerEnabled,
	}
}

func newApplicationSetExtension(ext *argocdv1.ApplicationSetExtension) *models.ApplicationSetExtension {
	var enabled bool
	if ext != nil {
		enabled = ext.Enabled
	}
	return &models.ApplicationSetExtension{Enabled: enabled}
}

func newAppReconciliationsRateLimiting(rt *argocdv1.AppReconciliationsRateLimiting) *models.AppReconciliationsRateLimiting {
	if rt == nil {
		return nil
	}
	return &models.AppReconciliationsRateLimiting{
		BucketRateLimiting: models.BucketRateLimiting{
			Enabled:    rt.BucketRateLimiting.Enabled,
			BucketSize: rt.BucketRateLimiting.BucketSize,
			BucketQps:  rt.BucketRateLimiting.BucketQps,
		},
		ItemRateLimiting: models.ItemRateLimiting{
			Enabled:         rt.ItemRateLimiting.Enabled,
			FailureCooldown: time.Duration(rt.ItemRateLimiting.FailureCooldown) * time.Millisecond,
			BaseDelay:       time.Duration(rt.ItemRateLimiting.BaseDelay) * time.Millisecond,
			MaxDelay:        time.Duration(rt.ItemRateLimiting.MaxDelay) * time.Millisecond,
			BackoffFactor:   float64(rt.ItemRateLimiting.BackoffFactor),
		},
	}
}

func newArgoCDInstanceSpecModel(spec *argocdv1.InstanceSpec) *models.InstanceConfigSpec {
	ipAllowList, _ := types.MapSlice(spec.GetIpAllowList(),
		func(in *argocdv1.IPAllowListEntry) (models.IpAllowlistEntry, error) {
			return models.IpAllowlistEntry{
				Ip:          in.GetIp(),
				Description: in.GetDescription(),
			}, nil
		})
	hostAliases, _ := types.MapSlice(spec.GetHostAliases(),
		func(in *argocdv1.HostAliases) (models.HostAliases, error) {
			return models.HostAliases{
				Ip:        in.GetIp(),
				Hostnames: in.GetHostnames(),
			}, nil
		})
	extensions, _ := types.MapSlice(spec.GetExtensions(),
		func(in *argocdv1.ArgoCDExtensionInstallEntry) (models.ArgoCDExtensionInstallEntry, error) {
			return models.ArgoCDExtensionInstallEntry{
				ID:      in.GetId(),
				Version: in.GetVersion(),
			}, nil
		})
	var delegate *models.RepoServerDelegate
	if spec.RepoServerDelegate != nil {
		delegate = &models.RepoServerDelegate{}
		if spec.RepoServerDelegate.ControlPlane {
			delegate.ControlPlane = &models.DelegateControlPlane{}
		} else if spec.RepoServerDelegate.ManagedCluster != nil {
			delegate.ManagedCluster = &models.ManagedCluster{ClusterName: spec.RepoServerDelegate.ManagedCluster.ClusterName}
		}
	}
	var imageUpdaterDelegate *models.ImageUpdaterDelegate
	if spec.ImageUpdaterDelegate != nil {
		imageUpdaterDelegate = &models.ImageUpdaterDelegate{}
		if spec.ImageUpdaterDelegate.ControlPlane {
			imageUpdaterDelegate.ControlPlane = &models.DelegateControlPlane{}
		} else if spec.ImageUpdaterDelegate.ManagedCluster != nil {
			imageUpdaterDelegate.ManagedCluster = &models.ManagedCluster{ClusterName: spec.ImageUpdaterDelegate.ManagedCluster.ClusterName}
		}
	}

	var appSetDelegate *models.AppSetDelegate
	if spec.AppSetDelegate != nil {
		appSetDelegate = &models.AppSetDelegate{}
		if spec.AppSetDelegate.ManagedCluster != nil {
			appSetDelegate.ManagedCluster = &models.ManagedCluster{ClusterName: spec.AppSetDelegate.ManagedCluster.ClusterName}
		}
	}

	var appsetPolicy models.AppsetPolicy
	if spec.AppsetPolicy != nil {
		appsetPolicy = models.AppsetPolicy{
			Policy:                spec.AppsetPolicy.Policy,
			PolicyOverrideEnabled: spec.AppsetPolicy.OverridePolicy,
		}
	}

	agentPermissionsRules, _ := types.MapSlice(spec.GetAgentPermissionsRules(),
		func(in *argocdv1.AgentPermissionsRule) (models.AgentPermissionsRule, error) {
			return models.AgentPermissionsRule{
				APIGroups: in.GetApiGroups(),
				Resources: in.GetResources(),
				Verbs:     in.GetVerbs(),
			}, nil
		})

	customDeprecatedApis, _ := types.MapSlice(spec.GetCustomDeprecatedApis(),
		func(in *argocdv1.CustomDeprecatedAPI) (models.CustomDeprecatedAPI, error) {
			return models.CustomDeprecatedAPI{
				APIVersion:                     in.GetApiVersion(),
				NewAPIVersion:                  in.GetNewApiVersion(),
				DeprecatedInKubernetesVersion:  in.GetDeprecatedInKubernetesVersion(),
				UnavailableInKubernetesVersion: in.GetUnavailableInKubernetesVersion(),
			}, nil
		})

	kubeVisionConfig := models.KubeVisionConfig{}
	if spec.GetKubeVisionConfig() != nil && spec.GetKubeVisionConfig().GetCveScanConfig() != nil {
		// Default rescan interval to 8 hours if not specified or error happens
		rescanInterval := spec.GetKubeVisionConfig().GetCveScanConfig().RescanInterval
		rescanIntervalDuration, err := time.ParseDuration(rescanInterval)
		if err != nil || rescanIntervalDuration < 8*time.Hour {
			rescanInterval = "8h"
		}
		kubeVisionConfig.CveScanConfig = models.CveScanConfig{
			ScanEnabled:    spec.MultiClusterK8SDashboardEnabled && spec.GetKubeVisionConfig().GetCveScanConfig().ScanEnabled,
			RescanInterval: rescanInterval,
		}
	} else {
		kubeVisionConfig.CveScanConfig = models.CveScanConfig{
			ScanEnabled:    false,
			RescanInterval: "8h",
		}
	}
	if spec.GetKubeVisionConfig() != nil && spec.GetKubeVisionConfig().GetAiConfig() != nil {
		for _, runbook := range spec.GetKubeVisionConfig().GetAiConfig().Runbooks {
			kubeVisionConfig.AIConfig.Runbooks = append(kubeVisionConfig.AIConfig.Runbooks, models.Runbook{
				Name:    runbook.GetName(),
				Content: runbook.GetContent(),
				AppliedTo: models.TargetSelector{
					ArgoCDApplications: runbook.GetAppliedTo().GetArgocdApplications(),
					K8SNamespaces:      runbook.GetAppliedTo().GetK8SNamespaces(),
					Clusters:           runbook.GetAppliedTo().GetClusters(),
					DegradedFor:        runbook.GetAppliedTo().GetDegradedFor(),
				},
			})
		}
		kubeVisionConfig.AIConfig.Incidents = models.IncidentsConfig{
			Triggers: lo.Map(spec.GetKubeVisionConfig().AiConfig.Incidents.Triggers, func(item *argocdv1.TargetSelector, index int) models.TargetSelector {
				return models.TargetSelector{
					ArgoCDApplications: item.ArgocdApplications,
					K8SNamespaces:      item.K8SNamespaces,
					Clusters:           item.Clusters,
					DegradedFor:        item.GetDegradedFor(),
				}
			}),
			Webhooks: lo.Map(spec.GetKubeVisionConfig().AiConfig.Incidents.Webhooks, func(item *argocdv1.IncidentWebhookConfig, index int) models.IncidentWebhookConfig {
				return models.IncidentWebhookConfig{
					Name:                      item.GetName(),
					K8SNamespacePath:          item.GetK8SNamespacePath(),
					DescriptionPath:           item.GetDescriptionPath(),
					ArgoCDApplicationNamePath: item.GetArgocdApplicationNamePath(),
					ClusterPath:               item.GetClusterPath(),
				}
			}),
		}
		kubeVisionConfig.AIConfig.ArgoCDSlackService = spec.GetKubeVisionConfig().AiConfig.ArgocdSlackService
	}
	var appInAnyNamespaceConfig *models.AppInAnyNamespaceConfig
	if spec.AppInAnyNamespaceConfig != nil {
		appInAnyNamespaceConfig = &models.AppInAnyNamespaceConfig{Enabled: spec.AppInAnyNamespaceConfig.Enabled}
	}

	appsetPlugins := []models.AppsetPlugins{}
	for _, plugin := range spec.AppsetPlugins {
		timeout := ""
		if plugin.RequestTimeout > 0 {
			timeout = fmt.Sprintf("%d", plugin.RequestTimeout)
		}
		appsetPlugins = append(appsetPlugins, models.AppsetPlugins{
			Name:           plugin.Name,
			Token:          plugin.Token,
			BaseUrl:        plugin.BaseUrl,
			RequestTimeout: timeout,
		})
	}
	return &models.InstanceConfigSpec{
		IpAllowlist:                  ipAllowList,
		HostAliases:                  hostAliases,
		DeclarativeManagementEnabled: spec.DeclarativeManagementEnabled,
		Extensions:                   extensions,
		ClusterCustomizationDefaults: models.ClusterCustomization{
			Kustomization:       spec.GetClusterCustomizationDefaults().GetKustomization().AsMap(),
			AutoUpgradeDisabled: spec.GetClusterCustomizationDefaults().GetAutoUpgradeDisabled(),
			AppReplication:      spec.GetClusterCustomizationDefaults().GetAppReplication(),
			RedisTunneling:      spec.GetClusterCustomizationDefaults().GetRedisTunneling(),
		},
		BackendIpAllowlistEnabled:       spec.BackendIpAllowListEnabled,
		AuditExtensionEnabled:           spec.AuditExtensionEnabled,
		SyncHistoryExtensionEnabled:     spec.SyncHistoryExtensionEnabled,
		AssistantExtensionEnabled:       spec.AssistantExtensionEnabled,
		CrossplaneExtension:             *newCrossplaneExtension(spec.CrossplaneExtension),
		RepoServerDelegate:              delegate,
		ImageUpdaterDelegate:            imageUpdaterDelegate,
		AppSetDelegate:                  appSetDelegate,
		AppsetPolicy:                    appsetPolicy,
		AppsetProgressiveSyncsEnabled:   spec.AppsetProgressiveSyncsEnabled,
		AgentPermissionsRules:           agentPermissionsRules,
		MultiClusterK8sDashboardEnabled: spec.MultiClusterK8SDashboardEnabled,
		AkuityIntelligenceExtension:     *newAkuityIntelligenceExtension(spec.AkuityIntelligenceExtension),
		ImageUpdaterVersion:             spec.ImageUpdaterVersion,
		CustomDeprecatedAPIs:            customDeprecatedApis,
		KubeVisionConfig:                kubeVisionConfig,
		AppInAnyNamespace:               appInAnyNamespaceConfig,
		Secrets:                         newSecretManagementConfig(spec.Secrets),
		AppsetPlugins:                   appsetPlugins,
		ApplicationSetExtension:         *newApplicationSetExtension(spec.ApplicationSetExtension),
		AppReconciliationsRateLimiting:  newAppReconciliationsRateLimiting(spec.AppReconciliationsRateLimiting),
	}
}
