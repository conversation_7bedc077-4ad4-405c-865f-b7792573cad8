package argocd

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/addons"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) UpdateInstanceAddon(
	ctx context.Context,
	req *argocdv1.UpdateInstanceAddonRequest,
) (*argocdv1.UpdateInstanceAddonResponse, error) {
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetFleetManagement().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "fleet management feature is not enabled")
	}

	_, actor, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId()))
	if err != nil {
		return nil, err
	}

	addonModel, err := newAddonModel(req.GetAddon(), actor)
	if err != nil {
		return nil, err
	}
	addon, err := s.addonsSvc.UpdateAddon(ctx, req.GetId(), addonModel)
	if err != nil {
		if errors.Is(err, addons.ErrOperationInProgress) {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
		return nil, err
	}

	addonv1, err := newAddonV1(*addon, req.GetWorkspaceId())
	if err != nil {
		return nil, err
	}

	return &argocdv1.UpdateInstanceAddonResponse{
		Addon: addonv1,
	}, nil
}

func newAddonModel(addon *argocdv1.Addon, actor *accesscontrol.Actor) (*models.Addon, error) {
	addonModel := models.Addon{}
	if addon.Spec != nil {
		clusterSelector, err := reverseConvertClusterSelector(addon.Spec.ClusterSelector)
		if err != nil {
			return nil, err
		}
		spec := models.AddonSpec{
			Enabled:             addon.Spec.Enabled,
			ClusterSelector:     clusterSelector,
			PatchCustomizations: reverseConvertPatchCustomization(addon.Spec.PatchCustomizations),
		}
		if addon.Spec.HelmValues != nil && len(addon.Spec.HelmValues.YamlPaths) > 0 {
			spec.HelmValues = &models.HelmValues{
				YamlPaths: addon.Spec.HelmValues.YamlPaths,
			}
		}
		if addon.Spec.AppTemplate != nil {
			onConflict := models.OnConflictActionOverwrite
			if addon.Spec.AppTemplate.CreationOptions != nil {
				switch addon.Spec.AppTemplate.CreationOptions.OnConflict {
				case argocdv1.OnConflictAction_ON_CONFLICT_ACTION_OVERWRITE:
					onConflict = models.OnConflictActionOverwrite
				case argocdv1.OnConflictAction_ON_CONFLICT_ACTION_SKIP:
					onConflict = models.OnConflictActionSkip
				default:
					onConflict = models.OnConflictActionOverwrite
				}
			}
			var nonCascade bool
			if addon.Spec.AppTemplate.DeletionOptions != nil {
				nonCascade = addon.Spec.AppTemplate.DeletionOptions.NonCascade
			}
			appTemplate := models.AppTemplate{
				NameTemplate: addon.Spec.AppTemplate.NameTemplate,
				CreationOptions: models.AppCreationOptions{
					OnConflict: onConflict,
				},
				DeletionOptions: models.AppDeletionOptions{
					NonCascade: nonCascade,
				},
				ProjectTemplate:   addon.Spec.AppTemplate.ProjectTemplate,
				NamespaceTemplate: addon.Spec.AppTemplate.NamespaceTemplate,
			}
			if addon.Spec.AppTemplate.SyncOptions != nil {
				appTemplate.SyncOptions = models.AppSyncOptions{
					AutoSync:        addon.Spec.AppTemplate.SyncOptions.AutoSync,
					AutoHeal:        addon.Spec.AppTemplate.SyncOptions.AutoHeal,
					PruneResources:  addon.Spec.AppTemplate.SyncOptions.PruneResources,
					SyncOptionsList: addon.Spec.AppTemplate.SyncOptions.SyncOptionsList,
				}
			}
			if addon.Spec.AppTemplate.KustomizeOptions != nil {
				appTemplate.KustomizeOptions = &models.KustomizeOptions{
					NamePrefixTemplate: addon.Spec.AppTemplate.KustomizeOptions.NamePrefixTemplate,
					NameSuffixTemplate: addon.Spec.AppTemplate.KustomizeOptions.NameSuffixTemplate,
				}
			}
			if addon.Spec.AppTemplate.HelmOptions != nil {
				appTemplate.HelmOptions = &models.HelmOptions{
					ReleaseNameTemplate: addon.Spec.AppTemplate.HelmOptions.ReleaseNameTemplate,
					PassCredentials:     addon.Spec.AppTemplate.HelmOptions.PassCredentials,
					SkipCrds:            addon.Spec.AppTemplate.HelmOptions.SkipCrds,
				}
			}
			spec.AppTemplate = appTemplate
		}

		if err := addonModel.SetSpec(spec); err != nil {
			return nil, err
		}
	}

	initiator := "akp_key: " + actor.ID
	if actor.Type == accesscontrol.ActorTypeUser {
		initiator = actor.Extras["email"].(string)
	}

	if addon.StatusOperation != nil {
		statusOp := models.StatusOperation{
			ClusterAddonStatusOperation: models.ClusterAddonStatusOperation{
				Revision:  "HEAD",
				Initiator: initiator,
			},
		}
		if addon.StatusOperation.ClusterAddonStatusOperation != nil {
			statusOp.Prune = addon.StatusOperation.ClusterAddonStatusOperation.Prune
			statusOp.SyncOptions = addon.StatusOperation.ClusterAddonStatusOperation.SyncOptions
			if addon.StatusOperation.ClusterAddonStatusOperation.Revision != "" {
				statusOp.Revision = addon.StatusOperation.ClusterAddonStatusOperation.Revision
			}
		}
		clusterSelector, err := reverseConvertClusterSelector(addon.StatusOperation.ClusterSelector)
		if err != nil {
			return nil, err
		}
		statusOp.ClusterSelector = clusterSelector

		if err := addonModel.SetStatusOperation(statusOp); err != nil {
			return nil, err
		}
	}

	if addon.StatusSourceUpdate != nil {
		sources := models.Sources{}
		if addon.StatusSourceUpdate.Sources != nil {
			clusterOverrides, envOverrides, defaultManifestSource, err := reverseConvertSources(addon.StatusSourceUpdate.Sources)
			if err != nil {
				return nil, err
			}
			sources = models.Sources{
				ClusterOverrides:      clusterOverrides,
				EnvOverrides:          envOverrides,
				DefaultManifestSource: defaultManifestSource,
			}
		}
		statusSource := models.StatusSourceUpdate{
			StartTimestamp: time.Now(),
			Cancelled:      addon.StatusSourceUpdate.Cancelled,
			Sources:        sources,
			Initiator:      initiator,
		}
		if err := addonModel.SetStatusSourceUpdate(statusSource); err != nil {
			return nil, err
		}
	}
	return &addonModel, nil
}

func getReverseKustomizeSource(kustomizeSource *argocdv1.KustomizeSource) (*models.KustomizeSource, error) {
	if kustomizeSource == nil {
		return nil, nil
	}

	resKustomizeSource := models.KustomizeSource{}

	if len(kustomizeSource.Images) > 0 {
		resKustomizeSource.Images = make([]map[string]interface{}, 0)
		for _, image := range kustomizeSource.Images {
			resKustomizeSource.Images = append(resKustomizeSource.Images, map[string]interface{}{
				"name":      image.Name,
				"newTag":    image.NewTag,
				"newName":   image.NewName,
				"tagSuffix": image.TagSuffix,
				"digest":    image.Digest,
			})
		}
	}
	if len(kustomizeSource.HelmCharts) > 0 {
		resKustomizeSource.HelmCharts = make([]map[string]interface{}, 0)
		for _, helmChart := range kustomizeSource.HelmCharts {
			resKustomizeSource.HelmCharts = append(resKustomizeSource.HelmCharts, map[string]interface{}{
				"name":    helmChart.Name,
				"version": helmChart.Version,
			})
		}

	}

	return &resKustomizeSource, nil
}

func getReverseHelmSource(helmSource *argocdv1.HelmSource) (*models.HelmSource, error) {
	if helmSource == nil {
		return nil, nil
	}

	return &models.HelmSource{
		Values:            helmSource.Values,
		ChartDependencies: reverseConvertChartDependencies(helmSource.Dependencies),
	}, nil
}

func reverseConvertChartDependencies(chartDeps []*argocdv1.ChartDependency) []models.ChartDependency {
	deps := make([]models.ChartDependency, len(chartDeps))
	if len(chartDeps) == 0 {
		return nil
	}
	for i, dep := range chartDeps {
		deps[i] = models.ChartDependency{
			Name:       dep.Name,
			Version:    dep.Version,
			Repository: dep.Repository,
		}
	}
	return deps
}

func reverseConvertSources(sources *argocdv1.SourceInfo) (map[string]models.ManifestSource, map[string]models.ManifestSource, *models.ManifestSource, error) {
	var err error
	clusterOverrides := map[string]models.ManifestSource{}
	for k, overrides := range sources.ClusterOverrides {
		if overrides.KustomizeSource != nil {
			var (
				path            string
				kustomizesource *models.KustomizeSource
			)
			if overrides.Path != nil {
				path = *overrides.Path
			}
			kustomizesource, err = getReverseKustomizeSource(overrides.KustomizeSource)
			if err != nil {
				return nil, nil, nil, err
			}
			clusterOverrides[k] = models.ManifestSource{
				Path:            path,
				KustomizeSource: kustomizesource,
			}
		} else if overrides.HelmSource != nil {
			var (
				path       string
				helmSource *models.HelmSource
			)
			if overrides.Path != nil {
				path = *overrides.Path
			}
			helmSource, err = getReverseHelmSource(overrides.HelmSource)
			if err != nil {
				return nil, nil, nil, err
			}
			clusterOverrides[k] = models.ManifestSource{
				Path:       path,
				HelmSource: helmSource,
			}
		}
	}
	envOverrides := map[string]models.ManifestSource{}
	for k, overrides := range sources.EnvOverrides {
		envOverrides[k] = models.ManifestSource{}
		if overrides.KustomizeSource != nil {
			var (
				path            string
				kustomizesource *models.KustomizeSource
			)
			if overrides.Path != nil {
				path = *overrides.Path
			}
			kustomizesource, err = getReverseKustomizeSource(overrides.KustomizeSource)
			if err != nil {
				return nil, nil, nil, err
			}
			envOverrides[k] = models.ManifestSource{
				Path:            path,
				KustomizeSource: kustomizesource,
			}
		} else if overrides.HelmSource != nil {
			var (
				path       string
				helmSource *models.HelmSource
			)
			if overrides.Path != nil {
				path = *overrides.Path
			}
			helmSource, err = getReverseHelmSource(overrides.HelmSource)
			if err != nil {
				return nil, nil, nil, err
			}
			envOverrides[k] = models.ManifestSource{
				Path:       path,
				HelmSource: helmSource,
			}
		}
	}
	defaultManifestSource := models.ManifestSource{}
	if sources.DefaultManifest != nil {
		if sources.DefaultManifest.KustomizeSource != nil {
			defaultManifestSource.Path = *sources.DefaultManifest.Path
			defaultManifestSource.KustomizeSource, err = getReverseKustomizeSource(sources.DefaultManifest.KustomizeSource)
			if err != nil {
				return nil, nil, nil, err
			}
		} else if sources.DefaultManifest.HelmSource != nil {
			defaultManifestSource.Path = *sources.DefaultManifest.Path
			defaultManifestSource.HelmSource, err = getReverseHelmSource(sources.DefaultManifest.HelmSource)
			if err != nil {
				return nil, nil, nil, err
			}
		}
	}

	return clusterOverrides, envOverrides, &defaultManifestSource, nil
}

func reverseConvertClusterSelector(selector *argocdv1.ClusterSelector) (models.ClusterSelector, error) {
	clusterSelector := models.ClusterSelector{}
	if selector != nil {
		if selector.NameFilters != nil {
			nameFilters, err := reverseConvertSelectors(selector.NameFilters)
			if err != nil {
				return clusterSelector, err
			}
			clusterSelector.NameFilters = nameFilters
		}

		if selector.LabelFilters != nil {
			labelFilters, err := reverseConvertSelectors(selector.LabelFilters)
			if err != nil {
				return clusterSelector, err
			}
			clusterSelector.LabelFilters = labelFilters
		}
	}
	return clusterSelector, nil
}

func reverseConvertPatchCustomization(patchList []*argocdv1.PatchCustomization) []models.PatchCustomization {
	patches := make([]models.PatchCustomization, len(patchList))
	if len(patchList) == 0 {
		return nil
	}
	for i, patch := range patchList {
		selector, err := reverseConvertClusterSelector(patch.ClusterSelector)
		if err != nil {
			return nil
		}
		patchStr, _ := json.Marshal(patch.Patch.AsMap())
		patches[i] = models.PatchCustomization{
			ClusterSelector: selector,
			PatchJson:       string(patchStr),
			Description:     patch.Description,
		}
	}
	return patches
}

func reverseConvertSelectors(selectors []*argocdv1.Selector) ([]models.Selector, error) {
	modelSelectors := make([]models.Selector, len(selectors))
	for i, selector := range selectors {
		var op models.SelectorOperator
		switch selector.SelectorOperator {
		case argocdv1.SelectorOperator_SELECTOR_OPERATOR_IN:
			op = models.SelectorOperatorIn
		case argocdv1.SelectorOperator_SELECTOR_OPERATOR_NOT_IN:
			op = models.SelectorOperatorNotIn
		default:
			return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("unknown selector operator: %s", selector.SelectorOperator.String()))
		}
		modelSelectors[i] = models.Selector{
			Operator: op,
			Values:   selector.Values,
		}
		if selector.Key != nil {
			modelSelectors[i].Key = selector.Key
		}
	}
	return modelSelectors, nil
}
