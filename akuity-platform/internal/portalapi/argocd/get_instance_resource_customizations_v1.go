package argocd

import (
	"context"
	"strconv"

	"k8s.io/utils/ptr"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) GetInstanceResourceCustomizations(
	ctx context.Context,
	req *argocdv1.GetInstanceResourceCustomizationsRequest,
) (*argocdv1.GetInstanceResourceCustomizationsResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	instance, err := instanceSvc.GetInstanceByID(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	cm, err := instance.GetArgoCDConfigMap()
	if err != nil {
		return nil, err
	}

	resourceCustomizations := []*argocdv1.ResourceCustomizationConfig{}
	for _, resourceCustomization := range cm.ResourceCustomizations {
		resourceCustomizations = append(resourceCustomizations, &argocdv1.ResourceCustomizationConfig{
			Group:             resourceCustomization.Group,
			Kind:              resourceCustomization.Kind,
			Health:            resourceCustomization.Health,
			Actions:           resourceCustomization.Actions,
			IgnoreDifferences: resourceCustomization.IgnoreDifferences,
			KnownTypeFields:   resourceCustomization.KnownTypeFields,
			UseOpenLibs: func() *bool {
				val, err := strconv.ParseBool(resourceCustomization.UseOpenLibs)
				if err != nil {
					return ptr.To(false)
				}
				return ptr.To(val)
			}(),
		})
	}

	return &argocdv1.GetInstanceResourceCustomizationsResponse{
		ResourceCustomizations: resourceCustomizations,
	}, nil
}
