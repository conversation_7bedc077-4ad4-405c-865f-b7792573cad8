package argocd

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) UpdateInstanceAccountPassword(
	ctx context.Context,
	req *argocdv1.UpdateInstanceAccountPasswordRequest,
) (*argocdv1.UpdateInstanceAccountPasswordResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId())); err != nil {
		return nil, err
	}

	password := req.GetPassword()
	if len(password) < instances.MinPasswordLength || len(password) > instances.MaxPasswordLength {
		return nil, status.Errorf(codes.InvalidArgument,
			"password must have characters length between %d-%d", instances.MinPasswordLength, instances.MaxPasswordLength)
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	if _, err := instanceSvc.UpdatePassword(ctx, req.GetInstanceId(), req.GetName(), password); err != nil {
		if instances.IsNotFoundErr(err) {
			return nil, shared.ErrNotFound
		}
		var apiErr *errorsutil.APIStatus
		if errors.As(err, &apiErr) {
			if apiErr.Status == http.StatusNotFound {
				return nil, shared.ErrNotFound
			}
		}
		return nil, fmt.Errorf("update password: %w", err)
	}
	return &argocdv1.UpdateInstanceAccountPasswordResponse{}, nil
}
