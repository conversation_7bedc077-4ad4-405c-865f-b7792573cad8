package argocd

import (
	"context"
	"crypto/tls"
	"encoding/pem"
	"fmt"
	"net/url"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/io"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) GetClusterAPIServerCAData(
	ctx context.Context,
	req *argocdv1.GetClusterAPIServerCADataRequest,
) (*argocdv1.GetClusterAPIServerCADataResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionCreateInstanceClusters(req.GetWorkspaceId(), req.GetInstanceId(), req.GetClusterName())); err != nil {
		return nil, err
	}

	parsedURL, err := url.Parse(req.GetServer())
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("invalid server URL: %s", err))
	}

	conf := &tls.Config{
		InsecureSkipVerify: true,
	}

	conn, err := tls.Dial("tcp", parsedURL.Host+":443", conf)
	if err != nil {
		return nil, status.Error(codes.Internal, fmt.Sprintf("failed to connect to server: %s", err))
	}
	defer io.Close(conn)
	certs := conn.ConnectionState().PeerCertificates
	var peerCerts []byte
	for _, cert := range certs {
		cert := pem.EncodeToMemory(&pem.Block{Type: "CERTIFICATE", Bytes: cert.Raw})
		peerCerts = append(peerCerts, cert...)
	}

	return &argocdv1.GetClusterAPIServerCADataResponse{
		Data: string(peerCerts),
	}, nil
}
