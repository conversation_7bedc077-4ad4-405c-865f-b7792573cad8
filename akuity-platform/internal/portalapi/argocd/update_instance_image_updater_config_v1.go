package argocd

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) UpdateInstanceImageUpdaterConfig(
	ctx context.Context,
	req *argocdv1.UpdateInstanceImageUpdaterConfigRequest,
) (*argocdv1.UpdateInstanceImageUpdaterConfigResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	if err := instanceSvc.ConfigureInstanceImageUpdaterConfig(ctx, req.GetId(), req.GetConfig(), req.GetVersion()); err != nil {
		return nil, err
	}
	return &argocdv1.UpdateInstanceImageUpdaterConfigResponse{}, nil
}
