package argocd

import (
	"context"
	"fmt"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) UpsertInstanceAccount(
	ctx context.Context,
	req *argocdv1.UpsertInstanceAccountRequest,
) (*argocdv1.UpsertInstanceAccountResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	capabilities := make([]string, 0)
	if req.GetCapabilities() != nil {
		if req.GetCapabilities().GetApiKey() {
			capabilities = append(capabilities, models.AccountCapabilityAPIKey)
		}
		if req.GetCapabilities().GetLogin() {
			capabilities = append(capabilities, models.AccountCapabilityLogin)
		}
	}
	if len(capabilities) == 0 {
		return nil, status.Error(codes.InvalidArgument, "account must have at least one capability")
	}
	account, err := instanceSvc.UpsertAccount(ctx, req.GetInstanceId(), models.Account{
		Name:         req.GetName(),
		Capabilities: capabilities,
		Disabled:     req.Disabled,
	})
	if err != nil {
		return nil, fmt.Errorf("upsert account: %w", err)
	}
	return &argocdv1.UpsertInstanceAccountResponse{
		Account: mapInstanceAccountModelToRPCModel(account),
	}, nil
}

func mapInstanceAccountModelToRPCModel(account *models.Account) *argocdv1.InstanceAccount {
	return &argocdv1.InstanceAccount{
		Name: account.Name,
		Capabilities: &argocdv1.InstanceAccountCapabilities{
			Login:  account.HasCapability(models.AccountCapabilityLogin),
			ApiKey: account.HasCapability(models.AccountCapabilityAPIKey),
		},
		Disabled: account.Disabled != nil && *account.Disabled,
	}
}
