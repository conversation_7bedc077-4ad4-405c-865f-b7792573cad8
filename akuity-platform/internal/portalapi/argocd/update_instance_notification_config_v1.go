package argocd

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) UpdateInstanceNotificationConfig(
	ctx context.Context,
	req *argocdv1.UpdateInstanceNotificationConfigRequest,
) (*argocdv1.UpdateInstanceNotificationConfigResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	if err := instanceSvc.ConfigureInstanceNotificationsConfig(ctx, req.GetId(), req.GetConfig()); err != nil {
		return nil, err
	}
	return &argocdv1.UpdateInstanceNotificationConfigResponse{}, nil
}
