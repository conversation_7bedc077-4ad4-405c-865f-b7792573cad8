package argocd

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"strings"

	"github.com/volatiletech/null/v8"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/internal/version"
	modelClient "github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) CreateInstanceCluster(
	ctx context.Context,
	req *argocdv1.CreateInstanceClusterRequest,
) (*argocdv1.CreateInstanceClusterResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionCreateInstanceClusters(req.GetWorkspaceId(), req.GetInstanceId(), req.GetName())); err != nil {
		return nil, err
	}

	data := req.GetData()
	if data == nil {
		return nil, status.Error(codes.InvalidArgument, "missing required field `data`")
	}

	targetVersion := req.GetData().GetTargetVersion()
	orgID := req.GetOrganizationId()

	var directClusterSpec *models.DirectClusterSpec
	// ensure that the user has access to the kargo instance if target kargo instance
	if data.DirectClusterSpec != nil {
		clusterType := apiToModelClusterType(data.DirectClusterSpec.ClusterType)
		if !clusterType.IsEmpty() {
			if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetArgocdClusterIntegration().Enabled() {
				return nil, status.Error(codes.PermissionDenied, "cluster integration feature not available")
			}
			if clusterType.IsKargo() {
				if data.DirectClusterSpec.GetKargoInstanceId() != "" {
					repoSet := modelClient.NewRepoSet(s.db)
					kargoInst, err := repoSet.KargoInstances().Filter(
						models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(req.GetOrganizationId())),
						models.KargoInstanceWhere.ID.EQ(data.DirectClusterSpec.GetKargoInstanceId()),
					).One(ctx)
					if err != nil {
						if errors.Is(err, sql.ErrNoRows) {
							return nil, shared.ErrNotFound
						}
						return nil, err
					}
					if !kargoInst.DeletionTimestamp.IsZero() {
						return nil, status.Error(codes.InvalidArgument, "requested kargo instance is being deleted")
					}
					if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
						accesscontrol.NewActionUpdateWorkspaceKargoInstances(kargoInst.WorkspaceID.String, data.DirectClusterSpec.GetKargoInstanceId())); err != nil {
						return nil, err
					}
				}
				directClusterSpec = &models.DirectClusterSpec{
					Type:            clusterType,
					KargoInstanceID: data.DirectClusterSpec.GetKargoInstanceId(),
				}
			} else {
				directClusterSpec = &models.DirectClusterSpec{
					Type: clusterType,
					Upbound: models.DirectClusterSpecUpboundConfig{
						Server:       data.DirectClusterSpec.GetServer(),
						Organization: data.DirectClusterSpec.GetOrganization(),
						Token:        data.DirectClusterSpec.GetToken(),
						CAData:       []byte(data.DirectClusterSpec.GetCaData()),
					},
				}
			}
			// auto set the agent version to latest for direct cluster
			targetVersion = version.GetLatestAgentVersion()
		}
	}

	var managedClusterConfig *models.ManagedClusterConfig
	if data.ManagedClusterConfig != nil {
		managedClusterConfig = &models.ManagedClusterConfig{
			SecretName: data.ManagedClusterConfig.GetSecretName(),
			SecretKey:  data.ManagedClusterConfig.GetSecretKey(),
		}
	}

	// backward compatibility
	namespaceScoped := data.GetNamespaceScoped()
	if !namespaceScoped {
		namespaceScoped = req.GetNamespaceScoped() // nolint:staticcheck
	}
	namespace := strings.TrimSpace(data.GetNamespace())
	if len(namespace) == 0 {
		namespace = strings.TrimSpace(req.GetNamespace()) // nolint:staticcheck
	}

	if len(namespace) == 0 {
		namespace = instances.DefaultClusterNamespace
	}

	var autoscalerConfig *common.AutoScalerConfig
	var clusterSize models.ClusterSize
	switch data.GetSize() {
	case argocdv1.ClusterSize_CLUSTER_SIZE_SMALL:
		clusterSize = models.ClusterSizeSmall
	case argocdv1.ClusterSize_CLUSTER_SIZE_MEDIUM:
		clusterSize = models.ClusterSizeMedium
	case argocdv1.ClusterSize_CLUSTER_SIZE_LARGE:
		clusterSize = models.ClusterSizeLarge
	case argocdv1.ClusterSize_CLUSTER_SIZE_AUTO:
		clusterSize = models.ClusterSizeAuto
		if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetClusterAutoscaler().Enabled() {
			return nil, status.Error(codes.InvalidArgument, "cluster size Auto can't be used when Auto Upgrade disabled")
		}
		// check if we can disable auto upgrade
		if data.GetAutoUpgradeDisabled() {
			return nil, status.Error(codes.InvalidArgument, "cluster size Auto can't be used when Auto Upgrade disabled")
		}
		if data.AutoscalerConfig != nil {
			var err error
			autoscalerConfig, err = parseAutoscalerConfig(data.AutoscalerConfig)
			if err != nil {
				return nil, err
			}
		}
	default:
		clusterSize = models.ClusterSizeSmall
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())

	if targetVersion == "" {
		targetVersion = version.GetLatestAgentVersion()
	}

	instance, err := instanceSvc.GetInstanceByID(ctx, req.GetInstanceId())
	if err != nil {
		return nil, err
	}
	spec, err := instance.GetSpec()
	if err != nil {
		return nil, err
	}

	clusterCustomization := spec.ClusterCustomizationDefaults
	if data.Kustomization != nil {
		clusterCustomization.Kustomization = data.Kustomization.AsMap()
	}

	if data.AppReplication != nil && *data.AppReplication {
		if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetArgocdAgentStateReplication().Enabled() {
			return nil, status.Error(codes.PermissionDenied, "app replication feature is not enabled")
		}
		clusterCustomization.AppReplication = *data.AppReplication
	}
	if data.RedisTunneling != nil {
		clusterCustomization.RedisTunneling = *data.RedisTunneling
	}
	if data.AutoUpgradeDisabled != nil {
		clusterCustomization.AutoUpgradeDisabled = *data.AutoUpgradeDisabled
	}
	var compatibility models.ClusterCompatibility
	if val := data.GetCompatibility(); val != nil {
		compatibility = models.ClusterCompatibility{IPV6Only: val.Ipv6Only}
	}
	var argoCDNotifications models.ClusterArgoCDNotificationsControllerSpec
	if val := data.GetArgocdNotificationsSettings(); val != nil {
		argoCDNotifications.InClusterSettings = val.InClusterSettings
	}
	clusterDashboardEnabled := true
	// If the instance is not enabling dashboard feature, or it's explicitly disabled in the request, disable it
	if !spec.MultiClusterK8sDashboardEnabled {
		clusterDashboardEnabled = false
	} else {
		if data.MultiClusterK8SDashboardEnabled != nil && !*data.MultiClusterK8SDashboardEnabled {
			clusterDashboardEnabled = false
		}
	}
	createdClusterModel, err := instanceSvc.CreateCluster(ctx,
		req.GetInstanceId(),
		req.GetName(),
		namespace,
		clusterSize,
		data.GetLabels(),
		data.GetAnnotations(),
		req.GetDescription(),
		namespaceScoped,
		clusterCustomization.AutoUpgradeDisabled,
		targetVersion,
		clusterCustomization.Kustomization,
		clusterCustomization.AppReplication,
		clusterCustomization.RedisTunneling,
		directClusterSpec,
		req.Force || s.cfg.DisableOptionalClusterValidation,
		data.GetDatadogAnnotationsEnabled(),
		data.GetEksAddonEnabled(),
		managedClusterConfig,
		clusterDashboardEnabled,
		autoscalerConfig,
		data.GetProject(),
		compatibility,
		argoCDNotifications,
	)
	if err != nil {
		if instances.IsAlreadyExistsErr(err) {
			existing, err := instanceSvc.GetClusterByName(ctx, req.GetInstanceId(), req.GetName())
			if err != nil {
				return nil, err
			}
			existingSpec, err := existing.GetSpec()
			if err != nil {
				return nil, err
			}

			updatedSpec := models.ClusterSpec{
				Size:                      clusterSize,
				Labels:                    data.GetLabels(),
				Annotations:               data.GetAnnotations(),
				Kustomization:             clusterCustomization.Kustomization,
				TargetVersion:             targetVersion,
				RedisTunneling:            clusterCustomization.RedisTunneling,
				DirectClusterSpec:         existingSpec.DirectClusterSpec,
				DatadogAnnotationsEnabled: data.GetDatadogAnnotationsEnabled(),
				AppReplication:            clusterCustomization.AppReplication,
				AutoscalerConfig:          autoscalerConfig,
				Project:                   data.GetProject(),
				Compatibility:             compatibility,
			}

			if !req.Upsert { // if upsert not set then return error
				return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "existing cluster spec is different, use upsert flag to force update")
			}

			createdClusterModel, err = instanceSvc.UpdateCluster(ctx, existing.ID, req.GetDescription(), namespace, namespaceScoped, clusterCustomization.AutoUpgradeDisabled, req.Force || s.cfg.DisableOptionalClusterValidation, updatedSpec)
			if err != nil {
				return nil, err
			}
		} else if instances.IsNotFoundErr(err) {
			return nil, shared.ErrNotFound
		} else {
			return nil, err
		}
	}
	createdCluster, err := argocdutil.NewArgoCDClusterV1(*createdClusterModel, s.cfg.ClusterProgressingDeadline, s.clusterAutoscalerConfig)
	if err != nil {
		return nil, err
	}
	return &argocdv1.CreateInstanceClusterResponse{
		Cluster: createdCluster,
	}, nil
}

func apiToModelClusterType(clusterType argocdv1.DirectClusterType) models.DirectClusterType {
	switch clusterType {
	case argocdv1.DirectClusterType_DIRECT_CLUSTER_TYPE_UPBOUND:
		return models.DirectClusterTypeUpbound
	case argocdv1.DirectClusterType_DIRECT_CLUSTER_TYPE_KARGO:
		return models.DirectClusterTypeKargo
	default:
		return models.DirectClusterType("")
	}
}
