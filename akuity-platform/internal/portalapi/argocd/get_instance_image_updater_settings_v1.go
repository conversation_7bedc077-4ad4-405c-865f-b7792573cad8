package argocd

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) GetInstanceImageUpdaterSettings(
	ctx context.Context,
	req *argocdv1.GetInstanceImageUpdaterSettingsRequest,
) (*argocdv1.GetInstanceImageUpdaterSettingsResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	instance, err := instanceSvc.GetInstanceByID(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	argoCDImageUpdaterSecret, err := instance.GetArgoCDImageUpdaterSecret()
	if err != nil {
		return nil, err
	}
	for key := range argoCDImageUpdaterSecret {
		argoCDImageUpdaterSecret[key] = ""
	}

	argoCDImageUpdaterCM, err := instance.GetArgoCDImageUpdaterConfigMap()
	if err != nil {
		return nil, err
	}
	argoCDImageUpdaterSSHCM, err := instance.GetArgoCDImageUpdaterSSHConfigMap()
	if err != nil {
		return nil, err
	}
	argoCDImageUpdaterVersion, err := instance.GetArgoCDImageUpdaterVersion()
	if err != nil {
		return nil, err
	}

	return &argocdv1.GetInstanceImageUpdaterSettingsResponse{
		Secret:    argoCDImageUpdaterSecret,
		Config:    argoCDImageUpdaterCM,
		SshConfig: argoCDImageUpdaterSSHCM,
		Version:   argoCDImageUpdaterVersion,
	}, nil
}
