package argocd

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) UpdateAddonMarketplaceInstall(ctx context.Context, req *argocdv1.UpdateAddonMarketplaceInstallRequest) (*argocdv1.UpdateAddonMarketplaceInstallResponse, error) {
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetFleetManagement().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "fleet management feature is not enabled")
	}

	_, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId()))
	if err != nil {
		return nil, err
	}

	res, err := s.addonsSvc.UpdateMarketplaceAddon(ctx, req.GetInstanceId(), req.GetId(), convertChartDependencies(req.GetDependencies()), req.GetRefresh(), req.GetForce())
	if err != nil {
		return nil, err
	}
	apiRes, err := reverseAddonMarketplaceInstallModel(res)
	if err != nil {
		return nil, err
	}
	return &argocdv1.UpdateAddonMarketplaceInstallResponse{
		AddonInstall: apiRes,
	}, nil
}
