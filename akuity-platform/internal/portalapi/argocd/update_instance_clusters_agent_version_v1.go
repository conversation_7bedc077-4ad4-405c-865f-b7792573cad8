package argocd

import (
	"context"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/services/permissions"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) UpdateInstanceClustersAgentVersion(
	ctx context.Context,
	req *argocdv1.UpdateInstanceClustersAgentVersionRequest,
) (*emptypb.Empty, error) {
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	instanceID := req.GetInstanceId()
	clusterNames := req.GetClusterNames()

	action := accesscontrol.NewActionUpdateInstanceClusters(req.GetWorkspaceId(), req.GetInstanceId(), accesscontrol.ResourceAny)

	ok, err := enforcer.CheckResourceOwnership(ctx, permissions.ObjectWorkspaceInstances, accesscontrol.FormatWorkspaceResource(req.GetWorkspaceId(), req.GetInstanceId()))
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, shared.NewPermissionDeniedErr(action)
	}

	var mods []qm.QueryMod
	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	if req.GetAllClusters() {
		resources, err := enforcer.GetResources(action.Object, action.Verb)
		if err != nil {
			return nil, err
		}
		workspaceID := req.GetWorkspaceId()
		if workspaceID == "" {
			inst, err := instanceSvc.GetInstanceByID(ctx, req.GetInstanceId())
			if err != nil {
				return nil, err
			}
			workspaceID = inst.WorkspaceID.String
		}
		resources, err = shared.FilterResources(req.GetInstanceId(), workspaceID, resources...)
		if err != nil {
			return nil, err
		}

		if len(resources) == 0 {
			return nil, shared.NewPermissionDeniedErr(action)
		}

		policyMods, err := instances.ClustersWhereNameInMod(req.GetInstanceId(), resources...)
		if err != nil {
			return nil, err
		}
		mods = append(mods, policyMods...)

	} else {
		for _, clusterName := range clusterNames {
			action := accesscontrol.NewActionUpdateInstanceClusters(req.GetWorkspaceId(), instanceID, clusterName)
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return nil, err
			}
			if !ok {
				return nil, shared.NewPermissionDeniedErr(action)
			}
		}
		mods = append(mods, models.ArgoCDClusterWhere.Name.IN(clusterNames))
		mods = append(mods, models.ArgoCDClusterWhere.InstanceID.EQ(instanceID))
	}

	if err := instanceSvc.BatchUpdateClustersVersion(ctx, req.GetNewVersion(), mods...); err != nil {
		return &emptypb.Empty{}, err
	}
	return &emptypb.Empty{}, nil
}
