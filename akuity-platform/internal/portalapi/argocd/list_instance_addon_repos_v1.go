package argocd

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

const (
	addonRepoMaxLimit     = int32(1000)
	addonRepoDefaultLimit = int32(10)
)

func (s *ArgoCDV1Server) ListInstanceAddonRepos(
	ctx context.Context,
	req *argocdv1.ListInstanceAddonReposRequest,
) (*argocdv1.ListInstanceAddonReposResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId())); err != nil {
		return nil, err
	}

	limit := req.GetLimit()
	offset := req.GetOffset()
	if limit == 0 {
		limit = addonRepoDefaultLimit
	} else if limit > addonRepoMaxLimit {
		limit = addonRepoMaxLimit
	}

	if offset < 0 {
		offset = 0
	}

	addonModels, err := s.addonsSvc.ListAddonRepos(ctx, req.GetInstanceId(), int(offset), int(limit))
	if err != nil {
		return nil, err
	}

	addonRepos := make([]*argocdv1.AddonRepo, len(addonModels))
	for i, m := range addonModels {
		a, err := newAddonRepoV1(*m)
		if err != nil {
			return nil, err
		}
		addonRepos[i] = a
	}

	return &argocdv1.ListInstanceAddonReposResponse{
		AddonRepos: addonRepos,
	}, nil
}
