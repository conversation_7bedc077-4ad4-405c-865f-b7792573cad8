package argocd

import (
	"fmt"

	"k8s.io/utils/ptr"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/events"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) WatchInstances(req *argocdv1.WatchInstancesRequest, ws argocdv1.ArgoCDService_WatchInstancesServer) error {
	ctx := ws.Context()

	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return err
	}

	instanceSvc := s.newInstanceServiceWithOptions(ctx, req.GetOrganizationId(), instances.WithInstanceWatcher(s.instancesWatcher))

	id := ptr.Deref(req.InstanceId, "")
	var existing []*instances.ArgoCDInstance
	if id == "" {
		instances, err := instanceSvc.GetAllInstances(ctx)
		if err != nil {
			return err
		}
		for _, i := range instances {
			action := accesscontrol.NewActionGetWorkspaceInstances(i.WorkspaceID.String, i.GetID())
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return err
			}
			if !ok {
				continue
			}
			existing = append(existing, i)
		}
	} else {
		instance, err := instanceSvc.GetInstanceByID(ctx, id)
		if err != nil {
			return err
		}
		action := accesscontrol.NewActionGetWorkspaceInstances(instance.WorkspaceID.String, id)
		ok, err := enforcer.EnforceAction(ctx, action)
		if err != nil {
			return err
		}
		if !ok {
			return shared.NewPermissionDeniedErr(action)
		}
		existing = []*instances.ArgoCDInstance{instance}
	}

	orgID := req.GetOrganizationId()
	workspaceEnabled := s.featSvc.GetFeatureStatuses(ctx, &orgID).GetWorkspaces().Enabled()
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return shared.ErrUnauthenticated
	}
	if workspaceEnabled && req.GetWorkspaceId() != "" {
		existing = s.filterInstanceByWorkspace(existing, req.GetWorkspaceId())
	}

	instances, err := instanceSvc.WatchInstances(ctx, existing, func(e events.InstanceEvent) bool {
		action := accesscontrol.NewActionGetWorkspaceInstances(e.WorkspaceID, e.ID)
		ok, _ := enforcer.EnforceAction(ctx, action, enforcer.OrgOwnershipChecker(e.OrganizationOwner))
		return ok && (id == "" || id == e.ID)
	})
	if err != nil {
		return fmt.Errorf("failed to watch instances: %w", err)
	}

	for event := range instances {
		instance, err := argocdutil.NewArgoCDInstanceV1(*event.Item, s.cfg.InstanceProgressingDeadline, argocdutil.IsComponentVersionSupported(event.Item.Version.String, s.versions) != nil)
		if err != nil {
			return fmt.Errorf("failed to map instance: %w", err)
		}
		if err := ws.Send(&argocdv1.WatchInstancesResponse{
			Item: instance,
			Type: argocdutil.MapDBEventTypeToGRPC(event.Type),
		}); err != nil {
			return err
		}
	}
	return nil
}
