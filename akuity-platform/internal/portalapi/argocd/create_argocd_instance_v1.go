package argocd

import (
	"context"
	"database/sql"
	"errors"
	"net/http"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	"github.com/akuityio/akuity-platform/internal/utils/aims"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) CreateInstance(
	ctx context.Context,
	req *argocdv1.CreateInstanceRequest,
) (*argocdv1.CreateInstanceResponse, error) {
	organizationId := req.GetOrganizationId()
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, organizationId)
	if err != nil {
		return nil, err
	}

	billing, err := models.Billings(models.BillingWhere.OrganizationID.EQ(organizationId)).One(ctx, s.db)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return nil, err
	}

	if billing == nil {
		internalConfig, err := aims.RetrieveInternalConfig(s.cmInformer)
		// if err, configMap may not exist. Default to allowed creation to prevent disruption.
		cannotCreateFreeInstance := false
		if err == nil && internalConfig != nil {
			cannotCreateFreeInstance = internalConfig.DisableFreeInstanceCreation && !internalConfig.VerifiedOrganizationMap[organizationId]
		}

		if cannotCreateFreeInstance {
			return nil, status.Error(codes.PermissionDenied, "instance creation is disabled")
		}
	}

	workspaceID := ""
	if s.featSvc.GetFeatureStatuses(ctx, &organizationId).GetWorkspaces().Enabled() {
		teamSvc := teams.NewService(s.db)
		workspaceSvc := workspaces.NewService(s.db, teamSvc, s.cfg.FeatureGatesSource)
		if req.GetWorkspaceId() == "" {
			// if the workspace id is not provided, for backward compatibility, we will use the default workspace
			defaultWorkspace, err := workspaceSvc.GetDefaultWorkspace(ctx, organizationId)
			if err != nil {
				return nil, errorsutil.NewAPIStatus(http.StatusInternalServerError, err.Error())
			}
			workspaceID = defaultWorkspace.ID
		} else {
			workspace, err := workspaceSvc.GetWorkspace(ctx, req.GetWorkspaceId())
			if err != nil {
				return nil, err
			}
			workspaceID = workspace.ID
		}
	}

	action := accesscontrol.NewActionCreateWorkspaceInstances(workspaceID)
	ok, err := enforcer.EnforceAction(ctx, action)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, status.Errorf(codes.PermissionDenied, "no permission to %s on %q %s",
			action.Verb, action.Object, req.GetName())
	}

	version := req.GetVersion()
	if version == "" {
		version = "latest"
	}
	if err := argocdutil.IsComponentVersionSupported(version, s.versions); err != nil {
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}

	instanceSvc := s.newInstanceService(ctx, organizationId)
	instanceModel, err := instanceSvc.CreateInstance(
		ctx, req.GetName(), version, req.GetDescription(), s.cfg.UsageLimits.TotalInstancesCount, req.GetShard(), workspaceID)
	if err != nil {
		if instances.IsValidationErr(err) {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
		if instances.IsNotFoundErr(err) {
			return nil, status.Error(codes.NotFound, "")
		}
		return nil, err
	}
	instance, err := argocdutil.NewArgoCDInstanceV1(*instanceModel, s.cfg.InstanceProgressingDeadline, argocdutil.IsComponentVersionSupported(instanceModel.Version.String, s.versions) != nil)
	if err != nil {
		return nil, err
	}

	if s.cfg.SlackCallbackIncomingWebhook != "" {
		go shared.PostSlackMessageAsSections(s.log, s.cfg.SlackCallbackIncomingWebhook,
			shared.InstanceCreatedMessage(ctx, organizationId, s.cfg.AimsURL, instance, s.repoSet, s.log))
	}

	return &argocdv1.CreateInstanceResponse{
		Instance: instance,
	}, nil
}
