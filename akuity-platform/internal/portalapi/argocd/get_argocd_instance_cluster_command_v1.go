package argocd

import (
	"context"
	"fmt"
	"net/url"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/models/client"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

const akuityCLIInstallAgentCommandTemplate = `akuity argocd cluster get-agent-manifests --org-id=%s --instance-id=%s %s | kubectl apply -f -`

const eksAddonDeleteSnippetTemplate = `
export EKS_CLUSTER_NAME=my-cluster

aws delete-addon --cluster-name $EKS_CLUSTER_NAME --addon-name akuity_agent
`

func (s *ArgoCDV1Server) GetInstanceClusterCommand(
	ctx context.Context,
	req *argocdv1.GetInstanceClusterCommandRequest,
) (*argocdv1.GetInstanceClusterCommandResponse, error) {
	organizationId := req.GetOrganizationId()
	clusterId := req.GetId()
	locationOrigin := req.GetLocationOrigin()
	commandType := req.GetType()

	// TODO: let's switch from clusterID to clusterName if it's possible.
	cluster, err := client.NewRepoSet(s.db).ArgoCDClusters().GetByID(ctx, clusterId)
	if err != nil {
		if instances.IsNotFoundErr(err) {
			// return permission denied so we not expose if cluster exists or not
			// before running enforcer
			return nil, status.Error(codes.PermissionDenied, "")
		}
		return nil, err
	}

	instanceId := cluster.InstanceID

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, organizationId,
		accesscontrol.NewActionGetInstanceClusters(req.GetWorkspaceId(), cluster.InstanceID, cluster.Name)); err != nil {
		return nil, err
	}

	token, err := shared.GetToken(ctx)
	if err != nil {
		return nil, err
	}

	apiURL := fmt.Sprintf("%s/api/v1/orgs/%s/argocd/instances/%s/clusters/%s/manifests",
		locationOrigin, organizationId, instanceId, clusterId)

	query := url.Values{}
	if req.Offline {
		query.Add("offlineInstallation", fmt.Sprintf("%v", req.Offline))
	}
	if req.GetSkipNamespace() {
		query.Add("skipNamespace", fmt.Sprintf("%v", req.GetSkipNamespace()))
	}
	finalQuery := query.Encode()
	if finalQuery != "" {
		apiURL = fmt.Sprintf("%s?%s", apiURL, finalQuery)
	}

	command := ""
	variables := map[string]string{}
	if req.GetCommandFor() == argocdv1.ClusterCommandFor_CLUSTER_COMMAND_FOR_EKS {
		if commandType == "delete" {
			command = eksAddonDeleteSnippetTemplate
		} else {
			variables["TOKEN"] = token
			variables["AKP_API_URL"] = apiURL
			variables["ADDON_NAME"] = "akuity_agent"
		}
	} else if req.GetCommandFor() == argocdv1.ClusterCommandFor_CLUSTER_COMMAND_FOR_AKUITY_CLI {
		if commandType == "delete" {
			return nil, status.Error(codes.InvalidArgument, "delete command using akuity CLI not implemented")
		}

		command = fmt.Sprintf(akuityCLIInstallAgentCommandTemplate, organizationId, instanceId, cluster.Name)
	} else {
		command = fmt.Sprintf("TOKEN=%q && curl -s -H \"Authorization: Bearer $TOKEN\" %q | kubectl %s -f -", token, apiURL, commandType)
	}

	return &argocdv1.GetInstanceClusterCommandResponse{Command: command, Variables: variables}, nil
}
