package argocd

import (
	"context"
	"fmt"
	"time"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/utils/consts"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) GetSyncOperationsStats(ctx context.Context, req *argocdv1.GetSyncOperationsStatsRequest) (*argocdv1.GetSyncOperationsStatsResponse, error) {
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	filters := req.GetFilter()
	if filters == nil {
		filters = &argocdv1.SyncOperationFilter{}
	}

	svc := s.newInstanceService(ctx, req.GetOrganizationId())

	instanceIds := filters.GetInstanceId()
	if len(instanceIds) > 0 {
		for _, id := range instanceIds {
			inst, err := svc.GetInstanceByID(ctx, id)
			if err != nil {
				return nil, err
			}
			action := accesscontrol.NewActionGetWorkspaceInstances(inst.WorkspaceID.String, id)
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return nil, err
			}
			if !ok {
				return nil, shared.NewPermissionDeniedErr(action)
			}
		}
	} else if len(filters.GetInstanceNames()) > 0 {
		for _, name := range filters.GetInstanceNames() {
			inst, err := svc.GetInstanceByName(ctx, name)
			if err != nil {
				return nil, err
			}
			action := accesscontrol.NewActionGetWorkspaceInstances(inst.WorkspaceID.String, inst.ID)
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return nil, err
			}
			if !ok {
				return nil, shared.NewPermissionDeniedErr(action)
			}
		}
	} else {
		// only org owner have access to sync operations.
		action := accesscontrol.NewActionGetOrganizationAllInstances()
		ok, err := enforcer.EnforceAction(ctx, action)
		if err != nil {
			return nil, err
		}
		if !ok {
			return nil, shared.NewPermissionDeniedErr(action)
		}
	}

	groupByInterval := shared.MapGroupByInterval(req.GetInterval())

	if err := shared.ValidateInterval(filters.GetStartTime(), filters.GetEndTime(), groupByInterval); err != nil {
		return nil, err
	}

	mods, err := GetSyncFilters(filters, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	groupByField := GetGroupByField(req.GetGroupByField(), req.GetGroupByLabelField())

	stats, err := svc.GetSyncOperationStats(ctx, mods, groupByInterval, groupByField)
	if err != nil {
		return nil, err
	}

	return &argocdv1.GetSyncOperationsStatsResponse{
		SyncOperationStats: MapStatsToResponse(stats),
	}, nil
}

func MapStatsToResponse(stats []instances.SyncOperationStats) []*argocdv1.SyncOperationStat {
	var statsResp []*argocdv1.SyncOperationStat

	statsResp = make([]*argocdv1.SyncOperationStat, 0)

	for _, stat := range stats {
		statsResp = append(statsResp, &argocdv1.SyncOperationStat{
			IntervalStart: stat.IntervalStart.String(),
			CountMap:      stat.CountMap,
			AverageMap:    stat.AverageMap,
		})
	}

	return statsResp
}

func GetGroupByField(field argocdv1.SyncOperationGroupField, labelField string) string {
	if labelField != "" {
		return fmt.Sprintf("details -> 'labels' ->> '%s'", labelField)
	}
	switch field {
	case argocdv1.SyncOperationGroupField_SYNC_OPERATION_GROUP_FIELD_APPS:
		return "sync.application_name"
	case argocdv1.SyncOperationGroupField_SYNC_OPERATION_GROUP_FIELD_PROJECTS:
		return "sync.details ->> 'project'"
	case argocdv1.SyncOperationGroupField_SYNC_OPERATION_GROUP_FIELD_INSTANCE_NAMES:
		return "instance.name"
	case argocdv1.SyncOperationGroupField_SYNC_OPERATION_GROUP_FIELD_INITIATORS:
		return "sync.details -> 'initiatedBy' ->> 'username'"
	default:
		return "sync.result_phase"
	}
}

func GetSyncFilters(filters *argocdv1.SyncOperationFilter, orgId string) ([]qm.QueryMod, error) {
	var err error
	startTime := time.Now().Add(-consts.Day)
	if startTimeStr := filters.GetStartTime(); startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "invalid startTime: %v", err)
		}
	}

	endTime := time.Now()
	if endTimeStr := filters.GetEndTime(); endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "invalid endTime: %v", err)
		}
	}

	if endTime.Before(startTime) {
		return nil, status.Errorf(codes.InvalidArgument, "invalid time range, end time before start time: start=%v, end=%v", startTime, endTime)
	}

	mods := []qm.QueryMod{
		qm.Where("sync.end_time >= ?", startTime),
		qm.Where("sync.end_time <= ?", endTime),
	}

	if len(filters.GetAppName()) > 0 {
		mods = append(mods, database.WhereJSONin("sync.application_name", filters.GetAppName()))
	}

	if len(filters.GetRepo()) > 0 {
		mods = append(mods, database.WhereJSONin("sync.details ->> 'repository'", filters.GetRepo()))
	}

	if len(filters.GetProjects()) > 0 {
		mods = append(mods, database.WhereJSONin("sync.details ->> 'project'", filters.GetProjects()))
	}

	if len(filters.GetLabels()) > 0 {
		// or query
		expr := []qm.QueryMod{}
		for key, value := range filters.GetLabels() {
			if len(expr) == 0 {
				expr = append(expr, qm.Where(fmt.Sprintf("sync.details -> 'labels' ->> '%s' like ?", key), value))
			} else {
				expr = append(expr, qm.Or(fmt.Sprintf("sync.details -> 'labels' ->> '%s' like ?", key), value))
			}
		}
		mods = append(mods, qm.Expr(expr...))
	}

	if len(filters.GetInstanceId()) > 0 {
		mods = append(mods, database.WhereJSONin("sync.instance_id", filters.GetInstanceId()))
	} else {
		mods = append(mods, qm.InnerJoin("argo_cd_instance as instance on sync.instance_id=instance.id"), qm.Where("instance.organization_owner = ?", orgId))
		// use instance name filters only at org level
		if len(filters.GetInstanceNames()) > 0 {
			mods = append(mods, database.WhereJSONin("instance.name", filters.GetInstanceNames()))
		}
	}

	if len(filters.GetInitiatedBy()) > 0 {
		mods = append(mods, database.WhereJSONin("sync.details -> 'initiatedBy' ->> 'username'", filters.GetInitiatedBy()))
	}

	return mods, nil
}
