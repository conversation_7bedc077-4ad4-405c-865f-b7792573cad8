package argocd

import (
	"context"
	"fmt"
	"maps"
	"slices"
	"strings"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/ptr"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

// labelToManagedSecretType converts a label value to the corresponding ManagedSecretType enum
func labelToManagedSecretType(label string) argocdv1.ManagedSecretType {
	switch label {
	case "repo-creds":
		return argocdv1.ManagedSecretType_MANAGED_SECRET_TYPE_REPO_CREDS
	default:
		return argocdv1.ManagedSecretType_MANAGED_SECRET_TYPE_UNSPECIFIED
	}
}

func fromK8sLabelSelector(selector string) (*argocdv1.ObjectSelector, error) {
	parsed, err := metav1.ParseToLabelSelector(selector)
	if err != nil {
		return nil, err
	}
	// Convert metav1.LabelSelector to argocdv1.ObjectSelector
	objectSelector := &argocdv1.ObjectSelector{
		MatchLabels:      parsed.MatchLabels,
		MatchExpressions: make([]*argocdv1.LabelSelectorRequirement, 0, len(parsed.MatchExpressions)),
	}
	for i := range parsed.MatchExpressions {
		objectSelector.MatchExpressions = append(objectSelector.MatchExpressions, &argocdv1.LabelSelectorRequirement{
			Key:      &parsed.MatchExpressions[i].Key,
			Operator: ptr.To(string(parsed.MatchExpressions[i].Operator)),
			Values:   parsed.MatchExpressions[i].Values,
		})
	}
	return objectSelector, nil
}

func (s *ArgoCDV1Server) ListInstanceManagedSecrets(
	ctx context.Context,
	req *argocdv1.ListInstanceManagedSecretsRequest,
) (*argocdv1.ListInstanceManagedSecretsResponse, error) {
	k3sKubeClient, err := s.getKubeClientForInstance(ctx, req.GetOrganizationId(), req.WorkspaceId, req.GetInstanceId())
	if err != nil {
		if instances.IsNotFoundErr(err) {
			return nil, status.Errorf(codes.NotFound, "instance %q not found", req.GetInstanceId())
		}
		return nil, err
	}
	// This will also return any synced secrets as the mutating webhook will add the label to any
	// secrets that are synced to the control plane
	resp, err := k3sKubeClient.CoreV1().Secrets(common.K3sArgoCDNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s=true", client.ManagedSecretLabel),
	})
	if err != nil {
		return nil, err
	}
	var managedSecrets []*argocdv1.ManagedSecret
	for _, secret := range resp.Items {
		// This shouldn't be possible, but just in case let's not panic and skip
		if secret.Labels == nil {
			continue
		}
		// Massage the labels and pop off the ArgoCD secret type label and managed secret label
		secretTypeLabel := secret.Labels[argocdManagedSecretTypeLabel]
		delete(secret.Labels, argocdManagedSecretTypeLabel)
		delete(secret.Labels, client.ManagedSecretLabel)
		// Pull out and parse the annotations
		var allowedClusters []string
		if allowedClustersStr, ok := secret.Annotations[client.ManagedSecretSyncAllowedClustersAnnotation]; ok {
			allowedClusters = strings.Split(strings.TrimSpace(allowedClustersStr), ",")
		}
		var clusterSelector *argocdv1.ObjectSelector
		if clusterSelectorStr, ok := secret.Annotations[client.ManagedSecretClusterSelectorAnnotation]; ok {
			clusterSelector, err = fromK8sLabelSelector(clusterSelectorStr)
			if err != nil {
				s.log.Error(err, "failed to parse cluster selector from managed secret, this is programmer error")
				return nil, status.Errorf(codes.Internal, "invalid cluster selector: %v", err)
			}
		}
		// Extract the keys from the secret data and sort them alphabetically
		keys := slices.Sorted(maps.Keys(secret.Data))
		managedSecrets = append(managedSecrets, &argocdv1.ManagedSecret{
			Name:            secret.Name,
			Labels:          secret.Labels,
			Type:            labelToManagedSecretType(secretTypeLabel),
			AllowedClusters: allowedClusters,
			ClusterSelector: clusterSelector,
			SecretKeys:      keys,
		})
	}

	return &argocdv1.ListInstanceManagedSecretsResponse{ManagedSecrets: managedSecrets}, nil
}
