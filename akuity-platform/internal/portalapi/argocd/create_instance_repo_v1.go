package argocd

import (
	"context"
	"fmt"
	"hash/fnv"

	"github.com/aws/smithy-go/ptr"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/argoproj"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

const (
	// Prefix to use for naming repository secrets
	repoSecretPrefix = "repo"
)

func (s *ArgoCDV1Server) CreateInstanceRepo(
	ctx context.Context,
	req *argocdv1.CreateInstanceRepoRequest,
) (*argocdv1.CreateInstanceRepoResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId()))
	if err != nil {
		return nil, err
	}
	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	instance, err := instanceSvc.GetInstanceByID(ctx, req.GetInstanceId())
	if err != nil {
		return nil, err
	}

	var k3sKubeClient kubernetes.Interface
	if instance.Shard == "" {
		tnt, err := client.NewArgoCDTenant(s.hostRestConfig, *s.log, req.GetInstanceId())
		if err != nil {
			return nil, err
		}
		k3sKubeClient, err = tnt.ControlPlaneKubeClientset(ctx)
		if err != nil {
			return nil, err
		}
	} else {
		privateSpec, err := instance.GetPrivateSpec()
		if err != nil {
			return nil, err
		}

		restConfig, err := privateSpec.GetRestConfig()
		if err != nil {
			return nil, err
		}
		k3sKubeClient, err = kubernetes.NewForConfig(restConfig)
		if err != nil {
			return nil, err
		}
	}

	data := req.GetData()
	url, ok := data["url"]
	if !ok {
		return nil, status.Errorf(codes.InvalidArgument, "url is required")
	}

	repoType := data["type"]
	if repoType != "git" {
		return nil, status.Errorf(codes.InvalidArgument, "only git repo type is supported")
	}

	project := data["project"]

	secretData := map[string][]byte{}
	for k, v := range data {
		secretData[k] = []byte(v)
	}

	secName := RepoURLToSecretName(repoSecretPrefix, url, project)

	repositorySecret := &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Labels:      map[string]string{argoproj.LabelKeySecretType: argoproj.LabelValueSecretTypeRepository},
			Name:        secName,
			Annotations: map[string]string{"managed-by": "argocd.argoproj.io"},
		},
		Data: secretData,
	}

	if _, err := k3sKubeClient.CoreV1().Secrets(common.K3sArgoCDNamespace).Create(ctx, repositorySecret, metav1.CreateOptions{}); err != nil {
		if k8serrors.IsAlreadyExists(err) {
			return nil, status.Errorf(codes.AlreadyExists, "repo with such url already exists")
		}
		return nil, err
	}

	return &argocdv1.CreateInstanceRepoResponse{Repo: &argocdv1.Repository{
		Repo:    ptr.String(url),
		Type:    ptr.String(repoType),
		Project: ptr.String(project),
	}}, nil
}

func RepoURLToSecretName(prefix, repo, project string) string {
	h := fnv.New32a()
	_, _ = h.Write([]byte(repo))
	_, _ = h.Write([]byte(project))
	return fmt.Sprintf("%s-%v", prefix, h.Sum32())
}
