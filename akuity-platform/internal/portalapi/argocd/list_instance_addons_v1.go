package argocd

import (
	"context"
	"fmt"

	"github.com/goccy/go-json"
	"google.golang.org/protobuf/types/known/structpb"
	"k8s.io/utils/ptr"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

const (
	addonMaxLimit     = int32(1000)
	addonDefaultLimit = int32(10)
)

func (s *ArgoCDV1Server) ListInstanceAddons(
	ctx context.Context,
	req *argocdv1.ListInstanceAddonsRequest,
) (*argocdv1.ListInstanceAddonsResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId())); err != nil {
		return nil, err
	}

	limit := req.GetLimit()
	offset := req.GetOffset()
	if limit == 0 {
		limit = addonDefaultLimit
	} else if limit > addonRepoMaxLimit {
		limit = addonMaxLimit
	}

	if offset < 0 {
		offset = 0
	}

	filter := req.GetFilter()
	if filter == nil {
		filter = &argocdv1.AddonFilter{}
	}

	totalCount, err := s.addonsSvc.CountAddons(ctx, req.GetInstanceId(), filter)
	if err != nil {
		return nil, err
	}
	addonModels, err := s.addonsSvc.ListAddons(ctx, req.GetInstanceId(), filter, int(offset), int(limit))
	if err != nil {
		return nil, err
	}

	addonList := make([]*argocdv1.Addon, len(addonModels))
	for i, m := range addonModels {
		a, err := newAddonV1(*m, req.GetWorkspaceId())
		if err != nil {
			return nil, err
		}
		addonList[i] = a
	}

	return &argocdv1.ListInstanceAddonsResponse{
		Addons:     addonList,
		TotalCount: int32(totalCount),
	}, nil
}

func reverseChartDependencies(chartDeps []models.ChartDependency) []*argocdv1.ChartDependency {
	deps := make([]*argocdv1.ChartDependency, len(chartDeps))
	if len(chartDeps) == 0 {
		return nil
	}
	for i, dep := range chartDeps {
		deps[i] = &argocdv1.ChartDependency{
			Name:           dep.Name,
			Version:        dep.Version,
			Repository:     dep.Repository,
			RepositoryName: ptr.To(dep.RepositoryName),
		}
	}
	return deps
}

func convertClusterSelectors(clusterSelector models.ClusterSelector) *argocdv1.ClusterSelector {
	selector := argocdv1.ClusterSelector{
		NameFilters:  convertSelectors(clusterSelector.NameFilters),
		LabelFilters: convertSelectors(clusterSelector.LabelFilters),
	}
	if selector.NameFilters == nil && selector.LabelFilters == nil {
		return nil
	}
	return &selector
}

func convertPatchCustomization(patchList []models.PatchCustomization) ([]*argocdv1.PatchCustomization, error) {
	patches := make([]*argocdv1.PatchCustomization, len(patchList))
	if len(patchList) == 0 {
		return nil, nil
	}
	for i, patch := range patchList {
		patchData := map[string]interface{}{}
		if err := json.Unmarshal([]byte(patch.PatchJson), &patchData); err != nil {
			return nil, fmt.Errorf("failed to unmarshal patch json: %w", err)
		}
		patchStruct, err := structpb.NewStruct(patchData)
		if err != nil {
			return nil, fmt.Errorf("failed to convert patch json to struct: %w", err)
		}
		patches[i] = &argocdv1.PatchCustomization{
			ClusterSelector: convertClusterSelectors(patch.ClusterSelector),
			Patch:           patchStruct,
			Description:     patch.Description,
		}
	}
	return patches, nil
}

func convertSelectors(selectors []models.Selector) []*argocdv1.Selector {
	protoSelectors := make([]*argocdv1.Selector, len(selectors))
	for i, selector := range selectors {
		if len(selector.Values) == 0 {
			continue
		}
		var op argocdv1.SelectorOperator
		switch selector.Operator {
		case models.SelectorOperatorIn:
			op = argocdv1.SelectorOperator_SELECTOR_OPERATOR_IN
		case models.SelectorOperatorNotIn:
			op = argocdv1.SelectorOperator_SELECTOR_OPERATOR_NOT_IN
		}
		protoSelectors[i] = &argocdv1.Selector{
			Key:              selector.Key,
			SelectorOperator: op,
			Values:           selector.Values,
		}
	}
	if len(protoSelectors) == 0 {
		return nil
	}
	return protoSelectors
}
