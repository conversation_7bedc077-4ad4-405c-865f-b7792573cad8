package argocd

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) GetInstanceAppsetSecret(
	ctx context.Context,
	req *argocdv1.GetInstanceAppsetSecretRequest,
) (*argocdv1.GetInstanceAppsetSecretResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetApplicationSetController().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "application set controller feature is not enabled")
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())

	secrets, err := instanceSvc.GetInstanceAppsetSecret(ctx, req.GetId())
	for key := range secrets {
		secrets[key] = ""
	}

	return &argocdv1.GetInstanceAppsetSecretResponse{
		Secret: secrets,
	}, err
}
