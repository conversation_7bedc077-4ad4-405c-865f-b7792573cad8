package argocd

import (
	"context"
	"fmt"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/dynamic"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/akuity-platform/internal/argoproj"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	idv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/id/v1"
)

func (s *ArgoCDV1Server) GetInstanceClusterInfo(
	ctx context.Context,
	req *argocdv1.GetInstanceClusterRequest,
) (*argocdv1.GetInstanceClusterInfoResponse, error) {
	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())

	// TODO: let's switch from clusterID to clusterName if it's possible.
	var getClusterFn func(context.Context, string) (*models.ArgoCDCluster, error)
	switch req.GetIdType() {
	case idv1.Type_UNSPECIFIED:
		getClusterFn = instanceSvc.GetClusterByID
	case idv1.Type_NAME:
		getClusterFn = func(ctx context.Context, id string) (*models.ArgoCDCluster, error) {
			return instanceSvc.GetClusterByName(ctx, req.GetInstanceId(), id)
		}
	default:
		return nil, status.Error(codes.InvalidArgument, "id or name should be provided")
	}

	clusterModel, err := getClusterFn(ctx, req.GetId())
	if err != nil {
		if instances.IsNotFoundErr(err) {
			// return permission denied so we not expose if cluster exists or not
			// before running enforcer
			return nil, status.Error(codes.PermissionDenied, "")
		}
		return nil, err
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetInstanceClusters(req.GetWorkspaceId(), clusterModel.InstanceID, clusterModel.Name)); err != nil {
		return nil, err
	}

	instance, err := instanceSvc.GetInstanceByID(ctx, req.GetInstanceId())
	if err != nil {
		return nil, err
	}
	var k3sKubeClient dynamic.Interface
	if instance.Shard == "" {
		tnt, err := client.NewArgoCDTenant(s.hostRestConfig, *s.log, clusterModel.InstanceID)
		if err != nil {
			return nil, err
		}
		k3sKubeClient, err = tnt.ControlPlaneDynamicClientset(ctx)
		if err != nil {
			return nil, err
		}
	} else {
		privateSpec, err := instance.GetPrivateSpec()
		if err != nil {
			return nil, err
		}

		restConfig, err := privateSpec.GetRestConfig()
		if err != nil {
			return nil, err
		}
		k3sKubeClient, err = dynamic.NewForConfig(restConfig)
		if err != nil {
			return nil, err
		}

	}

	app, err := k3sKubeClient.Resource(misc.ApplicationGVR).Namespace(argoproj.K3sArgoCDNamespace).List(ctx,
		metav1.ListOptions{
			LabelSelector: fmt.Sprintf("cluster=%s", clusterModel.Name),
			Limit:         1,
		})
	if err != nil {
		return nil, err
	}

	return &argocdv1.GetInstanceClusterInfoResponse{HasApplications: len(app.Items) > 0}, nil
}
