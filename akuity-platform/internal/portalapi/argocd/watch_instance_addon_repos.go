package argocd

import (
	"fmt"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/events"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) WatchInstanceAddonRepos(req *argocdv1.WatchInstanceAddonReposRequest, ws argocdv1.ArgoCDService_WatchInstanceAddonReposServer) error {
	ctx := ws.Context()

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId())); err != nil {
		return err
	}

	// list all addon repos
	repos := []*models.AddonRepo{}
	var err error
	if req.GetAddonRepoId() != "" {
		repo, err := s.addonsSvc.GetAddonRepo(ctx, req.GetAddonRepoId())
		if err != nil {
			return fmt.Errorf("failed to get addon repos: %w", err)
		}
		repos = append(repos, repo)
	} else {
		repos, err = s.addonsSvc.ListAddonRepos(ctx, req.InstanceId, 0, 0)
		if err != nil {
			return fmt.Errorf("failed to list addon repos: %w", err)
		}
	}

	addonRepos, err := s.addonsSvc.WatchAddonRepos(ctx, repos, func(e events.AddonEvent) bool {
		if req.GetAddonRepoId() != "" && e.ID != req.GetAddonRepoId() {
			return false
		}
		return req.InstanceId == e.InstanceID
	}, func(e *models.AddonRepo) bool {
		return e.InstanceID == req.InstanceId
	})
	if err != nil {
		return fmt.Errorf("failed to watch addon repos: %w", err)
	}

	for event := range addonRepos {
		instance, err := newAddonRepoV1(*event.Item)
		if err != nil {
			return fmt.Errorf("failed to map addon repo: %w", err)
		}
		if err := ws.Send(&argocdv1.WatchInstanceAddonReposResponse{
			Item: instance,
			Type: argocdutil.MapDBEventTypeToGRPC(event.Type),
		}); err != nil {
			return err
		}
	}
	return nil
}
