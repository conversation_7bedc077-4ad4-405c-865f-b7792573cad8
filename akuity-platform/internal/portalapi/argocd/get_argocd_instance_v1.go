package argocd

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	idv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/id/v1"
)

func (s *ArgoCDV1Server) GetInstance(
	ctx context.Context,
	req *argocdv1.GetInstanceRequest,
) (*argocdv1.GetInstanceResponse, error) {
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())

	var instanceModel *instances.ArgoCDInstance
	switch req.GetIdType() {
	case idv1.Type_UNSPECIFIED:
		instanceModel, err = instanceSvc.GetInstanceByID(ctx, req.GetId())
		if err != nil {
			if instances.IsNotFoundErr(err) {
				return nil, status.Errorf(codes.NotFound, "instance %q not found", req.GetId())
			}
			return nil, err
		}
		action := accesscontrol.NewActionGetWorkspaceInstances(instanceModel.WorkspaceID.String, req.GetId())
		ok, err := enforcer.EnforceAction(ctx, action)
		if err != nil {
			if instances.IsNotFoundErr(err) {
				return nil, status.Errorf(codes.NotFound, "instance %q not found", req.GetId())
			}
			return nil, err
		}
		if !ok {
			return nil, shared.NewPermissionDeniedErr(action)
		}
	case idv1.Type_NAME:
		instanceModel, err = instanceSvc.GetInstanceByName(ctx, req.GetId())
		if err != nil {
			if instances.IsNotFoundErr(err) {
				// we don't need to do any extrace permissions checks here
				// and we don't leak actuall instanceID
				return nil, status.Errorf(codes.NotFound, "instance %q not found", req.GetId())
			}
			return nil, err
		}
		action := accesscontrol.NewActionGetWorkspaceInstances(instanceModel.WorkspaceID.String, instanceModel.GetID())
		ok, err := enforcer.EnforceAction(ctx, action)
		if err != nil {
			return nil, err
		}
		if !ok {
			// if we found instance but user don't have access to it, we are not returning instanceID
			return nil, status.Errorf(codes.PermissionDenied, "no permission to %s on %q %s",
				action.Verb, action.Object, req.GetId())
		}
	default:
		return nil, status.Error(codes.InvalidArgument, "id or name should be provided")
	}

	instance, err := argocdutil.NewArgoCDInstanceV1(*instanceModel, s.cfg.InstanceProgressingDeadline, argocdutil.IsComponentVersionSupported(instanceModel.Version.String, s.versions) != nil)
	if err != nil {
		return nil, err
	}
	return &argocdv1.GetInstanceResponse{
		Instance: instance,
	}, nil
}
