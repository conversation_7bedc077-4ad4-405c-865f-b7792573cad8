package argocd

import (
	"fmt"
	"slices"
	"strings"

	"k8s.io/utils/ptr"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/services/permissions"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/events"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func getClusterFilterPredicate(filter *argocdv1.ClusterFilter, minClusterName, maxClusterName string) func(*models.ArgoCDCluster) bool {
	if filter == nil {
		return func(cluster *models.ArgoCDCluster) bool {
			return true
		}
	}
	return func(cluster *models.ArgoCDCluster) bool {
		if minClusterName != "" && cluster.Name <= minClusterName {
			return false
		}
		if maxClusterName != "" && cluster.Name >= maxClusterName {
			return false
		}

		if filter.GetNameLike() != "" && !strings.Contains(cluster.Name, filter.GetNameLike()) {
			return false
		}
		clusterStatus, err := cluster.GetStatus()
		if err != nil {
			return false
		}
		agentState := clusterStatus.GetAgentState()
		if len(filter.GetAgentStatus()) > 0 {
			if agentState == nil {
				return false
			}
			phase := argocdutil.MapTenantPhaseToRpcPhase(agentState.Status.PriorityStatus)
			if !slices.Contains(filter.GetAgentStatus(), phase) {
				return false
			}
		}

		if len(filter.GetAgentVersion()) > 0 {
			if agentState == nil {
				return false
			}
			if !slices.Contains(filter.GetAgentVersion(), agentState.Version) {
				return false
			}
		}

		if len(filter.GetArgocdVersion()) > 0 {
			if agentState == nil {
				return false
			}
			if !slices.Contains(filter.GetArgocdVersion(), agentState.ArgoCDVersion) {
				return false
			}
		}

		if filter.OutdatedManifest != nil {
			isOutdated := agentState == nil || agentState.Status == nil || int64(cluster.Generation) != agentState.Status.MinObservedGeneration
			if *filter.OutdatedManifest != isOutdated {
				return false
			}
		}

		if len(filter.GetNamespace()) > 0 {
			if !slices.Contains(filter.GetNamespace(), cluster.Namespace) {
				return false
			}
		}

		if filter.NamespaceScoped != nil && *filter.NamespaceScoped {
			if !cluster.NamespaceScoped {
				return false
			}
		}
		clusterSpec, err := cluster.GetSpec()
		if err != nil {
			return false
		}

		if len(filter.GetLabels()) > 0 {
			filterLabels := filter.GetLabels()
			for key, value := range filterLabels {
				clusterLabelValue, exists := clusterSpec.Labels[key]
				if !exists || clusterLabelValue != value {
					return false
				}
			}
		}

		if excludeAgentVersion := filter.GetExcludeAgentVersion(); excludeAgentVersion != "" {
			if agentState != nil && agentState.Version == fmt.Sprint("v", excludeAgentVersion) {
				return false
			}
			// ignore direct clusters when filtering for outdated agent versions
			if clusterSpec.DirectClusterSpec != nil && clusterSpec.DirectClusterSpec.Type != "" {
				return false
			}
		}

		return true
	}
}

func (s *ArgoCDV1Server) WatchInstanceClusters(req *argocdv1.WatchInstanceClustersRequest, ws argocdv1.ArgoCDService_WatchInstanceClustersServer) error {
	ctx := ws.Context()
	orgID := req.GetOrganizationId()
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, orgID)
	if err != nil {
		return err
	}

	instanceID := req.GetInstanceId()

	action := accesscontrol.NewActionGetInstanceClusters(req.GetWorkspaceId(), instanceID, accesscontrol.ResourceAny)

	// first we are checking that user owns requested instance
	ok, err := enforcer.CheckResourceOwnership(ctx, permissions.ObjectWorkspaceInstances, accesscontrol.FormatWorkspaceResource(req.GetWorkspaceId(), req.GetInstanceId()))
	if err != nil {
		return err
	}
	if !ok {
		return shared.NewPermissionDeniedErr(action)
	}

	resources, err := enforcer.GetResources(action.Object, action.Verb)
	if err != nil {
		return err
	}
	instanceSvc := s.newInstanceServiceWithOptions(ctx, orgID, instances.WithClusterWatcher(s.clustersWatcher))
	workspaceID := req.GetWorkspaceId()
	if workspaceID == "" {
		inst, err := instanceSvc.GetInstanceByID(ctx, req.GetInstanceId())
		if err != nil {
			return err
		}
		workspaceID = inst.WorkspaceID.String
	}
	resources, err = shared.FilterResources(req.GetInstanceId(), workspaceID, resources...)
	if err != nil {
		return err
	}
	if len(resources) == 0 {
		return shared.NewPermissionDeniedErr(action)
	}

	mods, err := getClusterFilterMods(req.GetFilter())
	if err != nil {
		return fmt.Errorf("failed to get cluster filter mods: %w", err)
	}

	policyMods, err := instances.ClustersWhereNameInMod(instanceID, resources...)
	if err != nil {
		return err
	}
	mods = append(mods, policyMods...)

	if req.GetMinClusterName() != "" {
		mods = append(mods, models.ArgoCDClusterWhere.Name.GTE(req.GetMinClusterName()))
	}
	if req.GetMaxClusterName() != "" {
		mods = append(mods, models.ArgoCDClusterWhere.Name.LTE(req.GetMaxClusterName()))
	}

	filterPredicate := getClusterFilterPredicate(req.GetFilter(), req.GetMinClusterName(), req.GetMaxClusterName())

	clusterID := ptr.Deref(req.ClusterId, "")

	if clusterID != "" {
		mods = append(mods, models.ArgoCDClusterWhere.ID.EQ(clusterID))
	}

	existing, err := instanceSvc.GetInstanceClusters(ctx, instanceID, mods...)
	if err != nil {
		return err
	}

	predicate := func(e events.ClusterEvent) bool {
		return (clusterID == "" || clusterID == e.ID) && instanceID == e.InstanceID
	}

	clusters, err := instanceSvc.WatchClusters(ctx, existing, predicate, filterPredicate)
	if err != nil {
		return fmt.Errorf("failed to watch clusters: %w", err)
	}
	for event := range clusters {
		cluster, err := argocdutil.NewArgoCDClusterV1(*event.Item, s.cfg.ClusterProgressingDeadline, s.clusterAutoscalerConfig)
		if err != nil {
			return fmt.Errorf("failed to map cluster: %w", err)
		}

		if err := ws.Send(&argocdv1.WatchInstanceClustersResponse{
			Item: cluster,
			Type: argocdutil.MapDBEventTypeToGRPC(event.Type),
		}); err != nil {
			return err
		}
	}
	return nil
}
