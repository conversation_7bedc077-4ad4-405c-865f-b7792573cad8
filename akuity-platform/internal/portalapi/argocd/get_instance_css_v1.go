package argocd

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) GetInstanceCSS(
	ctx context.Context,
	req *argocdv1.GetInstanceCSSRequest,
) (*argocdv1.GetInstanceCSSResponse, error) {
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetArgocdCustomStyles().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "custom styles feature is not enabled")
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	instance, err := instanceSvc.GetInstanceByID(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	spec, err := instance.GetSpec()
	if err != nil {
		return nil, err
	}
	return &argocdv1.GetInstanceCSSResponse{
		Css: spec.Css,
	}, nil
}
