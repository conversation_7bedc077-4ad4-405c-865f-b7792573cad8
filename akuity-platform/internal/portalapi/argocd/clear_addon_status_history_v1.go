package argocd

import (
	"context"
	"errors"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/addons"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) ClearAddonStatusSourceHistory(
	ctx context.Context,
	req *argocdv1.ClearAddonStatusSourceHistoryRequest,
) (*argocdv1.ClearAddonStatusSourceHistoryResponse, error) {
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetFleetManagement().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "fleet management feature is not enabled")
	}

	_, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId()))
	if err != nil {
		return nil, err
	}

	addon, err := s.addonsSvc.ClearAddonStatusSourceUpdateHistory(ctx, req.GetId())
	if err != nil {
		if errors.Is(err, addons.ErrOperationInProgress) {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
		return nil, err
	}

	addonv1, err := newAddonV1(*addon, req.GetWorkspaceId())
	if err != nil {
		return nil, err
	}
	return &argocdv1.ClearAddonStatusSourceHistoryResponse{
		Addon: addonv1,
	}, nil
}
