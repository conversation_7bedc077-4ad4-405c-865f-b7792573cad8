package argocd

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) PatchInstanceAppsetSecret(
	ctx context.Context,
	req *argocdv1.PatchInstanceAppsetSecretRequest,
) (*argocdv1.PatchInstanceAppsetSecretResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetApplicationSetController().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "application set controller feature is not enabled")
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())

	secrets := make(map[string]*string)

	for key, value := range req.GetSecret() {
		if value != nil {
			secrets[key] = value.Value
		}
	}

	err := instanceSvc.PatchInstanceAppsetSecret(ctx, req.GetId(), secrets)

	return &argocdv1.PatchInstanceAppsetSecretResponse{}, err
}
