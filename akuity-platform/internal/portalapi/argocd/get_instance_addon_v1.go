package argocd

import (
	"context"

	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/addons"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) GetInstanceAddon(
	ctx context.Context,
	req *argocdv1.GetInstanceAddonRequest,
) (*argocdv1.GetInstanceAddonResponse, error) {
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}
	instanceID := req.GetInstanceId()
	workspaceID := req.GetWorkspaceId()
	orgID := req.GetOrganizationId()
	action := accesscontrol.NewActionGetWorkspaceInstances(workspaceID, req.GetInstanceId())
	if req.GetInstanceName() != "" {
		action = accesscontrol.NewActionGetWorkspaceInstances(workspaceID, req.GetInstanceName())
		instanceSvc := s.newInstanceService(ctx, orgID)
		instance, err := instanceSvc.GetInstanceByName(ctx, req.GetInstanceName())
		if err != nil {
			if instances.IsNotFoundErr(err) {
				return nil, shared.NewPermissionDeniedErr(action)
			}
			return nil, err
		}
		workspaceID = instance.WorkspaceID.String
		instanceID = instance.InstanceID
	}

	ok, err := enforcer.EnforceAction(ctx, accesscontrol.NewActionGetWorkspaceInstances(workspaceID, instanceID))
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, shared.NewPermissionDeniedErr(action)
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, orgID,
		accesscontrol.NewActionGetWorkspaceInstances(workspaceID, instanceID)); err != nil {
		return nil, err
	}

	addon, err := s.addonsSvc.GetAddon(ctx, req.GetId(), false)
	if err != nil {
		return nil, err
	}
	addonV1, err := newAddonV1(*addon, workspaceID)
	if err != nil {
		return nil, err
	}
	return &argocdv1.GetInstanceAddonResponse{
		Addon: addonV1,
	}, nil
}

func newAddonSpecV1(spec models.AddonSpec) (*argocdv1.AddonSpec, error) {
	var onConflict argocdv1.OnConflictAction

	switch spec.AppTemplate.CreationOptions.OnConflict {
	case models.OnConflictActionOverwrite:
		onConflict = argocdv1.OnConflictAction_ON_CONFLICT_ACTION_OVERWRITE
	case models.OnConflictActionSkip:
		onConflict = argocdv1.OnConflictAction_ON_CONFLICT_ACTION_SKIP
	default:
		onConflict = argocdv1.OnConflictAction_ON_CONFLICT_ACTION_OVERWRITE
	}

	clusterOverrides, envOverrides, defaultManifestSource, err := newSourcesV1(spec.ClusterOverrides, spec.EnvOverrides, &spec.DefaultManifest)
	if err != nil {
		return nil, err
	}

	var helmValues *argocdv1.HelmValues

	if spec.HelmValues != nil && len(spec.HelmValues.YamlPaths) > 0 {
		helmValues = &argocdv1.HelmValues{
			YamlPaths: spec.HelmValues.YamlPaths,
		}
	}

	var kustomizeOption *argocdv1.KustomizeOptions
	var helmOption *argocdv1.HelmOptions

	if spec.AppTemplate.KustomizeOptions != nil {
		kustomizeOption = &argocdv1.KustomizeOptions{
			NamePrefixTemplate: spec.AppTemplate.KustomizeOptions.NamePrefixTemplate,
			NameSuffixTemplate: spec.AppTemplate.KustomizeOptions.NameSuffixTemplate,
		}
	}
	if spec.AppTemplate.HelmOptions != nil {
		helmOption = &argocdv1.HelmOptions{
			ReleaseNameTemplate: spec.AppTemplate.HelmOptions.ReleaseNameTemplate,
			PassCredentials:     spec.AppTemplate.HelmOptions.PassCredentials,
			SkipCrds:            spec.AppTemplate.HelmOptions.SkipCrds,
		}
	}

	patch, err := convertPatchCustomization(spec.PatchCustomizations)
	if err != nil {
		return nil, err
	}
	return &argocdv1.AddonSpec{
		Name:             spec.Name,
		ClusterOverrides: clusterOverrides,
		EnvOverrides:     envOverrides,
		DefaultManifest:  defaultManifestSource,
		AddonType:        string(spec.AddonType),
		Enabled:          spec.Enabled,
		ClusterSelector:  convertClusterSelectors(spec.ClusterSelector),
		AppTemplate: &argocdv1.AppTemplate{
			NameTemplate: spec.AppTemplate.NameTemplate,
			CreationOptions: &argocdv1.AppCreationOptions{
				OnConflict: onConflict,
			},
			DeletionOptions: &argocdv1.AppDeletionOptions{
				NonCascade: spec.AppTemplate.DeletionOptions.NonCascade,
			},
			SyncOptions: &argocdv1.AppSyncOptions{
				AutoSync:        spec.AppTemplate.SyncOptions.AutoSync,
				AutoHeal:        spec.AppTemplate.SyncOptions.AutoHeal,
				PruneResources:  spec.AppTemplate.SyncOptions.PruneResources,
				SyncOptionsList: spec.AppTemplate.SyncOptions.SyncOptionsList,
			},
			ProjectTemplate:   spec.AppTemplate.ProjectTemplate,
			NamespaceTemplate: spec.AppTemplate.NamespaceTemplate,
			KustomizeOptions:  kustomizeOption,
			HelmOptions:       helmOption,
		},
		HelmValues:          helmValues,
		PatchCustomizations: patch,
	}, nil
}

func newSourceUpdateResultV1(sourceUpdateResult models.SourceUpdateResult) (*argocdv1.SourceUpdateResult, error) {
	tempChanges := map[string]interface{}{}
	if err := types.RemarshalTo(sourceUpdateResult.Changes, &tempChanges); err != nil {
		return nil, err
	}
	structVal, err := structpb.NewStruct(tempChanges)
	if err != nil {
		return nil, err
	}

	sourceUpdate := &argocdv1.SourceUpdateResult{
		StartTimestamp: timestamppb.New(sourceUpdateResult.StartTimestamp),
		Attempts:       int32(sourceUpdateResult.Attempts),
		Cancelled:      sourceUpdateResult.Cancelled,
		Error:          sourceUpdateResult.Error,
		CommitSha:      sourceUpdateResult.CommitSha,
		Changes:        structVal,
		Initiator:      sourceUpdateResult.Initiator,
	}

	if sourceUpdateResult.CompletedTimestamp != nil {
		sourceUpdate.CompletedTimestamp = timestamppb.New(*sourceUpdateResult.CompletedTimestamp)
	}
	return sourceUpdate, nil
}

func newSourcesV1(clusterSources, envSources map[string]models.ManifestSource, defaultSource *models.ManifestSource) (map[string]*argocdv1.ManifestSource, map[string]*argocdv1.ManifestSource, *argocdv1.ManifestSource, error) {
	var err error
	clusterOverrides := map[string]*argocdv1.ManifestSource{}
	for k, overrides := range clusterSources {
		clusterOverrides[k] = &argocdv1.ManifestSource{
			Path: &overrides.Path,
		}
		if overrides.KustomizeSource != nil {
			clusterOverrides[k].KustomizeSource, err = getKustomizeSource(overrides.KustomizeSource)
			if err != nil {
				return nil, nil, nil, err
			}
		} else if overrides.HelmSource != nil {
			clusterOverrides[k].HelmSource, err = getHelmSource(overrides.HelmSource)
			if err != nil {
				return nil, nil, nil, err
			}
		}
	}
	envOverrides := map[string]*argocdv1.ManifestSource{}
	for k, overrides := range envSources {
		envOverrides[k] = &argocdv1.ManifestSource{
			Path: &overrides.Path,
		}
		if overrides.KustomizeSource != nil {
			envOverrides[k].KustomizeSource, err = getKustomizeSource(overrides.KustomizeSource)
			if err != nil {
				return nil, nil, nil, err
			}
		} else if overrides.HelmSource != nil {
			envOverrides[k].HelmSource, err = getHelmSource(overrides.HelmSource)
			if err != nil {
				return nil, nil, nil, err
			}
		}
	}
	defaultManifestSource := argocdv1.ManifestSource{}
	if defaultSource != nil {
		defaultManifestSource.Path = &defaultSource.Path
		if defaultSource.KustomizeSource != nil {
			defaultManifestSource.KustomizeSource, err = getKustomizeSource(defaultSource.KustomizeSource)
			if err != nil {
				return nil, nil, nil, err
			}
		} else if defaultSource.HelmSource != nil {
			defaultManifestSource.HelmSource, err = getHelmSource(defaultSource.HelmSource)
			if err != nil {
				return nil, nil, nil, err
			}
		}
	}
	return clusterOverrides, envOverrides, &defaultManifestSource, nil
}

func getKustomizeSource(kustomizeSource *models.KustomizeSource) (*argocdv1.KustomizeSource, error) {
	if kustomizeSource == nil {
		return nil, nil
	}

	resKustomizeSource := &argocdv1.KustomizeSource{}

	if len(kustomizeSource.Images) > 0 {
		resKustomizeSource.Images = []*argocdv1.KustomizeImage{}
		for _, image := range kustomizeSource.Images {
			resKustomizeSource.Images = append(resKustomizeSource.Images, &argocdv1.KustomizeImage{
				Name:      getString(image, "name"),
				NewTag:    getString(image, "newTag"),
				NewName:   getString(image, "newName"),
				TagSuffix: getString(image, "tagSuffix"),
				Digest:    getString(image, "digest"),
			})
		}
	}
	if len(kustomizeSource.HelmCharts) > 0 {
		resKustomizeSource.HelmCharts = []*argocdv1.KustomizeHelmChart{}
		for _, helmChart := range kustomizeSource.HelmCharts {
			resKustomizeSource.HelmCharts = append(resKustomizeSource.HelmCharts, &argocdv1.KustomizeHelmChart{
				Name:    getString(helmChart, "name"),
				Version: getString(helmChart, "version"),
			})
		}

	}
	return resKustomizeSource, nil
}

func getHelmSource(helmSource *models.HelmSource) (*argocdv1.HelmSource, error) {
	if helmSource == nil {
		return nil, nil
	}

	return &argocdv1.HelmSource{
		Values:       helmSource.Values,
		Dependencies: reverseChartDependencies(helmSource.ChartDependencies),
	}, nil
}

func newAddonStatusOperationV1(statusOp models.StatusOperation) *argocdv1.StatusOperation {
	return &argocdv1.StatusOperation{
		ClusterAddonStatusOperation: &argocdv1.ClusterAddonStatusOperation{
			Revision:    statusOp.Revision,
			Prune:       statusOp.Prune,
			SyncOptions: statusOp.SyncOptions,
			Initiator:   statusOp.Initiator,
		},
		ClusterSelector: convertClusterSelectors(statusOp.ClusterSelector),
	}
}

func newAddonStatusSourceUpdateV1(statusOp models.StatusSourceUpdate) (*argocdv1.StatusSourceUpdate, error) {
	clusterOverrides, envOverrides, defaultManifestSource, err := newSourcesV1(statusOp.ClusterOverrides, statusOp.EnvOverrides, statusOp.DefaultManifestSource)
	if err != nil {
		return nil, err
	}

	return &argocdv1.StatusSourceUpdate{
		StartTimestamp: timestamppb.New(statusOp.StartTimestamp),
		Cancelled:      statusOp.Cancelled,
		Initiator:      statusOp.Initiator,
		Sources: &argocdv1.SourceInfo{
			ClusterOverrides: clusterOverrides,
			EnvOverrides:     envOverrides,
			DefaultManifest:  defaultManifestSource,
		},
	}, nil
}

func newAddonV1(addon addons.Addon, workspaceID string) (*argocdv1.Addon, error) {
	spec, err := addon.GetSpec()
	if err != nil {
		return nil, err
	}
	status, err := addon.GetStatus()
	if err != nil {
		return nil, err
	}
	amiStatus := models.AddonMarketplaceStatus{}
	if !addon.AddonMarketplaceInstallStatusInfo.IsZero() {
		err := addon.AddonMarketplaceInstallStatusInfo.Unmarshal(&amiStatus)
		if err != nil {
			return nil, err
		}
	}

	addonV1 := argocdv1.Addon{
		Id:                                addon.ID,
		OrganizationId:                    addon.OrganizationID,
		InstanceId:                        addon.InstanceID,
		RepoId:                            addon.RepoID,
		Generation:                        uint32(addon.Generation),
		WorkspaceId:                       workspaceID,
		AddonMarketplaceInstallsId:        addon.AddonMarketplaceInstallID,
		AddonMarketplaceInstallStatusInfo: reverseAddonMarketplaceStatusInfo(&amiStatus, addon.ID == "" && addon.AddonMarketplaceGeneration != addon.AddonMarketplaceProcessedGeneration.Int),
	}
	if !addon.DeletionTimestamp.IsZero() {
		addonV1.DeleteTime = timestamppb.New(addon.DeletionTimestamp.Time)
	}

	if status != nil {
		generationMatch := addon.Generation == status.ProcessedGeneration
		var lastSourceUpdateResult *argocdv1.SourceUpdateResult
		if status.LastSourceUpdateStatus != nil {
			lastSourceUpdateResult, err = newSourceUpdateResultV1(*status.LastSourceUpdateStatus)
			if err != nil {
				return nil, err
			}
		}
		envOverrideStatus := map[string]*argocdv1.AddonHealthStatus{}
		clusterOverrideStatus := map[string]*argocdv1.AddonHealthStatus{}
		if addon.ManifestSourceHealth != nil {
			for k, v := range addon.EnvOverrides {
				envOverrideStatus[k] = &argocdv1.AddonHealthStatus{
					Health: &argocdv1.ApplicationsHealth{
						HealthyCount:     uint32(v.HealthyCount),
						DegradedCount:    uint32(v.DegradedCount),
						ProgressingCount: uint32(v.ProgressingCount),
						UnknownCount:     uint32(v.UnknownCount),
						SuspendedCount:   uint32(v.SuspendedCount),
						MissingCount:     uint32(v.MissingCount),
					},
					SyncStatus: &argocdv1.ApplicationsSyncStatus{
						SyncedCount:    uint32(v.SyncedCount),
						OutOfSyncCount: uint32(v.OutOfSyncCount),
						UnknownCount:   uint32(v.SyncUnknownCount),
					},
				}
			}
			for k, v := range addon.ClusterOverrides {
				clusterOverrideStatus[k] = &argocdv1.AddonHealthStatus{
					Health: &argocdv1.ApplicationsHealth{
						HealthyCount:     uint32(v.HealthyCount),
						DegradedCount:    uint32(v.DegradedCount),
						ProgressingCount: uint32(v.ProgressingCount),
						UnknownCount:     uint32(v.UnknownCount),
						SuspendedCount:   uint32(v.SuspendedCount),
						MissingCount:     uint32(v.MissingCount),
					},
					SyncStatus: &argocdv1.ApplicationsSyncStatus{
						SyncedCount:    uint32(v.SyncedCount),
						OutOfSyncCount: uint32(v.OutOfSyncCount),
						UnknownCount:   uint32(v.SyncUnknownCount),
					},
				}
			}
		}
		statusV1 := argocdv1.AddonStatus{
			LastSyncTime:        status.LastSyncTime,
			LastSyncCommit:      status.LastSyncCommit,
			ClusterCount:        uint32(status.ClusterCount),
			ProcessedGeneration: uint32(status.ProcessedGeneration),
			Health: &argocdv1.ApplicationsHealth{
				HealthyCount:     uint32(addon.HealthyCount),
				DegradedCount:    uint32(addon.DegradedCount),
				ProgressingCount: uint32(addon.ProgressingCount),
				UnknownCount:     uint32(addon.UnknownCount),
				SuspendedCount:   uint32(addon.SuspendedCount),
				MissingCount:     uint32(addon.MissingCount),
			},
			SyncStatus: &argocdv1.ApplicationsSyncStatus{
				SyncedCount:    uint32(addon.SyncedCount),
				OutOfSyncCount: uint32(addon.OutOfSyncCount),
				UnknownCount:   uint32(addon.SyncUnknownCount),
			},
			EnvOverrides:           envOverrideStatus,
			ClusterOverrides:       clusterOverrideStatus,
			ReconciliationStatus:   getAddonReconciliationStatus(generationMatch, spec != nil && spec.Enabled, status.Conditions),
			LastSourceUpdateStatus: lastSourceUpdateResult,
			HasError:               addon.HasErrors,
		}

		addonV1.Status = &statusV1
	}
	if !addon.StatusOperation.IsZero() {
		statusOp, err := addon.GetStatusOperation()
		if err != nil {
			return nil, err
		}
		if statusOp != nil {
			addonV1.StatusOperation = newAddonStatusOperationV1(*statusOp)
		}
	}

	if !addon.StatusSourceUpdate.IsZero() {
		statusSource, err := addon.GetStatusSourceUpdate()
		if err != nil {
			return nil, err
		}
		if statusSource != nil {
			addonV1.StatusSourceUpdate, err = newAddonStatusSourceUpdateV1(*statusSource)
			if err != nil {
				return nil, err
			}
		}
	}

	if spec != nil {
		addonV1.Spec, err = newAddonSpecV1(*spec)
		if err != nil {
			return nil, err
		}
	}

	return &addonV1, nil
}

func getString(m map[string]interface{}, key string) string {
	if val, ok := m[key].(string); ok {
		return val
	}
	return ""
}
