package argocd

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) RegenerateInstanceAccountPassword(
	ctx context.Context,
	req *argocdv1.RegenerateInstanceAccountPasswordRequest,
) (*argocdv1.RegenerateInstanceAccountPasswordResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	password, err := database.RandomAlphabetString()
	if err != nil {
		return nil, fmt.Errorf("failed to generate password: %w", err)
	}
	if _, err := instanceSvc.UpdatePassword(ctx, req.GetInstanceId(), req.GetName(), password); err != nil {
		if instances.IsNotFoundErr(err) {
			return nil, shared.ErrNotFound
		}
		var apiErr *errorsutil.APIStatus
		if errors.As(err, &apiErr) {
			if apiErr.Status == http.StatusNotFound {
				return nil, shared.ErrNotFound
			}
		}
		return nil, fmt.Errorf("update password: %w", err)
	}
	return &argocdv1.RegenerateInstanceAccountPasswordResponse{
		Password: password,
	}, nil
}
