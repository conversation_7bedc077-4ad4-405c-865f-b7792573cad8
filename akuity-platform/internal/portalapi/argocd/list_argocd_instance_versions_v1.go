package argocd

import (
	"context"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) ListInstanceVersions(
	ctx context.Context,
	req *argocdv1.ListInstanceVersionsRequest,
) (*argocdv1.ListInstanceVersionsResponse, error) {
	versions, _ := types.MapSlice(s.versions, func(in client.ComponentVersion) (*argocdv1.InstanceVersion, error) {
		return &argocdv1.InstanceVersion{
			Version: in.Version,
			Label:   in.Label,
		}, nil
	})
	return &argocdv1.ListInstanceVersionsResponse{
		Versions: versions,
	}, nil
}
