package argocd

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	"google.golang.org/genproto/googleapis/api/httpbody"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"gopkg.in/yaml.v3"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"

	agentclient "github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/util/encryption"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

var (
	configMapsGVR  = schema.GroupVersionResource{Version: "v1", Resource: "configmaps"}
	secretsGVR     = schema.GroupVersionResource{Version: "v1", Resource: "secrets"}
	internalFields = [][]string{{
		"metadata", "annotations", "kubectl.kubernetes.io/last-applied-configuration",
	}, {
		"metadata", "managedFields",
	}, {
		"metadata", "resourceVersion",
	}, {
		"metadata", "uid",
	}, {
		"metadata", "creationTimestamp",
	}}
)

func (s *ArgoCDV1Server) GetInstanceClusterManifests(
	req *argocdv1.GetInstanceClusterManifestsRequest,
	ws argocdv1.ArgoCDService_GetInstanceClusterManifestsServer,
) error {
	ctx := ws.Context()

	organizationId := req.GetOrganizationId()
	clusterId := req.GetId()

	instanceSvc := s.newInstanceService(ctx, organizationId)

	// TODO: let's switch from clusterID to clusterName if it's possible.
	cluster, err := instanceSvc.GetClusterByID(ctx, clusterId)
	if err != nil {
		if instances.IsNotFoundErr(err) {
			// return permission denied so we not expose if cluster exists or not
			// before running enforcer
			return status.Error(codes.PermissionDenied, "")
		}
		return err
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetInstanceClusters(req.GetWorkspaceId(), cluster.InstanceID, cluster.Name)); err != nil {
		return err
	}

	instanceId := cluster.InstanceID

	if instanceId != req.GetInstanceId() {
		return status.Error(codes.PermissionDenied, "")
	}

	instance, err := instanceSvc.GetInstanceByID(ctx, instanceId)
	if err != nil {
		if instances.IsNotFoundErr(err) {
			return shared.ErrNotFound
		}
		return err
	}

	spec, err := cluster.GetSpec()
	if err != nil {
		return err
	}
	if spec.DirectClusterSpec != nil && !spec.DirectClusterSpec.Type.IsEmpty() {
		return status.Error(codes.Unavailable, fmt.Sprintf("%v type clusters do no support installation manifests", spec.DirectClusterSpec.Type))
	}

	if err := grpc.SendHeader(ctx, metadata.New(map[string]string{
		"Content-Disposition": fmt.Sprintf("attachment; filename=akuity-manifests-%s.yaml", cluster.Name),
	})); err != nil {
		return err
	}

	// default manifest size is around 50Kb, let's make it so we deliver it in one chunk.
	const chunkSize = 4096 * 16
	cursor := 1
	accumulated := ""
	decoder, err := encryption.NewCTRDecoder()
	firstSend := true
	if err != nil {
		return err
	}
	for {
		chunk, err := instanceSvc.GetClusterManifestByID(ctx, clusterId, cursor, chunkSize)
		if err != nil {
			return err
		}
		if chunk == "" {
			break
		}
		decrypted, err := decoder.Next(chunk)
		if err != nil {
			if !errors.Is(err, encryption.ErrNotEncoded) {
				return err
			}
			// backward compatibily for not encrypted status manifest
			decrypted = chunk
		}
		accumulated += decrypted
		lastNewlineIndex := strings.LastIndex(accumulated, "\n")
		if lastNewlineIndex != -1 {
			sendChunk := accumulated[:lastNewlineIndex]
			if req.GetSkipNamespace() && firstSend {
				// we expect the namespace resource to be in the first chunk that is being sent
				sendChunk = shared.RemoveNamespaceFromChunked(sendChunk)
			}
			if err := ws.Send(&httpbody.HttpBody{
				Data:        []byte(sendChunk),
				ContentType: "application/yaml",
			}); err != nil {
				return err
			}
			firstSend = false
			accumulated = accumulated[lastNewlineIndex+1:]
		}
		cursor += chunkSize
	}

	if accumulated != "" {
		if err := ws.Send(&httpbody.HttpBody{
			Data:        []byte(accumulated),
			ContentType: "application/yaml",
		}); err != nil {
			return err
		}
	}

	if cursor == 1 {
		return shared.ErrUnavailable
	}

	if req.GetOfflineInstallation() {
		if !config.IsSelfHosted {
			return status.Error(codes.Unavailable, "offline installation is only available for self-hosted instances")
		}

		var clientset dynamic.Interface
		if instance.Shard == "" {

			tenant, err := agentclient.NewArgoCDTenant(s.hostRestConfig, *s.log, req.InstanceId)
			if err != nil {
				return err
			}
			clientset, err = tenant.ControlPlaneDynamicClientset(ctx)
			if err != nil {
				return err
			}
		} else {
			privateSpec, err := instance.GetPrivateSpec()
			if err != nil {
				return err
			}

			restConfig, err := privateSpec.GetRestConfig()
			if err != nil {
				return err
			}
			clientset, err = dynamic.NewForConfig(restConfig)
			if err != nil {
				return err
			}
		}
		resources := []struct {
			GVR      schema.GroupVersionResource
			Selector string
		}{{
			GVR: misc.AppprojectsGVR,
		}, {
			GVR: misc.ApplicationGVR, Selector: "cluster=" + cluster.Name,
		}, {
			GVR: configMapsGVR, Selector: "akuity.io/argo-cd-instance-id",
		}, {
			GVR: secretsGVR, Selector: "argocd.argoproj.io/secret-type=repository", // repo secrets
		}, {
			GVR: secretsGVR, Selector: "argocd.argoproj.io/secret-type=repo-creds", // repo creds secrets
		}, {
			GVR: secretsGVR, Selector: "argocd.argoproj.io/secret-type=cluster,akuity.io/argo-cd-cluster-name=" + cluster.Name, // destination cluster secret
		}, {
			GVR: secretsGVR, Selector: "app.kubernetes.io/part-of=argocd,!akuity.io/secret-type", // image updater, notifications controller etc secrets
		}}
		for _, resource := range resources {
			items, err := clientset.Resource(resource.GVR).Namespace(common.K3sArgoCDNamespace).List(ctx, v1.ListOptions{
				LabelSelector: resource.Selector,
			})
			if err != nil {
				return err
			}
			for _, item := range items.Items {
				for i := range internalFields {
					unstructured.RemoveNestedField(item.Object, internalFields[i]...)
					if len(item.GetAnnotations()) == 0 {
						item.SetAnnotations(nil)
					}
				}
				// apply generation label to all resources except argoproj resources
				if item.GroupVersionKind().Group != "argoproj.io" {
					if err := unstructured.SetNestedField(item.Object, strconv.Itoa(cluster.Generation), "metadata", "labels", "generation"); err != nil {
						return err
					}
				}
				item.SetNamespace(cluster.Namespace)
				if item.GetAPIVersion() == "v1" && item.GetKind() == "Secret" && item.GetName() == "argocd-secret" {
					unstructured.RemoveNestedField(item.Object, "data")
				}
				if item.GetAPIVersion() == "v1" && item.GetKind() == "ConfigMap" && item.GetName() == "argocd-cm" {
					if data, ok, err := unstructured.NestedStringMap(item.Object, "data"); err == nil && ok {
						if inclusionsData, err := agentclient.UpdateArgoCDConfigMapInclusions([]byte(data["resource.inclusions"])); err == nil {
							data["resource.inclusions"] = string(inclusionsData)
						} else {
							return err
						}
						if err := unstructured.SetNestedStringMap(item.Object, data, "data"); err != nil {
							return err
						}
					}
				}

				if data, err := yaml.Marshal(item.Object); err != nil {
					return err
				} else {
					if err := ws.Send(&httpbody.HttpBody{
						Data:        []byte("---\n" + string(data)),
						ContentType: "application/yaml",
					}); err != nil {
						return err
					}
				}
			}
		}
	}
	return nil
}
