package argocd

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) UpdateInstanceCSS(
	ctx context.Context,
	req *argocdv1.UpdateInstanceCSSRequest,
) (*argocdv1.UpdateInstanceCSSResponse, error) {
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetArgocdCustomStyles().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "custom styles feature is not enabled")
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())
	instance, err := instanceSvc.GetInstanceByID(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	spec, err := instance.GetSpec()
	if err != nil {
		return nil, err
	}

	spec.Css = req.GetCss()

	if err := instance.SetSpec(spec); err != nil {
		return nil, err
	}
	if err := instanceSvc.ArgoCDInstanceConfigs().Update(ctx, &models.ArgoCDInstanceConfig{
		InstanceID: instance.ID,
		Spec:       instance.Spec,
	}, models.ArgoCDInstanceConfigColumns.Spec); err != nil {
		return nil, err
	}
	return &argocdv1.UpdateInstanceCSSResponse{
		Css: spec.Css,
	}, nil
}
