package argocd

import (
	"context"
	"errors"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

// UpdateManagedSecret updates an existing managed secret in the ArgoCD instance. For all intents
// and purposes, this is a full replacement of the secret, as we don't want to send raw secret data
// back over the API and have that be viewable to the user
func (s *ArgoCDV1Server) UpdateManagedSecret(
	ctx context.Context,
	req *argocdv1.UpdateManagedSecretRequest,
) (*argocdv1.UpdateManagedSecretResponse, error) {
	if err := s.updateSecret(
		ctx,
		req.ManagedSecret,
		req.ManagedSecretData,
		req.GetOrganizationId(),
		req.GetWorkspaceId(),
		req.GetInstanceId(),
		req.GetName(),
	); err != nil {
		return nil, err
	}
	return &argocdv1.UpdateManagedSecretResponse{}, nil
}

func (s *ArgoCDV1Server) PatchManagedSecret(
	ctx context.Context,
	req *argocdv1.PatchManagedSecretRequest,
) (*argocdv1.PatchManagedSecretResponse, error) {
	if err := s.updateSecret(
		ctx,
		req.ManagedSecret,
		nil, // No secret data in patch request
		req.GetOrganizationId(),
		req.GetWorkspaceId(),
		req.GetInstanceId(),
		req.GetName(),
	); err != nil {
		return nil, err
	}
	return &argocdv1.PatchManagedSecretResponse{}, nil
}

func (s *ArgoCDV1Server) updateSecret(
	ctx context.Context,
	managedSecret *argocdv1.ManagedSecret,
	secretData map[string]string,
	orgID, workspaceID, instanceID, name string,
) error {
	k3sKubeClient, err := s.getKubeClientForInstance(ctx, orgID, workspaceID, instanceID)
	if err != nil {
		if instances.IsNotFoundErr(err) {
			return status.Errorf(codes.NotFound, "instance %q not found", instanceID)
		}
		return err
	}

	// Get the existing secret
	existingSecret, err := ensureSecretIsManaged(ctx, k3sKubeClient, name)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return status.Errorf(codes.NotFound, "managed secret %q not found", name)
		} else if errors.Is(err, errorNotManagedSecret) {
			// If the secret is not managed, we should let the user know
			return status.Errorf(codes.InvalidArgument, "cannot update reserved secret %q", name)
		}
		return status.Errorf(codes.Internal, "failed to fetch managed secret")
	}

	// Pull the owned by cluster labels if it exists and add it back to the labels
	currentLabels := managedSecret.Labels
	if existingSecret.Labels[client.LabelKeySecretOwnedByCluster] != "" {
		if len(currentLabels) != 0 {
			return status.Errorf(codes.InvalidArgument, "cannot update managed secret %q: labels can only be updated by the managed cluster", name)
		}
		// Set the current labels to the existing labels
		currentLabels = existingSecret.Labels
	}

	// We explicitly do not allow changing the secret type once it is created so we ignore that here
	// Prepare labels, ensuring we add the secret type label
	labels := prepManagedSecretLabels(
		currentLabels,
		existingSecret.Labels[argocdManagedSecretTypeLabel],
	)

	clusterSelector, err := toK8sLabelSelector(managedSecret.ClusterSelector)
	if err != nil {
		return status.Errorf(codes.InvalidArgument, "invalid cluster selector: %v", err)
	}

	annotations := prepManagedSecretAnnotations(
		managedSecret.AllowedClusters,
		clusterSelector,
	)

	// Update the secret
	existingSecret.Labels = labels
	existingSecret.Annotations = annotations
	// Only update the data if provided and non empty
	if len(secretData) > 0 {
		// We need to clear the existing data to avoid merging issues
		existingSecret.Data = make(map[string][]byte)
		for key, value := range secretData {
			existingSecret.Data[key] = []byte(value)
		}
	}

	_, err = k3sKubeClient.CoreV1().Secrets(common.K3sArgoCDNamespace).Update(ctx, existingSecret, metav1.UpdateOptions{})
	if err != nil {
		return err
	}
	return nil
}
