package argocd

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) RefreshInstanceAddon(
	ctx context.Context,
	req *argocdv1.RefreshInstanceAddonRequest,
) (*argocdv1.RefreshInstanceAddonResponse, error) {
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetFleetManagement().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "fleet management feature is not enabled")
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId())); err != nil {
		return nil, err
	}

	addon, err := s.addonsSvc.RefreshAddon(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	addonv1, err := newAddonV1(*addon, req.GetWorkspaceId())
	if err != nil {
		return nil, err
	}

	return &argocdv1.RefreshInstanceAddonResponse{
		Addon: addonv1,
	}, nil
}
