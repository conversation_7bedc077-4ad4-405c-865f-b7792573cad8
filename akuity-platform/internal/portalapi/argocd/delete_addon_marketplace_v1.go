package argocd

import (
	"context"
	"net/http"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) DeleteAddonMarketplaceInstall(
	ctx context.Context,
	req *argocdv1.DeleteAddonMarketplaceInstallRequest,
) (*argocdv1.DeleteAddonMarketplaceInstallResponse, error) {
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetFleetManagement().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "this feature is not available")
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetInstanceId())); err != nil {
		return nil, err
	}

	instanceCfg, err := s.repoSet.ArgoCDInstanceConfigs().GetByID(ctx, req.GetInstanceId())
	if err != nil {
		return nil, err
	}

	// only allow to delete addon if instance is running ak version > 59
	isAkp, version := misc.GetAKPVersion(instanceCfg.Version.String)
	if !isAkp || version < 59 {
		return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "addons delete supported only for AKP argocd version instances, running ak.59 or higher")
	}

	if err := s.addonsSvc.DeleteMarketplaceAddon(ctx, req.GetId()); err != nil {
		return nil, err
	}

	return &argocdv1.DeleteAddonMarketplaceInstallResponse{}, nil
}
