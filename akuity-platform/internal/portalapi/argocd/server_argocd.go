package argocd

import (
	"context"
	"database/sql"
	"time"

	"github.com/go-logr/logr"
	"github.com/go-playground/validator/v10"
	"google.golang.org/protobuf/encoding/protojson"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sruntime "k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/client-go/informers"
	informerv1 "k8s.io/client-go/informers/core/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/cache"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/controllers/platform/integration/clusterautoscaler"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/addons"
	"github.com/akuityio/akuity-platform/internal/services/apikeys"
	"github.com/akuityio/akuity-platform/internal/services/customroles"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	modelClient "github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/events"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

type ArgoCDV1Server struct {
	argocdv1.ArgoCDServiceServer

	cfg                  config.PortalServerConfig
	jsonMarshalOptions   protojson.MarshalOptions
	jsonUnmarshalOptions protojson.UnmarshalOptions

	db       *sql.DB
	versions []client.ComponentVersion
	acs      accesscontrol.PolicyService
	repoSet  modelClient.RepoSet

	instancesWatcher database.Watcher[events.InstanceEvent]
	clustersWatcher  database.Watcher[events.ClusterEvent]

	hostRestConfig *rest.Config
	hostKubeClient *kubernetes.Clientset

	cmInformer informerv1.ConfigMapInformer

	log *logr.Logger

	featSvc                 features.Service
	clusterAutoscalerConfig common.AutoScalerConfig
	addonsSvc               addons.Service
}

func NewArgoCDV1Server(
	cfg config.PortalServerConfig,
	log *logr.Logger,
	versions []client.ComponentVersion,
	db *sql.DB,
	v *validator.Validate,
	instancesWatcher database.Watcher[events.InstanceEvent],
	clustersWatcher database.Watcher[events.ClusterEvent],
	addonReposWatcher database.Watcher[events.AddonEvent],
	addonMarketplaceInstallWatcher database.Watcher[events.AddonEvent],
	addonsWatcher database.Watcher[events.AddonEvent],
	addonClusterWatcher database.Watcher[events.AddonEvent],
	hostRestConfig *rest.Config,
	hostKubeClient *kubernetes.Clientset,
) *ArgoCDV1Server {
	factory := informers.NewSharedInformerFactoryWithOptions(hostKubeClient, 5*time.Minute, informers.WithNamespace(config.InternalCmNamespace), informers.WithTweakListOptions(
		func(options *metav1.ListOptions) {
			options.FieldSelector = "metadata.name=" + config.InternalCmName
		},
	))
	configMapInformer := factory.Core().V1().ConfigMaps()

	repoSet := modelClient.NewRepoSet(db)
	aks := apikeys.NewService(repoSet, v)
	teamSvc := teams.NewService(db)
	workspaceSvc := workspaces.NewService(db, teamSvc, cfg.FeatureGatesSource)
	clusterAutoscalerConfig, err := clusterautoscaler.NewConfig(log, hostKubeClient)
	cli.CheckErr(err)
	return &ArgoCDV1Server{
		cfg:                     cfg,
		jsonMarshalOptions:      protojson.MarshalOptions{},
		jsonUnmarshalOptions:    protojson.UnmarshalOptions{},
		db:                      db,
		repoSet:                 repoSet,
		versions:                versions,
		acs:                     accesscontrol.NewPolicyService(v, customroles.New(repoSet), workspaceSvc, aks, teamSvc),
		instancesWatcher:        instancesWatcher,
		clustersWatcher:         clustersWatcher,
		hostRestConfig:          hostRestConfig,
		hostKubeClient:          hostKubeClient,
		log:                     log,
		cmInformer:              configMapInformer,
		featSvc:                 features.NewService(repoSet, db, config.IsSelfHosted, config.IsSelfHostedReleaseBuild, cfg.FeatureGatesSource, config.GetLicense(), features.WithLogger(log)),
		clusterAutoscalerConfig: clusterAutoscalerConfig,
		addonsSvc:               addons.NewService(log, db, addonReposWatcher, addonMarketplaceInstallWatcher, addonsWatcher, addonClusterWatcher, cfg.FeatureGatesSource),
	}
}

func (s *ArgoCDV1Server) StartInformer(ctx context.Context) {
	informer := s.cmInformer.Informer()
	stopper := make(chan struct{})
	defer k8sruntime.HandleCrash()
	go informer.Run(ctx.Done())
	if !cache.WaitForCacheSync(stopper, informer.HasSynced) {
		panic("failed to sync informer cache")
	}
	<-ctx.Done()
}

// newInstanceService is a helper wrapper for getting a new instances Service with the default options. It is a convenience
// wrapper for the newInstanceServiceWithOptions method.
func (s *ArgoCDV1Server) newInstanceService(ctx context.Context, orgId string) *instances.Service {
	return s.newInstanceServiceWithOptions(ctx, orgId)
}

// newInstanceServiceWithOptions is a helper wrapper for getting a new instances Service with
// options. This is a common operation across the endpoints in this module and is mostly here for DRY purposes.
func (s *ArgoCDV1Server) newInstanceServiceWithOptions(ctx context.Context, orgId string, opts ...instances.Option) *instances.Service {
	// This puts the default options to the front of the list, so that they can overrided by the
	// options passed in by the caller. The opts are iterated over in order so they will override
	opts = append([]instances.Option{
		instances.WithOrganizationScope(orgId),
		instances.WithLogger(logging.Extract(ctx)),
		// If termination isn't enabled, that means we are in a mode where redis tunneling should
		// happen by default
		instances.WithRedisTunneling(!s.cfg.TLSTerminationEnabled),
	}, opts...)
	return instances.NewServiceWithOptions(s.db,
		s.cfg.DomainSuffix,
		s.featSvc,
		opts...,
	)
}

// getKubeClientForInstance is a helper function that retrieves a Kubernetes client for a specific
// instance within an organization and workspace.
func (s *ArgoCDV1Server) getKubeClientForInstance(ctx context.Context, orgID, workspaceID, instanceID string) (kubernetes.Interface, error) {
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, orgID)
	if err != nil {
		return nil, err
	}

	action := accesscontrol.NewActionGetWorkspaceInstances(workspaceID, instanceID)

	ok, err := enforcer.EnforceAction(ctx, action)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, shared.NewPermissionDeniedErr(action)
	}

	instanceSvc := s.newInstanceService(ctx, orgID)

	instance, err := instanceSvc.GetInstanceByID(ctx, instanceID)
	if err != nil {
		return nil, err
	}

	var k3sKubeClient kubernetes.Interface
	if instance.Shard == "" {
		tnt, err := client.NewArgoCDTenant(s.hostRestConfig, *s.log, instanceID)
		if err != nil {
			return nil, err
		}
		k3sKubeClient, err = tnt.ControlPlaneKubeClientset(ctx)
		if err != nil {
			return nil, err
		}
	} else {
		privateSpec, err := instance.GetPrivateSpec()
		if err != nil {
			return nil, err
		}

		restConfig, err := privateSpec.GetRestConfig()
		if err != nil {
			return nil, err
		}
		k3sKubeClient, err = kubernetes.NewForConfig(restConfig)
		if err != nil {
			return nil, err
		}
	}
	return k3sKubeClient, nil
}
