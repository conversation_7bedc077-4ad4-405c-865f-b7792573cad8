package argocd

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
)

func (s *ArgoCDV1Server) PatchInstanceSecret(
	ctx context.Context,
	req *argocdv1.PatchInstanceSecretRequest,
) (*argocdv1.PatchInstanceSecretResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	instanceSvc := s.newInstanceService(ctx, req.GetOrganizationId())

	secrets := make(map[string]*string)
	for key, value := range req.GetSecret() {
		if value != nil {
			secrets[key] = value.Value
		}
	}

	if err := instanceSvc.PatchInstanceSecret(ctx, req.GetId(), secrets); err != nil {
		return nil, err
	}
	return &argocdv1.PatchInstanceSecretResponse{}, nil
}
