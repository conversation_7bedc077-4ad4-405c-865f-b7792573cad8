package apikey

import (
	"context"
	"database/sql"

	"github.com/go-playground/validator/v10"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"github.com/xhit/go-str2duration/v2"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/apikeys"
	"github.com/akuityio/akuity-platform/internal/services/customroles"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	"github.com/akuityio/akuity-platform/models/client"
	apikeyv1 "github.com/akuityio/akuity-platform/pkg/api/gen/apikey/v1"
)

type APIKeyV1Server struct {
	apikeyv1.APIKeyServiceServer

	acs accesscontrol.PolicyService
	aks apikeys.Service
	db  boil.ContextExecutor
}

func NewAPIKeyServer(db *sql.DB, v *validator.Validate, featureGatesSource features.FeatureGatesSource) *APIKeyV1Server {
	repoSet := client.NewRepoSet(db)
	aks := apikeys.NewService(repoSet, v)
	teamSvc := teams.NewService(db)
	workspaceSvc := workspaces.NewService(db, teamSvc, featureGatesSource)
	return &APIKeyV1Server{
		acs: accesscontrol.NewPolicyService(v, customroles.New(repoSet), workspaceSvc, aks, teamSvc),
		aks: aks,
		db:  db,
	}
}

func (s *APIKeyV1Server) GetWorkspaceAPIKey(ctx context.Context, req *apikeyv1.GetWorkspaceAPIKeyRequest) (*apikeyv1.GetWorkspaceAPIKeyResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceAPIKeys(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}
	key, err := s.aks.Get(ctx, req.GetId())
	if err != nil {
		if apikeys.IsNotFoundErr(err) {
			return nil, status.Errorf(codes.NotFound, "APIKey %s not found", req.GetId())
		}
		return nil, err
	}

	return &apikeyv1.GetWorkspaceAPIKeyResponse{
		ApiKey: shared.NewAPIKeyV1(*key, ""),
	}, nil
}

func (s *APIKeyV1Server) DeleteWorkspaceAPIKey(ctx context.Context, req *apikeyv1.DeleteWorkspaceAPIKeyRequest) (*apikeyv1.DeleteWorkspaceAPIKeyResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionDeleteWorkspaceAPIKeys(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	if err := s.aks.Delete(ctx, req.GetId()); err != nil {
		if !apikeys.IsNotFoundErr(err) {
			return nil, err
		}
	}
	return &apikeyv1.DeleteWorkspaceAPIKeyResponse{}, nil
}

func (s *APIKeyV1Server) RegenerateWorkspaceAPIKeySecret(ctx context.Context, req *apikeyv1.RegenerateWorkspaceAPIKeySecretRequest) (*apikeyv1.RegenerateWorkspaceAPIKeySecretResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceAPIKeys(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}
	expiry, err := str2duration.ParseDuration(req.Expiry)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid expiry '%s'", req.Expiry)
	}
	opts := apikeys.RegenerateOpts{
		Expiry: expiry,
	}
	key, secret, err := s.aks.RegenerateSecret(ctx, req.GetId(), opts)
	if err != nil {
		if apikeys.IsNotFoundErr(err) {
			return nil, status.Errorf(codes.NotFound, "APIKey %s not found", req.GetId())
		}
		return nil, err
	}
	return &apikeyv1.RegenerateWorkspaceAPIKeySecretResponse{
		ApiKey: shared.NewAPIKeyV1(*key, secret),
	}, nil
}

func (s *APIKeyV1Server) GetAPIKey(ctx context.Context, req *apikeyv1.GetAPIKeyRequest) (*apikeyv1.GetAPIKeyResponse, error) {
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}

	key, err := s.aks.Get(ctx, req.GetId())
	if err != nil {
		if apikeys.IsNotFoundErr(err) {
			return nil, status.Errorf(codes.NotFound, "APIKey %s not found", req.GetId())
		}
		return nil, err
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, key.OrganizationID,
		accesscontrol.NewActionGetAPIKeys(key.ID)); err != nil {
		return nil, err
	}
	return &apikeyv1.GetAPIKeyResponse{
		ApiKey: shared.NewAPIKeyV1(*key, ""),
	}, nil
}

func (s *APIKeyV1Server) DeleteAPIKey(ctx context.Context, req *apikeyv1.DeleteAPIKeyRequest) (*apikeyv1.DeleteAPIKeyResponse, error) {
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}

	key, err := s.aks.Get(ctx, req.GetId())
	if err != nil {
		if apikeys.IsNotFoundErr(err) {
			return nil, status.Errorf(codes.NotFound, "APIKey %s not found", req.GetId())
		}
		return nil, err
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, key.OrganizationID,
		accesscontrol.NewActionDeleteAPIKeys(key.ID)); err != nil {
		return nil, err
	}
	if err := s.aks.Delete(ctx, req.GetId()); err != nil {
		if !apikeys.IsNotFoundErr(err) {
			return nil, err
		}
	}
	return &apikeyv1.DeleteAPIKeyResponse{}, nil
}

func (s *APIKeyV1Server) RegenerateAPIKeySecret(ctx context.Context, req *apikeyv1.RegenerateAPIKeySecretRequest) (*apikeyv1.RegenerateAPIKeySecretResponse, error) {
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}

	key, err := s.aks.Get(ctx, req.GetId())
	if err != nil {
		if apikeys.IsNotFoundErr(err) {
			return nil, status.Errorf(codes.NotFound, "APIKey %s not found", req.GetId())
		}
		return nil, err
	}

	_, _, err = shared.EnforceOrganizationActions(ctx, s.db, s.acs, key.OrganizationID,
		accesscontrol.NewActionUpdateAPIKeys(key.ID))
	if err != nil {
		return nil, err
	}

	expiry, err := str2duration.ParseDuration(req.Expiry)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid expiry '%s'", req.Expiry)
	}
	opts := apikeys.RegenerateOpts{
		Expiry: expiry,
	}
	key, secret, err := s.aks.RegenerateSecret(ctx, req.GetId(), opts)
	if err != nil {
		if apikeys.IsNotFoundErr(err) {
			return nil, status.Errorf(codes.NotFound, "APIKey %s not found", req.GetId())
		}
		return nil, err
	}
	return &apikeyv1.RegenerateAPIKeySecretResponse{
		ApiKey: shared.NewAPIKeyV1(*key, secret),
	}, nil
}
