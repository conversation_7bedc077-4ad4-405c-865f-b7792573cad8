package kargo

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	jsonpatch "github.com/evanphx/json-patch"
	"github.com/volatiletech/null/v8"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/structpb"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/restmapper"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/agent/pkg/kube"
	"github.com/akuityio/akuity-platform/api/akuity/v1alpha1"
	"github.com/akuityio/akuity-platform/internal/kargo"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
	idv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/id/v1"
)

type convertedApplyRequest struct {
	patchInstanceRequest        *kargov1.PatchKargoInstanceRequest
	createInstanceAgentRequests []*kargov1.CreateKargoInstanceAgentRequest
	updateInstanceAgentRequests []*kargov1.UpdateKargoInstanceAgentRequest
	deleteInstanceAgentRequests []*kargov1.DeleteInstanceAgentRequest
}

func (s *KargoV1Server) ApplyKargoInstance(
	ctx context.Context,
	req *kargov1.ApplyKargoInstanceRequest,
) (*kargov1.ApplyKargoInstanceResponse, error) {
	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	var ki *kargov1.KargoInstance
	var err error
	if req.GetIdType() == idv1.Type_ID {
		if _, _, err = shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
			accesscontrol.NewActionGetWorkspaceKargoInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
			return nil, err
		}
		instanceModel, err := s.instances.Filter(
			models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(req.GetOrganizationId())),
			models.KargoInstanceWhere.ID.EQ(req.GetId()),
		).One(ctx)
		if err != nil && !instances.IsNotFoundErr(err) {
			return nil, err
		}
		if instanceModel != nil {
			ki, err = s.newInstanceV1(instanceModel, misc.IsKargoComponentVersionSupported(instanceModel.Config.Version.String, s.versions, s.kargoUnstableVersion, true) != nil)
			if err != nil {
				return nil, err
			}
		}
	} else {
		resp, err := s.GetKargoInstance(ctx, &kargov1.GetKargoInstanceRequest{
			OrganizationId: req.GetOrganizationId(),
			Name:           req.GetId(),
			WorkspaceId:    req.GetWorkspaceId(),
		})
		if err != nil && !instances.IsNotFoundErr(err) {
			return nil, err
		}
		if resp.GetInstance() != nil {
			ki = resp.GetInstance()
		}
	}

	if ki == nil {
		ki, err = createInstance(ctx, req, s.CreateKargoInstance, req.GetWorkspaceId())
		if err != nil {
			return nil, err
		}
	}

	agents, err := s.ListKargoInstanceAgents(ctx, &kargov1.ListKargoInstanceAgentsRequest{
		OrganizationId: req.GetOrganizationId(),
		InstanceId:     ki.GetId(),
		WorkspaceId:    req.GetWorkspaceId(),
	})
	if err != nil {
		return nil, err
	}

	applyReqs, err := convertApplyRequest(req, ki, agents.GetAgents())
	if err != nil {
		return nil, err
	}

	if applyReqs.patchInstanceRequest != nil {
		_, err = s.PatchKargoInstance(ctx, applyReqs.patchInstanceRequest)
		if err != nil {
			return nil, err
		}
	}

	if len(applyReqs.createInstanceAgentRequests) > 0 {
		for _, c := range applyReqs.createInstanceAgentRequests {
			_, err = s.CreateKargoInstanceAgent(ctx, c)
			if err != nil {
				return nil, err
			}
		}
	}

	if len(applyReqs.updateInstanceAgentRequests) > 0 {
		for _, u := range applyReqs.updateInstanceAgentRequests {
			_, err = s.UpdateKargoInstanceAgent(ctx, u)
			if err != nil {
				return nil, err
			}
		}
	}

	if len(applyReqs.deleteInstanceAgentRequests) > 0 {
		for _, d := range applyReqs.deleteInstanceAgentRequests {
			if d.GetId() == ki.GetSpec().GetDefaultShardAgent() {
				if err = s.clearDefaultShardAgent(ctx, req, ki); err != nil {
					return nil, err
				}
			}
			_, err = s.DeleteInstanceAgent(ctx, d)
			if err != nil {
				return nil, err
			}
		}
	}

	if err := wait.PollUntilContextTimeout(ctx, 2*time.Second, 120*time.Second, true, func(ctx context.Context) (bool, error) {
		return s.applyK3sResources(ctx, req, ki.Id)
	}); err != nil {
		if k8serrors.IsInvalid(err) {
			return nil, status.Errorf(codes.InvalidArgument, "failed to apply manifests: %s", err.Error())
		}
		return nil, err
	}
	return &kargov1.ApplyKargoInstanceResponse{}, nil
}

func (s *KargoV1Server) applyK3sResources(ctx context.Context, applyReq *kargov1.ApplyKargoInstanceRequest, instanceId string) (bool, error) {
	pruneResourceTypes := map[kargov1.PruneResourceType]bool{}
	for _, t := range applyReq.GetPruneResourceTypes() {
		pruneResourceTypes[t] = true
	}
	tnt, err := client.NewKargoTenant(s.hostRestConfig, *s.log, instanceId)
	if err != nil {
		return false, err
	}

	kubectl, err := tnt.ControlPlaneKubectl(ctx)
	if err != nil {
		// If the k3s kubeconfig doesn't exist, we skip the next steps and wait until the kubeconfig secret is created.
		if k8serrors.IsNotFound(err) {
			return false, nil
		}
		// We don't return the error directly since it may contain AKP implementation details.
		return false, status.Errorf(codes.InvalidArgument, "The instance is still being provisioned. Please try again in a few minutes")
	}
	k3sDynamicClient, err := tnt.ControlPlaneDynamicClientset(ctx)
	if err != nil {
		// If the k3s kubeconfig doesn't exist, we skip the next steps and wait until the kubeconfig secret is created.
		if k8serrors.IsNotFound(err) {
			return false, nil
		}
		// We don't return the error directly since it may contain AKP implementation details.
		return false, status.Errorf(codes.InvalidArgument, "The instance is still being provisioned. Please try again in a few minutes")
	}

	k3sKubeClient, err := tnt.ControlPlaneKubeClientset(ctx)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return false, nil
		}
		return false, status.Errorf(codes.InvalidArgument, "The instance is still being provisioned. Please try again in a few minutes")
	}
	gr, err := restmapper.GetAPIGroupResources(k3sKubeClient.Discovery())
	if err != nil {
		return false, err
	}
	mapper := restmapper.NewDiscoveryRESTMapper(gr)

	var groupsObjs [][]*unstructured.Unstructured
	for _, group := range k3sResourceGroups {
		var objs []*unstructured.Unstructured
		for i, item := range group.getApplyItems(applyReq) {
			obj := &unstructured.Unstructured{Object: item.AsMap()}
			if group.clusterScoped {
				obj.SetNamespace("")
			} else if obj.GetNamespace() == "" {
				obj.SetNamespace(common.K3sKargoNamespace)
			} else if obj.GetNamespace() == "kube-system" {
				return false, status.Errorf(codes.InvalidArgument, "invalid namespace in %s[%d], kube-system is not allowed", group.field, i)
			}

			if gk := obj.GroupVersionKind().GroupKind(); gk != group.gk {
				return false, status.Error(codes.InvalidArgument, fmt.Sprintf("invalid group/kind in %s[%d], expected %s but was %s", group.field, i, group.gk, gk))
			}
			if group.validate != nil {
				if err := group.validate(obj); err != nil {
					return false, err
				}
			}
			objs = append(objs, obj)
		}
		groupsObjs = append(groupsObjs, objs)
	}

	for i := range k3sResourceGroups {
		for _, obj := range groupsObjs[i] {
			if _, err := kubectl.ApplyResource(ctx, obj, kube.ApplyOpts{Validate: true}); err != nil {
				if isWebhookNotReadyErr(err) || isCRDDiscoveryNotReadyErr(err) || errorsutil.IsInstanceReadinessError(err) {
					return false, nil
				}
				return false, err
			}
		}
	}

	projectNamespaces, err := getProjectNamespacesFromK3s(ctx, k3sDynamicClient, mapper)
	if err != nil {
		return false, err
	}

	for i, group := range k3sResourceGroups {
		if !pruneResourceTypes[kargov1.PruneResourceType_PRUNE_RESOURCE_TYPE_ALL] && !pruneResourceTypes[group.prune] {
			continue
		}
		appliedKeys := make(map[string]bool)
		for _, obj := range groupsObjs[i] {
			appliedKeys[obj.GetNamespace()+"/"+obj.GetName()] = true
		}

		mapping, err := mapper.RESTMapping(k3sResourceGroups[i].gk)
		if err != nil {
			if isCRDDiscoveryNotReadyErr(err) || errorsutil.IsInstanceReadinessError(err) {
				return false, nil
			}
			return false, err
		}
		gvr := mapping.Resource

		listOpts := createListOptionsWithNamespaceFilter(group.listOps, projectNamespaces, group.clusterScoped)
		res, err := k3sDynamicClient.Resource(gvr).List(ctx, listOpts)
		if err != nil {
			if isCRDDiscoveryNotReadyErr(err) || errorsutil.IsInstanceReadinessError(err) {
				return false, nil
			}
			return false, err
		}

		if group.annotationFilters != nil {
			res.Items = filterResourcesByAnnotations(res.Items, group.annotationFilters)
		}
		for _, item := range res.Items {
			if item.GetNamespace() == "kube-system" || appliedKeys[item.GetNamespace()+"/"+item.GetName()] {
				continue
			}
			if err := k3sDynamicClient.Resource(gvr).Namespace(item.GetNamespace()).Delete(ctx, item.GetName(), metav1.DeleteOptions{}); err != nil {
				return false, err
			}
		}

	}

	return true, nil
}

func isWebhookNotReadyErr(err error) bool {
	return strings.Contains(err.Error(), "failed to call webhook")
}

func isCRDDiscoveryNotReadyErr(err error) bool {
	if err == nil {
		return false
	}
	msg := err.Error()
	return strings.Contains(msg, "resource mapping not found") ||
		strings.Contains(msg, "no matches for kind")
}

// convertApplyRequest converts an apply request to other API requests which we will make as part of the overall apply
func convertApplyRequest(applyReq *kargov1.ApplyKargoInstanceRequest, existing *kargov1.KargoInstance, existingAgents []*kargov1.KargoAgent) (*convertedApplyRequest, error) {
	var reqs convertedApplyRequest
	var err error
	instanceID := existing.GetId()
	if applyReq.GetKargo() != nil {
		reqs.patchInstanceRequest, err = toPatchInstanceRequest(applyReq, instanceID, existing)
		if err != nil {
			return nil, err
		}
	}

	createReqs := make([]*kargov1.CreateKargoInstanceAgentRequest, 0)
	updateReqs := make([]*kargov1.UpdateKargoInstanceAgentRequest, 0)
	deleteReqs := make([]*kargov1.DeleteInstanceAgentRequest, 0)

	agentsInApplyReq := make(map[string]v1alpha1.KargoAgent)
	existingAgentsMap := make(map[string]*kargov1.KargoAgent)
	for _, a := range existingAgents {
		existingAgentsMap[a.GetName()] = a
	}
	if applyReq.GetAgents() != nil {
		for _, a := range applyReq.GetAgents() {
			agentMap := a.AsMap()
			if agentMap["spec"] == nil {
				return nil, newInvalidApplySpec("spec", "missing KargoAgent spec")
			}
			labels := make(map[string]string)
			annotations := make(map[string]string)
			if metadata, ok := agentMap["metadata"].(map[string]any); ok {
				if l, ok := metadata["labels"].(map[string]any); ok {
					for k, v := range l {
						if str, ok := v.(string); ok {
							labels[k] = str
						}
					}
				}
				if a, ok := metadata["annotations"].(map[string]interface{}); ok {
					for k, v := range a {
						if str, ok := v.(string); ok {
							annotations[k] = str
						}
					}
				}
			}

			var agent v1alpha1.KargoAgent
			if err := runtime.DefaultUnstructuredConverter.FromUnstructured(agentMap, &agent); err != nil {
				return nil, newInvalidApplySpec("spec", err.Error())
			}
			agentsInApplyReq[agent.Name] = agent
			kustomization := &structpb.Struct{}
			if agent.Spec.Data.Kustomization.String() != "" {
				kustomizationMap, err := shared.ProtoToMap(agent.Spec.Data.Kustomization)
				if err != nil {
					return nil, newInvalidApplySpec("kustomization", err.Error())
				}
				kustomization, err = structpb.NewStruct(kustomizationMap)
				if err != nil {
					return nil, newInvalidApplySpec("kustomization", err.Error())
				}
			}

			data := &kargov1.KargoAgentData{
				Size:                newKargoAgentSize(string(agent.Spec.Data.Size)),
				Labels:              labels,
				Annotations:         annotations,
				AutoUpgradeDisabled: agent.Spec.Data.AutoUpgradeDisabled,
				TargetVersion:       agent.Spec.Data.TargetVersion,
				Kustomization:       kustomization,
				RemoteArgocd:        agent.Spec.Data.RemoteArgocd,
				AkuityManaged:       agent.Spec.Data.AkuityManaged,
				Namespace:           agent.Namespace,
				ArgocdNamespace:     agent.Spec.Data.ArgocdNamespace,
			}
			if data.Namespace == "" {
				data.Namespace = instances.DefaultClusterNamespace
			}
			if _, ok := existingAgentsMap[agent.Name]; !ok {
				createReqs = append(createReqs, &kargov1.CreateKargoInstanceAgentRequest{
					OrganizationId: applyReq.GetOrganizationId(),
					InstanceId:     instanceID,
					Name:           agent.Name,
					Description:    agent.Spec.Description,
					Data:           data,
				})
			} else {
				if diffAgents(agent.Spec.Description, data, existingAgentsMap[agent.Name]) {
					updateReqs = append(updateReqs, &kargov1.UpdateKargoInstanceAgentRequest{
						OrganizationId: applyReq.GetOrganizationId(),
						InstanceId:     instanceID,
						Id:             existingAgentsMap[agent.Name].GetId(),
						Description:    agent.Spec.Description,
						Data:           data,
					})
				}
			}
		}
	}
	for _, a := range existingAgents {
		if _, ok := agentsInApplyReq[a.GetName()]; !ok && shouldPruneAgents(applyReq.GetPruneResourceTypes()) {
			deleteReqs = append(deleteReqs, &kargov1.DeleteInstanceAgentRequest{
				OrganizationId: applyReq.GetOrganizationId(),
				InstanceId:     instanceID,
				Id:             a.GetId(),
			})
		}
	}

	reqs.createInstanceAgentRequests = createReqs
	reqs.updateInstanceAgentRequests = updateReqs
	reqs.deleteInstanceAgentRequests = deleteReqs
	return &reqs, nil
}

func getPatch(origin, updated any) (map[string]interface{}, error) {
	originJSON, err := json.Marshal(origin)
	if err != nil {
		return nil, err
	}
	updatedJSON, err := json.Marshal(updated)
	if err != nil {
		return nil, err
	}
	data, err := jsonpatch.CreateMergePatch(originJSON, updatedJSON)
	if err != nil {
		return nil, err
	}
	patch := map[string]interface{}{}
	if err := json.Unmarshal(data, &patch); err != nil {
		return nil, err
	}
	return patch, nil
}

func toPatchInstanceRequest(applyReq *kargov1.ApplyKargoInstanceRequest, instanceID string, existing *kargov1.KargoInstance) (*kargov1.PatchKargoInstanceRequest, error) {
	if existing == nil {
		return nil, fmt.Errorf("cannot patch non-existent instance")
	}
	kargoMap := applyReq.GetKargo().AsMap()
	if kargoMap == nil || kargoMap["spec"] == nil {
		return nil, nil
	}

	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(kargoMap, &v1alpha1.Kargo{}); err != nil {
		return nil, newInvalidApplySpec("Kargo", err.Error())
	}

	kargoMap, err := shared.ProtoToMap(kargoMap)
	if err != nil {
		return nil, err
	}

	patchFields := make(map[string]*structpb.Value)
	if spec, ok := kargoMap["spec"].(map[string]interface{}); ok {
		for k, v := range spec {
			if k == "oidcConfig" {
				existingOIDC := make(map[string]any)
				if existing.GetOidcConfig() != nil {
					existingOIDCMap, err := shared.ProtoToMap(existing.GetOidcConfig())
					if err != nil {
						return nil, fmt.Errorf("failed to convert existing spec to map: %w", err)
					}
					existingOIDC = existingOIDCMap
				}
				oidcPatch, err := getPatch(existingOIDC, v)
				if err != nil {
					return nil, err
				}
				oidcStruct, err := structpb.NewStruct(oidcPatch)
				if err != nil {
					return nil, fmt.Errorf("failed to convert oidc patch to struct: %w", err)
				}
				patchFields["oidcConfig"] = structpb.NewStructValue(oidcStruct)
				continue
			}
			switch t := v.(type) {
			case map[string]any:
				s, err := structpb.NewStruct(t)
				if err != nil {
					return nil, fmt.Errorf("failed to convert %s to struct: %w", k, err)
				}
				if k == "kargoInstanceSpec" {
					k = "spec"
				}
				patchFields[k] = structpb.NewStructValue(s)
			case string:
				patchFields[k] = structpb.NewStringValue(t)
			}
		}
	}

	structPatch := &structpb.Struct{
		Fields: patchFields,
	}

	if applyReq.GetKargoConfigmap() != nil {
		unstructuredMap := applyReq.GetKargoConfigmap().AsMap()
		s, err := unstructuredDataToStruct(&unstructured.Unstructured{Object: unstructuredMap})
		if err != nil {
			return nil, err
		}
		patchFields["apiCm"] = structpb.NewStructValue(s)
	}

	if applyReq.GetKargoSecret() != nil {
		secretData, err := toSecretMap(kargo.KargoSecretName, applyReq.GetKargoSecret())
		if err != nil {
			return nil, err
		}
		s, err := unstructuredDataToStruct(&unstructured.Unstructured{Object: secretData})
		if err != nil {
			return nil, err
		}
		patchFields["apiSecret"] = structpb.NewStructValue(s)
	}

	return &kargov1.PatchKargoInstanceRequest{
		OrganizationId: applyReq.GetOrganizationId(),
		Id:             instanceID,
		Patch:          structPatch,
	}, nil
}

func shouldPruneAgents(pruneTypes []kargov1.PruneResourceType) bool {
	for _, pruneType := range pruneTypes {
		if pruneType == kargov1.PruneResourceType_PRUNE_RESOURCE_TYPE_ALL ||
			pruneType == kargov1.PruneResourceType_PRUNE_RESOURCE_TYPE_AGENTS {
			return true
		}
	}
	return false
}

func unstructuredDataToStruct(obj *unstructured.Unstructured) (*structpb.Struct, error) {
	o := obj.Object
	data, ok := o["data"]
	if !ok {
		data, ok = o["stringData"]
		if !ok {
			return structpb.NewStruct(nil)
		}
	}
	dataMap, ok := data.(map[string]any)
	for k, v := range dataMap {
		if vstr, ok := v.(string); ok {
			if b, err := strconv.ParseBool(vstr); err == nil {
				dataMap[k] = b
			}
		}
	}
	if !ok {
		return structpb.NewStruct(nil)
	}
	return structpb.NewStruct(dataMap)
}

func (s *KargoV1Server) clearDefaultShardAgent(
	ctx context.Context,
	req *kargov1.ApplyKargoInstanceRequest,
	ki *kargov1.KargoInstance,
) error {
	_, err := s.PatchKargoInstance(ctx, &kargov1.PatchKargoInstanceRequest{
		OrganizationId: req.GetOrganizationId(),
		Id:             ki.GetId(),
		Patch: &structpb.Struct{
			Fields: map[string]*structpb.Value{
				"spec": {
					Kind: &structpb.Value_StructValue{
						StructValue: &structpb.Struct{
							Fields: map[string]*structpb.Value{
								"defaultShardAgent": {
									Kind: &structpb.Value_NullValue{},
								},
							},
						},
					},
				},
			},
		},
	})
	return err
}

func createInstance(ctx context.Context,
	req *kargov1.ApplyKargoInstanceRequest,
	createFunc func(context.Context, *kargov1.CreateKargoInstanceRequest) (*kargov1.CreateKargoInstanceResponse, error),
	workspaceID string,
) (*kargov1.KargoInstance, error) {
	kargoMap := req.GetKargo().AsMap()
	if kargoMap == nil || kargoMap["spec"] == nil {
		return nil, status.Error(codes.InvalidArgument, "missing kargo spec")
	}
	var kargo v1alpha1.Kargo
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(kargoMap, &kargo); err != nil {
		return nil, newInvalidApplySpec("spec", err.Error())
	}

	var description *string
	if kargo.Spec.Description != "" {
		d := kargo.Spec.Description
		description = &d
	}

	createResp, err := createFunc(ctx, &kargov1.CreateKargoInstanceRequest{
		OrganizationId: req.GetOrganizationId(),
		Name:           kargo.Name,
		Version:        kargo.Spec.Version,
		Description:    description,
		WorkspaceId:    workspaceID,
	})
	if err != nil {
		return nil, err
	}
	return createResp.GetInstance(), nil
}

func newInvalidApplySpec(specName, message string) error {
	return status.Error(codes.InvalidArgument, fmt.Sprintf("invalid %s spec: %s", specName, message))
}

func newKargoAgentSize(size string) kargov1.KargoAgentSize {
	switch size {
	case "small":
		return kargov1.KargoAgentSize_KARGO_AGENT_SIZE_SMALL
	case "medium":
		return kargov1.KargoAgentSize_KARGO_AGENT_SIZE_MEDIUM
	case "large":
		return kargov1.KargoAgentSize_KARGO_AGENT_SIZE_LARGE
	}
	return kargov1.KargoAgentSize_KARGO_AGENT_SIZE_UNSPECIFIED
}

func toSecretMap(secretName string, secret interface{}) (map[string]any, error) {
	secretData := make(map[string]any)
	secretJSON, err := json.Marshal(secret)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal %s secret: %w", secretName, err)
	}
	if err := json.Unmarshal(secretJSON, &secretData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal %s secret: %w", secretName, err)
	}
	return secretData, nil
}

func diffAgents(description string, data *kargov1.KargoAgentData, existing *kargov1.KargoAgent) bool {
	if existing == nil {
		return true
	}
	return description != existing.GetDescription() ||
		!proto.Equal(data, existing.GetData())
}
