package kargo

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/secret"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *KargoV1Server) RotateInstanceAgentCredentials(
	ctx context.Context,
	req *kargov1.RotateInstanceAgentCredentialsRequest,
) (*kargov1.RotateInstanceAgentCredentialsResponse, error) {
	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	for _, agentName := range req.GetAgentNames() {
		if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
			accesscontrol.NewActionUpdateKargoInstanceAgents(
				req.GetWorkspaceId(),
				req.GetInstanceId(),
				agentName,
			),
		); err != nil {
			return &kargov1.RotateInstanceAgentCredentialsResponse{}, err
		}
	}

	if _, err := s.instances.Filter(
		models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(req.GetOrganizationId())),
		models.KargoInstanceWhere.ID.EQ(req.GetInstanceId()),
	).One(ctx); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errorsutil.NewAPIStatus(http.StatusForbidden, fmt.Sprintf("instance %s not found", req.GetInstanceId()))
		}
		return nil, err
	}

	skipped, err := s.batchRotateAgentCredentials(ctx, req.GetInstanceId(), req.GetAgentNames())
	if err != nil {
		return &kargov1.RotateInstanceAgentCredentialsResponse{}, err
	}
	return &kargov1.RotateInstanceAgentCredentialsResponse{SkippedAgents: skipped}, nil
}

func (s *KargoV1Server) batchRotateAgentCredentials(
	ctx context.Context,
	instanceID string,
	agentNames []string,
) ([]string, error) {
	txDB, txBeginner := database.WithTxBeginner(s.db)
	repoSet := client.NewRepoSet(txDB)

	txCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := txBeginner.Begin(txCtx)
	if err != nil {
		return nil, err
	}

	agents, err := repoSet.KargoAgents(
		qm.Select("id", "name", "spec", "status_observed_cred_rotation"),
		models.KargoAgentWhere.Name.IN(agentNames),
		models.KargoAgentWhere.InstanceID.EQ(instanceID)).ListAll(txCtx)
	if err != nil {
		return nil, err
	}

	skipped := []string{}
	for _, agent := range agents {
		spec, err := agent.GetSpec()
		if err != nil {
			return nil, err
		}
		status, err := agent.GetStatus()
		if err != nil {
			return nil, err
		}
		// if observed rotation count doesn't match required rotation count skip agent
		if status.ObservedRotationCount.Int != spec.AgentRotationCount {
			skipped = append(skipped, agent.Name)
			continue
		}
		spec.AgentRotationCount++
		privateSpec, err := agent.GetPrivateSpec()
		if err != nil {
			return nil, err
		}

		newPass, err := secret.GenerateNewPassword(privateSpec.AgentPassword, 5)
		if err != nil {
			return nil, err
		}
		privateSpec.AgentPassword = newPass
		if err := agent.SetSpec(*spec); err != nil {
			return nil, err
		}
		if err := agent.SetPrivateSpec(*privateSpec); err != nil {
			return nil, err
		}

		if err := repoSet.KargoAgents().Update(ctx, agent, "spec", "private_spec"); err != nil {
			return nil, err
		}
	}
	if err := tx.Commit(); err != nil {
		return nil, err
	}

	return skipped, nil
}
