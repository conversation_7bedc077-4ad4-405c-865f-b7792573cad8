package kargo

import (
	"database/sql"

	"github.com/go-logr/logr"
	"github.com/go-playground/validator/v10"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/protobuf/encoding/protojson"
	"k8s.io/client-go/rest"

	agentClient "github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/apikeys"
	"github.com/akuityio/akuity-platform/internal/services/customroles"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/events"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/models/util/dal"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

type KargoV1Server struct {
	kargov1.KargoServiceServer
	repoSet              client.RepoSet
	acs                  accesscontrol.PolicyService
	instances            dal.Repository[*Instance]
	instancesWatcher     database.Watcher[events.InstanceEvent]
	agentsWatcher        database.Watcher[events.ClusterEvent]
	jsonMarshalOptions   protojson.MarshalOptions
	jsonUnmarshalOptions protojson.UnmarshalOptions
	db                   *sql.DB
	cfg                  config.PortalServerConfig
	versions             []agentClient.ComponentVersion
	kargoUnstableVersion *agentClient.ComponentVersion
	featSvc              features.Service
	hostRestConfig       *rest.Config
	log                  *logr.Logger
}

func NewKargoV1Server(
	v *validator.Validate,
	log *logr.Logger,
	db *sql.DB,
	cfg config.PortalServerConfig,
	instancesWatcher database.Watcher[events.InstanceEvent],
	agentsWatcher database.Watcher[events.ClusterEvent],
	versions []agentClient.ComponentVersion,
	kargoUnstableVersion *agentClient.ComponentVersion,
	hostRestConfig *rest.Config,
) *KargoV1Server {
	instances := dal.NewDBRepo[*Instance](db, models.NewQuery(
		qm.Select("kargo_instance.*", "kargo_instance_config.*, organization.name as owner_organization_name"),
		qm.From("kargo_instance"),
		qm.InnerJoin("kargo_instance_config on kargo_instance.id = kargo_instance_config.instance_id"),
		qm.InnerJoin("organization on kargo_instance.organization_owner = organization.id"),
	), "kargo_instance.id")
	repoSet := client.NewRepoSet(db)
	aks := apikeys.NewService(repoSet, v)
	teamSvc := teams.NewService(db)
	workspaceSvc := workspaces.NewService(db, teamSvc, cfg.FeatureGatesSource)
	return &KargoV1Server{
		acs:                  accesscontrol.NewPolicyService(v, customroles.New(repoSet), workspaceSvc, aks, teamSvc),
		instances:            instances,
		instancesWatcher:     instancesWatcher,
		agentsWatcher:        agentsWatcher,
		db:                   db,
		repoSet:              repoSet,
		cfg:                  cfg,
		versions:             versions,
		featSvc:              features.NewService(client.NewRepoSet(db), db, config.IsSelfHosted, config.IsSelfHostedReleaseBuild, cfg.FeatureGatesSource, config.GetLicense(), features.WithLogger(log)),
		kargoUnstableVersion: kargoUnstableVersion,
		hostRestConfig:       hostRestConfig,
		log:                  log,
	}
}

type Instance struct {
	*models.KargoInstance `boil:",bind"`
	Config                *models.KargoInstanceConfig `boil:",bind"`
	OwnerOrganizationName string                      `boil:"owner_organization_name"`
}
