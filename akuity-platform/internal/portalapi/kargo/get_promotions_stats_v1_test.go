package kargo

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func TestMapStatsToResponse(t *testing.T) {
	testCases := []struct {
		name     string
		input    []PromotionStats
		expected []*kargov1.PromotionStat
	}{
		{
			name:     "empty input",
			input:    []PromotionStats{},
			expected: []*kargov1.PromotionStat{},
		},
		{
			name: "single element",
			input: []PromotionStats{
				{
					IntervalStart: time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC),
					CountMap:      map[string]uint32{"test": 1},
					AverageMap:    map[string]float32{"test": 1.0},
				},
			},
			expected: []*kargov1.PromotionStat{
				{
					IntervalStart: "2022-01-01 00:00:00 +0000 UTC",
					CountMap:      map[string]uint32{"test": 1},
					AverageMap:    map[string]float32{"test": 1.0},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := MapStatsToResponse(tc.input)
			assert.Equal(t, tc.expected, result)
		})
	}
}
