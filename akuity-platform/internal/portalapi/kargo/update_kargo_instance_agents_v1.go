package kargo

import (
	"context"
	"fmt"
	"net/http"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/agent/pkg/client/apis/kargo/clusteragent"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/validator"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *KargoV1Server) UpdateKargoInstanceAgents(
	ctx context.Context,
	req *kargov1.UpdateKargoInstanceAgentsRequest,
) (*kargov1.UpdateKargoInstanceAgentsResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateKargoInstanceAgents(
			req.GetWorkspaceId(),
			req.GetInstanceId(),
			accesscontrol.ResourceAny,
		),
	); err != nil {
		return nil, err
	}

	txDB, txBeginner := database.WithTxBeginner(s.db)
	repoSet := client.NewRepoSet(txDB)

	txCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := txBeginner.Begin(txCtx)
	if err != nil {
		return nil, err
	}

	instanceID := req.GetInstanceId()
	customizations := req.GetAgentCustomizations()

	clusters, err := repoSet.KargoAgents().Filter(qm.Select("id", "spec", "auto_upgrade_disabled"), models.KargoAgentWhere.InstanceID.EQ(instanceID)).ListAll(ctx)
	if err != nil {
		return nil, err
	}

	for _, cluster := range clusters {
		spec, err := cluster.GetSpec()
		if err != nil {
			return nil, err
		}
		// skip any akuity managed agents as customizations don't apply for those
		if spec.AkuityManaged {
			continue
		}

		if spec.TargetVersion == "" {
			spec.TargetVersion = clusteragent.NewDataValuesKargo().GetVersion()
		} else if err := validator.ValidateTargetVersion(spec.TargetVersion); err != nil {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Sprintf("agent %v is running an unsupported agent version %v, please update version before proceeding", cluster.Name, spec.TargetVersion))
		}
		spec.Kustomization = customizations.Kustomization.AsMap()
		if err := cluster.SetSpec(*spec); err != nil {
			return nil, err
		}
		cluster.AutoUpgradeDisabled = customizations.AutoUpgradeDisabled

		if err := repoSet.KargoAgents().Update(ctx, cluster, "spec", "auto_upgrade_disabled"); err != nil {
			return nil, err
		}
	}

	if err := tx.Commit(); err != nil {
		return nil, err
	}
	return &kargov1.UpdateKargoInstanceAgentsResponse{}, nil
}
