package kargo

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
	"google.golang.org/protobuf/types/known/timestamppb"
	"k8s.io/utils/ptr"

	agentclient "github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
	healthv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/status/health/v1"
	reconciliationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/status/reconciliation/v1"
)

func TestNewKargoAgentV1(t *testing.T) {
	autoUpgradeDisabled := false
	unixTime := time.Unix(0, 0)

	agentState := models.KargoAgentState{
		Version:      "1.0.0",
		KargoVersion: "latest",
		ObservedAt:   null.TimeFrom(unixTime),
		Status: &agentclient.AggregatedHealthResponse{
			Healthy: map[string]*agentclient.HealthStatus{
				"test": {
					Status:  common.TenantPhaseProgressing,
					Message: "Progressing",
				},
			},
		},
	}

	statusAgentState, err := json.Marshal(agentState)
	require.NoError(t, err)

	type testCase struct {
		input    *models.KargoAgent
		deadline time.Duration
		expected *kargov1.KargoAgent
		wantErr  bool
	}

	testCases := []testCase{
		{
			input: &models.KargoAgent{
				ID:                "test-id",
				Name:              "test-name",
				Namespace:         "test-ns",
				Description:       null.StringFrom("test-desc"),
				DeletionTimestamp: null.TimeFrom(unixTime),
				StatusAgentState:  null.JSONFrom(statusAgentState),
			},
			deadline: time.Second,
			expected: &kargov1.KargoAgent{
				Id:          "test-id",
				Name:        "test-name",
				Namespace:   "test-ns",
				Description: "test-desc",
				DeleteTime:  timestamppb.New(unixTime),
				HealthStatus: &healthv1.Status{
					Code:    healthv1.StatusCode_STATUS_CODE_UNKNOWN,
					Message: "Cluster agent health is unknown. Did you install the agent manifests?",
				},
				ReconciliationStatus: &reconciliationv1.Status{
					Code:    reconciliationv1.StatusCode_STATUS_CODE_PROGRESSING,
					Message: "Working on configuring the cluster...",
				},
				Data: &kargov1.KargoAgentData{
					TargetVersion:       "",
					Size:                1,
					Labels:              nil,
					Annotations:         nil,
					AutoUpgradeDisabled: &autoUpgradeDisabled,
					AkuityManaged:       false,
					Namespace:           "test-ns",
					ArgocdNamespace:     "",
				},
				AgentState: &kargov1.KargoAgentState{
					Version:      "1.0.0",
					KargoVersion: "latest",
					ObserveTime:  &timestamppb.Timestamp{},
					Status: &healthv1.AgentAggregatedHealthResponse{
						MinObservedGeneration: 0,
						Healthy: map[string]*healthv1.AgentHealthStatus{
							"test": {
								Status:  healthv1.TenantPhase_TENANT_PHASE_PROGRESSING,
								Message: "Progressing",
							},
						},
						Progressing: map[string]*healthv1.AgentHealthStatus{},
						Degraded:    map[string]*healthv1.AgentHealthStatus{},
						Unknown:     map[string]*healthv1.AgentHealthStatus{},
					},
					UpdateStatus: ptr.To(reconciliationv1.AgentUpdateStatus_AGENT_UPDATE_STATUS_UPDATED),
				},
				ReadonlySettingsChangedGeneration: new(uint64),
				ObservedGeneration:                new(uint64),
			},
			wantErr: false,
		},
	}

	for _, tc := range testCases {
		result, err := NewKargoAgentV1(tc.input, tc.deadline)
		assert.NoError(t, err)
		assert.Equal(t, tc.expected, result)
	}
}
