package kargo

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/agent/pkg/client/apis/clusteragent"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
	healthv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/status/health/v1"
)

func TestGetAgentFilterMods(t *testing.T) {
	nameLike := "test"
	var queryMods []qm.QueryMod
	trueBool := true
	agentVersion := "1.0.0"

	testCases := []struct {
		name     string
		filter   *kargov1.KargoAgentFilter
		expected []qm.QueryMod
	}{
		{
			name:     "Nil filter",
			filter:   nil,
			expected: queryMods,
		},
		{
			name:     "NameLike filter",
			filter:   &kargov1.KargoAgentFilter{NameLike: &nameLike},
			expected: []qm.QueryMod{qm.Where("name like ?", "%"+nameLike+"%")},
		},
		{
			name:   "AgentStatus filter",
			filter: &kargov1.KargoAgentFilter{AgentStatus: []healthv1.StatusCode{healthv1.StatusCode_STATUS_CODE_HEALTHY}},
			expected: []qm.QueryMod{qm.Where(`case when
        (status_agent_state -> 'status' ->> 'PriorityStatus' is null) or
        extract(epoch from interval '600 seconds' - age(now(), to_timestamp(status_agent_state ->> 'observedAt', 'YYYY-MM-DD"T"HH24:MI:SS"Z"'))) < 0
			then 'Unknown'
		else
			status_agent_state -> 'status' ->> 'PriorityStatus'
		end in (?)`, string(common.TenantPhaseHealthy))},
		},
		{
			name:     "AgentVersion filter",
			filter:   &kargov1.KargoAgentFilter{AgentVersion: []string{"1.0.0"}},
			expected: []qm.QueryMod{database.WhereJSONin(`status_agent_state ->> 'version'`, []string{"1.0.0"})},
		},
		{
			name:     "RemoteArgocdIds filter",
			filter:   &kargov1.KargoAgentFilter{RemoteArgocdIds: []string{"argocd1"}},
			expected: []qm.QueryMod{models.KargoAgentWhere.RemoteArgocdInstanceID.IN([]string{"argocd1"})},
		},
		{
			name:     "SelfManaged filter",
			filter:   &kargov1.KargoAgentFilter{SelfManaged: &trueBool},
			expected: []qm.QueryMod{models.KargoAgentWhere.RemoteArgocdInstanceID.IsNull()},
		},
		{
			name:     "ExcludeAgentVersion filter",
			filter:   &kargov1.KargoAgentFilter{ExcludeAgentVersion: &agentVersion},
			expected: []qm.QueryMod{qm.Where("coalesce(spec ->> 'targetVersion', ?) != ?", clusteragent.NewDataValuesAgent().GetVersion(), "1.0.0")},
		},
		{
			name:     "OutdatedManifest filter",
			filter:   &kargov1.KargoAgentFilter{OutdatedManifest: &trueBool},
			expected: []qm.QueryMod{qm.Where("status_agent_state->>'status' is null or status_agent_state->'status'->>'minObservedGeneration' != generation::text")},
		},
		{
			name:     "KargoVersion filter",
			filter:   &kargov1.KargoAgentFilter{KargoVersion: []string{"1.0.0"}},
			expected: []qm.QueryMod{database.WhereJSONin(`status_agent_state ->> 'kargoVersion'`, []string{"1.0.0"})},
		},
		{
			name:     "Namespace filter",
			filter:   &kargov1.KargoAgentFilter{Namespace: []string{"namespace1"}},
			expected: []qm.QueryMod{database.WhereJSONin("namespace", []string{"namespace1"})},
		},
		{
			name:     "Labels filter",
			filter:   &kargov1.KargoAgentFilter{Labels: map[string]string{"key1": "value1"}},
			expected: []qm.QueryMod{qm.Where("spec -> 'labels' ->> ? = ?", "key1", "value1")},
		},
		{
			name:     "NeedReapply filter",
			filter:   &kargov1.KargoAgentFilter{NeedReapply: &trueBool},
			expected: []qm.QueryMod{qm.Where("status_agent_state is not null and (coalesce((status_agent_state ->> 'lastUserAppliedGeneration')::integer, 0) < readonly_settings_changed_generation)")},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := getAgentFilterMods(tc.filter)
			assert.NoError(t, err)
			assert.Equal(t, tc.expected, result)
		})
	}
}
