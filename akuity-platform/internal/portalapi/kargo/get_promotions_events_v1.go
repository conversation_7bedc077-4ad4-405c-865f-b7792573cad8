package kargo

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	sqltypes "github.com/volatiletech/sqlboiler/v4/types"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/consts"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *KargoV1Server) GetPromotionEvents(ctx context.Context, req *kargov1.GetPromotionEventsRequest) (*kargov1.GetPromotionEventsResponse, error) {
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	filters := req.GetFilter()
	if filters == nil {
		filters = &kargov1.PromotionFilter{}
	}

	instanceIds := filters.GetInstanceId()
	if len(instanceIds) > 0 {
		for _, id := range instanceIds {
			action := accesscontrol.NewActionGetWorkspaceKargoInstances(req.GetWorkspaceId(), id)
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return nil, err
			}
			if !ok {
				return nil, shared.NewPermissionDeniedErr(action)
			}
		}
	} else if len(filters.GetInstanceNames()) > 0 {
		for _, name := range filters.GetInstanceNames() {
			inst, err := s.repoSet.KargoInstances().Filter(models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(req.GetOrganizationId())), models.KargoInstanceWhere.Name.EQ(name)).One(ctx)
			if err != nil {
				return nil, err
			}
			action := accesscontrol.NewActionGetWorkspaceKargoInstances(inst.WorkspaceID.String, inst.ID)
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return nil, err
			}
			if !ok {
				return nil, shared.NewPermissionDeniedErr(action)
			}
		}
	} else {
		action := accesscontrol.NewActionGetOrganizationAllKargoInstances()
		ok, err := enforcer.EnforceAction(ctx, action)
		if err != nil {
			return nil, err
		}
		if !ok {
			return nil, shared.NewPermissionDeniedErr(action)
		}
	}

	mods := GetPromotionFilters(filters, req.GetOrganizationId())
	timeMods, err := GetPromoTimeFilter("promo", filters)
	if err != nil {
		return nil, err
	}

	mods = append(mods, timeMods...)

	// if field is specified skip real query and just return all fields
	if req.GetField() != kargov1.PromotionField_PROMOTION_FIELD_UNSPECIFIED && MapField(req.GetField(), len(filters.GetInstanceNames()) > 0) != "" {
		field := MapField(req.GetField(), len(filters.GetInstanceNames()) > 0)
		// limit to 10
		mods = append(mods, qm.Limit(10))
		if req.FieldLike != "" {
			mods = append(mods, qm.Where(fmt.Sprintf("%v like ?", field), req.FieldLike))
		}
		res, err := s.getPromotionEventsField(ctx, mods, field)
		if err != nil {
			return nil, err
		}
		return &kargov1.GetPromotionEventsResponse{
			FieldResult: res,
		}, nil

	}

	count, err := s.getPromotionEventsCount(ctx, mods)
	if err != nil {
		return nil, err
	}

	// limit max result count
	maxLimit := misc.MaxPaginationLimit
	if req.Limit != nil {
		if req.GetLimit() < maxLimit {
			maxLimit = req.GetLimit()
		}
	}
	mods = append(mods, qm.Limit(int(maxLimit)))

	if req.Offset != nil {
		mods = append(mods, qm.Offset(int(req.GetOffset())))
	}

	events, err := s.getPromotionEvents(ctx, mods)
	if err != nil {
		return nil, err
	}

	eventResp := []*kargov1.PromotionEvent{}

	for _, event := range events {
		e, err := MapEvent(event)
		if err != nil {
			return nil, err
		}
		eventResp = append(eventResp, e)
	}

	return &kargov1.GetPromotionEventsResponse{
		PromotionEvents: eventResp,
		Count:           count,
	}, nil
}

func MapEvent(modelEvent *models.KargoPromotion) (*kargov1.PromotionEvent, error) {
	if modelEvent == nil {
		return nil, nil
	}
	details, err := modelEvent.GetDetails()
	if err != nil {
		return nil, err
	}
	misc, err := structpb.NewStruct(details.Miscellaneous)
	if err != nil {
		return nil, err
	}
	event := &kargov1.PromotionEvent{
		Id:            modelEvent.ID,
		InstanceId:    modelEvent.InstanceID,
		PromotionName: modelEvent.PromotionName,
		StartTime:     modelEvent.StartTime.String(),
		EndTime:       modelEvent.EndTime.String(),
		ResultPhase:   modelEvent.ResultPhase,
		ResultMessage: modelEvent.ResultMessage,
		Details: &kargov1.PromotionEventDetails{
			Project: modelEvent.ProjectName,
			Stage:   modelEvent.StageName,
			InitiatedBy: &kargov1.OperationInitiator{
				Username:  details.InitiatedBy.Username,
				Automated: details.InitiatedBy.Automated,
			},
			PromotionStatus:       details.PromotionStatus,
			FreightName:           details.FreightDetails.FreightName,
			FreightAlias:          details.FreightDetails.FreightAlias,
			FreightCreationTime:   details.FreightDetails.FreightCreationTime,
			VerificationStartTime: details.FreightDetails.VerificationStartTime,
			VerificationEndTime:   details.FreightDetails.VerificationEndTime,
			VerificationStatus:    details.FreightDetails.VerificationStatus,
			Miscellaneous:         misc,
		},
	}
	return event, nil
}

func MapField(field kargov1.PromotionField, instaceLevel bool) string {
	switch field {
	case kargov1.PromotionField_PROMOTION_FIELD_STAGES:
		return "promo.stage_name"
	case kargov1.PromotionField_PROMOTION_FIELD_PROJECTS:
		return "promo.project_name"
	case kargov1.PromotionField_PROMOTION_FIELD_INITIATORS:
		return "promo.details -> 'initiatedBy' ->> 'username'"
	case kargov1.PromotionField_PROMOTION_FIELD_PROMOTIONS:
		return "promo.promotion_name"
	case kargov1.PromotionField_PROMOTION_FIELD_INSTANCE_NAMES:
		if !instaceLevel {
			return "instance.name"
		}
	}
	return ""
}

func GetPromoTimeFilter(tableAlias string, filters *kargov1.PromotionFilter) ([]qm.QueryMod, error) {
	var err error
	startTime := time.Now().Add(-consts.Day)
	if startTimeStr := filters.GetStartTime(); startTimeStr != "" {
		startTime, err = time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "invalid startTime: %v", err)
		}
	}

	endTime := time.Now()
	if endTimeStr := filters.GetEndTime(); endTimeStr != "" {
		endTime, err = time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "invalid endTime: %v", err)
		}
	}

	if endTime.Before(startTime) {
		return nil, status.Errorf(codes.InvalidArgument, "invalid time range, end time before start time: start=%v, end=%v", startTime, endTime)
	}

	mods := []qm.QueryMod{
		qm.Where(fmt.Sprintf("%v.end_time >= '%v'", tableAlias, string(sqltypes.FormatTimestamp(startTime)))),
		qm.Where(fmt.Sprintf("%v.end_time <= '%v'", tableAlias, string(sqltypes.FormatTimestamp(endTime)))),
	}
	return mods, nil
}

func GetPromotionFilters(filters *kargov1.PromotionFilter, orgId string) []qm.QueryMod {
	mods := []qm.QueryMod{}

	if len(filters.GetProjects()) > 0 {
		mods = append(mods, database.WhereJSONin("promo.project_name", filters.GetProjects()))
	}

	if len(filters.GetStageName()) > 0 {
		stageNames, _ := types.MapSlice(filters.GetStageName(), func(in string) (string, error) {
			return strings.ReplaceAll(in, "*", "%"), nil
		})
		mods = append(mods, qm.Where("promo.stage_name LIKE ANY (?)", sqltypes.StringArray(stageNames)))
	}

	if len(filters.GetPromotionName()) > 0 {
		mods = append(mods, database.WhereJSONin("promo.promotion_name", filters.GetPromotionName()))
	}

	if len(filters.GetInstanceId()) > 0 {
		mods = append(mods, database.WhereJSONin("promo.instance_id", filters.GetInstanceId()))
	} else {
		mods = append(mods, qm.InnerJoin("kargo_instance as instance on promo.instance_id=instance.id"), qm.Where("instance.organization_owner = ?", orgId))
		// use instance name filters only at org level
		if len(filters.GetInstanceNames()) > 0 {
			mods = append(mods, database.WhereJSONin("instance.name", filters.GetInstanceNames()))
		}
	}

	if len(filters.GetInitiatedBy()) > 0 {
		mods = append(mods, database.WhereJSONin("promo.details -> 'initiatedBy' ->> 'username'", filters.GetInitiatedBy()))
	}

	return mods
}

func (s *KargoV1Server) getPromotionEventsField(ctx context.Context, mods []qm.QueryMod, field string) ([]string, error) {
	mods = append(mods, qm.Distinct(fmt.Sprintf("%v as field", field)),
		qm.From("kargo_promotions as promo"))
	sqlRows, err := models.NewQuery(mods...).QueryContext(ctx, s.db)
	if err != nil {
		return nil, err
	}
	defer func() { _ = sqlRows.Close() }()

	result := []string{}
	for sqlRows.Next() {
		res := ""
		if err := sqlRows.Scan(&res); err != nil {
			return nil, err
		}
		result = append(result, res)
	}

	if err := sqlRows.Err(); err != nil {
		return nil, err
	}

	return result, nil
}

func (s *KargoV1Server) getPromotionEvents(ctx context.Context, mods []qm.QueryMod) ([]*models.KargoPromotion, error) {
	events := []*models.KargoPromotion{}
	mods = append(mods, qm.Select("promo.*"),
		qm.From("kargo_promotions as promo"),
		qm.OrderBy("promo.start_time desc"))
	if err := models.NewQuery(mods...).Bind(ctx, s.db, &events); err != nil {
		return nil, err
	}
	return events, nil
}

func (s *KargoV1Server) getPromotionEventsCount(ctx context.Context, mods []qm.QueryMod) (int64, error) {
	count := 0
	mods = append(mods, qm.Select("count(*)"),
		qm.From("kargo_promotions as promo"))
	if err := models.NewQuery(mods...).QueryRowContext(ctx, s.db).Scan(&count); err != nil {
		return 0, err
	}
	return int64(count), nil
}
