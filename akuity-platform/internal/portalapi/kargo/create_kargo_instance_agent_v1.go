package kargo

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/services/kargo"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/validator"
	"github.com/akuityio/akuity-platform/internal/version"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

const (
	OrgAgentLimitError       = "organization has reached the maximum number of allowed kargo agents"
	LicenseClusterLimitError = "product has reached the maximum number of allowed kargo agents as per license"
)

func (s *KargoV1Server) CreateKargoInstanceAgent(
	ctx context.Context,
	req *kargov1.CreateKargoInstanceAgentRequest,
) (*kargov1.CreateKargoInstanceAgentResponse, error) {
	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionCreateKargoInstanceAgents(
			req.GetWorkspaceId(),
			req.GetInstanceId(),
			req.GetName(),
		)); err != nil {
		return nil, err
	}

	if config.IsSelfHosted {
		licenseData := config.GetLicense()
		count, err := s.repoSet.KargoAgents().Count(ctx)
		if err != nil {
			return nil, err
		}
		if licenseData.KargoAgents > 0 && licenseData.KargoAgents < uint64(count)+1 {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, LicenseClusterLimitError)
		}
	} else {
		summary, err := kargo.NewKargoStatSource(s.db, req.GetOrganizationId()).GetSummary(ctx)
		if err != nil {
			return nil, err
		}

		quota, err := s.featSvc.GetOrgQuotas(ctx, req.GetOrganizationId())
		if err != nil {
			return nil, err
		}
		maxAgents := quota.MaxKargoAgents
		if maxAgents > 0 && maxAgents < int64(summary.AgentsCount)+1 {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, OrgAgentLimitError)
		}

		org, err := s.repoSet.Organizations().GetByID(ctx, req.GetOrganizationId())
		if err != nil {
			return nil, err
		}
		orgStatus, err := org.GetOrgStatus()
		if err != nil {
			return nil, err
		}
		// TODO: currently trial accounts are not monitored for expiry
		if orgStatus != nil && !orgStatus.Trial && orgStatus.ExpiryTime > 0 &&
			time.Now().After(time.Unix(orgStatus.ExpiryTime, 0).Add(s.cfg.GracePeriodDuration)) {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, OrgPlanExpiredError)
		}
	}

	data := req.GetData()
	if data == nil {
		return nil, status.Error(codes.InvalidArgument, "missing required field `data`")
	}

	// ensure that there are no other agents connected to the same argocd instance for this kargo instance
	if data.RemoteArgocd != "" {
		// check if user has enough permissions to connect the kargo agent to the provided argocd instance
		if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
			accesscontrol.NewActionUpdateWorkspaceInstances(req.GetWorkspaceId(), data.RemoteArgocd)); err != nil {
			return nil, err
		}
		// check if argocd instance exists
		instance, err := s.repoSet.ArgoCDInstances().Filter(models.ArgoCDInstanceWhere.ID.EQ(data.RemoteArgocd), models.ArgoCDInstanceWhere.OrganizationOwner.EQ(req.GetOrganizationId())).One(ctx)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return nil, errorsutil.NewAPIStatus(http.StatusNotFound, "remote argocd instance not found")
			}
			return nil, err
		}
		if !instance.DeletionTimestamp.IsZero() {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "requested remote argocd instance is being deleted")
		}
		if data.AkuityManaged {
			count, err := s.repoSet.KargoAgents().Filter(models.KargoAgentWhere.InstanceID.EQ(req.GetInstanceId()), models.KargoAgentWhere.RemoteArgocdInstanceID.EQ(null.StringFrom(data.RemoteArgocd)), qm.Where("spec ->> 'akuityManaged' = 'true'")).Count(ctx)
			if err != nil {
				return nil, err
			}
			if count > 0 {
				return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "an existing akp managed agent is already configured with this remote argocd instance")
			}
		}
	} else if data.AkuityManaged {
		count, err := s.repoSet.KargoAgents().Filter(models.KargoAgentWhere.InstanceID.EQ(req.GetInstanceId()), models.KargoAgentWhere.RemoteArgocdInstanceID.IsNull(), qm.Where("spec ->> 'akuityManaged' = 'true'")).Count(ctx)
		if err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "only 1 akuity managed agent is allowed to be configured without a remote akp argocd instance")
		}

		if data.SelfManagedArgocdUrl != "" {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "akuity managed agent is not allowed to be configured with a self managed argocd instance")
		}
	}

	// backward compatibility
	namespace := strings.TrimSpace(data.GetNamespace())
	if len(namespace) == 0 {
		namespace = strings.TrimSpace(req.GetNamespace()) // nolint:staticcheck
	}
	if len(namespace) == 0 {
		namespace = instances.DefaultClusterNamespace
	}
	argocdNs := strings.TrimSpace(data.GetArgocdNamespace())
	if len(argocdNs) == 0 {
		argocdNs = instances.DefaultArgoCDNamespace
	}

	targetVersion := req.GetData().GetTargetVersion()
	if targetVersion == "" {
		targetVersion = version.GetLatestAgentVersion()
	}
	if err := validator.ValidateTargetVersion(targetVersion); err != nil {
		return nil, err
	}

	instanceModel, err := s.instances.Filter(
		models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(req.GetOrganizationId())),
		models.KargoInstanceWhere.ID.EQ(req.GetInstanceId()),
	).One(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, shared.ErrNotFound
		}
		return nil, err
	}

	spec, err := instanceModel.Config.GetSpec()
	if err != nil {
		return nil, err
	}

	agentCustomization := spec.KargoAgentCustomizationDefaults
	if data.Kustomization != nil {
		agentCustomization.Kustomization = data.Kustomization.AsMap()
	}

	if data.AutoUpgradeDisabled != nil {
		agentCustomization.AutoUpgradeDisabled = *data.AutoUpgradeDisabled
	}

	if err := validator.ValidateResourceName(req.GetName(), MinInstanceNameLength, MaxInstanceNameLength); err != nil {
		return nil, err
	}

	if err := validator.ValidateResourceNamespace(namespace, 3, 63); err != nil {
		return nil, err
	}
	if err := validator.ValidateResourceNamespace(argocdNs, 3, 63); err != nil {
		return nil, err
	}
	if err := validator.ValidateResourceDescription(req.GetDescription(), 255); err != nil {
		return nil, err
	}

	if err := validator.ValidateLabelsAndAnnotations(data.GetLabels(), true); err != nil {
		return nil, err
	}
	if err := validator.ValidateLabelsAndAnnotations(data.GetAnnotations(), false); err != nil {
		return nil, err
	}

	cnt, err := s.repoSet.KargoAgents().Filter(
		models.KargoAgentWhere.Name.EQ(req.Name),
		models.KargoAgentWhere.InstanceID.EQ(req.InstanceId)).Count(ctx)
	if err != nil {
		return nil, err
	}
	if cnt > 0 {
		return nil, errorsutil.NewAPIStatus(http.StatusConflict, fmt.Sprintf("agent %s already exists", req.Name))
	}

	agent := &models.KargoAgent{
		InstanceID:          req.GetInstanceId(),
		Name:                req.GetName(),
		Namespace:           namespace,
		Description:         null.StringFrom(req.GetDescription()),
		AutoUpgradeDisabled: agentCustomization.AutoUpgradeDisabled,
	}
	// set remote argocd id and unset argocd ns
	// if remote argocd is set or akuity managed
	if data.RemoteArgocd != "" {
		agent.RemoteArgocdInstanceID = null.StringFrom(data.RemoteArgocd)
		argocdNs = ""
	} else if data.AkuityManaged {
		argocdNs = ""
	}

	agentSpec := models.KargoAgentSpec{
		Size:            s.newAgentSize(data.GetSize()),
		Labels:          data.GetLabels(),
		Annotations:     data.GetAnnotations(),
		Kustomization:   agentCustomization.Kustomization,
		TargetVersion:   targetVersion,
		SizeVersion:     models.KargoClientSizeSchemaVersion,
		AkuityManaged:   data.AkuityManaged,
		ArgocdNamespace: argocdNs,
	}
	if data.AkuityManaged {
		// reset agent kustomizations as it is not allowed for akuity managed agents
		if len(data.Kustomization.AsMap()) != 0 {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "kustomization is not allowed for akuity managed kargo agents")
		}
		agentSpec.Kustomization = map[string]interface{}{}
		// auto upgrade is always true for akuity managed agents
		agent.AutoUpgradeDisabled = false
	} else if data.SelfManagedArgocdUrl != "" {
		u, err := url.Parse(data.SelfManagedArgocdUrl)
		if err != nil || u.Scheme == "" || u.Host == "" {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "invalid selfManagedArgocdUrl")
		}
		argocdURL := strings.TrimSuffix(data.SelfManagedArgocdUrl, "/")
		agentSpec.SelfManagedArgocdURL = argocdURL
	}

	if err := agent.SetSpec(agentSpec); err != nil {
		return nil, err
	}

	password, err := database.RandomAlphabetString()
	if err != nil {
		return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "failed to generate agent password")
	}
	if err := agent.SetPrivateSpec(models.KargoAgentPrivateSpec{AgentPassword: password}); err != nil {
		return nil, err
	}

	if err := s.repoSet.KargoAgents().Create(ctx, agent); err != nil {
		if strings.Contains(err.Error(), shared.DuplicateKeyErrorString) {
			return nil, errorsutil.NewAPIStatus(http.StatusConflict, fmt.Sprintf("agent %s already exists", req.Name))
		}
		return nil, err
	}
	createdAgent, err := NewKargoAgentV1(agent, s.cfg.ClusterProgressingDeadline)
	if err != nil {
		return nil, err
	}
	return &kargov1.CreateKargoInstanceAgentResponse{Agent: createdAgent}, nil
}
