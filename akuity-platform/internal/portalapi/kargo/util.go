package kargo

import (
	"context"
	"strings"
	"time"

	"github.com/samber/lo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"k8s.io/apimachinery/pkg/api/meta"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/utils/ptr"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
	healthv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/status/health/v1"
	reconciliationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/status/reconciliation/v1"
)

func (s *KargoV1Server) newInstanceV1(in *Instance, unsupportedVersion bool) (*kargov1.KargoInstance, error) {
	return s.newInstanceWithCensorV1(in, true, unsupportedVersion)
}

func (s *KargoV1Server) newInstanceWithCensorV1(in *Instance, censor, unsupportedVersion bool) (*kargov1.KargoInstance, error) {
	var deleteTime *timestamppb.Timestamp
	if !in.DeletionTimestamp.IsZero() {
		deleteTime = timestamppb.New(in.DeletionTimestamp.Time)
	}
	instanceStatus, err := in.GetStatus()
	if err != nil {
		return nil, err
	}

	var certStatus *kargov1.CertificateStatus
	msg := "Certificate is up to date and has not expired"
	if !instanceStatus.Info.CertificateStatus.IsCNameSet {
		msg = "Verifying DNS Record"
	} else if !instanceStatus.Info.CertificateStatus.IsIssued {
		msg = "Provisioning Certificate"
	}

	if !config.IsSelfHosted && in.Config.FQDN.String != "" {
		certStatus = &kargov1.CertificateStatus{
			IsCnameSet: instanceStatus.Info.CertificateStatus.IsCNameSet,
			IsIssued:   instanceStatus.Info.CertificateStatus.IsIssued,
			Message:    msg,
		}
	}
	reconciliationStatus, err := in.GetReconciliationStatus(s.cfg.InstanceProgressingDeadline)
	if err != nil {
		return nil, err
	}
	spec, err := in.Config.GetSpec()
	if err != nil {
		return nil, err
	}

	var kustomization *structpb.Struct
	if len(spec.KargoAgentCustomizationDefaults.Kustomization) != 0 {
		if kustomization, err = structpb.NewStruct(spec.KargoAgentCustomizationDefaults.Kustomization); err != nil {
			return nil, err
		}
	}
	var ipAllowList []*kargov1.KargoIPAllowListEntry
	for _, item := range spec.IpAllowlist {
		ipAllowList = append(ipAllowList, &kargov1.KargoIPAllowListEntry{Ip: item.Ip, Description: item.Description})
	}

	var akuityIntelligence *kargov1.AkuityIntelligence
	if spec.AkuityIntelligence != nil {
		akuityIntelligence = &kargov1.AkuityIntelligence{
			AiSupportEngineerEnabled: spec.AkuityIntelligence.AiSupportEngineerEnabled,
			Enabled:                  spec.AkuityIntelligence.Enabled,
			AllowedUsernames:         spec.AkuityIntelligence.AllowedUsernames,
			AllowedGroups:            spec.AkuityIntelligence.AllowedGroups,
		}
	}

	specV1 := &kargov1.KargoInstanceSpec{
		BackendIpAllowListEnabled: spec.BackendIpAllowlistEnabled,
		AkuityIntelligence:        akuityIntelligence,
		IpAllowList:               ipAllowList,
		AgentCustomizationDefaults: &kargov1.KargoAgentCustomization{
			AutoUpgradeDisabled: spec.KargoAgentCustomizationDefaults.AutoUpgradeDisabled,
			Kustomization:       kustomization,
		},
		DefaultShardAgent:      spec.DefaultShardAgentID,
		GlobalCredentialsNs:    spec.GlobalCredentialsNs,
		GlobalServiceAccountNs: spec.GlobalServiceAccountNs,
	}

	if spec.GCConfig != nil {
		specV1.GcConfig = &kargov1.GarbageCollectorConfig{
			MaxRetainedFreight:      spec.GCConfig.MaxRetainedFreight,
			MaxRetainedPromotions:   spec.GCConfig.MaxRetainedPromotions,
			MinFreightDeletionAge:   uint32(spec.GCConfig.MinFreightDeletionAge.Seconds()),
			MinPromotionDeletionAge: uint32(spec.GCConfig.MinPromotionDeletionAge.Seconds()),
		}
	}

	// webhook cm
	if _, err := in.Config.GetWebhookCM(); err != nil {
		return nil, err
	}
	var webhookCM *kargov1.KargoWebhookCM

	// controller cm
	if _, err := in.Config.GetControllerCM(); err != nil {
		return nil, err
	}
	var controllerCMV1 *kargov1.KargoControllerCM

	// api cm
	apiCM, err := in.Config.GetApiCM()
	if err != nil {
		return nil, err
	}
	apiCMV1 := &kargov1.KargoApiCM{
		AdminAccountEnabled:  apiCM.AdminAccountEnabled,
		AdminAccountTokenTtl: apiCM.AdminAccountTokenTTL,
	}

	// api secret
	apiSecret, err := in.Config.GetAPISecret()
	if err != nil {
		return nil, err
	}
	apiSecretV1 := &kargov1.KargoApiSecret{
		AdminAccountPasswordHash: apiSecret.AdminAccountPasswordHash, // safe to return as it's hashed
	}

	// oidc config
	oidcConfig, err := in.Config.GetOidcConfig()
	if err != nil {
		return nil, err
	}
	oidcConfigV1 := &kargov1.KargoOidcConfig{
		Enabled:    oidcConfig.Enabled,
		DexEnabled: oidcConfig.DexEnabled,
		AdminAccount: &kargov1.KargoPredefinedAccountData{
			//nolint:staticcheck
			Email: oidcConfig.AdminAccount.Email,
			//nolint:staticcheck
			Sub: oidcConfig.AdminAccount.Sub,
			//nolint:staticcheck
			Groups: oidcConfig.AdminAccount.Groups,
			Claims: convertModelsClaimToAPI(oidcConfig.AdminAccount.Claims),
		},
		ViewerAccount: &kargov1.KargoPredefinedAccountData{
			//nolint:staticcheck
			Email: oidcConfig.ViewerAccount.Email,
			//nolint:staticcheck
			Sub: oidcConfig.ViewerAccount.Sub,
			//nolint:staticcheck
			Groups: oidcConfig.ViewerAccount.Groups,
			Claims: convertModelsClaimToAPI(oidcConfig.ViewerAccount.Claims),
		},
		UserAccount: &kargov1.KargoPredefinedAccountData{
			//nolint:staticcheck
			Email: oidcConfig.UserAccount.Email,
			//nolint:staticcheck
			Sub: oidcConfig.UserAccount.Sub,
			//nolint:staticcheck
			Groups: oidcConfig.UserAccount.Groups,
			Claims: convertModelsClaimToAPI(oidcConfig.UserAccount.Claims),
		},
		ProjectCreatorAccount: &kargov1.KargoPredefinedAccountData{
			//nolint:staticcheck
			Email: oidcConfig.ProjectCreator.Email,
			//nolint:staticcheck
			Sub: oidcConfig.ProjectCreator.Sub,
			//nolint:staticcheck
			Groups: oidcConfig.ProjectCreator.Groups,
			Claims: convertModelsClaimToAPI(oidcConfig.ProjectCreator.Claims),
		},
	}
	if oidcConfig.DexEnabled && oidcConfig.DexConfig != "" {
		oidcConfigV1.DexConfig = oidcConfig.DexConfig
		oidcConfigV1.DexConfigSecret = map[string]*kargov1.KargoOidcConfig_Value{}
		// remove secrets value
		for key := range oidcConfig.DexConfigSecrets {
			if censor {
				oidcConfigV1.DexConfigSecret[key] = &kargov1.KargoOidcConfig_Value{Value: ptr.To("")}
			} else {
				oidcConfigV1.DexConfigSecret[key] = &kargov1.KargoOidcConfig_Value{Value: ptr.To(oidcConfig.DexConfigSecrets[key])}
			}
		}
	} else {
		oidcConfigV1.CliClientId = oidcConfig.CliClientID
		oidcConfigV1.ClientId = oidcConfig.ClientID
		oidcConfigV1.IssuerUrl = oidcConfig.IssuerURL
		oidcConfigV1.AdditionalScopes = oidcConfig.AdditionalScopes
	}

	secret, err := in.Config.GetMiscellaneousSecrets(censor)
	if err != nil {
		return nil, err
	}

	miscSecretV1 := &kargov1.KargoMiscellaneousSecrets{}
	if secret.DatadogRolloutsSecret != nil {
		miscSecretV1.DatadogRolloutsSecret = &kargov1.DataDogRolloutsSecret{
			Address: secret.DatadogRolloutsSecret.Address,
			ApiKey:  secret.DatadogRolloutsSecret.APIKey,
			AppKey:  secret.DatadogRolloutsSecret.AppKey,
		}
	}
	if secret.NewRelicRolloutsSecret != nil {
		miscSecretV1.NewrelicRolloutsSecret = &kargov1.NewRelicRolloutsSecret{
			PersonalApiKey:   secret.NewRelicRolloutsSecret.PersonalAPIKey,
			AccountId:        secret.NewRelicRolloutsSecret.AccountID,
			Region:           secret.NewRelicRolloutsSecret.Region,
			BaseUrlRest:      secret.NewRelicRolloutsSecret.BaseURLRest,
			BaseUrlNerdgraph: secret.NewRelicRolloutsSecret.BaseURLNerdGraph,
		}
	}
	if secret.InfluxDbRolloutsSecret != nil {
		miscSecretV1.InfluxdbRolloutsSecret = &kargov1.InfluxDbRolloutsSecret{
			InfluxdbAddress: secret.InfluxDbRolloutsSecret.Address,
			AuthToken:       secret.InfluxDbRolloutsSecret.AuthToken,
			Org:             secret.InfluxDbRolloutsSecret.Org,
		}
	}
	subdomain := in.ID
	if in.Config.Subdomain.String != "" {
		subdomain = in.Config.Subdomain.String
	}

	fqdn := ""
	if in.Config.FQDN.String != "" {
		fqdn = in.Config.FQDN.String
	}
	return &kargov1.KargoInstance{
		Id:                    in.ID,
		Name:                  in.Name,
		Description:           in.Description.String,
		Spec:                  specV1,
		Hostname:              instanceStatus.Hostname.String,
		Generation:            uint32(in.Generation),
		HealthStatus:          misc.NewHealthStatusV1(instanceStatus.Health),
		ReconciliationStatus:  misc.NewReconciliationStatusV1(reconciliationStatus),
		DeleteTime:            deleteTime,
		OwnerOrganizationName: in.OwnerOrganizationName,
		Version:               in.Config.Version.String,
		ControllerCm:          controllerCMV1,
		WebhookCm:             webhookCM,
		ApiCm:                 apiCMV1,
		ApiSecret:             apiSecretV1,
		OidcConfig:            oidcConfigV1,
		Subdomain:             subdomain,
		Fqdn:                  fqdn,
		WorkspaceId:           in.WorkspaceID.String,
		MiscellaneousSecrets:  miscSecretV1,
		CertificateStatus:     certStatus,
		UnsupportedVersion:    &unsupportedVersion,
	}, nil
}

func NewKargoAgentV1(in *models.KargoAgent, deadline time.Duration) (*kargov1.KargoAgent, error) {
	var deleteTime *timestamppb.Timestamp
	if !in.DeletionTimestamp.IsZero() {
		deleteTime = timestamppb.New(in.DeletionTimestamp.Time)
	}

	spec, err := in.GetSpec()
	if err != nil {
		return nil, err
	}
	var kustomization *structpb.Struct
	if len(spec.Kustomization) > 0 {
		if kustomization, err = structpb.NewStruct(spec.Kustomization); err != nil {
			return nil, err
		}
	}
	var size kargov1.KargoAgentSize
	switch spec.Size {
	case models.ClusterSizeSmall:
		size = kargov1.KargoAgentSize_KARGO_AGENT_SIZE_SMALL
	case models.ClusterSizeMedium:
		size = kargov1.KargoAgentSize_KARGO_AGENT_SIZE_MEDIUM
	case models.ClusterSizeLarge:
		size = kargov1.KargoAgentSize_KARGO_AGENT_SIZE_LARGE
	default:
		size = kargov1.KargoAgentSize_KARGO_AGENT_SIZE_SMALL
	}

	reconciliationStatus, err := in.GetReconciliationStatus(deadline, false)
	if err != nil {
		return nil, err
	}
	status, err := in.GetStatus()
	if err != nil {
		return nil, err
	}
	agentState := newKargoAgentStateV1(in, &status, deadline)

	return &kargov1.KargoAgent{
		Id:                   in.ID,
		Name:                 in.Name,
		Namespace:            in.Namespace,
		Description:          in.Description.String,
		DeleteTime:           deleteTime,
		HealthStatus:         misc.NewHealthStatusV1(status.GetHealth()),
		ReconciliationStatus: misc.NewReconciliationStatusV1(reconciliationStatus),
		ObservedGeneration:   ptr.To(uint64(in.Generation)),
		Data: &kargov1.KargoAgentData{
			TargetVersion:        spec.TargetVersion,
			Size:                 size,
			Kustomization:        kustomization,
			Labels:               spec.Labels,
			Annotations:          spec.Annotations,
			AutoUpgradeDisabled:  &in.AutoUpgradeDisabled,
			RemoteArgocd:         in.RemoteArgocdInstanceID.String,
			AkuityManaged:        spec.AkuityManaged,
			Namespace:            in.Namespace,
			ArgocdNamespace:      spec.ArgocdNamespace,
			SelfManagedArgocdUrl: spec.SelfManagedArgocdURL,
		},
		AgentState:                        agentState,
		ReadonlySettingsChangedGeneration: ptr.To(uint64(in.ReadonlySettingsChangedGeneration.Int)),
	}, nil
}

func newKargoAgentStateV1(agent *models.KargoAgent, status *models.KargoAgentStatus, autoUpdateDeadline time.Duration) *kargov1.KargoAgentState {
	if status.AgentState == nil {
		return nil
	}
	state := status.AgentState
	var observeTime *timestamppb.Timestamp
	if !state.ObservedAt.IsZero() {
		observeTime = timestamppb.New(state.ObservedAt.Time)
	}
	updateStatus := reconciliationv1.AgentUpdateStatus_AGENT_UPDATE_STATUS_UPDATED
	if agent.DeletionTimestamp.IsZero() && !agent.AutoUpgradeDisabled && int64(status.ObservedGeneration.Int) != state.Status.MinObservedGeneration && state.Status.MinObservedGeneration > -1 {
		if !state.ObservedGenerationAppliedAt.IsZero() && time.Since(state.ObservedGenerationAppliedAt.Time) > autoUpdateDeadline {
			updateStatus = reconciliationv1.AgentUpdateStatus_AGENT_UPDATE_STATUS_DELAYED
		} else {
			updateStatus = reconciliationv1.AgentUpdateStatus_AGENT_UPDATE_STATUS_IN_PROGRESS
		}
	}
	return &kargov1.KargoAgentState{
		Version:      state.Version,
		KargoVersion: state.KargoVersion,
		ObserveTime:  observeTime,
		Status: &healthv1.AgentAggregatedHealthResponse{
			MinObservedGeneration: uint64(state.Status.MinObservedGeneration),
			Healthy:               misc.NewAggregatedHealthStatus(state.Status.Healthy),
			Progressing:           misc.NewAggregatedHealthStatus(state.Status.Progressing),
			Degraded:              misc.NewAggregatedHealthStatus(state.Status.Degraded),
			Unknown:               misc.NewAggregatedHealthStatus(state.Status.Unknown),
			PriorityStatus:        misc.MapTenantPhaseToRpcPhase(state.Status.PriorityStatus),
		},
		AgentIds:                  state.AgentIDs,
		LastUserAppliedGeneration: uint64(state.LastUserAppliedGeneration),
		UpdateStatus:              &updateStatus,
	}
}

func (s *KargoV1Server) newAgentSize(size kargov1.KargoAgentSize) models.ClusterSize {
	var agentSize models.ClusterSize
	switch size {
	case kargov1.KargoAgentSize_KARGO_AGENT_SIZE_SMALL:
		agentSize = models.ClusterSizeSmall
	case kargov1.KargoAgentSize_KARGO_AGENT_SIZE_MEDIUM:
		agentSize = models.ClusterSizeMedium
	case kargov1.KargoAgentSize_KARGO_AGENT_SIZE_LARGE:
		agentSize = models.ClusterSizeLarge
	default:
		agentSize = models.ClusterSizeSmall
	}
	return agentSize
}

func (s *KargoV1Server) kargoEnabled(ctx context.Context, orgID string) error {
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetKargo().Enabled() {
		return status.Error(codes.PermissionDenied, "kargo feature is not enabled")
	}
	return nil
}

func (s *KargoV1Server) filterInstanceByWorkspace(instances []*Instance, workspaceID string) []*Instance {
	filtered := make([]*Instance, 0, len(instances))
	for _, i := range instances {
		if i.WorkspaceID.String == workspaceID {
			filtered = append(filtered, i)
		}
	}
	return filtered
}

func convertModelsClaimToAPI(claim map[string][]string) map[string]*kargov1.KargoPredefinedAccountClaimValue {
	claims := make(map[string]*kargov1.KargoPredefinedAccountClaimValue)
	for k, v := range claim {
		claims[k] = &kargov1.KargoPredefinedAccountClaimValue{Values: v}
	}
	return claims
}

// k3sResourceGroups holds metadata required to export and apply resources Kargo k3s resources
var k3sResourceGroups = []struct {
	gk             schema.GroupKind
	getApplyItems  func(req *kargov1.ApplyKargoInstanceRequest) []*structpb.Struct
	setExportItems func(resp *kargov1.ExportKargoInstanceResponse, items []*structpb.Struct)
	prune          kargov1.PruneResourceType
	listOps        metav1.ListOptions
	field          string
	clusterScoped  bool
	validate       func(obj *unstructured.Unstructured) error
	// annotationFilters is a client-side filter to filter resources by annotations.
	annotationFilters map[string]string
}{{
	gk:            schema.GroupKind{Group: "kargo.akuity.io", Kind: "Project"},
	getApplyItems: func(req *kargov1.ApplyKargoInstanceRequest) []*structpb.Struct { return req.Projects },
	setExportItems: func(req *kargov1.ExportKargoInstanceResponse, items []*structpb.Struct) {
		req.Projects = items
	},
	prune:         kargov1.PruneResourceType_PRUNE_RESOURCE_TYPE_PROJECTS,
	field:         "projects",
	clusterScoped: true,
}, {
	gk:            schema.GroupKind{Group: "kargo.akuity.io", Kind: "ClusterPromotionTask"},
	getApplyItems: func(req *kargov1.ApplyKargoInstanceRequest) []*structpb.Struct { return req.ClusterPromotionTasks },
	setExportItems: func(req *kargov1.ExportKargoInstanceResponse, items []*structpb.Struct) {
		req.ClusterPromotionTasks = items
	},
	prune:         kargov1.PruneResourceType_PRUNE_RESOURCE_TYPE_CLUSTER_PROMOTION_TASKS,
	field:         "cluster_promotion_tasks",
	clusterScoped: true,
}, {
	gk:            schema.GroupKind{Group: "kargo.akuity.io", Kind: "Stage"},
	getApplyItems: func(req *kargov1.ApplyKargoInstanceRequest) []*structpb.Struct { return req.Stages },
	setExportItems: func(req *kargov1.ExportKargoInstanceResponse, items []*structpb.Struct) {
		req.Stages = items
	},
	prune: kargov1.PruneResourceType_PRUNE_RESOURCE_TYPE_STAGES,
	field: "stages",
}, {
	gk:            schema.GroupKind{Group: "kargo.akuity.io", Kind: "Warehouse"},
	getApplyItems: func(req *kargov1.ApplyKargoInstanceRequest) []*structpb.Struct { return req.Warehouses },
	setExportItems: func(req *kargov1.ExportKargoInstanceResponse, items []*structpb.Struct) {
		req.Warehouses = items
	},
	prune: kargov1.PruneResourceType_PRUNE_RESOURCE_TYPE_WAREHOUSES,
	field: "warehouses",
}, {
	gk:            schema.GroupKind{Group: "argoproj.io", Kind: "AnalysisTemplate"},
	getApplyItems: func(req *kargov1.ApplyKargoInstanceRequest) []*structpb.Struct { return req.AnalysisTemplates },
	setExportItems: func(req *kargov1.ExportKargoInstanceResponse, items []*structpb.Struct) {
		req.AnalysisTemplates = items
	},
	prune: kargov1.PruneResourceType_PRUNE_RESOURCE_TYPE_ANALYSIS_TEMPLATES,
	field: "analysis_templates",
}, {
	gk:            schema.GroupKind{Group: "kargo.akuity.io", Kind: "PromotionTask"},
	getApplyItems: func(req *kargov1.ApplyKargoInstanceRequest) []*structpb.Struct { return req.PromotionTasks },
	setExportItems: func(req *kargov1.ExportKargoInstanceResponse, items []*structpb.Struct) {
		req.PromotionTasks = items
	},
	prune: kargov1.PruneResourceType_PRUNE_RESOURCE_TYPE_PROMOTION_TASKS,
	field: "promotion_tasks",
}, {
	gk:            schema.GroupKind{Group: "", Kind: "Secret"},
	getApplyItems: func(req *kargov1.ApplyKargoInstanceRequest) []*structpb.Struct { return req.RepoCredentials },
	prune:         kargov1.PruneResourceType_PRUNE_RESOURCE_TYPE_REPO_CREDENTIALS,
	listOps: metav1.ListOptions{
		LabelSelector: "kargo.akuity.io/cred-type",
	},
	field: "repo_credentials",
	validate: func(obj *unstructured.Unstructured) error {
		if labels := obj.GetLabels(); labels == nil || labels["kargo.akuity.io/cred-type"] == "" {
			return status.Errorf(codes.InvalidArgument, "missing label kargo.akuity.io/cred-type in %s", obj.GetName())
		}
		return nil
	},
}, {
	gk:            schema.GroupKind{Group: "", Kind: "ServiceAccount"},
	getApplyItems: func(req *kargov1.ApplyKargoInstanceRequest) []*structpb.Struct { return req.ServiceAccounts },
	setExportItems: func(resp *kargov1.ExportKargoInstanceResponse, items []*structpb.Struct) {
		resp.ServiceAccounts = items
	},
	prune: kargov1.PruneResourceType_PRUNE_RESOURCE_TYPE_SERVICE_ACCOUNTS,
	field: "service_accounts",
	annotationFilters: map[string]string{
		"rbac.kargo.akuity.io/managed": "true",
	},
	validate: func(obj *unstructured.Unstructured) error {
		if annotations := obj.GetAnnotations(); annotations == nil || annotations["rbac.kargo.akuity.io/managed"] != "true" {
			return status.Errorf(codes.InvalidArgument, "missing annotation rbac.kargo.akuity.io/managed=true in %s", obj.GetName())
		}
		return nil
	},
}, {
	gk:            schema.GroupKind{Group: "rbac.authorization.k8s.io", Kind: "Role"},
	getApplyItems: func(req *kargov1.ApplyKargoInstanceRequest) []*structpb.Struct { return req.Roles },
	setExportItems: func(resp *kargov1.ExportKargoInstanceResponse, items []*structpb.Struct) {
		resp.Roles = items
	},
	prune: kargov1.PruneResourceType_PRUNE_RESOURCE_TYPE_ROLES,
	field: "roles",
	annotationFilters: map[string]string{
		"rbac.kargo.akuity.io/managed": "true",
	},
	validate: func(obj *unstructured.Unstructured) error {
		if annotations := obj.GetAnnotations(); annotations == nil || annotations["rbac.kargo.akuity.io/managed"] != "true" {
			return status.Errorf(codes.InvalidArgument, "missing annotation rbac.kargo.akuity.io/managed=true in %s", obj.GetName())
		}
		return nil
	},
}, {
	gk:            schema.GroupKind{Group: "rbac.authorization.k8s.io", Kind: "RoleBinding"},
	getApplyItems: func(req *kargov1.ApplyKargoInstanceRequest) []*structpb.Struct { return req.RoleBindings },
	setExportItems: func(resp *kargov1.ExportKargoInstanceResponse, items []*structpb.Struct) {
		resp.RoleBindings = items
	},
	prune: kargov1.PruneResourceType_PRUNE_RESOURCE_TYPE_ROLE_BINDINGS,
	field: "role_bindings",
	annotationFilters: map[string]string{
		"rbac.kargo.akuity.io/managed": "true",
	},
	validate: func(obj *unstructured.Unstructured) error {
		if annotations := obj.GetAnnotations(); annotations == nil || annotations["rbac.kargo.akuity.io/managed"] != "true" {
			return status.Errorf(codes.InvalidArgument, "missing annotation rbac.kargo.akuity.io/managed=true in %s", obj.GetName())
		}
		return nil
	},
}, {
	gk:            schema.GroupKind{Group: "", Kind: "ConfigMap"},
	getApplyItems: func(req *kargov1.ApplyKargoInstanceRequest) []*structpb.Struct { return req.Configmaps },
	setExportItems: func(resp *kargov1.ExportKargoInstanceResponse, items []*structpb.Struct) {
		resp.Configmaps = items
	},
	prune: kargov1.PruneResourceType_PRUNE_RESOURCE_TYPE_CONFIGMAPS,
	field: "configmaps",
}}

func getProjectNamespacesFromK3s(ctx context.Context, k3sDynamicClient dynamic.Interface, mapper meta.RESTMapper) ([]string, error) {
	projectGK := schema.GroupKind{Group: "kargo.akuity.io", Kind: "Project"}
	mapping, err := mapper.RESTMapping(projectGK)
	if err != nil {
		if isCRDDiscoveryNotReadyErr(err) {
			return nil, nil
		}
		return nil, err
	}

	gvr := mapping.Resource
	res, err := k3sDynamicClient.Resource(gvr).List(ctx, metav1.ListOptions{})
	if err != nil {
		if isCRDDiscoveryNotReadyErr(err) {
			return nil, nil
		}
		return nil, err
	}

	namepaces := lo.Map(res.Items, func(item unstructured.Unstructured, _ int) string {
		return item.GetName()
	})

	return namepaces, nil
}

func createListOptionsWithNamespaceFilter(baseOpts metav1.ListOptions, projectNamespaces []string, isClusterScoped bool) metav1.ListOptions {
	if isClusterScoped || len(projectNamespaces) == 0 {
		return baseOpts
	}

	opts := baseOpts.DeepCopy()
	if opts == nil {
		opts = &metav1.ListOptions{}
	}

	var fieldSelector string
	if len(projectNamespaces) == 1 {
		fieldSelector = "metadata.namespace=" + projectNamespaces[0]
	} else {
		fieldSelector = "metadata.namespace in (" + strings.Join(projectNamespaces, ",") + ")"
	}

	if opts.FieldSelector == "" {
		opts.FieldSelector = fieldSelector
	} else {
		opts.FieldSelector = opts.FieldSelector + "," + fieldSelector
	}

	return *opts
}

func filterResourcesByAnnotations(items []unstructured.Unstructured, annotationFilters map[string]string) []unstructured.Unstructured {
	if len(annotationFilters) == 0 {
		return items
	}

	filtered := make([]unstructured.Unstructured, 0, len(items))
	for _, item := range items {
		annotations := item.GetAnnotations()
		if annotations == nil {
			continue
		}

		matches := true
		for key, expectedValue := range annotationFilters {
			actualValue, exists := annotations[key]
			if !exists {
				matches = false
				break
			}
			if expectedValue != "" && actualValue != expectedValue {
				matches = false
				break
			}
		}

		if matches {
			filtered = append(filtered, item)
		}
	}

	return filtered
}
