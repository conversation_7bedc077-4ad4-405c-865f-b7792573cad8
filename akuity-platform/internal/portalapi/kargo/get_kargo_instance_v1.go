package kargo

import (
	"context"

	"github.com/volatiletech/null/v8"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *KargoV1Server) GetKargoInstance(
	ctx context.Context,
	req *kargov1.GetKargoInstanceRequest,
) (*kargov1.GetKargoInstanceResponse, error) {
	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	instanceModel, err := s.instances.Filter(
		models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(req.GetOrganizationId())),
		models.KargoInstanceWhere.Name.EQ(req.GetName()),
	).One(ctx)
	if err != nil {
		return nil, err
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceKargoInstances(instanceModel.WorkspaceID.String, instanceModel.ID)); err != nil {
		return nil, err
	}

	instance, err := s.newInstanceV1(instanceModel, misc.IsKargoComponentVersionSupported(instanceModel.Config.Version.String, s.versions, s.kargoUnstableVersion, true) != nil)
	if err != nil {
		return nil, err
	}

	return &kargov1.GetKargoInstanceResponse{
		Instance: instance,
	}, nil
}
