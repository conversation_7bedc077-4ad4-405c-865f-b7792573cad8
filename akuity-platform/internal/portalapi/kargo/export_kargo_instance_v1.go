package kargo

import (
	"context"
	"encoding/json"
	"time"

	grpcgatewayruntime "github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/volatiletech/null/v8"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/structpb"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/restmapper"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/akuity-platform/api/akuity/v1alpha1"
	"github.com/akuityio/akuity-platform/internal/kargo"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *KargoV1Server) ExportKargoInstance(
	ctx context.Context,
	req *kargov1.ExportKargoInstanceRequest,
) (*kargov1.ExportKargoInstanceResponse, error) {
	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetWorkspaceKargoInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	instance, err := s.instances.Filter(
		models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(req.GetOrganizationId())),
		models.KargoInstanceWhere.ID.EQ(req.GetId()),
	).One(ctx)
	if err != nil {
		return nil, err
	}
	kargoInstance, err := s.newInstanceV1(instance, misc.IsKargoComponentVersionSupported(instance.Config.Version.String, s.versions, s.kargoUnstableVersion, true) != nil)
	if err != nil {
		return nil, err
	}

	var marshaller grpcgatewayruntime.JSONPb
	objBytes, err := marshaller.Marshal(kargoInstance.Spec)
	if err != nil {
		return nil, err
	}
	var instanceSpec v1alpha1.KargoInstanceSpec
	if err := json.Unmarshal(objBytes, &instanceSpec); err != nil {
		return nil, err
	}

	// only one of fqdn and subdomain should be the effective value
	fqdn := kargoInstance.Fqdn
	subdomain := kargoInstance.Subdomain
	if fqdn != "" && subdomain != "" {
		subdomain = ""
	}
	kargoAPIObject := v1alpha1.Kargo{
		TypeMeta:   metav1.TypeMeta{Kind: "Kargo", APIVersion: kargo.APIVersion},
		ObjectMeta: metav1.ObjectMeta{Name: instance.Name},
		Spec: v1alpha1.KargoSpec{
			Description:       instance.Description.String,
			Version:           kargoInstance.Version,
			KargoInstanceSpec: instanceSpec,
			Fqdn:              fqdn,
			Subdomain:         subdomain,
			OidcConfig:        getOidcConfig(kargoInstance.OidcConfig),
		},
	}

	res := &kargov1.ExportKargoInstanceResponse{}
	kargoMap := map[string]any{}
	if err := types.RemarshalTo(kargoAPIObject, &kargoMap); err != nil {
		return nil, err
	}
	if res.Kargo, err = structpb.NewStruct(kargoMap); err != nil {
		return nil, err
	}

	agents, err := s.ListKargoInstanceAgents(ctx, &kargov1.ListKargoInstanceAgentsRequest{
		OrganizationId: req.GetOrganizationId(),
		WorkspaceId:    req.GetWorkspaceId(),
		InstanceId:     req.GetId(),
	})
	if err != nil {
		return nil, err
	}

	agentStructs := make([]*structpb.Struct, 0, len(agents.Agents))
	for _, agent := range agents.Agents {
		agentAPIObject := v1alpha1.KargoAgent{
			TypeMeta: metav1.TypeMeta{Kind: "KargoAgent", APIVersion: kargo.APIVersion},
			ObjectMeta: metav1.ObjectMeta{
				Name:        agent.Name,
				Namespace:   agent.Data.GetNamespace(),
				Annotations: agent.GetData().GetAnnotations(),
				Labels:      agent.GetData().GetLabels(),
			},
			Spec: v1alpha1.KargoAgentSpec{
				Description: agent.Description,
				Data: v1alpha1.KargoAgentData{
					Size:                v1alpha1.KargoAgentSize(s.newAgentSize(agent.Data.Size)),
					AutoUpgradeDisabled: agent.Data.AutoUpgradeDisabled,
					TargetVersion:       agent.Data.TargetVersion,
					RemoteArgocd:        agent.Data.RemoteArgocd,
					AkuityManaged:       agent.Data.AkuityManaged,
					ArgocdNamespace:     agent.Data.ArgocdNamespace,
				},
			},
		}

		if agent.Data.Kustomization != nil {
			raw, err := runtime.DefaultUnstructuredConverter.ToUnstructured(agent.Data.Kustomization)
			if err != nil {
				return nil, status.Errorf(codes.Internal, "failed to convert kustomization: %v", err)
			}
			agentAPIObject.Spec.Data.Kustomization = runtime.RawExtension{Object: &unstructured.Unstructured{Object: raw}}
		}

		agentMap := make(map[string]any)
		if err := types.RemarshalTo(agentAPIObject, &agentMap); err != nil {
			return nil, err
		}
		agentStruct, err := structpb.NewStruct(agentMap)
		if err != nil {
			return nil, err
		}
		agentStructs = append(agentStructs, agentStruct)
	}

	res.Agents = agentStructs

	if cm := kargoInstance.GetApiCm(); cm != nil {
		bytes, err := protojson.MarshalOptions{
			UseProtoNames: false,
		}.Marshal(cm)
		if err != nil {
			return nil, err
		}

		var kargoCM map[string]any
		if err := json.Unmarshal(bytes, &kargoCM); err != nil {
			return nil, err
		}
		res.KargoConfigmap, err = structpb.NewStruct(kargoCM)
		if err != nil {
			return nil, err
		}
	}

	if err := wait.PollUntilContextTimeout(ctx, 2*time.Second, 120*time.Second, true, func(ctx context.Context) (bool, error) {
		return s.populateK3sObjects(ctx, kargoInstance.Id, res)
	}); err != nil {
		return nil, err
	}
	return res, nil
}

func (s *KargoV1Server) populateK3sObjects(ctx context.Context, instanceId string, resp *kargov1.ExportKargoInstanceResponse) (bool, error) {
	tnt, err := client.NewKargoTenant(s.hostRestConfig, *s.log, instanceId)
	if err != nil {
		return false, err
	}
	k3sDynamicClient, err := tnt.ControlPlaneDynamicClientset(ctx)
	if err != nil {
		// If the k3s kubeconfig doesn't exist, we skip the next steps and wait until the kubeconfig secret is created.
		if k8serrors.IsNotFound(err) {
			return false, nil
		}
		// We don't return the error directly since it may contain AKP implementation details.
		return false, status.Errorf(codes.InvalidArgument, "The instance is still being provisioned. Please try again in a few minutes")
	}

	k3sKubeClient, err := tnt.ControlPlaneKubeClientset(ctx)
	if err != nil {
		if k8serrors.IsNotFound(err) {
			return false, nil
		}
		return false, status.Errorf(codes.InvalidArgument, "The instance is still being provisioned. Please try again in a few minutes")
	}
	gr, err := restmapper.GetAPIGroupResources(k3sKubeClient.Discovery())
	if err != nil {
		return false, err
	}
	mapper := restmapper.NewDiscoveryRESTMapper(gr)

	projectNamespaces, err := getProjectNamespacesFromK3s(ctx, k3sDynamicClient, mapper)
	if err != nil {
		return false, err
	}

	for _, group := range k3sResourceGroups {
		if group.setExportItems == nil {
			continue
		}
		mapping, err := mapper.RESTMapping(group.gk)
		if err != nil {
			return false, err
		}
		gvr := mapping.Resource
		var res *unstructured.UnstructuredList
		listOpts := createListOptionsWithNamespaceFilter(group.listOps, projectNamespaces, group.clusterScoped)
		res, err = k3sDynamicClient.Resource(gvr).List(ctx, listOpts)
		if err != nil {
			return false, err
		}

		if group.annotationFilters != nil {
			res.Items = filterResourcesByAnnotations(res.Items, group.annotationFilters)
		}

		shared.RemoveUnmanagedFields(res)
		structs := make([]*structpb.Struct, 0, len(res.Items))
		for _, item := range res.Items {
			newStruct, err := structpb.NewStruct(item.Object)
			if err != nil {
				return false, err
			}
			structs = append(structs, newStruct)
		}
		group.setExportItems(resp, structs)
	}
	return true, nil
}

func getOidcConfig(cfg *kargov1.KargoOidcConfig) *v1alpha1.KargoOidcConfig {
	res := &v1alpha1.KargoOidcConfig{}
	if cfg == nil {
		return res
	}
	if !cfg.Enabled &&
		!cfg.DexEnabled &&
		cfg.DexConfig == "" &&
		cfg.IssuerUrl == "" &&
		cfg.ClientId == "" &&
		cfg.CliClientId == "" &&
		len(cfg.AdditionalScopes) == 0 &&
		len(cfg.AdminAccount.Claims) == 0 &&
		len(cfg.ViewerAccount.Claims) == 0 {
		return res
	}
	adminAccountClaims := map[string]v1alpha1.KargoPredefinedAccountClaimValue{}
	if cfg.AdminAccount.Claims != nil {
		adminAccountClaims = make(map[string]v1alpha1.KargoPredefinedAccountClaimValue)
		for k, v := range cfg.AdminAccount.Claims {
			adminAccountClaims[k] = v1alpha1.KargoPredefinedAccountClaimValue{Values: v.Values}
		}
	}
	viewerAccountClaims := map[string]v1alpha1.KargoPredefinedAccountClaimValue{}
	if cfg.ViewerAccount.Claims != nil {
		viewerAccountClaims = make(map[string]v1alpha1.KargoPredefinedAccountClaimValue)
		for k, v := range cfg.ViewerAccount.Claims {
			viewerAccountClaims[k] = v1alpha1.KargoPredefinedAccountClaimValue{Values: v.Values}
		}
	}
	return &v1alpha1.KargoOidcConfig{
		Enabled:         cfg.Enabled,
		DexEnabled:      cfg.DexEnabled,
		DexConfig:       cfg.DexConfig,
		DexConfigSecret: nil,
		IssuerURL:       cfg.IssuerUrl,
		ClientID:        cfg.ClientId,
		CliClientID:     cfg.CliClientId,
		AdminAccount: v1alpha1.KargoPredefinedAccountData{
			Claims: adminAccountClaims,
		},
		ViewerAccount: v1alpha1.KargoPredefinedAccountData{
			Claims: viewerAccountClaims,
		},
		AdditionalScopes: cfg.AdditionalScopes,
	}
}
