package kargo

import (
	"context"
	"net/http"

	"github.com/pkg/errors"
	"github.com/volatiletech/null/v8"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *KargoV1Server) UpdateKargoInstanceWorkspace(
	ctx context.Context,
	req *kargov1.UpdateKargoInstanceWorkspaceRequest,
) (*kargov1.UpdateKargoInstanceWorkspaceResponse, error) {
	instanceID := req.GetId()
	srcWorkspace := req.GetWorkspaceId()
	dstWorkspace := req.GetNewWorkspaceId()
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionDeleteWorkspaceKargoInstances(srcWorkspace, instanceID),
		accesscontrol.NewActionCreateWorkspaceKargoInstances(dstWorkspace),
	); err != nil {
		return nil, err
	}

	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	inst, err := s.repoSet.KargoInstances().Filter(
		models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(req.GetOrganizationId())),
	).GetByID(ctx, instanceID)
	if err != nil {
		if instances.IsNotFoundErr(err) {
			return nil, status.Errorf(codes.NotFound, "instance %q not found", instanceID)
		}
		return nil, err
	}

	if inst.WorkspaceID.String == dstWorkspace {
		return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "can't transfer into same workspace")
	}
	_, err = s.repoSet.Workspaces().GetByID(ctx, dstWorkspace)
	if err != nil {
		if instances.IsNotFoundErr(err) {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "workspace not found")
		}
		return nil, errors.Wrap(err, "failed to get workspace")
	}

	inst.WorkspaceID = null.StringFrom(dstWorkspace)
	if err := s.repoSet.KargoInstances().Update(ctx, inst); err != nil {
		return nil, err
	}
	updatedModel, err := s.instances.GetByID(ctx, instanceID)
	if err != nil {
		return nil, err
	}
	updated, err := s.newInstanceV1(updatedModel, misc.IsKargoComponentVersionSupported(updatedModel.Config.Version.String, s.versions, s.kargoUnstableVersion, true) != nil)
	if err != nil {
		return nil, err
	}

	return &kargov1.UpdateKargoInstanceWorkspaceResponse{
		Instance: updated,
	}, nil
}
