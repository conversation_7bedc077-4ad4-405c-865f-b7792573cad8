package kargo

import (
	"context"
	"fmt"
	"maps"
	"net/http"
	"reflect"
	"slices"
	"time"

	jsonpatch "github.com/evanphx/json-patch"
	"github.com/volatiletech/null/v8"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"k8s.io/apimachinery/pkg/util/sets"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/internal/utils/validator"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

var restrictedNamespaces = map[string]bool{
	"default":          true,
	"kube-system":      true,
	"kube-public":      true,
	"kubec-node-lease": true,
	"kargo":            true,
}

func (s *KargoV1Server) updateKargoInstanceV1(ctx context.Context, orgID, id string, instance *kargov1.KargoInstance) (*Instance, error) {
	txDB, txBeginner := database.WithTxBeginner(s.db)
	repoSet := client.NewRepoSet(txDB)

	txCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := txBeginner.Begin(txCtx)
	if err != nil {
		return nil, err
	}

	changedSpec := false
	changedMetadata := false

	existingConfig, err := repoSet.KargoInstanceConfigs().GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	existingInstance, err := repoSet.KargoInstances().GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// check description
	if existingInstance.Description.String != instance.Description {
		if err := validator.ValidateResourceDescription(instance.Description, 255); err != nil {
			return nil, err
		}
		existingInstance.Description = null.StringFrom(instance.Description)
		changedMetadata = true
	}

	// check name
	if existingInstance.Name != instance.Name {
		if err := validator.ValidateResourceName(instance.Name, MinInstanceNameLength, MaxInstanceNameLength); err != nil {
			return nil, err
		}
		cnt, err := repoSet.KargoInstances().Filter(
			models.KargoInstanceWhere.Name.EQ(instance.Name),
			models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(orgID))).Count(ctx)
		if err != nil {
			return nil, err
		}
		if cnt > 0 {
			return nil, status.Error(codes.AlreadyExists, "kargo instance with this name already exists")
		}
		existingInstance.Name = instance.Name
		changedMetadata = true
	}

	// check workspace
	workspaceEnabled := s.featSvc.GetFeatureStatuses(ctx, &orgID).GetWorkspaces().Enabled()
	if workspaceEnabled && instance.WorkspaceId != "" && existingInstance.WorkspaceID.String != instance.WorkspaceId {
		return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "can't change workspace, please use transfer endpoint")
	}

	if instance.Version != "" {
		if existingConfig.Version.String != instance.Version {
			if err := misc.IsKargoComponentVersionSupported(instance.Version, s.versions, s.kargoUnstableVersion, s.featSvc.GetFeatureStatuses(ctx, &orgID).GetKargoEnterprise().Enabled()); err != nil {
				return nil, err
			}
			existingConfig.Version = null.StringFrom(instance.Version)
			changedSpec = true
		}
	}

	// check subdomain has changed
	if instance.Subdomain != "" {
		if existingConfig.Subdomain.String != instance.Subdomain {
			if err := instances.ValidateSubdomain(instance.Subdomain); err != nil {
				return nil, err
			}
			cnt, err := s.repoSet.KargoInstanceConfigs().Filter(
				models.KargoInstanceConfigWhere.Subdomain.EQ(null.StringFrom(instance.Subdomain)),
				models.KargoInstanceConfigWhere.InstanceID.NEQ(id),
			).Count(ctx)
			if err != nil {
				return nil, err
			}
			if cnt > 0 {
				return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "subdomain is already in use")
			}
			existingConfig.Subdomain = null.StringFrom(instance.Subdomain)
			changedSpec = true
		}
	}

	// check fqdn has changed
	if instance.Fqdn != "" {
		if existingConfig.FQDN.String != instance.Fqdn {
			kargoCnt, err := s.repoSet.KargoInstanceConfigs().Filter(
				models.KargoInstanceConfigWhere.FQDN.EQ(null.StringFrom(instance.Fqdn)),
				models.KargoInstanceConfigWhere.InstanceID.NEQ(id),
			).Count(ctx)
			if err != nil {
				return nil, err
			}
			argoCnt, err := s.repoSet.ArgoCDInstanceConfigs().Filter(
				models.ArgoCDInstanceConfigWhere.FQDN.EQ(null.StringFrom(instance.Fqdn)),
			).Count(ctx)
			if err != nil {
				return nil, err
			}
			if kargoCnt+argoCnt > 0 {
				return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "fqdn is already in use")
			}
			existingConfig.FQDN = null.StringFrom(instance.Fqdn)
			changedSpec = true
		}
	} else if existingConfig.FQDN.Valid {
		existingConfig.FQDN = null.StringFromPtr(nil)
		changedSpec = true
	}

	// check if spec has changed
	if instance.Spec != nil {
		if err := checkRestrictedNamespace(instance.Spec.GlobalCredentialsNs); err != nil {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, err.Error())
		}
		if err := checkRestrictedNamespace(instance.Spec.GlobalServiceAccountNs); err != nil {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, err.Error())
		}

		updatedSpec := models.KargoInstanceConfigSpec{
			BackendIpAllowlistEnabled:       instance.Spec.BackendIpAllowListEnabled,
			KargoAgentCustomizationDefaults: models.KargoAgentCustomization{},
			DefaultShardAgentID:             instance.Spec.DefaultShardAgent,
			GlobalCredentialsNs:             dedupArray(instance.Spec.GlobalCredentialsNs),
			GlobalServiceAccountNs:          dedupArray(instance.Spec.GlobalServiceAccountNs),
		}
		if instance.Spec.AgentCustomizationDefaults != nil {
			updatedSpec.KargoAgentCustomizationDefaults = models.KargoAgentCustomization{
				AutoUpgradeDisabled: instance.Spec.AgentCustomizationDefaults.AutoUpgradeDisabled,
			}
			if instance.Spec.AgentCustomizationDefaults.Kustomization != nil {
				updatedSpec.KargoAgentCustomizationDefaults.Kustomization = instance.Spec.AgentCustomizationDefaults.Kustomization.AsMap()
			}
		}

		if instance.Spec.AkuityIntelligence != nil {
			updatedSpec.AkuityIntelligence = &kargov1.AkuityIntelligence{
				AiSupportEngineerEnabled: instance.Spec.AkuityIntelligence.AiSupportEngineerEnabled,
				Enabled:                  instance.Spec.AkuityIntelligence.Enabled,
				AllowedUsernames:         dedupArray(instance.Spec.AkuityIntelligence.AllowedUsernames),
				AllowedGroups:            dedupArray(instance.Spec.AkuityIntelligence.AllowedGroups),
			}
		}

		for _, item := range instance.Spec.IpAllowList {
			updatedSpec.IpAllowlist = append(updatedSpec.IpAllowlist, models.IpAllowlistEntry{
				Ip:          item.Ip,
				Description: item.Description,
			})
			if err := instances.ValidateIpAllowlist(updatedSpec.IpAllowlist); err != nil {
				return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, err.Error())
			}
		}

		if instance.Spec.GcConfig != nil {
			updatedSpec.GCConfig = &models.GarbageCollectorConfig{
				MaxRetainedFreight:      instance.Spec.GcConfig.MaxRetainedFreight,
				MaxRetainedPromotions:   instance.Spec.GcConfig.MaxRetainedPromotions,
				MinFreightDeletionAge:   time.Duration(int64(instance.Spec.GcConfig.MinFreightDeletionAge)) * time.Second,
				MinPromotionDeletionAge: time.Duration(int64(instance.Spec.GcConfig.MinPromotionDeletionAge)) * time.Second,
			}
			if updatedSpec.GCConfig.MaxRetainedFreight <= 0 {
				updatedSpec.GCConfig.MaxRetainedFreight = DefaultMaxRetainedFreight
			}
			if updatedSpec.GCConfig.MaxRetainedPromotions <= 0 {
				updatedSpec.GCConfig.MaxRetainedPromotions = DefaultMaxRetainedPromotions
			}
			if updatedSpec.GCConfig.MinFreightDeletionAge <= 0 {
				updatedSpec.GCConfig.MinFreightDeletionAge = DefaultMinFreightDeletionAge
			}
			if updatedSpec.GCConfig.MinPromotionDeletionAge <= 0 {
				updatedSpec.GCConfig.MinPromotionDeletionAge = DefaultMinPromotionDeletionAge
			}
		} else {
			updatedSpec.GCConfig = nil
		}

		existingSpec, err := existingConfig.GetSpec()
		if err != nil {
			return nil, err
		}
		if existingConfig.Version.String != instance.Version {
			existingConfig.Version = null.StringFrom(instance.Version)
			changedSpec = true
		}
		if !reflect.DeepEqual(existingSpec, updatedSpec) {
			if err := existingConfig.SetSpec(updatedSpec); err != nil {
				return nil, err
			}
			changedSpec = true
		}
	}
	if instance.MiscellaneousSecrets != nil {
		existingMiscSecrets, err := existingConfig.GetMiscellaneousSecrets(false)
		if err != nil {
			return nil, err
		}
		updatedMiscSecrets := models.KargoMiscellaneousSecrets{}
		if instance.MiscellaneousSecrets.DatadogRolloutsSecret != nil {
			updatedMiscSecrets.DatadogRolloutsSecret = &models.DataDogRolloutsSecret{
				Address: instance.MiscellaneousSecrets.DatadogRolloutsSecret.Address,
				APIKey:  instance.MiscellaneousSecrets.DatadogRolloutsSecret.ApiKey,
				AppKey:  instance.MiscellaneousSecrets.DatadogRolloutsSecret.AppKey,
			}
		}
		if instance.MiscellaneousSecrets.NewrelicRolloutsSecret != nil {
			updatedMiscSecrets.NewRelicRolloutsSecret = &models.NewRelicRolloutsSecret{
				PersonalAPIKey:   instance.MiscellaneousSecrets.NewrelicRolloutsSecret.PersonalApiKey,
				AccountID:        instance.MiscellaneousSecrets.NewrelicRolloutsSecret.AccountId,
				Region:           instance.MiscellaneousSecrets.NewrelicRolloutsSecret.Region,
				BaseURLRest:      instance.MiscellaneousSecrets.NewrelicRolloutsSecret.BaseUrlRest,
				BaseURLNerdGraph: instance.MiscellaneousSecrets.NewrelicRolloutsSecret.BaseUrlNerdgraph,
			}
		}
		if instance.MiscellaneousSecrets.InfluxdbRolloutsSecret != nil {
			updatedMiscSecrets.InfluxDbRolloutsSecret = &models.InfluxDbRolloutsSecret{
				Address:   instance.MiscellaneousSecrets.InfluxdbRolloutsSecret.InfluxdbAddress,
				AuthToken: instance.MiscellaneousSecrets.InfluxdbRolloutsSecret.AuthToken,
				Org:       instance.MiscellaneousSecrets.InfluxdbRolloutsSecret.Org,
			}
		}
		if !reflect.DeepEqual(existingMiscSecrets, updatedMiscSecrets) {
			if err := existingConfig.SetMiscellaneousSecrets(updatedMiscSecrets); err != nil {
				return nil, err
			}
			changedSpec = true
		}
	}

	// check if webhook cm changed
	if instance.WebhookCm != nil {
		existingWebhookCM, err := existingConfig.GetWebhookCM()
		if err != nil {
			return nil, err
		}
		updatedWebhookCM := models.KargoWebhookCM{}
		if !reflect.DeepEqual(existingWebhookCM, updatedWebhookCM) {
			if err := existingConfig.SetWebhookCM(updatedWebhookCM); err != nil {
				return nil, err
			}
			changedSpec = true
		}
	}

	// check if controller cm changed
	if instance.ControllerCm != nil {
		existingControllerCM, err := existingConfig.GetControllerCM()
		if err != nil {
			return nil, err
		}
		updatedControllerCM := models.KargoControllerCM{}
		if !reflect.DeepEqual(existingControllerCM, updatedControllerCM) {
			if err := existingConfig.SetControllerCM(updatedControllerCM); err != nil {
				return nil, err
			}
			changedSpec = true
		}
	}
	existingApiCM, err := existingConfig.GetApiCM()
	if err != nil {
		return nil, err
	}
	adminEnabled := existingApiCM.AdminAccountEnabled
	// check if api cm changed
	if instance.ApiCm != nil {
		updatedApiCM := models.KargoApiCM{
			AdminAccountEnabled:       instance.ApiCm.AdminAccountEnabled,
			AdminAccountTokenIssuer:   existingApiCM.AdminAccountTokenIssuer,
			AdminAccountTokenAudience: existingApiCM.AdminAccountTokenAudience,
			AdminAccountTokenTTL:      instance.ApiCm.AdminAccountTokenTtl,
		}
		adminEnabled = instance.ApiCm.AdminAccountEnabled
		if !reflect.DeepEqual(existingApiCM, updatedApiCM) {
			if err := existingConfig.SetApiCM(updatedApiCM); err != nil {
				return nil, err
			}
			changedSpec = true
		}
	}

	// check if api secret changed
	if instance.ApiSecret != nil {
		existingApiSecret, err := existingConfig.GetAPISecret()
		if err != nil {
			return nil, err
		}
		if adminEnabled && instance.ApiSecret.AdminAccountPasswordHash == "" {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "AdminAccountPasswordHash is required if Admin Account is enabled")
		}
		updatedApiSecret := models.KargoApiSecret{
			AdminAccountPasswordHash: instance.ApiSecret.AdminAccountPasswordHash,
		}
		if !reflect.DeepEqual(existingApiSecret, updatedApiSecret) {
			if err := existingConfig.SetAPISecret(updatedApiSecret); err != nil {
				return nil, err
			}
			changedSpec = true
		}
	}

	// check if oidc config changed
	if instance.OidcConfig != nil {
		existingOidcConfig, err := existingConfig.GetOidcConfig()
		if err != nil {
			return nil, err
		}

		// Clear out the encrypted config before comparison
		existingOidcConfig.DexEncryptedConfig = ""
		existingOidcConfig.DexEncryptedConfigSecrets = ""

		updatedOidcConfig := models.KargoOidcConfig{
			Enabled:    instance.OidcConfig.Enabled,
			DexEnabled: instance.OidcConfig.DexEnabled,
			AdminAccount: func() models.KargoPredefinedAccountData {
				if instance.OidcConfig.AdminAccount != nil {
					return models.KargoPredefinedAccountData{
						Email:  dedupArray(instance.OidcConfig.AdminAccount.Email),  //nolint:staticcheck
						Sub:    dedupArray(instance.OidcConfig.AdminAccount.Sub),    //nolint:staticcheck
						Groups: dedupArray(instance.OidcConfig.AdminAccount.Groups), //nolint:staticcheck
						Claims: convertKargoSSOClaims(instance.OidcConfig.AdminAccount.Claims),
					}
				}
				return models.KargoPredefinedAccountData{}
			}(),
			ViewerAccount: func() models.KargoPredefinedAccountData {
				if instance.OidcConfig.ViewerAccount != nil {
					return models.KargoPredefinedAccountData{
						Email:  dedupArray(instance.OidcConfig.ViewerAccount.Email),  //nolint:staticcheck
						Sub:    dedupArray(instance.OidcConfig.ViewerAccount.Sub),    //nolint:staticcheck
						Groups: dedupArray(instance.OidcConfig.ViewerAccount.Groups), //nolint:staticcheck
						Claims: convertKargoSSOClaims(instance.OidcConfig.ViewerAccount.Claims),
					}
				}
				return models.KargoPredefinedAccountData{}
			}(),
			UserAccount: func() models.KargoPredefinedAccountData {
				if instance.OidcConfig.UserAccount != nil {
					return models.KargoPredefinedAccountData{
						Email:  dedupArray(instance.OidcConfig.UserAccount.Email),  //nolint:staticcheck
						Sub:    dedupArray(instance.OidcConfig.UserAccount.Sub),    //nolint:staticcheck
						Groups: dedupArray(instance.OidcConfig.UserAccount.Groups), //nolint:staticcheck
						Claims: convertKargoSSOClaims(instance.OidcConfig.UserAccount.Claims),
					}
				}
				return models.KargoPredefinedAccountData{}
			}(),
			ProjectCreator: func() models.KargoPredefinedAccountData {
				if instance.OidcConfig.ProjectCreatorAccount != nil {
					return models.KargoPredefinedAccountData{
						Email:  dedupArray(instance.OidcConfig.ProjectCreatorAccount.Email),  //nolint:staticcheck
						Sub:    dedupArray(instance.OidcConfig.ProjectCreatorAccount.Sub),    //nolint:staticcheck
						Groups: dedupArray(instance.OidcConfig.ProjectCreatorAccount.Groups), //nolint:staticcheck
						Claims: convertKargoSSOClaims(instance.OidcConfig.ProjectCreatorAccount.Claims),
					}
				}
				return models.KargoPredefinedAccountData{}
			}(),
			DexConfigSecrets: map[string]string{},
		}
		maps.Copy(updatedOidcConfig.DexConfigSecrets, existingOidcConfig.DexConfigSecrets)
		if instance.OidcConfig.Enabled {
			if instance.OidcConfig.DexEnabled {
				if instance.OidcConfig.DexConfig != "" {
					updatedOidcConfig.DexConfig = instance.OidcConfig.DexConfig
				} else {
					return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "DexConfig is required if DexEnabled is true")
				}
				for key, value := range instance.OidcConfig.DexConfigSecret {
					if value == nil || value.Value == nil {
						delete(updatedOidcConfig.DexConfigSecrets, key)
						continue
					}
					updatedOidcConfig.DexConfigSecrets[key] = *value.Value
				}
			} else {
				if instance.OidcConfig.CliClientId == "" || instance.OidcConfig.ClientId == "" || instance.OidcConfig.IssuerUrl == "" {
					return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "CliClientId, ClientId and IssuerUrl are required if Dex is disabled")
				}
				updatedOidcConfig.CliClientID = instance.OidcConfig.CliClientId
				updatedOidcConfig.ClientID = instance.OidcConfig.ClientId
				updatedOidcConfig.IssuerURL = instance.OidcConfig.IssuerUrl
				updatedOidcConfig.AdditionalScopes = dedupArray(instance.OidcConfig.AdditionalScopes)
			}
		}

		if !reflect.DeepEqual(existingOidcConfig, updatedOidcConfig) {
			if err := existingConfig.SetOidcConfig(updatedOidcConfig, s.cfg.DomainSuffix); err != nil {
				return nil, err
			}
			changedSpec = true
		}
	}

	if changedSpec {
		columns := sets.New[string](models.KargoInstanceConfigAllColumns...).
			Delete(models.KargoInstanceConfigColumns.PrivateSpec, models.KargoInstanceConfigColumns.InternalSpec)
		if err := repoSet.KargoInstanceConfigs().Update(ctx, existingConfig, columns.UnsortedList()...); err != nil {
			return nil, err
		}
	}

	if changedMetadata {
		if err := repoSet.KargoInstances().Update(ctx, existingInstance); err != nil {
			return nil, err
		}
	}

	if err := tx.Commit(); err != nil {
		return nil, err
	}
	return s.instances.GetByID(ctx, id)
}

func (s *KargoV1Server) PatchKargoInstance(
	ctx context.Context,
	req *kargov1.PatchKargoInstanceRequest,
) (*kargov1.PatchKargoInstanceResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateWorkspaceKargoInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	currentInstanceModel, err := s.instances.Filter(models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(req.GetOrganizationId()))).GetByID(ctx, req.GetId())
	if err != nil {
		if instances.IsNotFoundErr(err) {
			return nil, status.Errorf(codes.NotFound, "instance %q not found", req.GetId())
		}
		return nil, err
	}
	currentInstance, err := s.newInstanceWithCensorV1(currentInstanceModel, false, false)
	if err != nil {
		return nil, err
	}

	// remove dex config secret from patch as that is not necessary
	currentInstance.OidcConfig.DexConfigSecret = map[string]*kargov1.KargoOidcConfig_Value{}

	currentInstanceJSON, err := s.jsonMarshalOptions.Marshal(currentInstance)
	if err != nil {
		return nil, err
	}
	patchJSON, err := s.jsonMarshalOptions.Marshal(req.GetPatch())
	if err != nil {
		return nil, err
	}
	patchedJSON, err := jsonpatch.MergePatch(currentInstanceJSON, patchJSON)
	if err != nil {
		return nil, err
	}
	patchedInstance := &kargov1.KargoInstance{}
	if err := s.jsonUnmarshalOptions.Unmarshal(patchedJSON, patchedInstance); err != nil {
		return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, fmt.Errorf("failed to unmarshal patched instance: %v", err).Error())
	}

	updatedModel, err := s.updateKargoInstanceV1(ctx, req.GetOrganizationId(), req.GetId(), patchedInstance)
	if err != nil {
		return nil, err
	}
	updated, err := s.newInstanceV1(updatedModel, misc.IsKargoComponentVersionSupported(updatedModel.Config.Version.String, s.versions, s.kargoUnstableVersion, true) != nil)
	if err != nil {
		return nil, err
	}
	return &kargov1.PatchKargoInstanceResponse{
		Instance: updated,
	}, nil
}

func convertKargoSSOClaims(claims map[string]*kargov1.KargoPredefinedAccountClaimValue) map[string][]string {
	result := make(map[string][]string)
	for key, value := range claims {
		result[key] = dedupArray(value.Values)
	}
	return result
}

func dedupArray(arr []string) []string {
	if len(arr) == 0 {
		return nil
	}
	m := make(map[string]bool)
	for _, v := range arr {
		m[v] = true
	}
	result := make([]string, 0, len(m))
	for k := range m {
		result = append(result, k)
	}
	// sort to prevent changes in array ordering
	slices.Sort(result)
	return result
}

func checkRestrictedNamespace(nsList []string) error {
	for _, ns := range nsList {
		if restrictedNamespaces[ns] {
			return fmt.Errorf("namespace %s is restricted", ns)
		}
	}
	return nil
}
