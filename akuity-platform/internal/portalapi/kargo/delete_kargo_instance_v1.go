package kargo

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *KargoV1Server) DeleteInstance(
	ctx context.Context,
	req *kargov1.DeleteInstanceRequest,
) (*kargov1.DeleteInstanceResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionDeleteWorkspaceKargoInstances(req.GetWorkspaceId(), req.GetId())); err != nil {
		return nil, err
	}

	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	currentInstanceModel, err := s.instances.Filter(models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(req.GetOrganizationId()))).GetByID(ctx, req.GetId())
	if err != nil {
		if instances.IsNotFoundErr(err) {
			return nil, errorsutil.NewAPIStatus(http.StatusForbidden, fmt.Sprintf("instance %s not found", req.GetId()))
		}
		return nil, err
	}

	// check if instance is being used as a direct cluster in argocd
	argocdCluster, err := s.repoSet.ArgoCDClusters().Filter(qm.Where("coalesce(spec -> 'directClusterSpec' ->> 'kargoInstanceID', '') = ?", req.GetId())).ListAll(ctx, "id", "instance_id")
	if err != nil {
		return nil, err
	}
	if len(argocdCluster) > 0 {
		return nil, errorsutil.NewAPIStatus(http.StatusForbidden, fmt.Sprintf("instance %s is being used as an integration in argocd instance %v", req.GetId(), argocdCluster[0].InstanceID))
	}

	txDB, txBeginner := database.WithTxBeginner(s.db)
	repoSet := client.NewRepoSet(txDB)

	txCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := txBeginner.Begin(txCtx)
	if err != nil {
		return nil, err
	}

	orgID := currentInstanceModel.OrganizationOwner
	org, err := repoSet.Organizations().GetByID(ctx, orgID.String)
	if err != nil {
		return nil, err
	}
	orgSpec, err := org.GetSpec()
	if err != nil {
		return nil, err
	}
	existingKargoSpec := orgSpec.KargoInstanceQuota
	if existingKargoSpec != nil {
		delete(existingKargoSpec, currentInstanceModel.ID)
	}
	if err := org.SetSpec(*orgSpec); err != nil {
		return nil, err
	}
	if err := repoSet.Organizations().Update(ctx, org, models.OrganizationColumns.Spec); err != nil {
		return nil, err
	}

	modelInstance := currentInstanceModel.KargoInstance
	modelInstance.DeletionTimestamp = null.TimeFrom(time.Now())
	if err := repoSet.KargoInstances().Update(ctx, modelInstance, models.KargoInstanceColumns.DeletionTimestamp); err != nil {
		return nil, err
	}

	if err := tx.Commit(); err != nil {
		return nil, err
	}

	return &kargov1.DeleteInstanceResponse{}, nil
}
