package kargo

import (
	"context"
	"fmt"
	"strings"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/agent/pkg/client/apis/clusteragent"
	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/kargo"
	"github.com/akuityio/akuity-platform/internal/services/permissions"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
	healthv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/status/health/v1"
)

func (s *KargoV1Server) ListKargoInstanceAgents(
	ctx context.Context,
	req *kargov1.ListKargoInstanceAgentsRequest,
) (*kargov1.ListKargoInstanceAgentsResponse, error) {
	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	action := accesscontrol.NewActionGetKargoInstanceAgents(
		req.GetWorkspaceId(), req.GetInstanceId(), accesscontrol.ResourceAny)

	// Check if that user owns requested instance
	ok, err := enforcer.CheckResourceOwnership(ctx,
		permissions.ObjectWorkspaceKargoInstances,
		accesscontrol.FormatWorkspaceResource(req.GetWorkspaceId(), req.GetInstanceId()),
	)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, shared.NewPermissionDeniedErr(action)
	}

	workspaceID := req.GetWorkspaceId()
	if workspaceID == "" {
		inst, err := s.instances.GetByID(ctx, req.GetInstanceId())
		if err != nil {
			return nil, err
		}
		workspaceID = inst.WorkspaceID.String
	}

	resources, err := enforcer.GetResources(action.Object, action.Verb)
	if err != nil {
		return nil, err
	}
	resources, err = shared.FilterResources(req.GetInstanceId(), workspaceID, resources...)
	if err != nil {
		return nil, err
	}

	if len(resources) == 0 {
		return nil, shared.NewPermissionDeniedErr(action)
	}

	filters := req.GetFilter()
	if filters == nil {
		filters = &kargov1.KargoAgentFilter{}
	}

	mods, err := getAgentFilterMods(filters)
	if err != nil {
		return nil, err
	}

	policyMods, err := kargo.AgentsWhereNameInMod(req.GetInstanceId(), resources...)
	if err != nil {
		return nil, err
	}
	mods = append(mods, policyMods...)

	mods = append(mods, models.KargoAgentWhere.InstanceID.EQ(req.GetInstanceId()))

	totalCount, err := s.repoSet.KargoAgents(mods...).Count(ctx)
	if err != nil {
		return nil, err
	}

	if filters.GetLimit() > 0 {
		mods = append(mods, qm.Limit(int(filters.GetLimit())))
	}
	if filters.GetOffset() > 0 {
		mods = append(mods, qm.Offset(int(filters.GetOffset())))
	}
	mods = append(mods, qm.OrderBy(models.KargoAgentColumns.Name+" asc"))
	mods = append(mods, models.KargoAgentNoStatusManifestMod)
	agentModels, err := s.repoSet.KargoAgents(mods...).ListAll(ctx)
	if err != nil {
		return nil, err
	}
	agents, err := types.MapSlice(agentModels, func(in *models.KargoAgent) (*kargov1.KargoAgent, error) {
		return NewKargoAgentV1(in, s.cfg.ClusterProgressingDeadline)
	})
	if err != nil {
		return nil, err
	}
	return &kargov1.ListKargoInstanceAgentsResponse{
		Agents:     agents,
		TotalCount: totalCount,
	}, nil
}

func getAgentFilterMods(filter *kargov1.KargoAgentFilter) ([]qm.QueryMod, error) {
	var mods []qm.QueryMod
	if filter == nil {
		return mods, nil
	}

	if nameLike := filter.GetNameLike(); nameLike != "" {
		mods = append(mods, qm.Where(models.ArgoCDClusterColumns.Name+" like ?", "%"+nameLike+"%"))
	}

	if agentStatus := filter.GetAgentStatus(); len(agentStatus) > 0 {
		var statusLit []interface{}
		queryString := ""
		for _, stat := range agentStatus {
			agentStat, err := mapRpcPhaseToTenantPhase(stat)
			if err != nil {
				return nil, err
			}
			statusLit = append(statusLit, string(*agentStat))
			queryString = fmt.Sprintf("%v?,", queryString)
		}
		mods = append(mods, qm.Where(fmt.Sprintf(`case when
        (status_agent_state -> 'status' ->> 'PriorityStatus' is null) or
        extract(epoch from interval '%d seconds' - age(now(), to_timestamp(status_agent_state ->> 'observedAt', 'YYYY-MM-DD"T"HH24:MI:SS"Z"'))) < 0
			then 'Unknown'
		else
			status_agent_state -> 'status' ->> 'PriorityStatus'
		end in (%v)`, int64(models.ClusterStatusExpirationDuration.Seconds()), strings.TrimSuffix(queryString, ",")), statusLit...))
	}

	if agentVersions := filter.GetAgentVersion(); len(agentVersions) > 0 {
		mods = append(mods, database.WhereJSONin(`status_agent_state ->> 'version'`, agentVersions))
	}

	if argocdIDs := filter.GetRemoteArgocdIds(); len(argocdIDs) > 0 {
		mods = append(mods, models.KargoAgentWhere.RemoteArgocdInstanceID.IN(argocdIDs))
	}

	if filter.GetSelfManaged() {
		mods = append(mods, models.KargoAgentWhere.RemoteArgocdInstanceID.IsNull())
	}

	if excludeAgentVersion := filter.GetExcludeAgentVersion(); excludeAgentVersion != "" {
		mods = append(mods, qm.Where("coalesce(spec ->> 'targetVersion', ?) != ?", clusteragent.NewDataValuesAgent().GetVersion(), excludeAgentVersion))
	}

	if filter.GetOutdatedManifest() {
		mods = append(mods, qm.Where("status_agent_state->>'status' is null or status_agent_state->'status'->>'minObservedGeneration' != generation::text"))
	}

	if kargoVersions := filter.GetKargoVersion(); len(kargoVersions) > 0 {
		mods = append(mods, database.WhereJSONin(`status_agent_state ->> 'kargoVersion'`, kargoVersions))
	}

	if namespaces := filter.GetNamespace(); len(namespaces) > 0 {
		mods = append(mods, database.WhereJSONin("namespace", namespaces))
	}

	if labels := filter.GetLabels(); len(labels) > 0 {
		for labelKey, labelVal := range labels {
			mods = append(mods, qm.Where("spec -> 'labels' ->> ? = ?", labelKey, labelVal))
		}
	}

	if filter.GetNeedReapply() {
		mods = append(mods, qm.Where("status_agent_state is not null and (coalesce((status_agent_state ->> 'lastUserAppliedGeneration')::integer, 0) < readonly_settings_changed_generation)"))
	}

	return mods, nil
}

func mapRpcPhaseToTenantPhase(p healthv1.StatusCode) (*common.ArgoCDTenantPhase, error) {
	switch p {
	case healthv1.StatusCode_STATUS_CODE_HEALTHY:
		return &common.TenantPhaseHealthy, nil
	case healthv1.StatusCode_STATUS_CODE_PROGRESSING:
		return &common.TenantPhaseProgressing, nil
	case healthv1.StatusCode_STATUS_CODE_DEGRADED:
		return &common.TenantPhaseDegraded, nil
	case healthv1.StatusCode_STATUS_CODE_UNKNOWN:
		return &common.TenantPhaseUnknown, nil
	case healthv1.StatusCode_STATUS_CODE_UNSPECIFIED:
		return nil, status.Error(codes.InvalidArgument, "unspecified tenant phase")
	default:
		return nil, fmt.Errorf("invalid health phase")
	}
}
