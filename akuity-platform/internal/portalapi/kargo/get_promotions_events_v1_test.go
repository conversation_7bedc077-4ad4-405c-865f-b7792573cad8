package kargo

import (
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	sqltypes "github.com/volatiletech/sqlboiler/v4/types"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func TestGetPromotionFilters(t *testing.T) {
	now := time.Now().UTC()
	after := now.Add(-24 * time.Hour)

	startTimeValidFilter := time.Date(after.Year(), after.Month(), after.Day(), after.Hour(), after.Minute(), after.Second(), after.Nanosecond(), after.Location())
	endTimeValidFilter := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second(), now.Nanosecond(), now.Location())
	endTimeValidFilterString := endTimeValidFilter.Format(time.RFC3339Nano)

	startTimeInvalidTimeString := now.Format(time.RFC3339Nano)
	startTimeInvalidTime, err := time.Parse(time.RFC3339Nano, startTimeInvalidTimeString)
	require.NoError(t, err)

	endTimeInvalidTimeString := now.Add(-24 * time.Hour).Format(time.RFC3339Nano)
	endTimeInvalidTime, err := time.Parse(time.RFC3339Nano, endTimeInvalidTimeString)
	require.NoError(t, err)

	testCases := []struct {
		name     string
		filters  *kargov1.PromotionFilter
		orgId    string
		expected []qm.QueryMod
		err      error
	}{
		{
			name: "valid filters",
			filters: &kargov1.PromotionFilter{
				StartTime:     startTimeValidFilter.Format(time.RFC3339Nano),
				EndTime:       &endTimeValidFilterString,
				Projects:      []string{"project1", "project2"},
				StageName:     []string{"stage1", "stage2"},
				PromotionName: []string{"promotion1", "promotion2"},
				InstanceId:    []string{"instance1", "instance2"},
				InstanceNames: []string{"instanceName1", "instanceName2"},
				InitiatedBy:   []string{"user1", "user2"},
			},
			orgId: "org1",
			expected: []qm.QueryMod{
				qm.Where(fmt.Sprintf("promo.end_time >= '%v'", string(sqltypes.FormatTimestamp(startTimeValidFilter)))),
				qm.Where(fmt.Sprintf("promo.end_time <= '%v'", string(sqltypes.FormatTimestamp(endTimeValidFilter)))),
				qm.WhereIn("promo.project_name IN ?", "project1", "project2"),
				qm.Where("promo.stage_name LIKE ANY (?)", sqltypes.StringArray{"stage1", "stage2"}),
				qm.WhereIn("promo.promotion_name IN ?", "promotion1", "promotion2"),
				qm.WhereIn("promo.instance_id IN ?", "instance1", "instance2"),
				qm.WhereIn("promo.details -> 'initiatedBy' ->> 'username' IN ?", "user1", "user2"),
			},
			err: nil,
		},
		{
			name: "invalid time range",
			filters: &kargov1.PromotionFilter{
				StartTime: startTimeInvalidTimeString,
				EndTime:   &endTimeInvalidTimeString,
			},
			orgId:    "org1",
			expected: nil,
			err:      status.Errorf(codes.InvalidArgument, "invalid time range, end time before start time: start=%v, end=%v", startTimeInvalidTime, endTimeInvalidTime),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := GetPromotionFilters(tc.filters, tc.orgId)
			timeMods, err := GetPromoTimeFilter("promo", tc.filters)
			assert.Equal(t, tc.err, err)
			if err == nil {
				timeMods = append(timeMods, result...)
				assert.Equal(t, tc.expected, timeMods)
			}
		})
	}
}

func TestMapEvent(t *testing.T) {
	now := time.Now().UTC()
	startTimeValidInput := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second(), now.Nanosecond(), now.Location())
	after := now.Add(+24 * time.Hour)
	endTimeValidInput := time.Date(after.Year(), after.Month(), after.Day(), after.Hour(), after.Minute(), after.Second(), after.Nanosecond(), after.Location())

	details := &models.KargoPromotionDetails{
		InitiatedBy: models.OperationInitiator{
			Username:  "user1",
			Automated: false,
		},
		PromotionStatus: "status1",
		FreightDetails: &models.FreightDetails{
			FreightName:           "freight1",
			FreightAlias:          "alias1",
			FreightCreationTime:   startTimeValidInput.String(),
			VerificationStartTime: startTimeValidInput.String(),
			VerificationEndTime:   endTimeValidInput.String(),
			VerificationStatus:    "status1",
		},
	}

	jsonDetails, err := json.Marshal(details)
	if err != nil {
		panic(err)
	}

	testCases := []struct {
		name     string
		input    *models.KargoPromotion
		expected *kargov1.PromotionEvent
		err      error
	}{
		{
			name: "valid input",
			input: &models.KargoPromotion{
				ID:            "id1",
				InstanceID:    "instance1",
				PromotionName: "promotion1",
				StartTime:     startTimeValidInput,
				EndTime:       endTimeValidInput,
				ResultPhase:   "phase1",
				ResultMessage: "message1",
				ProjectName:   "project1",
				StageName:     "stage1",
				Details:       null.JSONFrom(jsonDetails),
			},
			expected: &kargov1.PromotionEvent{
				Id:            "id1",
				InstanceId:    "instance1",
				PromotionName: "promotion1",
				StartTime:     startTimeValidInput.String(),
				EndTime:       endTimeValidInput.String(),
				ResultPhase:   "phase1",
				ResultMessage: "message1",
				Details: &kargov1.PromotionEventDetails{
					Project: "project1",
					Stage:   "stage1",
					InitiatedBy: &kargov1.OperationInitiator{
						Username:  "user1",
						Automated: false,
					},
					PromotionStatus:       "status1",
					FreightName:           "freight1",
					FreightAlias:          "alias1",
					FreightCreationTime:   startTimeValidInput.String(),
					VerificationStartTime: startTimeValidInput.String(),
					VerificationEndTime:   endTimeValidInput.String(),
					VerificationStatus:    "status1",
					Miscellaneous: &structpb.Struct{
						Fields: make(map[string]*structpb.Value),
					},
				},
			},
			err: nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := MapEvent(tc.input)
			require.Equal(t, tc.err, err)
			require.Equal(t, tc.expected, result)
		})
	}
}
