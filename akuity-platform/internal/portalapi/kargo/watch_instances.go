package kargo

import (
	"context"
	"fmt"

	"github.com/volatiletech/null/v8"
	"k8s.io/utils/ptr"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/events"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *KargoV1Server) watchInstances(ctx context.Context, id, workspaceID, organizationID string) (<-chan database.Event[*Instance], error) {
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, organizationID)
	if err != nil {
		return nil, err
	}

	var existing []*Instance
	if id == "" {
		instances, err := s.instances.Filter(models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(organizationID))).ListAll(ctx)
		if err != nil {
			return nil, err
		}
		for _, i := range instances {
			action := accesscontrol.NewActionGetWorkspaceKargoInstances(i.WorkspaceID.String, i.GetID())
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return nil, err
			}
			if !ok {
				continue
			}
			existing = append(existing, i)
		}
	} else {
		instance, err := s.instances.Filter(models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(organizationID))).GetByID(ctx, id)
		if err != nil {
			return nil, err
		}
		action := accesscontrol.NewActionGetWorkspaceKargoInstances(instance.WorkspaceID.String, id)
		ok, err := enforcer.EnforceAction(ctx, action)
		if err != nil {
			return nil, err
		}
		if !ok {
			return nil, shared.NewPermissionDeniedErr(action)
		}
		existing = []*Instance{instance}
	}

	workspaceEnabled := s.featSvc.GetFeatureStatuses(ctx, &organizationID).GetWorkspaces().Enabled()
	if err != nil {
		return nil, err
	}
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}
	if workspaceEnabled && workspaceID != "" {
		existing = s.filterInstanceByWorkspace(existing, workspaceID)
	}

	items := s.instancesWatcher.Subscribe(ctx, func(e events.InstanceEvent) bool {
		action := accesscontrol.NewActionGetWorkspaceKargoInstances(e.WorkspaceID, e.ID)
		ok, _ := enforcer.EnforceAction(ctx, action, enforcer.OrgOwnershipChecker(e.OrganizationOwner))
		return ok && (id == "" || id == e.ID)
	})
	res := make(chan database.Event[*Instance])
	go func() {
		for _, instance := range existing {
			res <- database.Event[*Instance]{Type: events.TypeAdded, Item: instance}
		}
		for e := range items {
			next := database.Event[*Instance]{Type: e.Type}
			if e.Type == events.TypeDeleted {
				next.Item = &Instance{
					KargoInstance: &models.KargoInstance{ID: e.ID},
					Config:        &models.KargoInstanceConfig{},
				}
			} else {
				instance, err := s.instances.GetByID(ctx, e.ID)
				if err != nil {
					continue
				}
				next.Item = instance
			}
			res <- next
		}
		close(res)
	}()
	return res, nil
}

func (s *KargoV1Server) WatchKargoInstances(req *kargov1.WatchKargoInstancesRequest, ws kargov1.KargoService_WatchKargoInstancesServer) error {
	ctx := ws.Context()
	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return err
	}

	instances, err := s.watchInstances(ctx, ptr.Deref(req.InstanceId, ""), req.GetWorkspaceId(), req.OrganizationId)
	if err != nil {
		return fmt.Errorf("failed to watch instances: %w", err)
	}
	for event := range instances {
		instance, err := s.newInstanceV1(event.Item, misc.IsKargoComponentVersionSupported(event.Item.Config.Version.String, s.versions, s.kargoUnstableVersion, true) != nil)
		if err != nil {
			return fmt.Errorf("failed to map instance: %w", err)
		}
		if err := ws.Send(&kargov1.WatchKargoInstancesResponse{
			Item: instance,
			Type: misc.MapDBEventTypeToGRPC(event.Type),
		}); err != nil {
			return err
		}
	}
	return nil
}
