package kargo

import (
	"context"

	"github.com/volatiletech/null/v8"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/internal/utils/types"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *KargoV1Server) ListKargoInstances(
	ctx context.Context,
	req *kargov1.ListKargoInstancesRequest,
) (*kargov1.ListKargoInstancesResponse, error) {
	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	instanceModels, err := s.instances.Filter(models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(req.GetOrganizationId()))).ListAll(ctx)
	if err != nil {
		return nil, err
	}

	filtered := []*Instance{}
	for _, i := range instanceModels {
		action := accesscontrol.NewActionGetWorkspaceKargoInstances(i.WorkspaceID.String, i.GetID())
		ok, err := enforcer.EnforceAction(ctx, action)
		if err != nil {
			return nil, err
		}
		if !ok {
			continue
		}
		filtered = append(filtered, i)
	}

	orgID := req.GetOrganizationId()
	workspaceEnabled := s.featSvc.GetFeatureStatuses(ctx, &orgID).GetWorkspaces().Enabled()
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}
	if workspaceEnabled && actor.Type == accesscontrol.ActorTypeUser && req.GetWorkspaceId() != "" {
		filtered = s.filterInstanceByWorkspace(filtered, req.GetWorkspaceId())
	}

	instances, err := types.MapSlice(filtered, func(i *Instance) (*kargov1.KargoInstance, error) {
		return s.newInstanceV1(i, misc.IsKargoComponentVersionSupported(i.Config.Version.String, s.versions, s.kargoUnstableVersion, true) != nil)
	})
	if err != nil {
		return nil, err
	}
	return &kargov1.ListKargoInstancesResponse{
		Instances: instances,
	}, nil
}
