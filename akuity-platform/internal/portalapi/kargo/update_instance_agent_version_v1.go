package kargo

import (
	"context"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/permissions"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/validator"
	"github.com/akuityio/akuity-platform/internal/version"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *KargoV1Server) UpdateInstanceAgentVersion(
	ctx context.Context,
	req *kargov1.UpdateInstanceAgentVersionRequest,
) (*kargov1.UpdateInstanceAgentVersionResponse, error) {
	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	actions := make([]permissions.Action, 0, len(req.GetAgentNames()))
	for _, agentName := range req.GetAgentNames() {
		actions = append(actions, accesscontrol.NewActionUpdateKargoInstanceAgents(
			req.GetWorkspaceId(),
			req.GetInstanceId(),
			agentName,
		))
	}
	for _, action := range actions {
		if _, _, err := shared.EnforceOrganizationActions(
			ctx, s.db, s.acs, req.GetOrganizationId(), action); err != nil {
			return nil, err
		}
	}

	if err := s.batchUpdateAgentVersion(ctx, req.GetInstanceId(), req.GetAgentNames(), req.GetNewVersion()); err != nil {
		return &kargov1.UpdateInstanceAgentVersionResponse{}, err
	}
	return &kargov1.UpdateInstanceAgentVersionResponse{}, nil
}

func (s *KargoV1Server) batchUpdateAgentVersion(
	ctx context.Context,
	instanceID string,
	agentNames []string,
	// empty to update to latest
	newVersion string,
) error {
	txDB, txBeginner := database.WithTxBeginner(s.db)
	repoSet := client.NewRepoSet(txDB)

	txCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := txBeginner.Begin(txCtx)
	if err != nil {
		return err
	}

	clusters, err := repoSet.KargoAgents(
		qm.Select("id", "name", "spec"),
		models.KargoAgentWhere.Name.IN(agentNames),
		models.KargoAgentWhere.InstanceID.EQ(instanceID)).ListAll(txCtx)
	if err != nil {
		return err
	}

	targetVersion := version.GetLatestAgentVersion()

	if newVersion != "" {
		if err := validator.ValidateTargetVersion(newVersion); err != nil {
			return err
		}

		targetVersion = newVersion
	}

	for _, cluster := range clusters {
		spec, err := cluster.GetSpec()
		if err != nil {
			return err
		}
		spec.TargetVersion = targetVersion

		if err := cluster.SetSpec(*spec); err != nil {
			return err
		}

		if err := repoSet.KargoAgents().Update(ctx, cluster, "spec"); err != nil {
			return err
		}
	}
	if err := tx.Commit(); err != nil {
		return err
	}
	return nil
}
