package kargo

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/url"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *KargoV1Server) GetInstanceAgentCommand(
	ctx context.Context,
	req *kargov1.GetInstanceAgentCommandRequest,
) (*kargov1.GetInstanceAgentCommandResponse, error) {
	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	organizationId := req.GetOrganizationId()
	instanceId := req.GetInstanceId()
	agentID := req.GetId()
	locationOrigin := req.GetLocationOrigin()
	commandType := req.GetType()

	agent, err := s.repoSet.KargoAgents(
		models.KargoAgentNoStatusManifestMod,
		models.KargoAgentWhere.InstanceID.EQ(instanceId),
	).GetByID(ctx, agentID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, shared.ErrNotFound
		}
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, organizationId,
		accesscontrol.NewActionGetKargoInstanceAgents(
			req.GetWorkspaceId(),
			instanceId,
			agent.Name,
		),
	); err != nil {
		return nil, err
	}

	token, err := shared.GetToken(ctx)
	if err != nil {
		return nil, err
	}

	apiURL := fmt.Sprintf("%s/api/v1/orgs/%s/kargo/instances/%s/agents/%s/manifests",
		locationOrigin, organizationId, instanceId, agentID)

	query := url.Values{}
	if req.GetSkipNamespace() {
		query.Add("skipNamespace", fmt.Sprintf("%v", req.GetSkipNamespace()))
	}
	finalQuery := query.Encode()
	if finalQuery != "" {
		apiURL = fmt.Sprintf("%s?%s", apiURL, finalQuery)
	}

	command := fmt.Sprintf("TOKEN=%q && curl -s -H \"Authorization: Bearer $TOKEN\" %q | kubectl %s -f -", token, apiURL, commandType)

	return &kargov1.GetInstanceAgentCommandResponse{Command: command}, nil
}
