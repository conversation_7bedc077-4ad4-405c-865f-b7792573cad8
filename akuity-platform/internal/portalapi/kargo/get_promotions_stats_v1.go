package kargo

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *KargoV1Server) GetPromotionStats(ctx context.Context, req *kargov1.GetPromotionStatsRequest) (*kargov1.GetPromotionStatsResponse, error) {
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	filters := req.GetFilter()
	if filters == nil {
		filters = &kargov1.PromotionFilter{}
	}

	instanceIds := filters.GetInstanceId()
	if len(instanceIds) > 0 {
		for _, id := range instanceIds {
			action := accesscontrol.NewActionGetWorkspaceKargoInstances(req.GetWorkspaceId(), id)
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return nil, err
			}
			if !ok {
				return nil, shared.NewPermissionDeniedErr(action)
			}
		}
	} else if len(filters.GetInstanceNames()) > 0 {
		for _, name := range filters.GetInstanceNames() {
			inst, err := s.repoSet.KargoInstances().Filter(models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(req.GetOrganizationId())), models.KargoInstanceWhere.Name.EQ(name)).One(ctx)
			if err != nil {
				return nil, err
			}
			action := accesscontrol.NewActionGetWorkspaceKargoInstances(inst.WorkspaceID.String, inst.ID)
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return nil, err
			}
			if !ok {
				return nil, shared.NewPermissionDeniedErr(action)
			}
		}
	} else {
		action := accesscontrol.NewActionGetOrganizationAllKargoInstances()
		ok, err := enforcer.EnforceAction(ctx, action)
		if err != nil {
			return nil, err
		}
		if !ok {
			return nil, shared.NewPermissionDeniedErr(action)
		}
	}

	groupByInterval := shared.MapGroupByInterval(req.GetInterval())

	if err := shared.ValidateInterval(filters.GetStartTime(), filters.GetEndTime(), groupByInterval); err != nil {
		return nil, err
	}

	mods := GetPromotionFilters(filters, req.GetOrganizationId())
	timeMods, err := GetPromoTimeFilter("promo", filters)
	if err != nil {
		return nil, err
	}

	mods = append(mods, timeMods...)

	groupByField := GetGroupByField(req.GetGroupByField())

	stats, err := s.getPromotionStats(ctx, mods, groupByInterval, groupByField)
	if err != nil {
		return nil, err
	}

	return &kargov1.GetPromotionStatsResponse{
		PromotionStats: MapStatsToResponse(stats),
	}, nil
}

func MapStatsToResponse(stats []PromotionStats) []*kargov1.PromotionStat {
	var statsResp []*kargov1.PromotionStat

	statsResp = make([]*kargov1.PromotionStat, 0)

	for _, stat := range stats {
		statsResp = append(statsResp, &kargov1.PromotionStat{
			IntervalStart: stat.IntervalStart.String(),
			CountMap:      stat.CountMap,
			AverageMap:    stat.AverageMap,
		})
	}

	return statsResp
}

func GetGroupByField(field kargov1.PromotionGroupField) string {
	switch field {
	case kargov1.PromotionGroupField_PROMOTION_GROUP_FIELD_STAGES:
		return "promo.stage_name"
	case kargov1.PromotionGroupField_PROMOTION_GROUP_FIELD_PROJECTS:
		return "promo.project_name"
	case kargov1.PromotionGroupField_PROMOTION_GROUP_FIELD_INSTANCE_NAMES:
		return "instance.name"
	case kargov1.PromotionGroupField_PROMOTION_GROUP_FIELD_INITIATORS:
		return "promo.details -> 'initiatedBy' ->> 'username'"
	default:
		return "promo.result_phase"
	}
}

type PromotionStats struct {
	IntervalStart time.Time          `json:"intervalStart"`
	CountMap      map[string]uint32  `json:"countMap"`
	AverageMap    map[string]float32 `json:"averageMap"`
}

type syncResultRow struct {
	Timestamp time.Time   `boil:"timestamp"`
	Count     uint32      `boil:"count"`
	Item      null.String `boil:"result_phase"`
	Average   float32     `boil:"avg"`
	Rank      uint32      `boil:"rank"`
}

func (s *KargoV1Server) getPromotionStats(ctx context.Context, mods []qm.QueryMod, interval instances.GroupByInterval, groupByField string) ([]PromotionStats, error) {
	mods = append(mods, qm.Select(fmt.Sprintf("date_trunc('%s', promo.end_time) as timestamp", interval), "extract(epoch from avg(promo.end_time - promo.start_time)) as avg", fmt.Sprintf("%v as field", groupByField), "count(*) as count"),
		qm.From("kargo_promotions as promo"),
		qm.GroupBy(fmt.Sprintf("timestamp, %v", groupByField)),
		qm.OrderBy("timestamp"))
	query, args := queries.BuildQuery(models.NewQuery(mods...))
	query, _ = queries.BuildQuery(models.NewQuery(qm.From(fmt.Sprintf("(%v) as res1", query)), qm.Select("res1.*", "rank() over ( partition by timestamp order by count desc)")))
	query = strings.ReplaceAll(query, ";", "")
	query, _ = queries.BuildQuery(models.NewQuery(qm.From(fmt.Sprintf("(%v) as res", query)), qm.Select("*"), qm.Where("res.rank<=5")))

	rows, err := queries.Raw(query, args...).QueryContext(ctx, s.db)
	if err != nil {
		return nil, err
	}
	defer func(rows *sql.Rows) { _ = rows.Close() }(rows)

	var stats []PromotionStats
	for rows.Next() {
		row := syncResultRow{}
		if err := rows.Scan(&row.Timestamp, &row.Average, &row.Item, &row.Count, &row.Rank); err != nil {
			return nil, err
		}
		if len(stats) == 0 || stats[len(stats)-1].IntervalStart != row.Timestamp {
			stats = append(stats, PromotionStats{IntervalStart: row.Timestamp, CountMap: map[string]uint32{}, AverageMap: map[string]float32{}})
		}
		stats[len(stats)-1].CountMap[row.Item.String] = row.Count
		stats[len(stats)-1].AverageMap[row.Item.String] = row.Average
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return stats, nil
}
