package kargo

import (
	"context"
	"fmt"
	"slices"
	"strings"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"k8s.io/utils/ptr"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/kargo"
	"github.com/akuityio/akuity-platform/internal/services/permissions"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	argocdutil "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/events"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
	healthv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/status/health/v1"
)

func getAgentFilterPredicate(filter *kargov1.KargoAgentFilter, minAgentName, maxAgentName string) func(*models.KargoAgent) bool {
	if filter == nil {
		return func(agent *models.KargoAgent) bool {
			return true
		}
	}
	return func(agent *models.KargoAgent) bool {
		if minAgentName != "" && agent.Name <= minAgentName {
			return false
		}
		if maxAgentName != "" && agent.Name >= maxAgentName {
			return false
		}

		if filter.GetNameLike() != "" && !strings.Contains(agent.Name, filter.GetNameLike()) {
			return false
		}
		agentStatus, err := agent.GetStatus()
		if err != nil {
			return false
		}
		agentState := agentStatus.GetKargoAgentState()
		if len(filter.GetAgentStatus()) > 0 {
			if agentState == nil {
				return false
			}
			phase := argocdutil.MapTenantPhaseToRpcPhase(agentState.Status.PriorityStatus)
			if !slices.Contains(filter.GetAgentStatus(), healthv1.StatusCode(phase)) {
				return false
			}
		}

		if len(filter.GetAgentVersion()) > 0 {
			if agentState == nil {
				return false
			}
			if !slices.Contains(filter.GetAgentVersion(), agentState.Version) {
				return false
			}
		}

		if len(filter.GetKargoVersion()) > 0 {
			if agentState == nil {
				return false
			}
			if !slices.Contains(filter.GetKargoVersion(), agentState.KargoVersion) {
				return false
			}
		}

		if excludeAgentVersion := filter.GetExcludeAgentVersion(); excludeAgentVersion != "" {
			if agentState != nil && agentState.Version == fmt.Sprint("v", excludeAgentVersion) {
				return false
			}
		}

		if filter.OutdatedManifest != nil {
			isOutdated := agentState == nil || agentState.Status == nil || int64(agent.Generation) != agentState.Status.MinObservedGeneration
			if *filter.OutdatedManifest != isOutdated {
				return false
			}
		}

		if filter.SelfManaged != nil {
			if *filter.SelfManaged && agent.RemoteArgocdInstanceID.IsZero() {
				return true
			}
		}

		if len(filter.GetRemoteArgocdIds()) > 0 {
			if agent.RemoteArgocdInstanceID.IsZero() {
				return false
			}
			if !slices.Contains(filter.GetRemoteArgocdIds(), agent.RemoteArgocdInstanceID.String) {
				return false
			}
		}

		if len(filter.GetNamespace()) > 0 {
			if !slices.Contains(filter.GetNamespace(), agent.Namespace) {
				return false
			}
		}

		if len(filter.GetLabels()) > 0 {
			filterLabels := filter.GetLabels()
			agentSpec, err := agent.GetSpec()
			if err != nil {
				return false
			}
			for key, value := range filterLabels {
				agentLabelValue, exists := agentSpec.Labels[key]
				if !exists || agentLabelValue != value {
					return false
				}
			}
		}
		return true
	}
}

func (s *KargoV1Server) watchAgents(
	ctx context.Context,
	id, instanceID string,
	mods []qm.QueryMod,
	predicate func(e events.ClusterEvent) bool,
	filterPredicate func(*models.KargoAgent) bool,
) (<-chan database.Event[*models.KargoAgent], error) {
	if id != "" {
		mods = append(mods, models.KargoAgentWhere.ID.EQ(id))
	}
	mods = append(mods, models.KargoAgentWhere.InstanceID.EQ(instanceID), models.KargoAgentNoStatusManifestMod)
	existing, err := s.repoSet.KargoAgents().Filter(mods...).ListAll(ctx)
	if err != nil {
		return nil, err
	}
	items := s.agentsWatcher.Subscribe(ctx, predicate)
	res := make(chan database.Event[*models.KargoAgent])
	go func() {
		watchedIds := map[string]bool{}
		for _, agent := range existing {
			res <- database.Event[*models.KargoAgent]{Type: events.TypeAdded, Item: agent}
			watchedIds[agent.ID] = true
		}
		for e := range items {
			next := database.Event[*models.KargoAgent]{Type: e.Type}
			if e.Type == events.TypeDeleted {
				next.Item = &models.KargoAgent{ID: e.ID}
			} else {
				agent, err := s.repoSet.KargoAgents(models.KargoAgentNoStatusManifestMod).GetByID(ctx, e.ID)
				if err != nil {
					continue
				}
				next.Item = agent
			}
			if ok := filterPredicate(next.Item); ok {
				watchedIds[next.Item.ID] = true
				res <- next
			} else if _, ok := watchedIds[next.Item.ID]; ok && e.Type != events.TypeDeleted {
				delete(watchedIds, next.Item.ID)
				res <- database.Event[*models.KargoAgent]{Type: events.TypeDeleted, Item: next.Item}
			}
		}
		close(res)
	}()
	return res, nil
}

func (s *KargoV1Server) WatchKargoInstanceAgents(req *kargov1.WatchKargoInstanceAgentsRequest, ws kargov1.KargoService_WatchKargoInstanceAgentsServer) error {
	ctx := ws.Context()
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return err
	}
	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return err
	}

	action := accesscontrol.NewActionGetKargoInstanceAgents(
		req.GetWorkspaceId(),
		req.GetInstanceId(),
		accesscontrol.ResourceAny,
	)

	// Check if that user owns requested instance
	ok, err := enforcer.CheckResourceOwnership(ctx,
		permissions.ObjectWorkspaceKargoInstances,
		accesscontrol.FormatWorkspaceResource(req.GetWorkspaceId(), req.GetInstanceId()),
	)
	if err != nil {
		return err
	}
	if !ok {
		return shared.NewPermissionDeniedErr(action)
	}

	workspaceID := req.GetWorkspaceId()
	if workspaceID == "" {
		inst, err := s.instances.GetByID(ctx, req.GetInstanceId())
		if err != nil {
			return err
		}
		workspaceID = inst.WorkspaceID.String
	}

	resources, err := enforcer.GetResources(action.Object, action.Verb)
	if err != nil {
		return err
	}
	resources, err = shared.FilterResources(req.GetInstanceId(), workspaceID, resources...)
	if err != nil {
		return err
	}
	if len(resources) == 0 {
		return shared.NewPermissionDeniedErr(action)
	}

	mods, err := getAgentFilterMods(req.GetFilter())
	if err != nil {
		return fmt.Errorf("failed to get agent filter mods: %w", err)
	}

	policyMods, err := kargo.AgentsWhereNameInMod(req.GetInstanceId(), resources...)
	if err != nil {
		return err
	}
	mods = append(mods, policyMods...)

	if req.GetMinAgentName() != "" {
		mods = append(mods, models.KargoAgentWhere.Name.GTE(req.GetMinAgentName()))
	}
	if req.GetMaxAgentName() != "" {
		mods = append(mods, models.KargoAgentWhere.Name.LTE(req.GetMaxAgentName()))
	}
	filterPredicate := getAgentFilterPredicate(req.GetFilter(), req.GetMinAgentName(), req.GetMaxAgentName())

	agentID := ptr.Deref(req.AgentId, "")

	predicate := func(e events.ClusterEvent) bool {
		return (agentID == "" || agentID == e.ID) && req.GetInstanceId() == e.InstanceID
	}

	agents, err := s.watchAgents(ctx, agentID, req.InstanceId, mods, predicate, filterPredicate)
	if err != nil {
		return fmt.Errorf("failed to watch agents: %w", err)
	}
	for event := range agents {
		agent, err := NewKargoAgentV1(event.Item, s.cfg.ClusterProgressingDeadline)
		if err != nil {
			return fmt.Errorf("failed to map agent: %w", err)
		}

		if err := ws.Send(&kargov1.WatchKargoInstanceAgentsResponse{
			Item: agent,
			Type: argocdutil.MapDBEventTypeToGRPC(event.Type),
		}); err != nil {
			return err
		}
	}
	return nil
}
