package kargo

import (
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"google.golang.org/genproto/googleapis/api/httpbody"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	utilErrors "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/models/models"
	"github.com/akuityio/akuity-platform/models/util/encryption"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *KargoV1Server) GetKargoInstanceAgentManifests(
	req *kargov1.GetKargoInstanceAgentManifestsRequest,
	ws kargov1.KargoService_GetKargoInstanceAgentManifestsServer,
) error {
	ctx := ws.Context()

	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return err
	}

	agent, err := s.repoSet.KargoAgents(models.KargoAgentNoStatusManifestMod).Filter(
		models.KargoAgentWhere.InstanceID.EQ(req.GetInstanceId()),
		models.KargoAgentWhere.ID.EQ(req.GetId()),
	).One(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return shared.ErrNotFound
		}
		return err
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetKargoInstanceAgents(
			req.GetWorkspaceId(),
			req.GetInstanceId(),
			agent.Name,
		),
	); err != nil {
		return err
	}

	// if akuity managed do not expose manifest
	spec, err := agent.GetSpec()
	if err != nil {
		return err
	}
	if spec.AkuityManaged {
		return utilErrors.NewAPIStatus(http.StatusBadRequest, "akuity managed agents do not have manifests")
	}

	if err := grpc.SendHeader(ctx, metadata.New(map[string]string{
		"Content-Disposition": fmt.Sprintf("attachment; filename=akuity-manifests-%s.yaml", agent.Name),
	})); err != nil {
		return err
	}

	const chunkSize = 4096 * 16
	cursor := 1
	accumulated := ""
	firstSend := true
	decoder, err := encryption.NewCTRDecoder()
	if err != nil {
		return err
	}
	for {
		query := fmt.Sprintf("SELECT SUBSTRING(COALESCE(%s, '') FROM %d FOR %d) FROM kargo_agent WHERE %s=$1 AND %s=$2", models.KargoAgentColumns.StatusManifests, cursor, chunkSize, models.KargoAgentColumns.ID, models.KargoAgentColumns.InstanceID)
		row := s.db.QueryRowContext(ctx, query, req.GetId(), req.GetInstanceId())
		var chunk string
		if err := row.Scan(&chunk); err != nil {
			return err
		}
		if chunk == "" {
			break
		}
		decrypted, err := decoder.Next(chunk)
		if err != nil {
			if !errors.Is(err, encryption.ErrNotEncoded) {
				return err
			}
			// backward compatibily for not encrypted status manifest
			decrypted = chunk
		}
		accumulated += decrypted
		lastNewlineIndex := strings.LastIndex(accumulated, "\n")
		if lastNewlineIndex != -1 {
			sendChunk := accumulated[:lastNewlineIndex]
			if req.GetSkipNamespace() && firstSend {
				// we expect the namespace resource to be in the first chunk that is being sent
				sendChunk = shared.RemoveNamespaceFromChunked(sendChunk)
			}
			if err := ws.Send(&httpbody.HttpBody{
				Data:        []byte(sendChunk),
				ContentType: "application/yaml",
			}); err != nil {
				return err
			}
			firstSend = false
			accumulated = accumulated[lastNewlineIndex+1:]
		}
		cursor += chunkSize
	}

	if accumulated != "" {
		if err := ws.Send(&httpbody.HttpBody{
			Data:        []byte(accumulated),
			ContentType: "application/yaml",
		}); err != nil {
			return err
		}
	}

	if cursor == 1 {
		return shared.ErrUnavailable
	}

	return nil
}
