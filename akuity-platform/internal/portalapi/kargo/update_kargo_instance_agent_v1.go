package kargo

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"net/url"
	"strings"

	"github.com/volatiletech/null/v8"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/validator"
	"github.com/akuityio/akuity-platform/internal/version"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *KargoV1Server) updateAgent(ctx context.Context, id, instanceID string, callback func(agent *models.KargoAgent) error) (*models.KargoAgent, error) {
	existing, err := s.repoSet.KargoAgents(
		models.KargoAgentNoStatusManifestMod,
		models.KargoAgentWhere.ID.EQ(id),
		models.KargoAgentWhere.InstanceID.EQ(instanceID)).One(ctx)
	if err != nil {
		return nil, err
	}
	updated, err := s.repoSet.KargoAgents(
		models.KargoAgentNoStatusManifestMod,
		models.KargoAgentWhere.ID.EQ(id),
		models.KargoAgentWhere.InstanceID.EQ(instanceID)).One(ctx)
	if err != nil {
		return nil, err
	}
	existingSpec, err := existing.GetSpec()
	if err != nil {
		return nil, err
	}
	if err := callback(updated); err != nil {
		return nil, err
	}
	updatedSpec, err := updated.GetSpec()
	if err != nil {
		return nil, err
	}
	if err := validator.ValidateResourceNamespace(updated.Namespace, 3, 63); err != nil {
		return nil, err
	}
	if err := validator.ValidateResourceNamespace(updatedSpec.ArgocdNamespace, 3, 63); err != nil {
		return nil, err
	}
	if err := validator.ValidateTargetVersion(updatedSpec.TargetVersion); err != nil {
		return nil, err
	}
	if err := validator.ValidateResourceDescription(updated.Description.String, 255); err != nil {
		return nil, err
	}
	if err := validator.ValidateLabelsAndAnnotations(updatedSpec.Labels, true); err != nil {
		return nil, err
	}
	if err := validator.ValidateLabelsAndAnnotations(updatedSpec.Annotations, false); err != nil {
		return nil, err
	}

	// set unmodifiable fields
	updated.RemoteArgocdInstanceID = existing.RemoteArgocdInstanceID
	updatedSpec.AkuityManaged = existingSpec.AkuityManaged
	updatedSpec.SizeVersion = existingSpec.SizeVersion
	updatedSpec.AgentRotationCount = existingSpec.AgentRotationCount

	// if remote argocd is set or akuity managed, unset argocd ns
	if updatedSpec.AkuityManaged || updated.RemoteArgocdInstanceID.String != "" {
		updatedSpec.ArgocdNamespace = ""
	}

	// if akuity managed, reset kustomization
	if updatedSpec.AkuityManaged && len(updatedSpec.Kustomization) != 0 {
		return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "kustomization is not allowed for akuity managed kargo agents")
	}

	if updatedSpec.SelfManagedArgocdURL != "" {
		u, err := url.Parse(updatedSpec.SelfManagedArgocdURL)
		if err != nil || u.Scheme == "" || u.Host == "" {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, "invalid selfManagedArgocdUrl")
		}
		updatedSpec.SelfManagedArgocdURL = strings.TrimSuffix(updatedSpec.SelfManagedArgocdURL, "/")
	}

	if err := updated.SetSpec(*updatedSpec); err != nil {
		return nil, err
	}

	if existingSpec.Equals(*updatedSpec) && existing.AutoUpgradeDisabled == updated.AutoUpgradeDisabled && existing.Description == updated.Description && existing.Namespace == updated.Namespace {
		return existing, nil
	}

	if err := s.repoSet.KargoAgents().Update(ctx, updated, models.KargoAgentColumns.Spec, models.KargoAgentColumns.AutoUpgradeDisabled, models.KargoAgentColumns.Description, models.KargoAgentColumns.Namespace); err != nil {
		return nil, err
	}
	return updated, nil
}

func (s *KargoV1Server) UpdateKargoInstanceAgent(
	ctx context.Context,
	req *kargov1.UpdateKargoInstanceAgentRequest,
) (*kargov1.UpdateKargoInstanceAgentResponse, error) {
	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	agent, err := s.repoSet.KargoAgents(
		models.KargoAgentNoStatusManifestMod,
		models.KargoAgentWhere.ID.EQ(req.GetId()),
		models.KargoAgentWhere.InstanceID.EQ(req.GetInstanceId()),
	).One(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, shared.ErrNotFound
		}
		return nil, err
	}
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateKargoInstanceAgents(
			req.GetWorkspaceId(),
			req.GetInstanceId(),
			agent.Name,
		),
	); err != nil {
		return nil, err
	}

	data := req.GetData()
	if data == nil {
		return nil, status.Error(codes.InvalidArgument, "missing required field `data`")
	}

	targetVersion := req.GetData().GetTargetVersion()
	if targetVersion == "" {
		targetVersion = version.GetLatestAgentVersion()
	}

	res, err := s.updateAgent(ctx, req.GetId(), req.GetInstanceId(), func(item *models.KargoAgent) error {
		item.Namespace = data.GetNamespace()
		item.Description = null.StringFrom(req.Description)
		argocdNs := data.GetArgocdNamespace()
		selfManagedArgocdUrl := data.GetSelfManagedArgocdUrl()

		if len(argocdNs) == 0 {
			argocdNs = instances.DefaultArgoCDNamespace
		}
		item.AutoUpgradeDisabled = data.GetAutoUpgradeDisabled()
		return item.SetSpec(models.KargoAgentSpec{
			Size:                 s.newAgentSize(data.GetSize()),
			Labels:               data.GetLabels(),
			Annotations:          data.GetAnnotations(),
			Kustomization:        data.Kustomization.AsMap(),
			TargetVersion:        targetVersion,
			ArgocdNamespace:      argocdNs,
			SelfManagedArgocdURL: selfManagedArgocdUrl,
		})
	})
	if err != nil {
		return nil, err
	}

	updatedAgent, err := NewKargoAgentV1(res, s.cfg.ClusterProgressingDeadline)
	if err != nil {
		return nil, err
	}
	return &kargov1.UpdateKargoInstanceAgentResponse{Agent: updatedAgent}, nil
}
