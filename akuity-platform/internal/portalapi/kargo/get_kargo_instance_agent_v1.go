package kargo

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *KargoV1Server) GetKargoInstanceAgent(
	ctx context.Context,
	req *kargov1.GetKargoInstanceAgentRequest,
) (*kargov1.GetKargoInstanceAgentResponse, error) {
	if err := s.kargoEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	existing, err := s.repoSet.KargoAgents(
		models.KargoAgentNoStatusManifestMod,
		models.KargoAgentWhere.ID.EQ(req.GetId()),
		models.KargoAgentWhere.InstanceID.EQ(req.GetInstanceId())).One(ctx)
	if err != nil {
		if instances.IsNotFoundErr(err) {
			return nil, shared.ErrNotFound
		}
		return nil, err
	}
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetKargoInstanceAgents(
			req.GetWorkspaceId(),
			req.GetInstanceId(),
			existing.Name,
		),
	); err != nil {
		return nil, err
	}

	agent, err := NewKargoAgentV1(existing, s.cfg.ClusterProgressingDeadline*5)
	if err != nil {
		return nil, err
	}
	return &kargov1.GetKargoInstanceAgentResponse{
		Agent: agent,
	}, nil
}
