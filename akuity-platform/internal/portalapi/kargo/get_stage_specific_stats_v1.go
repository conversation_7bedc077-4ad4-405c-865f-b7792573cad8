package kargo

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

func (s *KargoV1Server) GetStageSpecificStats(ctx context.Context, req *kargov1.GetStageSpecificStatsRequest) (*kargov1.GetStageSpecificStatsResponse, error) {
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	filters := req.GetFilter()
	if filters == nil {
		filters = &kargov1.PromotionFilter{}
	}

	instanceIds := filters.GetInstanceId()
	if len(instanceIds) > 0 {
		for _, id := range instanceIds {
			action := accesscontrol.NewActionGetWorkspaceKargoInstances(req.GetWorkspaceId(), id)
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return nil, err
			}
			if !ok {
				return nil, shared.NewPermissionDeniedErr(action)
			}
		}
	} else if len(filters.GetInstanceNames()) > 0 {
		for _, name := range filters.GetInstanceNames() {
			inst, err := s.repoSet.KargoInstances().Filter(models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(req.GetOrganizationId())), models.KargoInstanceWhere.Name.EQ(name)).One(ctx)
			if err != nil {
				return nil, err
			}
			action := accesscontrol.NewActionGetWorkspaceKargoInstances(inst.WorkspaceID.String, inst.ID)
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return nil, err
			}
			if !ok {
				return nil, shared.NewPermissionDeniedErr(action)
			}
		}
	} else {
		action := accesscontrol.NewActionGetOrganizationAllKargoInstances()
		ok, err := enforcer.EnforceAction(ctx, action)
		if err != nil {
			return nil, err
		}
		if !ok {
			return nil, shared.NewPermissionDeniedErr(action)
		}
	}

	mods := GetPromotionFilters(filters, req.GetOrganizationId())
	timeMods, err := GetPromoTimeFilter("promo", filters)
	if err != nil {
		return nil, err
	}
	mods = append(mods, timeMods...)

	leadStats, err := s.getLeadTimeDataStats(ctx, mods)
	if err != nil {
		return nil, err
	}

	// recovery data cannot use initiated by filters
	filters.InitiatedBy = nil

	mods = GetPromotionFilters(filters, req.GetOrganizationId())
	timeMods, err = GetPromoTimeFilter("p", filters)
	if err != nil {
		return nil, err
	}

	recoveryStats, err := s.getRecoveryDataStats(ctx, mods, timeMods[0], timeMods[1])
	if err != nil {
		return nil, err
	}

	return &kargov1.GetStageSpecificStatsResponse{
		LeadTimeData:     leadStats,
		RecoveryTimeData: recoveryStats,
	}, nil
}

type RecoveryTimeData struct {
	EndTime     time.Time `json:"endTime"`
	ResultPhase string    `json:"resultPhase"`
	StageName   string    `boil:"stage_name"`
}

type recoveryDataRow struct {
	EndTime     time.Time `boil:"end_time"`
	ResultPhase string    `boil:"result_phase"`
	StageName   string    `boil:"stage_name"`
}

type leadTimeDataRow struct {
	EndTime             time.Time `boil:"end_time"`
	FreightCreationTime time.Time `boil:"freight_creation_time"`
	LeadTime            float32   `boil:"lead_time"`
	StageName           string    `boil:"stage_name"`
}

func (s *KargoV1Server) getRecoveryDataStats(ctx context.Context, mods []qm.QueryMod, startTime, endTime qm.QueryMod) ([]*kargov1.RecoveryTimeData, error) {
	mods = append(mods, qm.Select("instance_id", "project_name", "stage_name", "end_time", "result_phase", "LAG(result_phase) OVER ( PARTITION BY project_name, stage_name, instance_id ORDER BY end_time ) AS prev_result_phase"),
		qm.From("kargo_promotions as promo"))
	query, args := queries.BuildQuery(models.NewQuery(mods...))
	query = strings.ReplaceAll(query, ";", "")
	query, _ = queries.BuildQuery(models.NewQuery(qm.From(fmt.Sprintf("(%v) as p", query)), qm.Select("p.result_phase", "concat_ws('/', p.instance_id, p.project_name, p.stage_name) as stage_name", "p.end_time"), qm.OrderBy("p.end_time"), qm.Where("(( p.result_phase != 'Succeeded' AND ( p.prev_result_phase IS NULL OR p.prev_result_phase = 'Succeeded' ) ) OR ( p.result_phase = 'Succeeded' AND ( p.prev_result_phase IS NOT NULL AND p.prev_result_phase != 'Succeeded' ) ) )"), startTime, endTime))

	rows, err := queries.Raw(query, args...).QueryContext(ctx, s.db)
	if err != nil {
		return nil, err
	}
	defer func(rows *sql.Rows) { _ = rows.Close() }(rows)

	var stats []*kargov1.RecoveryTimeData
	for rows.Next() {
		row := recoveryDataRow{}
		if err := rows.Scan(&row.ResultPhase, &row.StageName, &row.EndTime); err != nil {
			return nil, err
		}
		stats = append(stats, &kargov1.RecoveryTimeData{PhaseChangeTime: row.EndTime.String(), Phase: row.ResultPhase, StageName: row.StageName})
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	finalStats := []*kargov1.RecoveryTimeData{}
	stageMap := map[string]bool{}
	for _, stat := range stats {
		// if the first entry for the stage is a success, ignore it
		if stat.Phase == "Succeeded" && !stageMap[stat.StageName] {
			stageMap[stat.StageName] = true
			continue
		}
		stageMap[stat.StageName] = true
		finalStats = append(finalStats, stat)
	}

	return finalStats, nil
}

func (s *KargoV1Server) getLeadTimeDataStats(ctx context.Context, mods []qm.QueryMod) ([]*kargov1.LeadTimeData, error) {
	mods = append(mods, qm.Select("promo.end_time", "stage_name", "(promo.details ->'freightDetails'->>'freightCreationTime'::text)::timestamptz as freight_creation_time", "extract(epoch from (promo.end_time - (promo.details ->'freightDetails'->>'freightCreationTime'::text)::timestamptz)) as lead_time"),
		qm.From("kargo_promotions as promo"), qm.OrderBy("promo.end_time asc"))
	query, args := queries.BuildQuery(models.NewQuery(mods...))
	rows, err := queries.Raw(query, args...).QueryContext(ctx, s.db)
	if err != nil {
		return nil, err
	}
	defer func(rows *sql.Rows) { _ = rows.Close() }(rows)

	var stats []*kargov1.LeadTimeData
	for rows.Next() {
		row := leadTimeDataRow{}
		if err := rows.Scan(&row.EndTime, &row.StageName, &row.FreightCreationTime, &row.LeadTime); err != nil {
			return nil, err
		}
		stats = append(stats, &kargov1.LeadTimeData{PromotionEndTime: row.EndTime.String(), FreightCreationTime: row.FreightCreationTime.String(), LeadTime: row.LeadTime, StageName: row.StageName})
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return stats, nil
}
