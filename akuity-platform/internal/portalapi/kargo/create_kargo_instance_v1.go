package kargo

import (
	"context"
	"net/http"
	"time"

	"github.com/volatiletech/null/v8"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/internal/utils/validator"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

const (
	MaxInstanceNameLength     = 50
	MinInstanceNameLength     = 3
	InstanceHardLimitError    = "Sorry, due to the high demand, we have run out of capacity for our free trial tier. <NAME_EMAIL> if you'd like to be notified as soon as we have more room or have an urgent ask."
	OrgInstanceLimitError     = "organization has reached the maximum number of allowed kargo instances"
	OrgPlanExpiredError       = "organization plan has expired"
	LicenseInstanceLimitError = "product has reached the maximum number of allowed kargo instances as per license"

	DefaultMaxRetainedFreight      = 20
	DefaultMaxRetainedPromotions   = 20
	DefaultMinFreightDeletionAge   = 336 * time.Hour // 14 days
	DefaultMinPromotionDeletionAge = 336 * time.Hour // 14 days
)

func (s *KargoV1Server) CreateKargoInstance(
	ctx context.Context,
	req *kargov1.CreateKargoInstanceRequest,
) (*kargov1.CreateKargoInstanceResponse, error) {
	organizationID := req.GetOrganizationId()
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.db, s.acs, organizationID)
	if err != nil {
		return nil, err
	}

	var workspaceID null.String
	teamSvc := teams.NewService(s.db)
	workspaceSvc := workspaces.NewService(s.db, teamSvc, s.cfg.FeatureGatesSource)
	if req.GetWorkspaceId() == "" {
		// if the workspace id is not provided, for backward compatibility, we will use the default workspace
		defaultWorkspace, err := workspaceSvc.GetDefaultWorkspace(ctx, organizationID)
		if err != nil {
			return nil, errorsutil.NewAPIStatus(http.StatusInternalServerError, err.Error())
		}
		workspaceID = null.StringFrom(defaultWorkspace.ID)
	} else {
		workspace, err := workspaceSvc.GetWorkspace(ctx, req.GetWorkspaceId())
		if err != nil {
			return nil, err
		}
		workspaceID = null.StringFrom(workspace.ID)
	}
	action := accesscontrol.NewActionCreateWorkspaceKargoInstances(workspaceID.String)
	ok, err := enforcer.EnforceAction(ctx, action)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, status.Errorf(codes.PermissionDenied, "no permission to %s on %q %s",
			action.Verb, action.Object, req.GetName())
	}

	txDB, txBeginner := database.WithTxBeginner(s.db)
	repoSet := client.NewRepoSet(txDB)

	if err := s.kargoEnabled(ctx, organizationID); err != nil {
		return nil, err
	}

	logger := logging.Extract(ctx)

	instance := &models.KargoInstance{
		OrganizationOwner: null.StringFrom(organizationID),
		Name:              req.GetName(),
		Description:       null.StringFrom(req.GetDescription()),
		WorkspaceID:       workspaceID,
	}

	if config.IsSelfHosted {
		licenseData := config.GetLicense()
		count, err := s.repoSet.KargoInstances().Count(ctx)
		if err != nil {
			return nil, err
		}
		if licenseData.KargoInstances > 0 && licenseData.KargoInstances < uint64(count+1) {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, LicenseInstanceLimitError)
		}
	} else {
		// if no hardlimit set skip check
		if s.cfg.UsageLimits.TotalKargoInstancesCount != 0 {
			count, err := repoSet.KargoInstances().Count(ctx)
			if err != nil {
				return nil, err
			}
			if s.cfg.UsageLimits.TotalKargoInstancesCount <= count {
				logger.Info("instance hardlimit hit!", "org_id", organizationID)
				return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, InstanceHardLimitError)
			}
		}

		res, err := repoSet.KargoInstances().Filter(models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(organizationID))).Count(ctx)
		if err != nil {
			return nil, err
		}

		quota, err := s.featSvc.GetOrgQuotas(ctx, organizationID)
		if err != nil {
			return nil, err
		}
		maxInstances := quota.MaxKargoInstances
		if maxInstances > 0 && maxInstances < res+1 {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, OrgInstanceLimitError)
		}

		org, err := repoSet.Organizations().GetByID(ctx, organizationID)
		if err != nil {
			return nil, err
		}
		orgStatus, err := org.GetOrgStatus()
		if err != nil {
			return nil, err
		}
		// TODO: currently trial accounts are not monitored for expiry
		if orgStatus != nil && !orgStatus.Trial && orgStatus.ExpiryTime > 0 &&
			time.Now().After(time.Unix(orgStatus.ExpiryTime, 0).Add(s.cfg.GracePeriodDuration)) {
			return nil, errorsutil.NewAPIStatus(http.StatusBadRequest, OrgPlanExpiredError)
		}
	}

	version := req.GetVersion()
	if err := misc.IsKargoComponentVersionSupported(version, s.versions, s.kargoUnstableVersion, s.featSvc.GetFeatureStatuses(ctx, &organizationID).GetKargoEnterprise().Enabled()); err != nil {
		return nil, err
	}

	txCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	tx, err := txBeginner.Begin(txCtx)
	if err != nil {
		return nil, err
	}

	if err := validator.ValidateResourceName(req.GetName(), MinInstanceNameLength, MaxInstanceNameLength); err != nil {
		return nil, err
	}

	if err := validator.ValidateResourceDescription(req.GetDescription(), 255); err != nil {
		return nil, err
	}

	cnt, err := repoSet.KargoInstances().Filter(
		models.KargoInstanceWhere.Name.EQ(req.Name),
		models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(organizationID))).Count(ctx)
	if err != nil {
		return nil, err
	}
	if cnt > 0 {
		return nil, status.Error(codes.AlreadyExists, "kargo instance with this name already exists")
	}

	if err := repoSet.KargoInstances().Create(ctx, instance); err != nil {
		return nil, err
	}
	if err := repoSet.KargoInstanceConfigs().Create(ctx, &models.KargoInstanceConfig{
		InstanceID: instance.ID,
		Version:    null.StringFrom(version),
		Subdomain:  null.StringFrom(instance.ID),
	}); err != nil {
		return nil, err
	}

	if err := tx.Commit(); err != nil {
		return nil, err
	}

	instanceModel, err := s.instances.GetByID(ctx, instance.ID)
	if err != nil {
		return nil, err
	}

	instanceV1, err := s.newInstanceV1(instanceModel, misc.IsKargoComponentVersionSupported(instanceModel.Config.Version.String, s.versions, s.kargoUnstableVersion, true) != nil)
	if err != nil {
		return nil, err
	}

	if s.cfg.SlackCallbackIncomingWebhook != "" {
		go shared.PostSlackMessageAsSections(s.log, s.cfg.SlackCallbackIncomingWebhook,
			shared.InstanceCreatedMessage(ctx, organizationID, s.cfg.AimsURL, instanceV1, s.repoSet, s.log))
	}
	return &kargov1.CreateKargoInstanceResponse{
		Instance: instanceV1,
	}, nil
}
