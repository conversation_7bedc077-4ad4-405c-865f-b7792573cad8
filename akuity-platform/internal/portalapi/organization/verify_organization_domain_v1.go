package organization

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) VerifyOrganizationDomains(
	ctx context.Context,
	req *organizationv1.VerifyOrganizationDomainsRequest,
) (*organizationv1.VerifyOrganizationDomainsResponse, error) {
	_, actor, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateOrganization(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}
	if len(req.Domains) == 0 {
		return nil, status.Error(codes.InvalidArgument, "empty domain list")
	}
	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	org, err := os.Get(ctx, req.GetOrganizationId())
	if err != nil {
		return nil, errors.Wrap(err, "get organization")
	}
	vd, err := org.GetVerifiedDomains()
	if err != nil {
		return nil, err
	}

	dupDomains := []string{}
	for _, domain := range req.Domains {
		for _, d := range vd {
			if d.Domain == domain {
				dupDomains = append(dupDomains, domain)
				continue
			}
		}
		vd = append(vd, models.VerifiedDomain{Domain: domain})
	}
	if len(dupDomains) > 0 {
		return nil, status.Errorf(codes.InvalidArgument, "domain already in the list: %s", strings.Join(dupDomains, ", "))
	}
	if err := org.SetVerifiedDomains(vd); err != nil {
		return nil, err
	}

	if err := os.UpdateDomains(ctx, org); err != nil {
		return nil, err
	}

	resp := &organizationv1.VerifyOrganizationDomainsResponse{
		Domains: make([]*organizationv1.DomainVerification, len(vd)),
	}
	for i, d := range vd {
		resp.Domains[i] = &organizationv1.DomainVerification{Domain: d.Domain, Verified: d.Verified}
	}

	if s.Cfg.SlackSupportIncomingWebhook != "" {
		initiator := "akp_key: " + actor.ID
		if actor.Type == accesscontrol.ActorTypeUser {
			initiator = actor.Extras["email"].(string)
		}
		msg := s.domainVerifyMessage(org, s.Cfg.AimsURL, initiator, strings.Join(req.GetDomains(), ", "))
		go shared.PostSlackMessageAsSections(&s.Log, s.Cfg.SlackSupportIncomingWebhook, msg)
	}

	return resp, nil
}

func (s *OrganizationV1Server) domainVerifyMessage(org *models.Organization, aimsURL, initiator, domain string) [][]string {
	if shared.SkipSlackCallback(org.Name, initiator) {
		return nil
	}

	organizationIdLink := shared.Code(org.ID)
	if aimsURL != "" {
		organizationIdLink = fmt.Sprintf("<%s|%s>", aimsURL+"/organizations/"+org.ID, org.ID)
	}

	return [][]string{
		{shared.HeaderSectionKey, "New Domain Verification Request"},
		{"User", initiator},
		{"Organization ID", organizationIdLink},
		{"Organization Name", shared.Code(org.Name)},
		{"Domain Name", domain},
	}
}
