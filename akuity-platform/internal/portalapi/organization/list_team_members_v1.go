package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

const (
	teamMembersMaxLimit     = int64(1000)
	teamMembersDefaultLimit = int64(10)
)

func (s *OrganizationV1Server) ListTeamMembers(
	ctx context.Context,
	req *organizationv1.ListTeamMembersRequest,
) (*organizationv1.ListTeamMembersResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.OrganizationId,
		accesscontrol.NewActionGetTeamMembers(req.GetTeamName()))
	if err != nil {
		return nil, err
	}

	if err := s.isTeamEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	limit := req.GetLimit()
	offset := req.GetOffset()
	if limit == 0 {
		limit = teamMembersDefaultLimit
	} else if limit > teamMembersMaxLimit {
		limit = teamMembersMaxLimit
	}

	if offset < 0 {
		offset = 0
	}

	teamSvc := teams.NewService(s.Db)
	team, err := teamSvc.GetTeam(ctx, req.GetOrganizationId(), req.GetTeamName())
	if err != nil {
		return nil, err
	}

	members, err := teamSvc.ListTeamMembers(ctx, team.ID, int(limit), int(offset))
	if err != nil {
		return nil, err
	}

	memberCount, err := teamSvc.CountTeamMembers(ctx, team.ID)
	if err != nil {
		return nil, err
	}
	return &organizationv1.ListTeamMembersResponse{
		TeamMembers: newTeamMembers(members),
		Count:       memberCount,
	}, nil
}

func newTeamMembers(teamMembers []*teams.TeamMember) []*organizationv1.TeamMember {
	res := make([]*organizationv1.TeamMember, 0, len(teamMembers))
	for _, teamMember := range teamMembers {
		res = append(res, &organizationv1.TeamMember{
			Id:    teamMember.ID,
			Email: teamMember.Email,
		})
	}
	return res
}
