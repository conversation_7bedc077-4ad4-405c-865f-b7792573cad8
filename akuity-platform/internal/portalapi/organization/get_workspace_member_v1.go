package organization

import (
	"context"

	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetWorkspaceMember(
	ctx context.Context,
	req *organizationv1.GetWorkspaceMemberRequest,
) (*organizationv1.GetWorkspaceMemberResponse, error) {
	if req.GetWorkspaceId() != "" {
		_, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.OrganizationId,
			accesscontrol.NewActionGetWorkspaceMembers(req.GetWorkspaceId(), req.GetId()))
		if err != nil {
			return nil, err
		}
	}
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetWorkspaces().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "workspaces feature is not enabled")
	}

	teamSvc := teams.NewService(s.Db)
	workspaceSvc := workspaces.NewService(s.Db, teamSvc, s.Cfg.FeatureGatesSource)
	member, err := workspaceSvc.GetWorkspaceMember(ctx, req.GetId())
	if err != nil {
		return nil, errors.Wrap(err, "get workspace member")
	}
	return &organizationv1.GetWorkspaceMemberResponse{
		WorkspaceMember: mapWorkspaceMemberToRPCEntity(*member),
	}, nil
}
