package organization

import (
	"context"

	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/permissions"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) UpdateWorkspaceMember(
	ctx context.Context,
	req *organizationv1.UpdateWorkspaceMemberRequest,
) (*organizationv1.UpdateWorkspaceMemberResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.OrganizationId,
		accesscontrol.NewActionUpdateWorkspaceMemberRole(req.GetWorkspaceId(), req.GetId()))
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetWorkspaces().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "workspaces feature is not enabled")
	}

	// Validate payload
	var role permissions.Role

	switch req.GetRole() {
	case organizationv1.WorkspaceMemberRole_WORKSPACE_MEMBER_ROLE_MEMBER:
		role = permissions.RoleWorkspaceMember
	case organizationv1.WorkspaceMemberRole_WORKSPACE_MEMBER_ROLE_ADMIN:
		role = permissions.RoleWorkspaceAdmin
	default:
		return nil, status.Errorf(codes.InvalidArgument, "invalid role %q", req.GetRole())
	}

	teamSvc := teams.NewService(s.Db)
	workspaceSvc := workspaces.NewService(s.Db, teamSvc, s.Cfg.FeatureGatesSource)
	member, err := workspaceSvc.UpdateWorkspaceMember(ctx, req.GetId(), role)
	if err != nil {
		return nil, errors.Wrap(err, "update workspace member")
	}
	return &organizationv1.UpdateWorkspaceMemberResponse{
		WorkspaceMember: mapWorkspaceMemberToRPCEntity(*member),
	}, nil
}
