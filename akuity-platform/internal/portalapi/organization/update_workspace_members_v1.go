package organization

import (
	"context"

	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/permissions"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/users"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) UpdateWorkspaceMembers(
	ctx context.Context,
	req *organizationv1.UpdateWorkspaceMembersRequest,
) (*organizationv1.UpdateWorkspaceMembersResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.OrganizationId,
		accesscontrol.NewActionCreateWorkspaceMembers(req.GetWorkspaceId()))
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetWorkspaces().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "workspaces feature is not enabled")
	}

	teamSvc := teams.NewService(s.Db)
	workspaceSvc := workspaces.NewService(s.Db, teamSvc, s.Cfg.FeatureGatesSource)
	us := users.NewService(s.RepoSet, s.Cfg.UsageLimits, &s.Log)

	// Validate payload
	memberRefs := make([]workspaces.WorkspaceMember, len(req.GetMemberRefs()))
	// userMembersByID and teamMembersByID are used to check if a user or team is already in the memberRefs
	userMembersByID, teamMembersByID := make(map[string]bool), make(map[string]bool)

	for idx, ref := range req.GetMemberRefs() {
		var memberRef workspaces.WorkspaceMember

		switch role := ref.Role; role {
		case organizationv1.WorkspaceMemberRole_WORKSPACE_MEMBER_ROLE_MEMBER:
			memberRef.Role = permissions.RoleWorkspaceMember
		case organizationv1.WorkspaceMemberRole_WORKSPACE_MEMBER_ROLE_ADMIN:
			memberRef.Role = permissions.RoleWorkspaceAdmin
		default:
			return nil, status.Errorf(codes.InvalidArgument, "invalid role %q", role.String())
		}

		switch member := ref.Member.(type) {
		case *organizationv1.WorkspaceMemberRef_UserId:
			user, err := us.GetUser(ctx, member.UserId)
			if err != nil {
				return nil, errors.Wrap(err, "get user")
			}
			memberRef.Ref = user

			if _, ok := userMembersByID[user.ID]; ok {
				return nil, status.Errorf(codes.InvalidArgument, "user %q is already in memberRefs", user.ID)
			}
			userMembersByID[user.ID] = true

		case *organizationv1.WorkspaceMemberRef_UserEmail:
			user, err := us.GetUserByEmail(ctx, member.UserEmail)
			if err != nil {
				return nil, errors.Wrap(err, "get user by email")
			}
			memberRef.Ref = user

			if _, ok := userMembersByID[user.ID]; ok {
				return nil, status.Errorf(codes.InvalidArgument, "user %q is already in memberRefs", user.Email)
			}
			userMembersByID[user.ID] = true

		case *organizationv1.WorkspaceMemberRef_TeamName:
			team, err := teamSvc.GetTeam(ctx, req.GetOrganizationId(), member.TeamName)
			if err != nil {
				return nil, errors.Wrap(err, "get team")
			}
			memberRef.Ref = &teams.Team{
				ID: team.ID,
			}

			if _, ok := teamMembersByID[team.ID]; ok {
				return nil, status.Errorf(codes.InvalidArgument, "team %q is already in memberRefs", team.Name)
			}
			teamMembersByID[team.ID] = true
		}
		memberRefs[idx] = memberRef
	}

	members, err := workspaceSvc.UpdateWorkspaceMembers(ctx, req.GetOrganizationId(), req.GetWorkspaceId(), memberRefs)
	if err != nil {
		return nil, errors.Wrap(err, "update workspace members")
	}
	res := make([]*organizationv1.WorkspaceMember, len(members))
	for idx, member := range members {
		res[idx] = mapWorkspaceMemberToRPCEntity(member)
	}
	return &organizationv1.UpdateWorkspaceMembersResponse{
		WorkspaceMembers: res,
	}, nil
}
