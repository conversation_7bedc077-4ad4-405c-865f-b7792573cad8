package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	idv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/id/v1"
)

func (s *OrganizationV1Server) GetOrganization(
	ctx context.Context,
	req *organizationv1.GetOrganizationRequest,
) (*organizationv1.GetOrganizationResponse, error) {
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	var getOrgFn func(context.Context, string) (*models.Organization, error)
	switch req.GetIdType() {
	case idv1.Type_UNSPECIFIED:
		getOrgFn = os.Get
	case idv1.Type_NAME:
		getOrgFn = os.GetByName
	default:
		return nil, status.Error(codes.InvalidArgument, "id or name should be provided")
	}

	org, err := getOrgFn(ctx, req.GetId())
	if err != nil {
		if organizations.IsNotFoundErr(err) {
			return nil, status.Errorf(codes.NotFound, "organization %q not found", req.GetId())
		}
		return nil, err
	}

	for _, binding := range actor.OrganizationBindings {
		if org.ID == binding.OrganizationID {
			quota, err := s.featSvc.GetOrgQuotas(ctx, org.GetID())
			if err != nil {
				return nil, err
			}

			usage, err := s.featSvc.GetOrgUsages(ctx, org.GetID())
			if err != nil {
				return nil, err
			}

			orgResp, err := mapOrganizationToRPCEntity(*org, quota, binding.Permissions)
			if err != nil {
				return nil, err
			}

			orgResp.Usage = usage

			return &organizationv1.GetOrganizationResponse{
				Organization: orgResp,
			}, nil
		}
	}
	return nil, status.Error(codes.PermissionDenied, "")
}
