package organization

import (
	"context"

	"github.com/pkg/errors"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListOrganizationDomains(
	ctx context.Context,
	req *organizationv1.ListOrganizationDomainsRequest,
) (*organizationv1.ListOrganizationDomainsResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetOrganization(req.GetOrganizationId())); err != nil {
		return nil, err
	}
	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	org, err := os.Get(ctx, req.GetOrganizationId())
	if err != nil {
		return nil, errors.Wrap(err, "get organization")
	}
	vd, err := org.GetVerifiedDomains()
	if err != nil {
		return nil, err
	}
	resp := &organizationv1.ListOrganizationDomainsResponse{
		Domains: make([]*organizationv1.DomainVerification, len(vd)),
	}
	for i, d := range vd {
		resp.Domains[i] = &organizationv1.DomainVerification{Domain: d.Domain, Verified: d.Verified}
	}

	return resp, nil
}
