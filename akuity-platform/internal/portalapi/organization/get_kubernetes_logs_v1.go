package organization

import (
	"k8s.io/client-go/kubernetes"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetKubernetesLogs(req *organizationv1.GetKubernetesLogsRequest, server organizationv1.OrganizationService_GetKubernetesLogsServer) error {
	enforcer, err := s.checkKubeVisionPermission(server.Context(), req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return err
	}

	resSvc := k8sresource.NewServiceWithOptions(
		s.RepoSet,
		s.Db,
		req.GetOrganizationId(),
		k8sresource.WithLogger(logging.Extract(server.Context())),
		k8sresource.WithEnforcer(enforcer),
	)

	cluster, err := resSvc.ValidateGetRequest(server.Context(), req.GetInstanceId(), req.GetClusterId())
	if err != nil {
		return err
	}

	restConfig := instances.ClusterRestConfig(cluster.Name, req.GetInstanceId())
	client, err := kubernetes.NewForConfig(restConfig)
	if err != nil {
		return err
	}

	if req.GetResourceId() == "" {
		return shared.ErrInvalidArgument
	}

	err = resSvc.GetLogs(server, client, req.GetInstanceId(), cluster.ID, req.GetResourceId(), req.GetSinceSeconds(), req.GetSinceTime(),
		req.GetTailLines(), req.GetFollow(), req.GetUntilTime(), req.GetPrevious(), req.GetFilter())
	if err != nil {
		return err
	}
	return nil
}
