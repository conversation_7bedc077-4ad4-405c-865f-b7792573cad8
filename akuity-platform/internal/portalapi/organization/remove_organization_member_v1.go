package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) RemoveOrganizationMember(
	ctx context.Context,
	req *organizationv1.RemoveOrganizationMemberRequest,
) (*organizationv1.RemoveOrganizationMemberResponse, error) {
	_, actor, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetId(),
		accesscontrol.NewActionDeleteOrganizationMembers(req.GetMemberId()))
	if err != nil {
		if actor == nil {
			return nil, err
		}
		if status.Code(err) == codes.PermissionDenied && actor.ID != req.GetMemberId() {
			return nil, err
		}
		// Ignore error if the actor tries to remove him/herself
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	if err := os.RemoveMember(ctx, req.GetId(), req.GetMemberId()); err != nil {
		return nil, err
	}
	return &organizationv1.RemoveOrganizationMemberResponse{}, nil
}
