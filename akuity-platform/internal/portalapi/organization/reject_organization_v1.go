package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	"github.com/akuityio/akuity-platform/internal/services/users"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) RejectOrganization(
	ctx context.Context,
	req *organizationv1.RejectOrganizationRequest,
) (*organizationv1.RejectOrganizationResponse, error) {
	userSvc := users.NewService(s.RepoSet, s.Cfg.UsageLimits, &s.Log)
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}

	actorUser, err := userSvc.GetUser(ctx, actor.ID)
	if err != nil {
		return nil, err
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	if err := os.RejectInvitation(ctx, req.GetId(), actorUser.Email); err != nil {
		if organizations.IsNotFoundErr(err) {
			return nil, shared.ErrNotFound
		}
		return nil, err
	}
	return &organizationv1.RejectOrganizationResponse{}, err
}
