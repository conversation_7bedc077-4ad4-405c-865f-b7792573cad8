package organization

import (
	"context"

	"github.com/samber/lo"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListKubernetesNamespacesDetails(
	ctx context.Context,
	req *organizationv1.ListKubernetesNamespacesDetailsRequest,
) (*organizationv1.ListKubernetesNamespacesDetailsResponse, error) {
	enforcer, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	resSvc := k8sresource.NewServiceWithOptions(s.RepoSet, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)), k8sresource.WithEnforcer(enforcer))

	clusterInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), false, req.GetClusterIds()...)
	if err != nil {
		return nil, err
	}

	details, err := resSvc.ListNamespacesDetails(ctx, req.GetInstanceId(), clusterInfo.GetClusterIDs(), req.GetGroupBy(), req.GetFiller())
	if err != nil {
		return nil, err
	}

	values := lo.Map(details, func(detail *organizationv1.NamespaceDetail, _ int) float64 {
		return detail.GetFillValue()
	})

	unit := organizationv1.FillValueUnit_FILL_VALUE_UNIT_COUNT
	if req.GetFiller() == organizationv1.NamespaceFiller_NAMESPACE_FILLER_USAGE_TO_REQUEST_CPU ||
		req.GetFiller() == organizationv1.NamespaceFiller_NAMESPACE_FILLER_USAGE_TO_REQUEST_MEMORY {
		unit = organizationv1.FillValueUnit_FILL_VALUE_UNIT_PERCENTAGE
	}
	return &organizationv1.ListKubernetesNamespacesDetailsResponse{
		NamespacesDetails: details,
		FillValueUnit:     unit,
		MinFillValue:      lo.Min(values),
		MaxFillValue:      lo.Max(values),
	}, nil
}
