package organization

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	errorsutil "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) UpdateNotificationConfig(
	ctx context.Context,
	req *organizationv1.UpdateNotificationConfigRequest,
) (*organizationv1.UpdateNotificationConfigResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(
		ctx,
		s.Db,
		s.acs,
		req.OrganizationId,
		accesscontrol.NewActionUpdateOrgNotificationConfig(req.GetId()),
	)
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetNotification().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "notification feature is not enabled")
	}

	switch req.GetPayload().(type) {
	case *organizationv1.UpdateNotificationConfigRequest_Webhook:
		return s.updateWebhookNotificationConfig(
			ctx,
			req.GetOrganizationId(),
			req.GetId(),
			req.GetName(),
			req.GetWebhook(),
		)
	default:
		return nil, status.Errorf(codes.InvalidArgument, "invalid payload type: %T", req.GetPayload())
	}
}

func (s *OrganizationV1Server) updateWebhookNotificationConfig(
	ctx context.Context,
	orgID string,
	id string,
	name string,
	payload *organizationv1.WebhookNotificationUpdatePayload,
) (*organizationv1.UpdateNotificationConfigResponse, error) {
	// Validate name
	if len(name) == 0 || len(name) > maxNotificationConfigNameLength {
		return nil, status.Errorf(
			codes.InvalidArgument,
			"name must be between 1 and %d characters",
			maxNotificationConfigNameLength,
		)
	}

	// Validate events
	events, err := s.validateWebhookEvents(payload.GetFilter().GetEvents())
	if err != nil {
		return nil, err
	}

	// Validate URL
	webhookURL := payload.GetUrl()
	if err := s.validateWebhookURL(webhookURL); err != nil {
		return nil, err
	}

	// Validate Filters
	if payload.GetFilter().GetFilterArgocdInstanceNames() {
		if len(payload.GetFilter().GetArgocdInstanceNames()) == 0 {
			return nil, status.Error(codes.InvalidArgument, "ArgoCD Instance names must not be empty")
		}
	}
	if payload.GetFilter().GetFilterKargoInstanceNames() {
		if len(payload.GetFilter().GetKargoInstanceNames()) == 0 {
			return nil, status.Error(codes.InvalidArgument, "Kargo Instance names must not be empty")
		}
	}

	cfg, err := s.RepoSet.OrganizationNotificationConfigs(
		models.OrganizationNotificationConfigWhere.OrganizationID.EQ(orgID),
		models.OrganizationNotificationConfigWhere.DeliveryMethod.EQ(
			models.DeliveryMethodWebhook,
		),
	).GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, status.Errorf(codes.NotFound, "webhook config not found")
		}
		return nil, errors.Wrap(err, "get webhook config")
	}

	cfg.Name = name
	if err := cfg.SetEvents(events); err != nil {
		return nil, errors.Wrap(err, "set events")
	}

	webhookCfg, err := cfg.GetOrganizationWebhookConfig()
	if err != nil {
		return nil, errors.Wrap(err, "get organization webhook config")
	}

	webhookCfg.URL = webhookURL
	webhookCfg.Active = payload.GetActive()
	if !payload.GetUsePreviousSecret() {
		webhookCfg.Secret = payload.GetSecret()
	}
	webhookCfg.Filter = models.OrganizationWebhookConfigFilter{
		FilterArgoCDInstanceNames: payload.GetFilter().GetFilterArgocdInstanceNames(),
		ArgoCDInstanceNames:       payload.GetFilter().GetArgocdInstanceNames(),
		FilterKargoInstanceNames:  payload.GetFilter().GetFilterKargoInstanceNames(),
		KargoInstanceNames:        payload.GetFilter().GetKargoInstanceNames(),
	}
	if err := cfg.SetOrganizationWebhookConfig(webhookCfg); err != nil {
		return nil, errors.Wrap(err, "set organization webhook config")
	}
	if err := s.RepoSet.OrganizationNotificationConfigs().Update(ctx, cfg); err != nil {
		if errorsutil.IsUniqueViolation(err, models.OrgNotificationConfigNameUniqueConstraint) {
			return nil, status.Errorf(
				codes.AlreadyExists,
				"notification config with name %q already exists",
				name,
			)
		}
		return nil, errors.Wrap(err, "update notification webhook config")
	}

	notification, err := s.RepoSet.Notifications(
		qm.Load(models.NotificationRels.Event),
		models.NotificationWhere.DeliveryMethod.EQ(cfg.DeliveryMethod),
		models.NotificationWhere.TargetID.EQ(null.StringFrom(cfg.ID)),
		models.NotificationWhere.LastDeliveryTimestamp.IsNotNull(),
		qm.OrderBy(
			fmt.Sprintf(`(
CASE WHEN %s is NULL THEN %s
WHEN %s > %s THEN %s
ELSE %s
END) DESC`,
				models.NotificationColumns.LastDeliveryTimestamp,
				models.NotificationColumns.CreationTimestamp,
				models.NotificationColumns.LastDeliveryTimestamp,
				models.NotificationColumns.CreationTimestamp,
				models.NotificationColumns.LastDeliveryTimestamp,
				models.NotificationColumns.CreationTimestamp,
			),
		),
	).One(ctx)
	if err != nil {
		if !errors.Is(err, sql.ErrNoRows) {
			return nil, errors.Wrap(err, "get notification")
		}
	}

	res, err := mapNotificationConfigToRPCEntity(*cfg, notification)
	if err != nil {
		return nil, errors.Wrap(err, "map notification config")
	}
	return &organizationv1.UpdateNotificationConfigResponse{
		NotificationConfig: res,
	}, nil
}
