package organization

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

const (
	notificationConfigDefaultLimit = 10
	notificationConfigMaxLimit     = 20
)

func (s *OrganizationV1Server) ListNotificationConfigs(
	ctx context.Context,
	req *organizationv1.ListNotificationConfigsRequest,
) (*organizationv1.ListNotificationConfigsResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(
		ctx,
		s.Db,
		s.acs,
		req.OrganizationId,
		accesscontrol.NewActionListOrgNotificationConfigs(),
	)
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetNotification().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "notification feature is not enabled")
	}

	limit := req.GetLimit()
	offset := req.GetOffset()
	if limit == 0 {
		limit = notificationConfigDefaultLimit
	} else if limit > notificationConfigMaxLimit {
		limit = notificationConfigDefaultLimit
	}

	deliveryMethods := make([]string, 0, len(req.GetDeliveryMethods()))
	for _, method := range req.GetDeliveryMethods() {
		switch method {
		case organizationv1.DeliveryMethod_DELIVERY_METHOD_UNSPECIFIED:
			return nil, status.Errorf(codes.InvalidArgument, "invalid delivery method: %q", method.String())
		case organizationv1.DeliveryMethod_DELIVERY_METHOD_WEBHOOK:
			deliveryMethods = append(deliveryMethods, models.DeliveryMethodWebhook)
		}
	}

	queries := []qm.QueryMod{
		models.OrganizationNotificationConfigWhere.OrganizationID.EQ(
			req.GetOrganizationId(),
		),
		qm.OrderBy(
			fmt.Sprintf("%s desc", models.OrganizationNotificationConfigColumns.CreationTimestamp),
		),
		qm.Limit(int(limit)),
		qm.Offset(int(offset)),
	}
	if len(deliveryMethods) > 0 {
		queries = append(queries, models.OrganizationNotificationConfigWhere.DeliveryMethod.IN(deliveryMethods))
	}

	cfgs, err := s.RepoSet.OrganizationNotificationConfigs(queries...).ListAll(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, status.Error(codes.NotFound, "config not found")
		}
		return nil, errors.Wrap(err, "list notification configs")
	}

	res := make([]*organizationv1.NotificationConfig, len(cfgs))
	for i, cfg := range cfgs {
		notification, err := s.RepoSet.Notifications(
			qm.Load(models.NotificationRels.Event),
			models.NotificationWhere.DeliveryMethod.EQ(cfg.DeliveryMethod),
			models.NotificationWhere.TargetID.EQ(null.StringFrom(cfg.ID)),
			models.NotificationWhere.LastDeliveryTimestamp.IsNotNull(),
			qm.OrderBy(
				fmt.Sprintf(`(
CASE WHEN %s is NULL THEN %s
WHEN %s > %s THEN %s
ELSE %s
END) DESC`,
					models.NotificationColumns.LastDeliveryTimestamp,
					models.NotificationColumns.CreationTimestamp,
					models.NotificationColumns.LastDeliveryTimestamp,
					models.NotificationColumns.CreationTimestamp,
					models.NotificationColumns.LastDeliveryTimestamp,
					models.NotificationColumns.CreationTimestamp,
				),
			),
		).One(ctx)
		if err != nil {
			if !errors.Is(err, sql.ErrNoRows) {
				return nil, errors.Wrap(err, "get notification")
			}
		}

		mapRes, err := mapNotificationConfigToRPCEntity(*cfg, notification)
		if err != nil {
			return nil, errors.Wrap(err, "map notification config")
		}
		res[i] = mapRes
	}
	return &organizationv1.ListNotificationConfigsResponse{
		NotificationConfigs: res,
	}, nil
}
