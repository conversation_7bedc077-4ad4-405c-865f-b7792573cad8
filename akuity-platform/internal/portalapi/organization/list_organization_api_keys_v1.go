package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/permissions"
	apikeyv1 "github.com/akuityio/akuity-platform/pkg/api/gen/apikey/v1"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListWorkspaceAPIKeys(
	ctx context.Context,
	req *organizationv1.ListWorkspaceAPIKeysRequest,
) (*organizationv1.ListWorkspaceAPIKeysResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetId(),
		accesscontrol.NewActionGetWorkspaceAPIKeys(req.GetWorkspaceId(), permissions.ResourceAny)); err != nil {
		return nil, err
	}

	rawKeys, err := s.aks.ListWorkspaceKeys(ctx, req.GetId(), req.GetWorkspaceId())
	if err != nil {
		return nil, err
	}

	apiKeys := make([]*apikeyv1.APIKey, 0, len(rawKeys))
	for _, key := range rawKeys {
		apiKeys = append(apiKeys, shared.NewAPIKeyV1(key, ""))
	}
	return &organizationv1.ListWorkspaceAPIKeysResponse{
		ApiKeys:     apiKeys,
		WorkspaceId: req.GetWorkspaceId(),
	}, nil
}

func (s *OrganizationV1Server) ListOrganizationAPIKeys(
	ctx context.Context,
	req *organizationv1.ListOrganizationAPIKeysRequest,
) (*organizationv1.ListOrganizationAPIKeysResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetId(),
		accesscontrol.NewActionGetAPIKeys(accesscontrol.ResourceAny)); err != nil {
		return nil, err
	}

	rawKeys, err := s.aks.ListOrganizationKeys(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	apiKeys := make([]*apikeyv1.APIKey, 0, len(rawKeys))
	for _, key := range rawKeys {
		apiKeys = append(apiKeys, shared.NewAPIKeyV1(key, ""))
	}
	return &organizationv1.ListOrganizationAPIKeysResponse{
		ApiKeys: apiKeys,
	}, nil
}
