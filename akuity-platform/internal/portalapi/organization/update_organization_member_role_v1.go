package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) UpdateOrganizationMemberRole(
	ctx context.Context,
	req *organizationv1.UpdateOrganizationMemberRoleRequest,
) (*organizationv1.UpdateOrganizationMemberRoleResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetId(),
		accesscontrol.NewActionUpdateOrganizationMemberRole(accesscontrol.ResourceAny)); err != nil {
		return nil, err
	}

	role := organizations.Role(req.GetRole())
	if !organizations.IsValidRole(role) {
		return nil, status.Errorf(codes.InvalidArgument, "invalid role: %q", role)
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	if err := os.UpdateMemberRole(ctx, req.GetId(), req.GetMemberId(), role); err != nil {
		return nil, err
	}
	return &organizationv1.UpdateOrganizationMemberRoleResponse{}, nil
}
