package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetKubernetesResourceDetail(
	ctx context.Context,
	req *organizationv1.GetKubernetesResourceDetailRequest,
) (*organizationv1.GetKubernetesResourceDetailResponse, error) {
	enforcer, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	resSvc := k8sresource.NewServiceWithOptions(
		s.RepoSet,
		s.Db,
		req.GetOrganizationId(),
		k8sresource.WithLogger(logging.Extract(ctx)),
		k8sresource.WithEnforcer(enforcer),
	)

	cluster, err := resSvc.ValidateGetRequest(ctx, req.GetInstanceId(), req.GetClusterId())
	if err != nil {
		return nil, err
	}

	obj, ownerInfo, childTypes, err := resSvc.GetResourceDetail(ctx, cluster.ID, req.GetInstanceId(), req.GetResourceId())
	if err != nil {
		return nil, err
	}

	deprecatedGVKMap, err := resSvc.GetDeprecatedGVKs([]*k8sresource.EnabledCluster{cluster})
	if err != nil {
		return nil, err
	}

	resource, err := mapResourceToRPCEntity(obj, deprecatedGVKMap, cluster.ID)
	if err != nil {
		return nil, err
	}

	var pc []*organizationv1.PodInfo
	if k8sresource.IsWorkload(resource.GetGroup(), resource.GetKind()) {
		pc, err = resSvc.PodContainersForWorkload(ctx, req.GetInstanceId(), cluster.ID, req.GetResourceId())
		if err != nil {
			return nil, err
		}
	}

	return &organizationv1.GetKubernetesResourceDetailResponse{
		Resource:   resource,
		OwnerInfo:  ownerInfo,
		ChildTypes: childTypes,
		PodInfo:    pc,
	}, nil
}
