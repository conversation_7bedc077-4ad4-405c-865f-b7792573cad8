package organization

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetOrganizationPermissions(
	ctx context.Context,
	req *organizationv1.GetOrganizationPermissionsRequest,
) (*organizationv1.GetOrganizationPermissionsResponse, error) {
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}
	enforcer, err := s.acs.NewOrganizationEnforcer(ctx, s.Db, req.GetId(), *actor)
	if err != nil {
		if errors.Is(err, accesscontrol.ErrOrgBindingNotFound) {
			return nil, status.Error(codes.PermissionDenied, err.Error())
		}
		return nil, fmt.Errorf("failed to create enforcer: %w", err)
	}
	pp, err := enforcer.GetPermissions()
	if err != nil {
		return nil, err
	}
	permissions := make([]*organizationv1.Permission, len(pp))
	for i, p := range pp {
		if len(p) < 4 {
			// shouldn't ever happen
			continue
		}
		permissions[i] = &organizationv1.Permission{
			Role:     p[0],
			Object:   p[1],
			Action:   p[2],
			Resource: p[3],
		}
	}
	rawPerms, err := enforcer.GetJsPermission()
	if err != nil {
		return nil, err
	}

	return &organizationv1.GetOrganizationPermissionsResponse{
		Permissions:    permissions,
		RawPermissions: rawPerms,
	}, nil
}
