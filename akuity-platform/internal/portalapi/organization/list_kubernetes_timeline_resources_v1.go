package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListKubernetesTimelineResources(ctx context.Context, req *organizationv1.ListKubernetesTimelineResourcesRequest) (*organizationv1.ListKubernetesTimelineResourcesResponse, error) {
	_, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	resSvc := k8sresource.NewServiceWithOptions(s.RepoSet, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)))

	clusterInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), false, req.GetClusterIds()...)
	if err != nil {
		return nil, err
	}
	clusterIDs := clusterInfo.GetClusterIDs()

	limit := req.GetLimit()
	offset := req.GetOffset()
	if limit == 0 {
		limit = 100
	}
	resources, count, err := resSvc.ListKubernetesTimelineResources(ctx, req.GetInstanceId(), clusterIDs, req.GetNamespaces(), req.GetGroup(), req.GetKind(), int(limit), int(offset))
	if err != nil {
		return nil, err
	}
	return &organizationv1.ListKubernetesTimelineResourcesResponse{
		Resources: resources,
		Count:     uint32(count),
	}, nil
}
