package organization

import (
	"context"
	"database/sql"

	"github.com/auth0/go-auth0/management"
	"github.com/go-logr/logr"
	"github.com/go-playground/validator/v10"
	"github.com/volatiletech/sqlboiler/v4/boil"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"k8s.io/client-go/rest"

	"github.com/akuityio/akuity-platform/internal/auth0"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/apikeys"
	"github.com/akuityio/akuity-platform/internal/services/customroles"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	"github.com/akuityio/akuity-platform/internal/services/permissions"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/mail"
	akuityvalidator "github.com/akuityio/akuity-platform/internal/utils/validator"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/events"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	"github.com/akuityio/akuity-platform/pkg/billing"
)

type OrganizationV1Server struct {
	organizationv1.OrganizationServiceServer
	shared.CommonServerConfig
	aiConversationWatcher database.Watcher[events.Event]
	crs                   customroles.Service
	mailer                mail.Mailer
	acs                   accesscontrol.PolicyService
	aks                   apikeys.Service
	featSvc               features.Service
	hostRestConfig        *rest.Config
	auth0Management       *management.Management
}

func NewOrganizationV1Server(
	ctx context.Context,
	db *sql.DB,
	cfg config.PortalServerConfig,
	validator *validator.Validate,
	billingProviders map[billing.ProviderName]billing.IProvider,
	mailer mail.Mailer,
	log logr.Logger,
	hostRestConfig *rest.Config,
	auth0Management *management.Management,
	aiConversationWatcher database.Watcher[events.Event],
) (*OrganizationV1Server, error) {
	repoSet := client.NewRepoSet(db)
	auth0Client, err := auth0.New(ctx, cfg.Auth0)
	if err != nil {
		return nil, err
	}
	rs := client.NewRepoSet(db)
	aks := apikeys.NewService(repoSet, validator)
	crs := customroles.New(repoSet)
	teamSvc := teams.NewService(db)
	workspaceSvc := workspaces.NewService(db, teamSvc, cfg.FeatureGatesSource)
	return &OrganizationV1Server{
		CommonServerConfig: shared.CommonServerConfig{
			Db:               db,
			Cfg:              cfg,
			Auth0Client:      auth0Client,
			BillingProviders: billingProviders,
			RepoSet:          rs,
			Validator:        akuityvalidator.New(),
			Log:              log,
		},
		mailer:                mailer,
		acs:                   accesscontrol.NewPolicyService(validator, crs, workspaceSvc, aks, teamSvc),
		crs:                   crs,
		aks:                   aks,
		featSvc:               features.NewService(rs, db, config.IsSelfHosted, config.IsSelfHostedReleaseBuild, cfg.FeatureGatesSource, config.GetLicense(), features.WithLogger(&log)),
		hostRestConfig:        hostRestConfig,
		auth0Management:       auth0Management,
		aiConversationWatcher: aiConversationWatcher,
	}, nil
}

func (s *OrganizationV1Server) newOrganizationServiceFunc() func(boil.ContextExecutor, database.TxBeginner) organizations.Service {
	return func(txDB boil.ContextExecutor, txBeginner database.TxBeginner) organizations.Service {
		rs := client.NewRepoSet(txDB)
		return organizations.New(txDB, txBeginner, s.Auth0Client, s.BillingProviders, rs, s.Cfg, s.Validator)
	}
}

func (s *OrganizationV1Server) isTeamEnabled(ctx context.Context, orgID string) error {
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetTeam().Enabled() {
		return status.Error(codes.PermissionDenied, "team feature is not enabled")
	}
	return nil
}

// checkPermission checks if the user has the permission to access the feature on
// akuity platform, Argo CD, or Kargo. Perticularly, for KubeVision and AISupportEngineer, both features are
// available on AKP, Argo CD, and Kargo.
func (s *OrganizationV1Server) checkPermission(ctx context.Context, actor *accesscontrol.Actor, organizationID, instanceID, kargoInstanceID string, actions ...permissions.Action) error {
	// If the user is argo, we need to further check if the organization and instance ID match of the argo instance
	if actor.Type == accesscontrol.ActorTypeArgoCD {
		if actor.Extras["organizationID"] != organizationID || actor.Extras["instanceID"] != instanceID {
			return status.Errorf(codes.PermissionDenied, "organization or instance mismatch")
		}
	}

	// If the user is kargo, we need to further check if the organization and instance ID match of the kargo instance
	if actor.Type == accesscontrol.ActorTypeKargo {
		if !s.featSvc.GetFeatureStatuses(ctx, &organizationID).GetKargoEnterprise().Enabled() {
			return status.Error(codes.PermissionDenied, "kargo enterprise feature is not enabled")
		}

		if actor.Extras["organizationID"] != organizationID || actor.Extras["instanceID"] != kargoInstanceID {
			return status.Errorf(codes.PermissionDenied, "organization or instance mismatch")
		}
	}

	return nil
}

func (s *OrganizationV1Server) checkKubeVisionPermission(ctx context.Context, organizationID, instanceID string, action permissions.Action) (*k8sresource.Enforcer, error) {
	if !s.featSvc.GetFeatureStatuses(ctx, &organizationID).GetMultiClusterK8SDashboard().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "multi-cluster k8s dashboard feature is not enabled")
	}

	_, actor, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, organizationID, action)
	if err != nil {
		return nil, err
	}

	// For KubeVision requests, we do not use the kargoInstanceID parameter. Instead, we use the
	// kargo instance ID from the requesting instance to filter resources, ensuring that
	// the correct Kargo instance is used for resource access.
	kargoInstanceID := ""
	if actor.Type == accesscontrol.ActorTypeKargo {
		kargoInstanceID = actor.Extras["instanceID"].(string)
	}

	if err := s.checkPermission(ctx, actor, organizationID, instanceID, kargoInstanceID, action); err != nil {
		return nil, err
	}

	return k8sresource.NewPermissionEnforcer(actor, organizationID, s.RepoSet, s.Db, s.hostRestConfig, s.Log, s.featSvc), nil
}

func (s *OrganizationV1Server) checkAISupportEngineerPermission(ctx context.Context, organizationID, instanceID, kargoInstanceID string, action permissions.Action) (*accesscontrol.Actor, error) {
	if !s.featSvc.GetFeatureStatuses(ctx, &organizationID).GetAiSupportEngineer().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "feature Akuity Intelligence is not enabled")
	}

	_, actor, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, organizationID, action)
	if err != nil {
		return nil, err
	}

	if err := s.checkPermission(ctx, actor, organizationID, instanceID, kargoInstanceID, action); err != nil {
		return nil, err
	}

	return actor, nil
}
