package organization

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/stripe/stripe-go/v74"
	"github.com/volatiletech/null/v8"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	billingutil "github.com/akuityio/akuity-platform/internal/utils/billing"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) UpdateSubscription(
	ctx context.Context,
	req *organizationv1.UpdateSubscriptionRequest,
) (*organizationv1.UpdateSubscriptionResponse, error) {
	if config.IsSelfHosted {
		return nil, shared.ErrNotAvailableInSelfHostedVersion
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)

	orgId := req.GetId()
	org, err := os.Get(ctx, orgId)
	if err != nil {
		if organizations.IsNotFoundErr(err) {
			return nil, status.Errorf(codes.NotFound, "organization %q not found", orgId)
		}
		return nil, err
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, org.GetID(),
		accesscontrol.NewActionUpdateOrganizationBilling(org.GetID())); err != nil {
		return nil, err
	}

	desiredPlanName := req.GetPlan() // plan by NAME, not by ID
	if desiredPlanName == "" {
		return nil, status.Error(codes.InvalidArgument, "plan is required")
	}

	if orgId == "" {
		return nil, status.Error(codes.InvalidArgument, "organization id is required")
	}

	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}

	stripeCfg, err := config.NewStripeConfig()
	if err != nil {
		return nil, err
	}

	stripe.Key = stripeCfg.Key

	billingData, err := os.GetBilling(ctx, orgId)
	if err != nil {
		if organizations.IsNotFoundErr(err) {
			return nil, status.Errorf(codes.NotFound, "billing data for organization %q not found", orgId)
		}
		return nil, err
	}

	if billingData.Manual.Bool {
		return nil, status.Error(codes.FailedPrecondition, "organization is managed manually")
	}

	billingDetails, err := billingutil.GetCustomerDetails(ctx, billingData.CustomerID, billingData.BillingAuthority, s.BillingProviders)
	if err != nil {
		s.Log.Info("Failed to check if customer has active subscription", "customerID", billingData.CustomerID, "billingAuthority", billingData.BillingAuthority, "error", err.Error())
	}

	if billingDetails.SubscriptionStatus == nil || org.Plan.String == "" {
		return nil, status.Error(codes.FailedPrecondition, "organization does not have an active plan subscription")
	}

	// NOTE: this quota DOES include addons whose limits are defined by billing authority (e.g. Stripe)
	currentQuota := billingDetails.UsageLimits

	desiredPlan, err := s.RepoSet.OrganizationPlans(models.OrganizationPlanWhere.Name.EQ(desiredPlanName)).One(ctx)
	if err != nil {
		if organizations.IsNotFoundErr(err) {
			return nil, status.Errorf(codes.NotFound, "plan %q not found", desiredPlanName)
		} else {
			return nil, err
		}
	}

	currentPlan, err := s.RepoSet.OrganizationPlans(models.OrganizationPlanWhere.Name.EQ(org.Plan.String)).One(ctx)
	if err != nil {
		if organizations.IsNotFoundErr(err) {
			return nil, status.Errorf(codes.NotFound, "plan %q not found", org.Plan.String)
		} else {
			return nil, err
		}
	}

	if currentPlan.Name != desiredPlanName && desiredPlan.Deprecated.Bool {
		return nil, status.Error(codes.InvalidArgument, "desired plan is deprecated")
	}

	desiredQuota, err := desiredPlan.GetOrgQuota()
	if err != nil {
		return nil, err
	}

	// retrieve includedAddons included with desired plan
	includedAddons, err := s.RepoSet.AddonPlans(models.AddonPlanWhere.IncludedWithPlan.EQ(null.StringFrom(desiredPlan.Name))).ListAll(ctx)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		// don't fail if no addons are found
		return nil, err
	}

	var requestedAddons []*organizationv1.SubscriptionAddon
	if len(includedAddons) > 0 {
		requestedAddonsMap := make(map[string]int, len(req.GetAddons()))
		requestedAddons = make([]*organizationv1.SubscriptionAddon, len(req.GetAddons()))
		for i, a := range req.GetAddons() {
			requestedAddons[i] = &organizationv1.SubscriptionAddon{
				Name:     a.GetName(),
				Quantity: uint32(a.GetQuantity()),
			}
			requestedAddonsMap[a.GetName()] = int(a.GetQuantity())
		}

		// add addons to desired quota
		// IMPORTANT NOTE: if addon quotas are defined by billing authority (e.g. Stripe) and not the DB, these limits will be 0
		for _, a := range includedAddons {
			q, err := a.GetQuota()
			if err != nil {
				return nil, err
			}
			quantity, ok := requestedAddonsMap[a.Name]
			if !ok {
				// if none of this addon was requested, use the included quantity
				quantity = a.IncludedQuantity.Int
			}
			q = q.MultiplyBy(quantity)
			desiredQuota = desiredQuota.AddQuotas(q, 1)
		}
	}

	// with kargo being in self-serve, we need to handle upgrade/downgrade
	desiredFeatures, err := desiredPlan.GetOrgFeatureGates()
	if err != nil {
		return nil, err
	}

	if currentQuota.IsQuotaGreaterThan(desiredQuota) {
		featureStatuses := s.featSvc.GetFeatureStatuses(ctx, &org.ID)
		kargoEnabled := featureStatuses.GetKargo().Enabled()

		// if downgrading, check if org meets updated quotas
		meetsQuota, reasonOfNotMatchingQuota, err := organizations.MeetsQuota(ctx, s.RepoSet, org.ID, desiredQuota, desiredFeatures, kargoEnabled)
		if err != nil {
			return nil, err
		}
		if !meetsQuota {
			return nil, status.Errorf(codes.InvalidArgument, "organization does not meet quotas for new plan: %s", reasonOfNotMatchingQuota)
		}
	}

	err = os.UpdatePlan(ctx, billingData, desiredPlan.Name, requestedAddons)
	if err != nil {
		return nil, fmt.Errorf("failed to update plan: %w", err)
	}

	return &organizationv1.UpdateSubscriptionResponse{}, nil
}
