package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetTeamMember(
	ctx context.Context,
	req *organizationv1.GetTeamMemberRequest,
) (*organizationv1.GetTeamMemberResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.OrganizationId,
		accesscontrol.NewActionGetTeamMembers(req.GetTeamName()))
	if err != nil {
		return nil, err
	}

	if err := s.isTeamEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	teamSvc := teams.NewService(s.Db)
	team, err := teamSvc.GetTeam(ctx, req.GetOrganizationId(), req.GetTeamName())
	if err != nil {
		return nil, err
	}

	member, err := teamSvc.GetTeamMember(ctx, team.ID, req.GetId())
	if err != nil {
		return nil, err
	}
	return &organizationv1.GetTeamMemberResponse{
		TeamMember: &organizationv1.TeamMember{
			Id:    member.ID,
			Email: member.Email,
		},
	}, nil
}
