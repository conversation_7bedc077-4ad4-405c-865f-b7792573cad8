package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListOrganizationInvitees(
	ctx context.Context,
	req *organizationv1.ListOrganizationInviteesRequest,
) (*organizationv1.ListOrganizationInviteesResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetId(),
		accesscontrol.NewActionGetOrganizationMembers(accesscontrol.ResourceAny)); err != nil {
		return nil, err
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	invitations, err := os.ListInvitations(ctx, req.GetId())
	if err != nil {
		return nil, err
	}
	return &organizationv1.ListOrganizationInviteesResponse{
		Invitees: newOrganizationInviteesV1(invitations),
	}, nil
}

func newOrganizationInviteesV1(invitees []organizations.OrganizationInvitee) []*organizationv1.OrganizationInvitee {
	v1Invitees := make([]*organizationv1.OrganizationInvitee, 0)

	for _, invitee := range invitees {
		workspaces := []*organizationv1.AssignWorkspace{}
		for name, role := range invitee.Workspaces {
			workspaces = append(workspaces, &organizationv1.AssignWorkspace{
				Role: mapWorkspaceMemberRole(role),
				Name: name,
			})
		}
		v1Invitees = append(v1Invitees, &organizationv1.OrganizationInvitee{
			Id:    invitee.ID,
			Email: invitee.Email,
			Role:  invitee.Role,
			Info: &organizationv1.InviteMembersInfo{
				Teams:      invitee.Teams,
				Workspaces: workspaces,
			},
		})
	}

	return v1Invitees
}
