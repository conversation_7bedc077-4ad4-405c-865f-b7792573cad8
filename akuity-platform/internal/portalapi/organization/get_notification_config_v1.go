package organization

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetNotificationConfig(
	ctx context.Context,
	req *organizationv1.GetNotificationConfigRequest,
) (*organizationv1.GetNotificationConfigResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(
		ctx,
		s.Db,
		s.acs,
		req.OrganizationId,
		accesscontrol.NewActionGetOrgNotificationConfig(req.GetId()),
	)
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetNotification().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "notification feature is not enabled")
	}

	if req.GetId() == "" {
		return nil, status.Error(codes.InvalidArgument, "id must not be empty")
	}
	cfg, err := s.RepoSet.OrganizationNotificationConfigs(
		models.OrganizationNotificationConfigWhere.OrganizationID.EQ(
			req.GetOrganizationId(),
		),
	).GetByID(ctx, req.GetId())
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, status.Error(codes.NotFound, "config not found")
		}
		return nil, errors.Wrap(err, "get notification config")
	}

	notification, err := s.RepoSet.Notifications(
		qm.Load(models.NotificationRels.Event),
		models.NotificationWhere.DeliveryMethod.EQ(cfg.DeliveryMethod),
		models.NotificationWhere.TargetID.EQ(null.StringFrom(cfg.ID)),
		models.NotificationWhere.LastDeliveryTimestamp.IsNotNull(),
		qm.OrderBy(
			fmt.Sprintf(`(
CASE WHEN %s is NULL THEN %s
WHEN %s > %s THEN %s
ELSE %s
END) DESC`,
				models.NotificationColumns.LastDeliveryTimestamp,
				models.NotificationColumns.CreationTimestamp,
				models.NotificationColumns.LastDeliveryTimestamp,
				models.NotificationColumns.CreationTimestamp,
				models.NotificationColumns.LastDeliveryTimestamp,
				models.NotificationColumns.CreationTimestamp,
			),
		),
	).One(ctx)
	if err != nil {
		if !errors.Is(err, sql.ErrNoRows) {
			return nil, errors.Wrap(err, "get notification")
		}
	}

	res, err := mapNotificationConfigToRPCEntity(*cfg, notification)
	if err != nil {
		return nil, errors.Wrap(err, "map notification config")
	}
	return &organizationv1.GetNotificationConfigResponse{
		NotificationConfig: res,
	}, nil
}
