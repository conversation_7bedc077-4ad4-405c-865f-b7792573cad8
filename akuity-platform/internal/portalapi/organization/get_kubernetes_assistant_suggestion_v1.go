package organization

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/sasha<PERSON>nov/go-openai"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"

	"github.com/akuityio/agent/pkg/client"
	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/ai/deprecated"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetKubernetesAssistantSuggestion(ctx context.Context, req *organizationv1.GetKubernetesAssistantSuggestionRequest) (*organizationv1.GetKubernetesAssistantSuggestionResponse, error) {
	enforcer, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	actor := ctxutil.GetActor(ctx)

	instanceSvc := instances.NewServiceWithOptions(s.Db,
		s.Cfg.DomainSuffix,
		s.featSvc,
		instances.WithOrganizationScope(req.GetOrganizationId()),
		instances.WithLogger(logging.Extract(ctx)),
	)

	instanceConfig, err := instanceSvc.ArgoCDInstanceConfigs().GetByID(ctx, req.GetInstanceId())
	if err != nil {
		return nil, fmt.Errorf("get instance config: %w", err)
	}

	if actor.Type == accesscontrol.ActorTypeArgoCD {
		instanceSpec, err := instanceConfig.GetSpec()
		if err != nil {
			return nil, fmt.Errorf("get instance spec: %w", err)
		}
		if !instanceSpec.AssistantExtensionEnabled && !instanceSpec.AkuityIntelligenceExtension.Enabled {
			return nil, status.Error(codes.PermissionDenied, "ai assistant extension is not enabled for this organization")
		}
	}

	internalSpec, err := instanceConfig.GetPrivateSpec()
	if err != nil {
		return nil, fmt.Errorf("get private spec: %w", err)
	}

	instance, err := models.ArgoCDInstances(models.ArgoCDInstanceWhere.ID.EQ(req.GetInstanceId())).One(ctx, s.Db)
	if err != nil {
		return nil, err
	}

	instanceStatus, err := instance.GetStatus()
	if err != nil {
		return nil, err
	}

	if instanceStatus.Info.OpenAI.TokensUsed >= deprecated.OrgTokensLimit {
		return nil, status.Error(codes.ResourceExhausted, "Organization reached OpenAI tokens limit. Please contact support to increase the limit.")
	}

	var token string
	if actor.Type == accesscontrol.ActorTypeArgoCD {
		token = actor.Extras["token"].(string)
	} else {
		token = internalSpec.KubeVisionArgoToken
	}

	var k8sClientset kubernetes.Interface
	var dynamicClient dynamic.Interface
	if req.GetClusterId() == "in-cluster" {
		tnt, err := client.NewArgoCDTenant(s.hostRestConfig, s.Log, instance.ID)
		if err != nil {
			return nil, err
		}
		k8sClientset, err = tnt.ControlPlaneKubeClientset(ctx)
		if err != nil {
			return nil, err
		}
		dynamicClient, err = tnt.ControlPlaneDynamicClientset(ctx)
		if err != nil {
			return nil, err
		}
	} else {
		var err error
		var clusterName string
		if actor.Type == accesscontrol.ActorTypeArgoCD {
			cluster, err := instanceSvc.GetClusterByID(ctx, req.GetClusterId())
			if err != nil {
				return nil, err
			}
			clusterName = cluster.Name
		} else {
			resSvc := k8sresource.NewServiceWithOptions(
				s.RepoSet,
				s.Db,
				req.GetOrganizationId(),
				k8sresource.WithLogger(logging.Extract(ctx)),
				k8sresource.WithEnforcer(enforcer),
			)

			clusterInfo, err := resSvc.ValidateGetRequest(ctx, req.GetInstanceId(), req.GetClusterId())
			if err != nil {
				return nil, err
			}
			clusterName = clusterInfo.Name
		}
		if clusterName == "" {
			return nil, status.Error(codes.InvalidArgument, "cluster not found")
		}

		clusterConfig := instances.ClusterRestConfig(clusterName, instance.ID)
		k8sClientset, err = kubernetes.NewForConfig(clusterConfig)
		if err != nil {
			return nil, err
		}
		dynamicClient, err = dynamic.NewForConfig(clusterConfig)
		if err != nil {
			return nil, err
		}
	}

	argocdClient, err := argocd.NewClient(token, fmt.Sprintf("http://argocd-server.argocd-%s%s", instance.ID, client.GetInClusterServicePostfix()))
	if err != nil {
		return nil, fmt.Errorf("create argocd client: %w", err)
	}

	var app *argocd.Application
	if req.ApplicationName != "" {
		app, err = argocdClient.GetApplication(ctx, req.ApplicationName)
		if err != nil {
			return nil, err
		}
	}

	conversationState := &deprecated.ConversationState{}
	if req.State != "" {
		if err := json.Unmarshal([]byte(req.State), conversationState); err != nil {
			return nil, err
		}
	}
	gv, err := schema.ParseGroupVersion(req.GetResource().GetApiVersion())
	if err != nil {
		return nil, err
	}

	var assistant deprecated.Assistant
	switch s.Cfg.AI.OpenAIAPIKey {
	case "":
		assistant = deprecated.NewNotSupportedAssistant()
	case "fake":
		assistant = deprecated.NewFakeAssistant()
	default:
		assistant = deprecated.NewOpenAIAssistant(s.Cfg.AI.OpenAIAPIKey)
	}

	handler := &deprecated.ResourceHandler{
		ResourceRef: argocd.ResourceRef{Kind: req.Resource.Kind, Name: req.Resource.Name, Namespace: req.Resource.Namespace, Group: gv.Group},
		ResourceGV:  gv,
		ResourceID: &organizationv1.ResourceID{
			ApiVersion: req.GetResource().GetApiVersion(),
			Kind:       req.GetResource().GetKind(),
			Namespace:  req.GetResource().GetNamespace(),
			Name:       req.GetResource().GetName(),
		},
		AppName:        req.ApplicationName,
		OrganizationID: req.GetOrganizationId(),
		InstanceID:     req.GetInstanceId(),
		ResourceUID:    req.GetResourceId(),
		ClusterID:      req.GetClusterId(),
		ArgocdClient:   argocdClient,
		Assistant:      assistant,
		State:          conversationState,
		K8sClientset:   k8sClientset,
		DynamicClient:  dynamicClient,
		App:            app,
	}
	if err := handler.Init(ctx); err != nil {
		return nil, err
	}
	var messages []openai.ChatCompletionMessage
	for _, msg := range req.Messages {
		item := openai.ChatCompletionMessage{Content: msg.Content, Role: msg.Role, Name: msg.Name, ToolCallID: msg.ToolCallId}
		for _, toolCall := range msg.ToolCalls {
			item.ToolCalls = append(item.ToolCalls, openai.ToolCall{
				ID:       toolCall.Id,
				Type:     openai.ToolTypeFunction,
				Function: openai.FunctionCall{Name: toolCall.FunctionName, Arguments: toolCall.Arguments},
			})
		}
		messages = append(messages, item)
	}

	messages, updatedState, err := deprecated.ProcessSuggestions(ctx, messages, handler, instance, &s.Log, s.Db)
	if err != nil {
		return nil, err
	}

	res := &organizationv1.GetKubernetesAssistantSuggestionResponse{State: updatedState}
	for _, msg := range messages {
		item := &organizationv1.AssistantMessage{
			Role:       msg.Role,
			Content:    msg.Content,
			Name:       msg.Name,
			ToolCallId: msg.ToolCallID,
		}
		res.Messages = append(res.Messages, item)
		for _, toolCall := range msg.ToolCalls {
			item.ToolCalls = append(item.ToolCalls, &organizationv1.AssistantToolCall{
				Id: toolCall.ID, FunctionName: toolCall.Function.Name, Arguments: toolCall.Function.Arguments,
			})
		}
	}
	for _, funcDef := range handler.GetFunctions(ctx) {
		res.SuggestedQuestions = append(res.SuggestedQuestions, funcDef.SamplePrompt)
	}

	return res, nil
}
