package organization

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"google.golang.org/genproto/googleapis/api/httpbody"
	"google.golang.org/genproto/googleapis/rpc/http"
	"google.golang.org/protobuf/types/known/anypb"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListKubernetesContainers(ctx context.Context, req *organizationv1.ListKubernetesContainersRequest,
) (*organizationv1.ListKubernetesContainersResponse, error) {
	enforcer, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	txDB, _ := database.WithTxBeginner(s.Db)
	rs := client.NewRepoSet(txDB)
	resSvc := k8sresource.NewServiceWithOptions(rs, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)), k8sresource.WithEnforcer(enforcer))

	offset := int(req.GetOffset())
	limit := int(req.GetLimit())
	if limit == 0 {
		limit = 100
	}

	clusterInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), false, req.GetClusterIds()...)
	if err != nil {
		return nil, err
	}
	clusterIDs := clusterInfo.GetClusterIDs()
	containers, err := resSvc.ListContainerResources(ctx, req.GetInstanceId(), req.GetPodId(), clusterIDs, req.GetImage(), req.GetImageTag(), req.GetImageDigest(), req.GetOrderBy(), offset, limit, req.GetNameContains(), req.GetStatus(), req.GetType())
	if err != nil {
		return nil, err
	}
	count, err := resSvc.CountContainerResources(ctx, req.GetInstanceId(), req.GetPodId(), clusterIDs, req.GetImage(), req.GetImageTag(), req.GetImageDigest(), req.GetNameContains(), req.GetStatus(), req.GetType())
	if err != nil {
		return nil, fmt.Errorf("failed to count containers: %w", err)
	}
	return &organizationv1.ListKubernetesContainersResponse{
		Containers: mapContainersToRPCEntity(containers),
		Count:      uint32(count),
	}, nil
}

func (s *OrganizationV1Server) ListKubernetesContainersToCSV(
	req *organizationv1.ListKubernetesContainersRequest,
	srv organizationv1.OrganizationService_ListKubernetesContainersToCSVServer,
) error {
	ctx := srv.Context()

	_, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return err
	}

	txDB, _ := database.WithTxBeginner(s.Db)
	rs := client.NewRepoSet(txDB)
	resSvc := k8sresource.NewServiceWithOptions(rs, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)))

	clusterInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), false, req.GetClusterIds()...)
	if err != nil {
		return err
	}
	clusterIDs := clusterInfo.GetClusterIDs()

	offset := 0
	limit := 100
	httpHeaders, err := anypb.New(&http.HttpHeader{
		Key:   "Content-Disposition",
		Value: fmt.Sprintf("attachment; filename=containers_%d.csv", time.Now().UTC().Unix()),
	})
	if err != nil {
		return err
	}

	body := &httpbody.HttpBody{
		ContentType: "text/csv",
		Data: []byte(strings.Join([]string{
			"Instance",
			"Cluster",
			"Container Name",
			"Pod ID",
			"Pod Name",
			"Status",
			"Type",
			"CPU usage",
			"Memory usage",
			"CPU Limit",
			"Memory Limit",
			"CPU Request",
			"Memory Request",
			"Age",
		}, ",")),
		Extensions: []*anypb.Any{httpHeaders},
	}
	err = srv.Send(body)
	if err != nil {
		return err
	}

	count, err := resSvc.CountContainerResources(ctx, req.GetInstanceId(), req.GetPodId(), clusterIDs, req.GetImage(), req.GetImageTag(), req.GetImageDigest(), req.GetNameContains(), req.GetStatus(), req.GetType())
	if err != nil {
		return fmt.Errorf("failed to count containers: %w", err)
	}
	for {
		containers, err := resSvc.ListContainerResources(ctx, req.GetInstanceId(), req.GetPodId(), clusterIDs, req.GetImage(), req.GetImageTag(), req.GetImageDigest(), req.GetOrderBy(), offset, limit, req.GetNameContains(), req.GetStatus(), req.GetType())
		if err != nil {
			return err
		}
		rpcEntities := mapContainersToRPCEntity(containers)
		rows := lo.Map(rpcEntities, func(entity *organizationv1.Container, idx int) string {
			var age string
			if entity.StartTime != nil {
				t, err := time.Parse(time.RFC3339, *entity.StartTime)
				if err == nil {
					age = time.Since(t).String()
				}
			}

			cluster := clusterInfo.GetCluster(entity.ClusterId)
			if cluster == nil {
				return ""
			}

			row := []string{
				cluster.InstanceName,
				cluster.Name,
				entity.Name,
				entity.PodId,
				entity.PodName,
				entity.Status.String(),
				entity.Type.String(),
				floatPtrToString(entity.CpuUsage),
				floatPtrToString(entity.MemoryUsage),
				floatPtrToString(entity.CpuLimit),
				floatPtrToString(entity.MemoryLimit),
				floatPtrToString(entity.CpuRequest),
				floatPtrToString(entity.MemoryRequest),
				age,
			}
			return strings.Join(row, ",")
		})

		body := &httpbody.HttpBody{
			ContentType: "text/csv",
			Data:        []byte(strings.Join(rows, "\n")),
			Extensions:  []*anypb.Any{httpHeaders},
		}
		err = srv.Send(body)
		if err != nil {
			return err
		}

		offset += limit
		if int64(offset) >= count {
			break
		}
	}
	return nil
}

func floatPtrToString(input *float64) string {
	if input == nil || *input == 0 {
		return ""
	}
	return fmt.Sprintf("%f", *input)
}

func mapContainerToRPCEntity(container, pod *models.ArgoCDClusterK8SObject) (*organizationv1.Container, error) {
	contCol := k8sresource.ContainerColumns{}
	if err := container.Columns.Unmarshal(&contCol); err != nil {
		return nil, err
	}

	state := contCol.State

	ct := organizationv1.ContainerType_CONTAINER_TYPE_CONTAINER
	switch contCol.ContainerType {
	case "initContainer":
		ct = organizationv1.ContainerType_CONTAINER_TYPE_INIT_CONTAINER
	case "sidecarContainer":
		ct = organizationv1.ContainerType_CONTAINER_TYPE_SIDECAR_CONTAINER
	case "ephemeralContainer":
		ct = organizationv1.ContainerType_CONTAINER_TYPE_EPHEMERAL_CONTAINER
	}

	cs := organizationv1.ContainerStatus_CONTAINER_STATUS_UNSPECIFIED
	switch state {
	case "running":
		cs = organizationv1.ContainerStatus_CONTAINER_STATUS_RUNNING
	case "waiting":
		cs = organizationv1.ContainerStatus_CONTAINER_STATUS_PENDING
	case "completed":
		cs = organizationv1.ContainerStatus_CONTAINER_STATUS_COMPLETED
	case "terminated":
		cs = organizationv1.ContainerStatus_CONTAINER_STATUS_ERROR
	}

	item := &organizationv1.Container{
		Name:          container.Name,
		Status:        cs,
		Type:          ct,
		CpuLimit:      contCol.LimitsCPU,
		CpuRequest:    contCol.RequestsCPU,
		MemoryLimit:   contCol.LimitsMemory,
		MemoryRequest: contCol.RequestsMemory,
		Image:         contCol.Image,
		ImageTag:      contCol.Tag,
		PodName:       contCol.PodName,
		PodId:         container.OwnerID.String,
		ClusterId:     container.ClusterID,
		InstanceId:    container.InstanceID,
		Id:            container.ID,
		ImageDigest:   contCol.Digest,
		CpuUsage:      contCol.UsageCPU,
		MemoryUsage:   contCol.UsageMemory,
		StartTime:     contCol.StartTime,
	}
	if pod != nil {
		podColumns := &k8sresource.PodColumns{}
		if err := pod.Columns.Unmarshal(podColumns); err != nil {
			return nil, err
		}
		item.NodeName = &podColumns.NodeName
	}
	return item, nil
}

func mapContainersToRPCEntity(containers []*models.ArgoCDClusterK8SObject) []*organizationv1.Container {
	res := make([]*organizationv1.Container, 0, len(containers))
	for _, container := range containers {
		item, err := mapContainerToRPCEntity(container, nil)
		if err != nil {
			continue
		}
		res = append(res, item)
	}
	return res
}
