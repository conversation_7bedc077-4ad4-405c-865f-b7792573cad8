package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/permissions"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListAuthenticatedUserOrganizations(
	ctx context.Context,
	req *organizationv1.ListAuthenticatedUserOrganizationsRequest,
) (*organizationv1.ListAuthenticatedUserOrganizationsResponse, error) {
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	switch actor.Type {
	case accesscontrol.ActorTypeAPIKey:
		orgs := make([]*organizationv1.Organization, 0, len(actor.OrganizationBindings))
		for _, binding := range actor.OrganizationBindings {
			org, err := os.Get(ctx, binding.OrganizationID)
			if err != nil {
				return nil, err
			}

			quota, err := s.featSvc.GetOrgQuotas(ctx, org.GetID())
			if err != nil {
				return nil, err
			}

			orgResp, err := mapOrganizationToRPCEntity(*org, quota, binding.Permissions)
			if err != nil {
				return nil, err
			}
			orgs = append(orgs, orgResp)
		}
		return &organizationv1.ListAuthenticatedUserOrganizationsResponse{
			Organizations: orgs,
		}, nil

	case accesscontrol.ActorTypeUser:
		orgSummaries, err := os.ListUserOrganizations(ctx, actor.ID)
		if err != nil {
			return nil, err
		}
		orgs := make([]*organizationv1.Organization, 0, len(orgSummaries))
		for _, org := range orgSummaries {
			quota, err := s.featSvc.GetOrgQuotas(ctx, org.GetID())
			if err != nil {
				return nil, err
			}

			orgResp, err := mapOrganizationToRPCEntity(
				org.Organization,
				quota,
				permissions.Permissions{
					Roles: []permissions.Role{
						permissions.Role(org.Role),
					},
				})
			if err != nil {
				return nil, err
			}
			orgs = append(orgs, orgResp)
		}
		return &organizationv1.ListAuthenticatedUserOrganizationsResponse{
			Organizations: orgs,
		}, nil

	default:
		return nil, status.Errorf(codes.InvalidArgument, "unsupported actor type: %q", actor.Type)
	}
}
