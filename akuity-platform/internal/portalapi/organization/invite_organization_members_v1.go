package organization

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"gopkg.in/gomail.v2"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	"github.com/akuityio/akuity-platform/internal/services/permissions"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/users"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) InviteMembers(
	ctx context.Context,
	req *organizationv1.InviteMembersRequest,
) (*organizationv1.InviteMembersResponse, error) {
	userSvc := users.NewService(s.RepoSet, s.Cfg.UsageLimits, &s.Log)
	_, actor, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetId(),
		accesscontrol.NewActionCreateOrganizationMembers(accesscontrol.ResourceAny))
	if err != nil {
		return nil, err
	}

	emails := req.GetEmails()
	if len(emails) == 0 {
		return nil, status.Error(codes.InvalidArgument, "no emails provided")
	}
	if len(emails) > s.Cfg.UsageLimits.MaxInvitationsPerBatch {
		return nil, status.Errorf(
			codes.InvalidArgument,
			"You can only invite up to %d members at a time",
			s.Cfg.UsageLimits.MaxInvitationsPerBatch,
		)
	}
	for _, email := range emails {
		emailSegments := strings.Split(email, "@")
		if len(emailSegments) != 2 {
			return nil, status.Errorf(codes.InvalidArgument, "invalid email: %q", email)
		}
	}
	role := organizations.Role(req.GetRole())
	if !organizations.IsValidRole(role) {
		return nil, status.Errorf(codes.InvalidArgument, "invalid role: %q", role)
	}

	org, err := s.RepoSet.Organizations().GetByID(ctx, req.GetId())
	if err != nil {
		return nil, err
	}
	subjectName := org.Name
	inviterEmail := ""
	switch actor.Type {
	case accesscontrol.ActorTypeUser:
		actorUser, err := userSvc.GetUser(ctx, actor.ID)
		if err != nil {
			return nil, err
		}
		actorUserInfo, err := actorUser.GetUserInfo()
		if err != nil {
			return nil, err
		}
		inviterEmail = actorUser.Email
		if actorUserInfo.GivenName != "" {
			subjectName = actorUserInfo.GivenName
		}
	case accesscontrol.ActorTypeAPIKey:
		inviterEmail = "API Key: " + actor.ID
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)

	joinWS := map[string]permissions.Role{}
	joinTeams := []string{}
	if info := req.GetInfo(); info != nil {
		if role == organizations.RoleOwner {
			return nil, status.Errorf(codes.InvalidArgument, "owner already has access to all teams and workspaces")
		}
		teamSvc := teams.NewService(s.Db)
		workspaceSvc := workspaces.NewService(s.Db, teamSvc, s.Cfg.FeatureGatesSource)
		for _, ws := range info.Workspaces {
			if _, err := workspaceSvc.GetWorkspaceByName(ctx, org.ID, ws.GetName()); err != nil {
				if errors.Is(err, sql.ErrNoRows) {
					return nil, status.Errorf(codes.InvalidArgument, "workspace not found %q", ws.GetName())
				}
				return nil, status.Error(codes.Internal, "failed to get workspace")
			}
			var role permissions.Role
			switch ws.GetRole() {
			case organizationv1.WorkspaceMemberRole_WORKSPACE_MEMBER_ROLE_MEMBER:
				role = permissions.RoleWorkspaceMember
			case organizationv1.WorkspaceMemberRole_WORKSPACE_MEMBER_ROLE_ADMIN:
				role = permissions.RoleWorkspaceAdmin
			default:
				return nil, status.Errorf(codes.InvalidArgument, "invalid role %q", ws.GetRole().String())
			}
			joinWS[ws.GetName()] = role
		}
		for _, teamName := range info.Teams {
			if _, err := teamSvc.GetTeam(ctx, org.ID, teamName); err != nil {
				return nil, err
			}
		}
		joinTeams = info.Teams
	}
	failedToSend := false
	for _, email := range emails {
		invitation, err := os.Invite(ctx, req.GetId(), inviterEmail, email, role, joinWS, joinTeams, time.Now)
		if err != nil {
			if organizations.IsConflictErr(err) {
				return nil, status.Error(codes.FailedPrecondition, err.Error())
			}
			return nil, status.Error(codes.Internal, err.Error())
		}

		if invitation != nil {
			m := gomail.NewMessage()
			m.SetAddressHeader("From", s.Cfg.SMTP.InviteEmail, "Akuity Platform")
			m.SetHeader("To", invitation.InviteeEmail)
			// https://us-west-2.console.aws.amazon.com/ses/home?region=us-west-2#/configuration-sets/ses-history
			m.SetHeader("X-SES-CONFIGURATION-SET", "ses-history")

			m.SetHeader("Subject", subjectName+" invited you to Akuity Platform!")
			joinURL := s.Cfg.PortalURL + "/organizations/join/" + invitation.OrganizationID
			m.SetBody("text/html", "Hello,<br><br>You have been invited to join <b>"+org.Name+"</b> on Akuity Platform!<br><br>Click <a href=\""+joinURL+"\">here</a> to accept the invitation.<br><br>Thanks,<br>The Akuity Team")

			if err := s.mailer.DialAndSend(m); err != nil {
				s.Log.Error(err, fmt.Sprintf("error sending email to %s", invitation.InviteeEmail))
				failedToSend = true
			}
			continue
		}
		return nil, fmt.Errorf("could not send invite email, nil invite")
	}

	if failedToSend {
		return nil, status.Error(codes.Internal, "failed to send one or more invitation emails. Please try again, or give members their invite code manually.")
	}
	return &organizationv1.InviteMembersResponse{}, nil
}
