package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetTeam(
	ctx context.Context,
	req *organizationv1.GetTeamRequest,
) (*organizationv1.GetTeamResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.OrganizationId,
		accesscontrol.NewActionGetTeams(req.GetName()))
	if err != nil {
		return nil, err
	}

	if err := s.isTeamEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	actor := ctxutil.GetActor(ctx)
	if actor == nil || actor.Type != accesscontrol.ActorTypeUser {
		return nil, shared.ErrUnauthenticated
	}

	teamSvc := teams.NewService(s.Db)
	userTeam, err := teamSvc.GetUserTeam(ctx, req.GetOrganizationId(), actor.ID, req.GetName())
	if err != nil {
		return nil, err
	}

	return &organizationv1.GetTeamResponse{
		UserTeam: newUserTeam(userTeam),
	}, nil
}
