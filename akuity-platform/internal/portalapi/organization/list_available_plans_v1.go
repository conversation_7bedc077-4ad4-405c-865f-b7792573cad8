package organization

import (
	"context"

	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListAvailablePlans(
	ctx context.Context,
	req *organizationv1.ListAvailablePlansRequest,
) (*organizationv1.ListAvailablePlansResponse, error) {
	plans, err := s.RepoSet.OrganizationPlans().ListAll(ctx)
	if err != nil {
		return nil, err
	}

	rpcPlans := make([]*organizationv1.Plan, 0, len(plans))
	for _, plan := range plans {
		rpcPlan := &organizationv1.Plan{
			Name:       plan.Name,
			Deprecated: plan.Deprecated.Bool,
		}

		rpcPlans = append(rpcPlans, rpcPlan)
	}

	return &organizationv1.ListAvailablePlansResponse{
		Plans: rpcPlans,
	}, nil
}
