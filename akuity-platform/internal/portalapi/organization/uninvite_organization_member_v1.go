package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) UninviteOrganizationMember(
	ctx context.Context,
	req *organizationv1.UninviteOrganizationMemberRequest,
) (*organizationv1.UninviteOrganizationMemberResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetId(),
		accesscontrol.NewActionDeleteOrganizationMembers(req.GetEmail())); err != nil {
		return nil, err
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	if err := os.WithdrawInvitation(ctx, req.GetId(), req.GetEmail()); err != nil {
		return nil, err
	}
	return &organizationv1.UninviteOrganizationMemberResponse{}, nil
}
