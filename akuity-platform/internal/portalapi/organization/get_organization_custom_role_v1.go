package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/customroles"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetCustomRole(
	ctx context.Context,
	req *organizationv1.GetCustomRoleRequest,
) (*organizationv1.GetCustomRoleResponse, error) {
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetCustomRoles().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "custom roles feature is not enabled")
	}

	_, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.OrganizationId,
		accesscontrol.NewActionGetCustomRole(req.GetId()))
	if err != nil {
		return nil, err
	}

	role, err := s.crs.GetCustomRole(ctx, req.OrganizationId, req.GetId())
	if err != nil {
		if customroles.IsNotFoundErr(err) {
			return nil, shared.ErrNotFound
		}
		return nil, err
	}
	return &organizationv1.GetCustomRoleResponse{
		CustomRole: newCustomRole(role),
	}, nil
}
