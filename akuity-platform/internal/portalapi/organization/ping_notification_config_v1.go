package organization

import (
	"context"
	"database/sql"

	"github.com/pkg/errors"
	"github.com/volatiletech/null/v8"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) PingNotificationConfig(
	ctx context.Context,
	req *organizationv1.PingNotificationConfigRequest,
) (*organizationv1.PingNotificationConfigResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(
		ctx,
		s.Db,
		s.acs,
		req.OrganizationId,
		accesscontrol.NewActionUpdateOrgNotificationConfig(req.GetId()),
	)
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetNotification().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "notification feature is not enabled")
	}

	if req.GetId() == "" {
		return nil, status.Error(codes.InvalidArgument, "id must not be empty")
	}
	cfg, err := s.RepoSet.OrganizationNotificationConfigs(
		models.OrganizationNotificationConfigWhere.OrganizationID.EQ(
			req.GetOrganizationId(),
		),
	).GetByID(ctx, req.GetId())
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, status.Error(codes.NotFound, "config not found")
		}
		return nil, errors.Wrap(err, "get notification config")
	}
	if cfg.DeliveryMethod != models.DeliveryMethodWebhook {
		return nil, status.Errorf(codes.InvalidArgument, "unsupported delivery method %q", cfg.DeliveryMethod)
	}

	event := &models.Event{
		EventType:      null.StringFrom(models.EventTypePing),
		OrganizationID: null.StringFrom(req.GetOrganizationId()),
	}
	if err := event.SetMetadata(models.EventMetadata{
		Ping: &models.Ping{
			NotificationConfigID: cfg.GetID(),
		},
	}); err != nil {
		return nil, errors.Wrap(err, "set event metadata")
	}

	if err := s.RepoSet.Events().Create(ctx, event); err != nil {
		return nil, errors.Wrap(err, "create notification")
	}
	return &organizationv1.PingNotificationConfigResponse{
		// Explicitly empty
	}, nil
}
