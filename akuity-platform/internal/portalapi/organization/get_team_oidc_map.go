package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetTeamOIDCMap(ctx context.Context, req *organizationv1.GetTeamOIDCMapRequest) (*organizationv1.GetTeamOIDCMapResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetOIDCMap(req.GetOrganizationId())); err != nil {
		return nil, err
	}
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetSso().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "sso feature is not enabled")
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	org, err := os.Get(ctx, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}
	oidcMap, err := org.GetOIDCTeamMap()
	if err != nil {
		return nil, err
	}

	return &organizationv1.GetTeamOIDCMapResponse{
		Entries: oidcMap,
	}, nil
}
