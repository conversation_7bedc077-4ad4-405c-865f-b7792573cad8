package organization

import (
	"context"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	"github.com/akuityio/akuity-platform/internal/services/users"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) JoinOrganization(
	ctx context.Context,
	req *organizationv1.JoinOrganizationRequest,
) (*organizationv1.JoinOrganizationResponse, error) {
	userSvc := users.NewService(s.RepoSet, s.Cfg.UsageLimits, &s.Log)
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}

	user, err := userSvc.GetUser(ctx, actor.ID)
	if err != nil {
		return nil, err
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	org, err := os.AcceptInvitation(ctx, req.GetId(), user.ID, user.Email, time.Now)
	if err != nil {
		if organizations.IsInvitationExpiredErr(err) || organizations.IsInvitationConflictErr(err) {
			return nil, status.Error(codes.FailedPrecondition, err.Error())
		}
		if organizations.IsNotFoundErr(err) {
			return nil, status.Error(codes.NotFound, err.Error())
		}
		return nil, err
	}

	status, err := org.GetOrgStatus()
	if err != nil {
		return nil, err
	}
	return &organizationv1.JoinOrganizationResponse{
		Organization: &organizationv1.Organization{
			Id:              org.ID,
			Name:            org.Name,
			MaxInstances:    int64(org.MaxInstances),
			MaxClusters:     int64(org.MaxClusters),
			MaxApplications: int64(org.MaxApplications),
			Status: &organizationv1.OrganizationStatus{
				Trial:  status.Trial,
				Expiry: uint64(status.ExpiryTime),
				State:  string(status.State),
			},
		},
	}, nil
}
