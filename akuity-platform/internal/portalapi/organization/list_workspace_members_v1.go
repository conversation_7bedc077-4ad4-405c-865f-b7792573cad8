package organization

import (
	"context"

	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListWorkspaceMembers(
	ctx context.Context,
	req *organizationv1.ListWorkspaceMembersRequest,
) (*organizationv1.ListWorkspaceMembersResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.OrganizationId,
		accesscontrol.NewActionGetWorkspaceMembers(req.GetWorkspaceId(), accesscontrol.ResourceAny))
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetWorkspaces().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "workspaces feature is not enabled")
	}

	offset := int(req.GetOffset())
	limit := int(req.GetLimit())
	if limit == 0 {
		// Set default for limit
		limit = 100
	}

	teamSvc := teams.NewService(s.Db)
	workspaceSvc := workspaces.NewService(s.Db, teamSvc, s.Cfg.FeatureGatesSource)
	members, err := workspaceSvc.ListWorkspaceMembers(ctx, req.GetWorkspaceId(), offset, limit)
	if err != nil {
		return nil, errors.Wrap(err, "list workspace members")
	}
	resp := &organizationv1.ListWorkspaceMembersResponse{
		WorkspaceMembers: make([]*organizationv1.WorkspaceMember, len(members)),
		TeamMemberCount:  0,
		UserMemberCount:  0,
	}
	for idx, m := range members {
		resp.WorkspaceMembers[idx] = mapWorkspaceMemberToRPCEntity(m)
		switch m.Ref.(type) {
		case *models.AkuityUser:
			resp.UserMemberCount += 1
		case *teams.Team:
			resp.TeamMemberCount += 1
		}
	}
	return resp, nil
}
