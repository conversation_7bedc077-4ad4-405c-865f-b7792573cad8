package organization

import (
	"context"
	"slices"
	"strings"

	"github.com/auth0/go-auth0/management"
	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/auth0"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetSSOConfiguration(
	ctx context.Context,
	req *organizationv1.GetSSOConfigurationRequest,
) (*organizationv1.GetSSOConfigurationResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(
		ctx, s.Db, s.acs, req.GetId(), accesscontrol.NewActionGetOrganizationSSOConfiguration()); err != nil {
		return nil, err
	}

	id := req.GetId()
	if !s.featSvc.GetFeatureStatuses(ctx, &id).GetSso().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "sso feature is not enabled")
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	org, err := os.Get(ctx, req.GetId())
	if err != nil {
		return nil, errors.Wrap(err, "get organization")
	}
	res, err := s.Auth0Client.GetConnection(ctx, req.GetId())
	if err != nil {
		if auth0.IsNotFoundError(err) {
			return &organizationv1.GetSSOConfigurationResponse{
				AutoAddMember: org.SsoAutoAddMember.Bool,
			}, nil
		}
		return nil, err
	}

	resp := &organizationv1.GetSSOConfigurationResponse{
		AutoAddMember: org.SsoAutoAddMember.Bool,
	}
	switch typedOpts := res.GetOptions().(type) {
	case *management.ConnectionOptionsAzureAD:
		resp.Options = &organizationv1.GetSSOConfigurationResponse_AzureAd{
			AzureAd: mapAzureADConnectionOptionsToRPCEntity(typedOpts),
		}
	case *management.ConnectionOptionsGoogleApps:
		resp.Options = &organizationv1.GetSSOConfigurationResponse_GoogleWorkspace{
			GoogleWorkspace: mapGoogleWorkspaceConnectionToRPCEntity(typedOpts),
		}
	case *management.ConnectionOptionsOIDC:
		resp.Options = &organizationv1.GetSSOConfigurationResponse_Oidc{
			Oidc: mapOIDCConnectionToRPCEntity(typedOpts),
		}
	case *management.ConnectionOptionsOkta:
		resp.Options = &organizationv1.GetSSOConfigurationResponse_Okta{
			Okta: mapOktaConnectionOptionsToRPCEntity(res.TenantDomain, typedOpts),
		}
	case *management.ConnectionOptionsSAML:
		resp.Options = &organizationv1.GetSSOConfigurationResponse_Saml{
			Saml: mapSAMLConnectionToRPCEntity(typedOpts),
		}
	default:
		return nil, status.Errorf(codes.Internal, "unknown connection type: %T", typedOpts)
	}
	return resp, nil
}

func mapAzureADConnectionOptionsToRPCEntity(opt *management.ConnectionOptionsAzureAD) *organizationv1.AzureADSSOOptions {
	var domainAlias string
	if len(opt.GetDomainAliases()) > 0 {
		domainAlias = opt.GetDomainAliases()[0]
	}
	return &organizationv1.AzureADSSOOptions{
		ClientId:      opt.GetClientID(),
		ClientSecret:  "", // Explicitly empty for the security purpose
		AzureAdDomain: opt.GetDomain(),
		DomainAlias:   domainAlias,
		DomainAliases: opt.GetDomainAliases(),
	}
}

func mapGoogleWorkspaceConnectionToRPCEntity(opt *management.ConnectionOptionsGoogleApps) *organizationv1.GoogleWorkspaceSSOOptions {
	return &organizationv1.GoogleWorkspaceSSOOptions{
		ClientId:              opt.GetClientID(),
		ClientSecret:          "", // Explicitly empty for the security purpose
		GoogleWorkspaceDomain: opt.GetDomain(),
		DomainAliases:         opt.GetDomainAliases(),
	}
}

func mapOIDCConnectionToRPCEntity(opt *management.ConnectionOptionsOIDC) *organizationv1.OIDCSSOOptions {
	ssoOpts := &organizationv1.OIDCSSOOptions{
		DiscoveryUrl:       opt.GetDiscoveryURL(),
		ClientId:           opt.GetClientID(),
		Domain:             opt.GetTenantDomain(),
		DomainAliases:      opt.GetDomainAliases(),
		GroupsScopeEnabled: slices.Contains(strings.Split(opt.GetScope(), " "), "groups"),
	}

	switch opt.GetType() {
	case auth0.OIDCBackChannelType:
		ssoOpts.Channel = &organizationv1.OIDCSSOOptions_Back{
			Back: &organizationv1.OIDCSSOBackChannel{
				ClientSecret:          "", // Explicitly empty for the security purpose
				Issuer:                opt.GetIssuer(),
				AuthorizationEndpoint: opt.GetAuthorizationEndpoint(),
				TokenEndpoint:         opt.GetTokenEndpoint(),
				JwksUri:               opt.GetJWKSURI(),
			},
		}
	case auth0.OIDCFrontChannelType:
		ssoOpts.Channel = &organizationv1.OIDCSSOOptions_Front{
			Front: &organizationv1.OIDCSSOFrontChannel{
				Issuer:                opt.GetIssuer(),
				AuthorizationEndpoint: opt.GetAuthorizationEndpoint(),
				JwksUri:               opt.GetJWKSURI(),
			},
		}
	}
	return ssoOpts
}

func mapOktaConnectionOptionsToRPCEntity(
	domain string,
	opt *management.ConnectionOptionsOkta,
) *organizationv1.OktaSSOOptions {
	var domainAlias string
	if len(opt.GetDomainAliases()) > 0 {
		domainAlias = opt.GetDomainAliases()[0]
	}

	return &organizationv1.OktaSSOOptions{
		ClientId:      opt.GetClientID(),
		ClientSecret:  "", // Explicitly empty for the security purpose
		OktaDomain:    domain,
		DomainAlias:   domainAlias,
		DomainAliases: opt.GetDomainAliases(),
	}
}

func mapSAMLConnectionToRPCEntity(opt *management.ConnectionOptionsSAML) *organizationv1.SAMLSSOOptions {
	ssoOpts := &organizationv1.SAMLSSOOptions{
		Domain:        opt.GetTenantDomain(),
		DomainAliases: opt.GetDomainAliases(),
	}
	if opt.GetMetadataXML() != "" {
		ssoOpts.Options = &organizationv1.SAMLSSOOptions_MetadataXml{
			MetadataXml: opt.GetMetadataXML(),
		}
		return ssoOpts
	}

	signatureAlgorithm := organizationv1.SAMLSignatureAlgorithm_SAML_SIGNATURE_ALGORITHM_UNSPECIFIED
	switch opt.GetSignatureAlgorithm() {
	case string(auth0.SAMLSignatureAlgorithmSHA256):
		signatureAlgorithm = organizationv1.SAMLSignatureAlgorithm_SAML_SIGNATURE_ALGORITHM_RSA_SHA256
	case string(auth0.SAMLSignatureAlgorithmSHA1):
		signatureAlgorithm = organizationv1.SAMLSignatureAlgorithm_SAML_SIGNATURE_ALGORITHM_RSA_SHA1
	}
	digestAlgorithm := organizationv1.SAMLDigestAlgorithm_SAML_DIGEST_ALGORITHM_UNSPECIFIED
	switch opt.GetDigestAglorithm() {
	case string(auth0.SAMLDigestAlgorithmSHA256):
		digestAlgorithm = organizationv1.SAMLDigestAlgorithm_SAML_DIGEST_ALGORITHM_SHA256
	case string(auth0.SAMLDigestAlgorithmSHA1):
		digestAlgorithm = organizationv1.SAMLDigestAlgorithm_SAML_DIGEST_ALGORITHM_SHA1
	}
	protocolBinding := organizationv1.SAMLProtocolBinding_SAML_PROTOCOL_BINDING_UNSPECIFIED
	switch opt.GetProtocolBinding() {
	case string(auth0.SAMLProtocolBindingHTTPRedirect):
		protocolBinding = organizationv1.SAMLProtocolBinding_SAML_PROTOCOL_BINDING_HTTP_REDIRECT
	case string(auth0.SAMLProtocolBindingHTTPPOST):
		protocolBinding = organizationv1.SAMLProtocolBinding_SAML_PROTOCOL_BINDING_HTTP_POST
	}
	ssoOpts.Options = &organizationv1.SAMLSSOOptions_ConnectionDetails{
		ConnectionDetails: &organizationv1.SAMLSSOConnectionDetails{
			SignInEndpoint:           opt.GetSignInEndpoint(),
			DisableSignOut:           opt.GetDisableSignOut(),
			SignOutEndpoint:          opt.SignOutEndpoint,
			Base64EncodedSigningCert: opt.GetSigningCert(),
			SignRequest:              opt.GetSignSAMLRequest(),
			SignatureAlgorithm:       signatureAlgorithm,
			DigestAlgorithm:          digestAlgorithm,
			ProtocolBinding:          protocolBinding,
		},
	}
	return ssoOpts
}
