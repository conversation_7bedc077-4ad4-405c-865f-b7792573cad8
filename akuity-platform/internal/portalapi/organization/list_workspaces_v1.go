package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListWorkspaces(
	ctx context.Context,
	req *organizationv1.ListWorkspacesRequest,
) (*organizationv1.ListWorkspacesResponse, error) {
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.Db, s.acs, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetWorkspaces().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "workspaces feature is not enabled")
	}

	offset := int(req.GetOffset())
	limit := int(req.GetLimit())
	if limit == 0 {
		// Set default for limit
		limit = 100
	}

	teamSvc := teams.NewService(s.Db)
	workspaceSvc := workspaces.NewService(s.Db, teamSvc, s.Cfg.FeatureGatesSource)
	wss, err := workspaceSvc.ListWorkspaces(ctx, req.GetOrganizationId(), offset, limit)
	if err != nil {
		return nil, err
	}
	workspaces := []*organizationv1.Workspace{}
	for _, ws := range wss {
		action := accesscontrol.NewActionGetOrganizationWorkspaces(ws.ID)
		ok, err := enforcer.EnforceAction(ctx, action)
		if err != nil {
			return nil, err
		}
		if !ok {
			continue
		}
		v1ws, err := mapWorkspaceToRPCEntity(ctx, ws, enforcer)
		if err != nil {
			return nil, err
		}
		workspaces = append(workspaces, v1ws)
	}
	return &organizationv1.ListWorkspacesResponse{
		Workspaces: workspaces,
	}, nil
}
