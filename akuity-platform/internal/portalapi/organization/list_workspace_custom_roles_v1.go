package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListWorkspaceCustomRoles(
	ctx context.Context,
	req *organizationv1.ListWorkspaceCustomRolesRequest,
) (*organizationv1.ListWorkspaceCustomRolesResponse, error) {
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetCustomRoles().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "custom roles feature is not enabled")
	}

	_, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.OrganizationId,
		// we don't support per resource permission yet
		accesscontrol.NewActionGetWorkspaceCustomRole(req.WorkspaceId, accesscontrol.ResourceAny))
	if err != nil {
		return nil, err
	}

	limit := req.GetLimit()
	offset := req.GetOffset()
	if limit == 0 {
		limit = customRoleDefaultLimit
	} else if limit > customRoleMaxLimit {
		limit = customRoleMaxLimit
	}

	roles, err := s.crs.ListWorkspaceCustomRoles(ctx, req.OrganizationId, req.WorkspaceId, limit, offset)
	if err != nil {
		return nil, err
	}

	count, err := s.crs.CountCustomRole(ctx, req.OrganizationId, req.WorkspaceId)
	if err != nil {
		return nil, err
	}
	return &organizationv1.ListWorkspaceCustomRolesResponse{
		CustomRoles: newCustomRoles(roles),
		TotalCount:  count,
		WorkspaceId: req.WorkspaceId,
	}, nil
}
