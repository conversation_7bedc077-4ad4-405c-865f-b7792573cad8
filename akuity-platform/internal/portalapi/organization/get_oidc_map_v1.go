package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetOIDCMap(ctx context.Context, req *organizationv1.GetOIDCMapRequest) (*organizationv1.GetOIDCMapResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetId(),
		accesscontrol.NewActionGetOIDCMap(req.GetId())); err != nil {
		return nil, err
	}
	orgID := req.GetId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetSso().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "sso feature is not enabled")
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	org, err := os.Get(ctx, req.GetId())
	if err != nil {
		return nil, err
	}
	oidcMap, err := org.GetOrgOIDCMap()
	if err != nil {
		return nil, err
	}

	return &organizationv1.GetOIDCMapResponse{
		Entries: oidcMap,
	}, nil
}
