package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/ai"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListAIConversationSuggestions(
	ctx context.Context,
	req *organizationv1.ListAIConversationSuggestionsRequest,
) (*organizationv1.ListAIConversationSuggestionsResponse, error) {
	_, err := s.checkAISupportEngineerPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(), req.GetKargoInstanceId(),
		accesscontrol.NewActionGetAISupportEngineer(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	featureStatuses := s.featSvc.GetFeatureStatuses(ctx, &orgID)
	aiSvc, err := ai.NewService(s.Db, s.RepoSet, featureStatuses, orgID, s.Cfg.AI, s.hostRestConfig, logging.Extract(ctx))
	if err != nil {
		return nil, err
	}

	suggestions, err := aiSvc.ListConversationSuggestions(ctx, req.GetConversationId(), req.GetContexts())
	if err != nil {
		return nil, err
	}

	return &organizationv1.ListAIConversationSuggestionsResponse{
		Suggestions: suggestions,
	}, nil
}
