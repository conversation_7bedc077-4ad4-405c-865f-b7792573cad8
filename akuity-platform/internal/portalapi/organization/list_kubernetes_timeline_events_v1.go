package organization

import (
	"context"
	"time"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListKubernetesTimelineEvents(ctx context.Context, req *organizationv1.ListKubernetesTimelineEventsRequest) (*organizationv1.ListKubernetesTimelineEventsResponse, error) {
	_, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	resSvc := k8sresource.NewServiceWithOptions(s.RepoSet, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)))

	clusterInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), false, req.GetClusterIds()...)
	if err != nil {
		return nil, err
	}
	clusterIDs := clusterInfo.GetClusterIDs()

	startTime := time.Time{}
	if req.GetStartTime() != nil {
		startTime = req.GetStartTime().AsTime()
	}
	endTime := time.Now()
	if req.GetEndTime() != nil {
		endTime = req.GetEndTime().AsTime()
	}
	events, err := resSvc.ListKubernetesTimelineEvents(ctx, req.GetInstanceId(), clusterIDs,
		req.GetNamespaces(), req.GetApplicationNames(), req.GetTimelineResourceIds(), startTime, endTime)
	if err != nil {
		return nil, err
	}
	return &organizationv1.ListKubernetesTimelineEventsResponse{
		Events: events,
	}, nil
}
