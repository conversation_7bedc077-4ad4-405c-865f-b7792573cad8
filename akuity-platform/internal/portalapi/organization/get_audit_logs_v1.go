package organization

import (
	"context"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"k8s.io/utils/ptr"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

const auditMaxLimit = uint32(1000)

func (s *OrganizationV1Server) GetAuditLogs(
	ctx context.Context,
	req *organizationv1.GetAuditLogsRequest,
) (*organizationv1.GetAuditLogsResponse, error) {
	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.Db, s.acs, req.GetId())
	if err != nil {
		return nil, err
	}

	filters := req.GetFilters()
	if filters == nil {
		filters = &organizationv1.AuditFilters{}
	}

	if filters.WorkspaceMember != nil {
		return nil, status.Error(codes.InvalidArgument, "workspace member filter is not allowed in user requests")
	}

	action := accesscontrol.NewActionGetOrganizationAuditLog()
	// first check if user has full access to audit logs
	ok, err := enforcer.EnforceAction(ctx, action)
	if err != nil {
		return nil, err
	}
	if !ok {
		if err := s.checkAuditFilterInstancePermissions(ctx, enforcer, filters, req.GetId()); err != nil {
			return nil, err
		}
	}

	workspaceNames := []string{}
	workspaceNames = append(workspaceNames, filters.GetWorkspace().GetObjectName()...)
	if len(workspaceNames) > 0 {
		if err := filterWorkspace(ctx, s, workspaceNames, filters); err != nil {
			return nil, err
		}
	}

	objects := CreateAuditLogObjectsFromFilter(filters)
	f := organizations.AuditLogFilter{
		ActorID:   filters.ActorId,
		Action:    filters.Action,
		ActorType: filters.ActorType,
		Objects:   objects,
	}

	if filters.StartTime != nil {
		if f.StartTime, err = time.Parse(time.RFC3339, *filters.StartTime); err != nil {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
	}

	if filters.EndTime != nil {
		if f.EndTime, err = time.Parse(time.RFC3339, *filters.EndTime); err != nil {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
	}

	limit := filters.GetLimit()
	offset := filters.GetOffset()

	if limit == 0 {
		limit = 10
	} else if limit > auditMaxLimit {
		limit = auditMaxLimit
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)

	auditLogs, err := os.ListAuditLogs(ctx, req.GetId(), f, int(limit), int(offset))
	if err != nil {
		return nil, err
	}

	totalCount, err := os.CountAuditLogs(ctx, req.GetId(), f)
	if err != nil {
		return nil, err
	}

	items := make([]*organizationv1.AuditLog, 0, len(auditLogs))
	for _, auditLog := range auditLogs {
		auditLogV1, err := MapAuditLogToRPCEntity(auditLog)
		if err != nil {
			return nil, err
		}
		items = append(items, auditLogV1)
	}

	return &organizationv1.GetAuditLogsResponse{
		Items:      items,
		TotalCount: uint32(totalCount),
	}, nil
}

func filterWorkspace(ctx context.Context, s *OrganizationV1Server, workspaceNames []string, filters *organizationv1.AuditFilters) error {
	workspace, err := s.RepoSet.Workspaces().Filter(
		models.WorkspaceWhere.Name.IN(workspaceNames),
	).ListAll(ctx, models.WorkspaceColumns.ID)
	if err != nil {
		return err
	}

	var workspaceIDs []string
	for _, ws := range workspace {
		workspaceIDs = append(workspaceIDs, ws.ID)
	}

	argocdInstances, err := s.RepoSet.ArgoCDInstances().Filter(
		models.ArgoCDInstanceWhere.WorkspaceID.IN(workspaceIDs),
	).ListAll(ctx, models.ArgoCDInstanceColumns.Name)
	if err != nil {
		return err
	}
	kargoInstances, err := s.RepoSet.KargoInstances().Filter(
		models.KargoInstanceWhere.WorkspaceID.IN(workspaceIDs),
	).ListAll(ctx, models.KargoInstanceColumns.Name)
	if err != nil {
		return err
	}
	apiKeys, err := s.RepoSet.APIKeys().Filter(
		models.APIKeyWhere.WorkspaceID.IN(workspaceIDs),
	).ListAll(ctx, models.APIKeyColumns.ID)
	if err != nil {
		return err
	}
	customRoles, err := s.RepoSet.CustomRoles().Filter(
		models.CustomRoleWhere.WorkspaceID.IN(workspaceIDs),
	).ListAll(ctx, models.CustomRoleColumns.Name)
	if err != nil {
		return err
	}
	workspaceMember, err := s.RepoSet.WorkspaceMembers().Filter(
		models.WorkspaceMemberWhere.WorkspaceID.IN(workspaceIDs),
	).ListAll(ctx, models.WorkspaceMemberColumns.UserID, models.WorkspaceMemberColumns.TeamID)
	if err != nil {
		return err
	}
	if len(argocdInstances) > 0 {
		var argocdInstanceNames []string
		for _, instance := range argocdInstances {
			argocdInstanceNames = append(argocdInstanceNames, instance.Name)
		}
		if filters.K8SResource == nil {
			filters.K8SResource = &organizationv1.ObjectFilter{}
		}
		filters.K8SResource.Enabled = ptr.To(true)
		filters.K8SResource.ObjectParentParentName = argocdInstanceNames

		if filters.ArgocdApplication == nil {
			filters.ArgocdApplication = &organizationv1.ObjectFilter{}
		}
		filters.ArgocdApplication.Enabled = ptr.To(true)
		filters.ArgocdApplication.ObjectParentName = argocdInstanceNames

		if filters.ArgocdInstance == nil {
			filters.ArgocdInstance = &organizationv1.ObjectFilter{}
		}
		filters.ArgocdInstance.Enabled = ptr.To(true)
		filters.ArgocdInstance.ObjectName = argocdInstanceNames

		if filters.ArgocdProject == nil {
			filters.ArgocdProject = &organizationv1.ObjectFilter{}
		}
		filters.ArgocdProject.Enabled = ptr.To(true)
		filters.ArgocdProject.ObjectParentName = argocdInstanceNames

		if filters.ArgocdCluster == nil {
			filters.ArgocdCluster = &organizationv1.ObjectFilter{}
		}
		filters.ArgocdCluster.Enabled = ptr.To(true)
		filters.ArgocdCluster.ObjectParentName = argocdInstanceNames

		if filters.Addons == nil {
			filters.Addons = &organizationv1.ObjectFilter{}
		}
		filters.Addons.Enabled = ptr.To(true)
		filters.Addons.ObjectParentParentName = argocdInstanceNames

		if filters.AddonRepos == nil {
			filters.AddonRepos = &organizationv1.ObjectFilter{}
		}
		filters.AddonRepos.Enabled = ptr.To(true)
		filters.AddonRepos.ObjectParentName = argocdInstanceNames

		if filters.AddonMarketplaceInstall == nil {
			filters.AddonMarketplaceInstall = &organizationv1.ObjectFilter{}
		}
		filters.AddonMarketplaceInstall.Enabled = ptr.To(true)
		filters.AddonMarketplaceInstall.ObjectParentParentName = argocdInstanceNames
	}

	if len(kargoInstances) > 0 {
		var kargoInstanceNames []string
		for _, instance := range kargoInstances {
			kargoInstanceNames = append(kargoInstanceNames, instance.Name)
		}
		if filters.KargoInstance == nil {
			filters.KargoInstance = &organizationv1.ObjectFilter{}
		}
		filters.KargoInstance.Enabled = ptr.To(true)
		filters.KargoInstance.ObjectName = kargoInstanceNames

		if filters.KargoAgent == nil {
			filters.KargoAgent = &organizationv1.ObjectFilter{}
		}
		filters.KargoAgent.Enabled = ptr.To(true)
		filters.KargoAgent.ObjectParentName = kargoInstanceNames

		if filters.KargoFreight == nil {
			filters.KargoFreight = &organizationv1.ObjectFilter{}
		}
		filters.KargoFreight.Enabled = ptr.To(true)
		filters.KargoFreight.ObjectParentParentName = kargoInstanceNames

		if filters.KargoPromotion == nil {
			filters.KargoPromotion = &organizationv1.ObjectFilter{}
		}
		filters.KargoPromotion.Enabled = ptr.To(true)
		filters.KargoPromotion.ObjectParentParentName = kargoInstanceNames
	}

	if len(apiKeys) > 0 {
		var apiKeyIDs []string
		for _, apiKey := range apiKeys {
			apiKeyIDs = append(apiKeyIDs, apiKey.ID)
		}
		if filters.ApiKeys == nil {
			filters.ApiKeys = &organizationv1.ObjectFilter{}
		}
		filters.ApiKeys.Enabled = ptr.To(true)
		filters.ApiKeys.ObjectName = apiKeyIDs
	}

	if len(customRoles) > 0 {
		var customRoleNames []string
		for _, customRole := range customRoles {
			customRoleNames = append(customRoleNames, customRole.Name)
		}
		if filters.CustomRoles == nil {
			filters.CustomRoles = &organizationv1.ObjectFilter{}
		}
		filters.CustomRoles.Enabled = ptr.To(true)
		filters.CustomRoles.ObjectName = customRoleNames
	}

	if len(workspaceMember) > 0 {
		var workspaceMemberNames []string
		for _, member := range workspaceMember {
			if member.UserID.Valid && member.UserID.String != "" {
				user, err := s.RepoSet.Users().Filter(
					models.AkuityUserWhere.ID.EQ(member.UserID.String),
				).One(ctx)
				if err != nil {
					return err
				}
				workspaceMemberNames = append(workspaceMemberNames, user.Email)
			} else if member.TeamID.String != "" {
				team, err := s.RepoSet.Teams().Filter(
					models.TeamWhere.ID.EQ(member.TeamID.String),
				).One(ctx)
				if err != nil {
					return err
				}
				workspaceMemberNames = append(workspaceMemberNames, team.Name)
			}
		}

		if filters.WorkspaceMember == nil {
			filters.WorkspaceMember = &organizationv1.ObjectFilter{}
		}
		filters.WorkspaceMember.Enabled = ptr.To(true)
		filters.WorkspaceMember.ObjectName = workspaceMemberNames
		filters.WorkspaceMember.ObjectKind = []string{"team", "user"}
		filters.WorkspaceMember.ObjectParentName = workspaceNames
	}

	if filters.Workspace == nil {
		filters.Workspace = &organizationv1.ObjectFilter{}
	}
	filters.Workspace.Enabled = ptr.To(true)
	filters.Workspace.ObjectName = workspaceNames

	return nil
}

func toAuditLogObject(objectType string, object *organizationv1.ObjectFilter) organizations.AuditLogObject {
	mappedObject := organizations.AuditLogObject{
		ObjectType: objectType,
	}

	if len(object.ObjectName) > 0 {
		mappedObject.ObjectName = object.ObjectName
	}

	if len(object.ObjectGroup) > 0 {
		mappedObject.ObjectGroup = object.ObjectGroup
	}

	if len(object.ObjectKind) > 0 {
		mappedObject.ObjectKind = object.ObjectKind
	}

	if len(object.ObjectParentName) > 0 {
		mappedObject.ObjectParentName = object.ObjectParentName
	}

	if len(object.ObjectParentParentName) > 0 {
		mappedObject.ObjectParentParentName = object.ObjectParentParentName
	}

	if len(object.ObjectParentApplicationName) > 0 {
		mappedObject.ObjectParentApplicationName = object.ObjectParentApplicationName
	}
	if len(object.ObjectParentKargoProjectName) > 0 {
		mappedObject.ObjectParentKargoProjectName = object.ObjectParentKargoProjectName
	}

	return mappedObject
}

func CreateAuditLogObjectsFromFilter(filters *organizationv1.AuditFilters) []organizations.AuditLogObject {
	objects := []organizations.AuditLogObject{}
	if filters.K8SResource != nil && filters.K8SResource.Enabled != nil && *filters.K8SResource.Enabled {
		objects = append(objects, toAuditLogObject(string(models.K8sResourceAuditObject), filters.K8SResource))
	}

	if filters.ArgocdApplication != nil && filters.ArgocdApplication.Enabled != nil && *filters.ArgocdApplication.Enabled {
		objects = append(objects, toAuditLogObject(string(models.ArgoCDAppAuditObject), filters.ArgocdApplication))
	}

	if filters.ArgocdCluster != nil && filters.ArgocdCluster.Enabled != nil && *filters.ArgocdCluster.Enabled {
		objects = append(objects, toAuditLogObject(string(models.ArgoCDClusterAuditObject), filters.ArgocdCluster))
	}

	if filters.ArgocdInstance != nil && filters.ArgocdInstance.Enabled != nil && *filters.ArgocdInstance.Enabled {
		objects = append(objects, toAuditLogObject(string(models.ArgoCDInstanceAuditObject), filters.ArgocdInstance))
	}

	if filters.ArgocdProject != nil && filters.ArgocdProject.Enabled != nil && *filters.ArgocdProject.Enabled {
		objects = append(objects, toAuditLogObject(string(models.ArgoCDProjectAuditObject), filters.ArgocdProject))
	}

	if filters.Member != nil && filters.Member.Enabled != nil && *filters.Member.Enabled {
		objects = append(objects, toAuditLogObject(string(models.MemberAuditObject), filters.Member))
	}

	if filters.OrganizationInvite != nil && filters.OrganizationInvite.Enabled != nil && *filters.OrganizationInvite.Enabled {
		objects = append(objects, toAuditLogObject(string(models.OrganizationInviteAuditObject), filters.OrganizationInvite))
	}

	if filters.KargoInstance != nil && filters.KargoInstance.Enabled != nil && *filters.KargoInstance.Enabled {
		objects = append(objects, toAuditLogObject(string(models.KargoInstanceAuditObject), filters.KargoInstance))
	}

	if filters.KargoAgent != nil && filters.KargoAgent.Enabled != nil && *filters.KargoAgent.Enabled {
		objects = append(objects, toAuditLogObject(string(models.KargoAgentAuditObject), filters.KargoAgent))
	}

	if filters.KargoPromotion != nil && filters.KargoPromotion.Enabled != nil && *filters.KargoPromotion.Enabled {
		objects = append(objects, toAuditLogObject(string(models.KargoPromotionAuditObject), filters.KargoPromotion))
	}

	if filters.KargoFreight != nil && filters.KargoFreight.Enabled != nil && *filters.KargoFreight.Enabled {
		objects = append(objects, toAuditLogObject(string(models.KargoFreightAuditObject), filters.KargoFreight))
	}

	if filters.CustomRoles != nil && filters.CustomRoles.Enabled != nil && *filters.CustomRoles.Enabled {
		objects = append(objects, toAuditLogObject(string(models.CustomRoleAuditObject), filters.CustomRoles))
	}

	if filters.ApiKeys != nil && filters.ApiKeys.Enabled != nil && *filters.ApiKeys.Enabled {
		objects = append(objects, toAuditLogObject(string(models.APIKeyAuditObject), filters.ApiKeys))
	}

	if filters.NotificationCfg != nil && filters.NotificationCfg.Enabled != nil && *filters.NotificationCfg.Enabled {
		objects = append(objects, toAuditLogObject(string(models.OrganizationNotificationConfigAuditObject), filters.NotificationCfg))
	}

	if filters.Addons != nil && filters.Addons.Enabled != nil && *filters.Addons.Enabled {
		objects = append(objects, toAuditLogObject(string(models.AddonAuditObject), filters.Addons))
	}

	if filters.AddonRepos != nil && filters.AddonRepos.Enabled != nil && *filters.AddonRepos.Enabled {
		objects = append(objects, toAuditLogObject(string(models.AddonRepoAuditObject), filters.AddonRepos))
	}

	if filters.AddonMarketplaceInstall != nil && filters.AddonMarketplaceInstall.Enabled != nil && *filters.AddonMarketplaceInstall.Enabled {
		objects = append(objects, toAuditLogObject(string(models.AddonMarketplaceInstallAuditObject), filters.AddonMarketplaceInstall))
	}

	if filters.Workspace != nil && filters.Workspace.Enabled != nil && *filters.Workspace.Enabled {
		objects = append(objects, toAuditLogObject(string(models.WorkspaceAuditObject), filters.Workspace))
	}

	if filters.WorkspaceMember != nil && filters.WorkspaceMember.Enabled != nil && *filters.WorkspaceMember.Enabled {
		objects = append(objects, toAuditLogObject(string(models.WorkspaceMemberAuditObject), filters.WorkspaceMember))
	}

	return objects
}
