package organization

import (
	"context"
	"fmt"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	billingutil "github.com/akuityio/akuity-platform/internal/utils/billing"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetCustomerDetails(
	ctx context.Context,
	req *organizationv1.GetCustomerDetailsRequest,
) (*organizationv1.GetCustomerDetailsResponse, error) {
	if config.IsSelfHosted {
		return nil, shared.ErrNotAvailableInSelfHostedVersion
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetId(),
		accesscontrol.NewActionGetOrganizationBilling(req.GetId())); err != nil {
		return nil, err
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	resp, err := os.GetBilling(ctx, req.GetId())
	if err != nil {
		if organizations.IsNotFoundErr(err) {
			// If entry does not exist, don't error, just return empty response
			return &organizationv1.GetCustomerDetailsResponse{}, nil
		}
		return nil, fmt.Errorf("failed to get billing: %w", err)
	}

	hasActiveSubscription := false
	customerData, err := billingutil.GetCustomerDetails(ctx, resp.CustomerID, resp.BillingAuthority, s.BillingProviders)
	if err != nil {
		s.Log.Info("Failed to retrieve customer data", "customerID", resp.CustomerID, "billingAuthority", resp.BillingAuthority, "error", err.Error())
	} else if customerData != nil {
		hasActiveSubscription = customerData.SubscriptionStatus != nil
	}

	metadata, err := resp.GetMetadata()
	if err != nil {
		return nil, err
	}

	apiResp := &organizationv1.GetCustomerDetailsResponse{
		BillingDetails: &organizationv1.BillingDetails{
			Email:                 resp.BillingEmail.String,
			HasActiveSubscription: hasActiveSubscription,
			Metadata: &organizationv1.BillingMetadata{
				Name: metadata.Name,
			},
			CustomerId:       resp.CustomerID,
			BillingAuthority: resp.BillingAuthority,
			Manual:           resp.Manual.Bool,
		},
	}

	if customerData != nil {
		apiResp.BillingDetails.LastFourCardDigits = customerData.Last4CardDigits
		apiResp.BillingDetails.Addons = customerData.Addons
	}

	return apiResp, nil
}
