package organization

import (
	"context"
	"strings"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetKubernetesImageDetail(
	ctx context.Context,
	req *organizationv1.GetKubernetesImageDetailRequest,
) (*organizationv1.GetKubernetesImageDetailResponse, error) {
	_, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	resSvc := k8sresource.NewServiceWithOptions(s.RepoSet, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)))

	clusterInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), false, req.GetClusterId())
	if err != nil {
		return nil, err
	}
	imageID := req.GetImageId()
	imageParts := strings.Split(imageID, "|")
	if len(imageParts) != 3 {
		return nil, status.Errorf(codes.InvalidArgument, "invalid image ID: %s", imageID)
	}
	imageName, imageTag, imageDigest := imageParts[0], imageParts[1], imageParts[2]
	image, err := resSvc.GetImage(ctx, req.GetInstanceId(), clusterInfo.GetClusterIDs(), imageName, imageTag, imageDigest)
	if err != nil {
		return nil, err
	}

	return &organizationv1.GetKubernetesImageDetailResponse{Image: mapImageToRPCEntity(image)}, nil
}
