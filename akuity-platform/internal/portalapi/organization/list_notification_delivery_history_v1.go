package organization

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/pkg/errors"
	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

const (
	notificationDeliveryHistoryDefaultLimit = 10
	notificationDeliveryHistoryMaxLimit     = 1000
)

var historySupportedDeliveryMethods = map[string]bool{
	models.DeliveryMethodWebhook: true,
}

func (s *OrganizationV1Server) ListNotificationDeliveryHistory(
	ctx context.Context,
	req *organizationv1.ListNotificationDeliveryHistoryRequest,
) (*organizationv1.ListNotificationDeliveryHistoryResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(
		ctx,
		s.Db,
		s.acs,
		req.OrganizationId,
		// Since DeliveryHistory is a subresource of NotificationConfig,
		// use GetOrgNotificationConfig action to check permission.
		accesscontrol.NewActionGetOrgNotificationConfig(req.GetId()),
	)
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetNotification().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "notification feature is not enabled")
	}

	limit := req.GetLimit()
	offset := req.GetOffset()
	if limit == 0 {
		limit = notificationDeliveryHistoryMaxLimit
	} else if limit > notificationDeliveryHistoryMaxLimit {
		limit = notificationDeliveryHistoryDefaultLimit
	}

	// Check notification config existence
	cfg, err := s.RepoSet.OrganizationNotificationConfigs(
		models.OrganizationNotificationConfigWhere.OrganizationID.EQ(req.GetOrganizationId()),
	).GetByID(ctx, req.GetId())
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, status.Error(codes.NotFound, "config not found")
		}
		return nil, errors.Wrap(err, "get notification config")
	}

	if _, ok := historySupportedDeliveryMethods[cfg.DeliveryMethod]; !ok {
		return nil, status.Errorf(codes.FailedPrecondition, "unsupported delivery method: %q", cfg.DeliveryMethod)
	}

	totalNotificationHistoryCount, err := s.RepoSet.Notifications(
		models.NotificationWhere.TargetID.EQ(null.StringFrom(req.GetId())),
	).Count(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "count notifications")
	}
	notifications, err := s.RepoSet.Notifications(
		qm.Load(models.NotificationRels.Event),
		models.NotificationWhere.TargetID.EQ(null.StringFrom(req.GetId())),
		qm.OrderBy(
			fmt.Sprintf(`(
CASE WHEN %s is NULL THEN %s
WHEN %s > %s THEN %s
ELSE %s
END) DESC`,
				models.NotificationColumns.LastDeliveryTimestamp,
				models.NotificationColumns.CreationTimestamp,
				models.NotificationColumns.LastDeliveryTimestamp,
				models.NotificationColumns.CreationTimestamp,
				models.NotificationColumns.LastDeliveryTimestamp,
				models.NotificationColumns.CreationTimestamp,
			),
		),
		qm.Limit(int(limit)),
		qm.Offset(int(offset)),
	).ListAll(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "list notifications")
	}

	res := make([]*organizationv1.NotificationDelivery, len(notifications))
	for idx, notification := range notifications {
		mapRes, err := mapNotificationDeliveryToRPCEntity(notification)
		if err != nil {
			return nil, errors.Wrap(err, "map notification delivery history")
		}
		res[idx] = mapRes
	}
	return &organizationv1.ListNotificationDeliveryHistoryResponse{
		History:           res,
		TotalHistoryCount: uint64(totalNotificationHistoryCount),
	}, nil
}
