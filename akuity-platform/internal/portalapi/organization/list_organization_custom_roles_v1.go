package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

const (
	customRoleMaxLimit     = uint32(1000)
	customRoleDefaultLimit = uint32(10)
)

func (s *OrganizationV1Server) ListCustomRoles(
	ctx context.Context,
	req *organizationv1.ListCustomRolesRequest,
) (*organizationv1.ListCustomRolesResponse, error) {
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetCustomRoles().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "custom roles feature is not enabled")
	}

	_, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.OrganizationId,
		// we don't support per resource permission yet
		accesscontrol.NewActionGetCustomRole(accesscontrol.ResourceAny))
	if err != nil {
		return nil, err
	}

	limit := req.GetLimit()
	offset := req.GetOffset()
	if limit == 0 {
		limit = customRoleDefaultLimit
	} else if limit > customRoleMaxLimit {
		limit = customRoleMaxLimit
	}

	roles, err := s.crs.ListOrganizationCustomRoles(ctx, req.OrganizationId, limit, offset)
	if err != nil {
		return nil, err
	}

	noWorkspace := ""
	count, err := s.crs.CountCustomRole(ctx, req.OrganizationId, noWorkspace)
	if err != nil {
		return nil, err
	}
	return &organizationv1.ListCustomRolesResponse{
		CustomRoles: newCustomRoles(roles),
		TotalCount:  count,
	}, nil
}

func newCustomRoles(rr []*models.CustomRole) []*organizationv1.CustomRole {
	v1Roles := make([]*organizationv1.CustomRole, 0, len(rr))

	for _, r := range rr {
		v1Roles = append(v1Roles, newCustomRole(r))
	}
	return v1Roles
}
