package organization

import (
	"context"
	"strings"

	"github.com/auth0/go-auth0/management"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/auth0"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) EnsureSSOConfiguration(
	ctx context.Context,
	req *organizationv1.EnsureSSOConfigurationRequest,
) (*organizationv1.EnsureSSOConfigurationResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(
		ctx, s.Db, s.acs, req.GetId(), accesscontrol.NewActionUpdateOrganizationSSOConfiguration()); err != nil {
		return nil, err
	}
	orgID := req.GetId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetSso().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "sso feature is not enabled")
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	currentConn, err := s.Auth0Client.GetConnection(ctx, req.GetId())
	if err != nil {
		if !auth0.IsNotFoundError(err) {
			return nil, errors.Wrap(err, "get current auth0 connection")
		}
	}

	var tenantDomain string
	var domainAliases []string
	var opts auth0.ConnectionOptions
	switch req.GetOptions().(type) {
	case *organizationv1.EnsureSSOConfigurationRequest_AzureAd:
		azureOpt := req.GetAzureAd()
		tenantDomain = azureOpt.GetAzureAdDomain()
		domainAliases = azureOpt.GetDomainAliases()
		// Backward compatibility
		if azureOpt.GetDomainAlias() != "" { // nolint:staticcheck
			domainAliases = append(domainAliases, req.GetAzureAd().GetDomainAlias()) // nolint:staticcheck
		}
		domainAliases = lo.Uniq(domainAliases)
		opts = &auth0.AzureADConnectionOptions{
			ClientID:      azureOpt.GetClientId(),
			ClientSecret:  azureOpt.GetClientSecret(),
			AzureADDomain: tenantDomain,
			DomainAliases: domainAliases,
		}
	case *organizationv1.EnsureSSOConfigurationRequest_GoogleWorkspace:
		googleOpt := req.GetGoogleWorkspace()
		tenantDomain = googleOpt.GetGoogleWorkspaceDomain()
		domainAliases = lo.Uniq(googleOpt.GetDomainAliases())
		clientSecret := googleOpt.GetClientSecret()
		if currentConn != nil {
			if currentOpt, ok := currentConn.GetOptions().(*management.ConnectionOptionsGoogleApps); ok {
				if clientSecret == "" {
					clientSecret = currentOpt.GetClientSecret()
				}
			}
		}
		opts = &auth0.GoogleWorkspaceConnectionOptions{
			ClientID:      googleOpt.GetClientId(),
			ClientSecret:  clientSecret,
			Domain:        tenantDomain,
			DomainAliases: domainAliases,
		}
	case *organizationv1.EnsureSSOConfigurationRequest_Oidc:
		oidcOpt := req.GetOidc()
		tenantDomain = oidcOpt.GetDomain()
		domainAliases = lo.Uniq(oidcOpt.GetDomainAliases())

		var oidcChan auth0.OIDCChannel
		switch typedChan := oidcOpt.GetChannel().(type) {
		case *organizationv1.OIDCSSOOptions_Back:
			back := typedChan.Back
			clientSecret := back.GetClientSecret()
			if currentConn != nil {
				if currentOpt, ok := currentConn.GetOptions().(*management.ConnectionOptionsOIDC); ok {
					if clientSecret == "" {
						clientSecret = currentOpt.GetClientSecret()
					}
				}
			}
			oidcChan = &auth0.OIDCBackChannel{
				ClientSecret:          clientSecret,
				Issuer:                back.GetIssuer(),
				AuthorizationEndpoint: back.GetAuthorizationEndpoint(),
				TokenEndpoint:         back.GetTokenEndpoint(),
				JWKSURL:               back.GetJwksUri(),
			}
		case *organizationv1.OIDCSSOOptions_Front:
			front := typedChan.Front
			oidcChan = &auth0.OIDCFrontChannel{
				Issuer:                front.GetIssuer(),
				AuthorizationEndpoint: front.GetAuthorizationEndpoint(),
				JWKSURL:               front.GetJwksUri(),
			}
		}

		opts = &auth0.OIDCConnectionOptions{
			DiscoveryURL:       oidcOpt.GetDiscoveryUrl(),
			ClientID:           oidcOpt.GetClientId(),
			Domain:             tenantDomain,
			DomainAliases:      domainAliases,
			Channel:            oidcChan,
			GroupsScopeEnabled: oidcOpt.GetGroupsScopeEnabled(),
		}
	case *organizationv1.EnsureSSOConfigurationRequest_Okta:
		oktaOpt := req.GetOkta()
		tenantDomain = oktaOpt.GetOktaDomain()
		domainAliases = oktaOpt.GetDomainAliases()
		// Backward compatibility
		if oktaOpt.GetDomainAlias() != "" { // nolint:staticcheck
			domainAliases = append(domainAliases, oktaOpt.GetDomainAlias()) // nolint:staticcheck
		}
		domainAliases = lo.Uniq(domainAliases)
		clientSecret := oktaOpt.GetClientSecret()
		if currentConn != nil {
			if currentOpt, ok := currentConn.GetOptions().(*management.ConnectionOptionsOkta); ok {
				if clientSecret == "" {
					clientSecret = currentOpt.GetClientSecret()
				}
			}
		}
		opts = &auth0.OktaConnectionOptions{
			ClientID:      oktaOpt.GetClientId(),
			ClientSecret:  clientSecret,
			OktaDomain:    oktaOpt.GetOktaDomain(),
			DomainAliases: domainAliases,
		}
	case *organizationv1.EnsureSSOConfigurationRequest_Saml:
		samlOpt := req.GetSaml()
		tenantDomain = samlOpt.GetDomain()
		domainAliases = lo.Uniq(samlOpt.GetDomainAliases())

		var connOpt auth0.SAMLConnectionOption
		switch typedOpt := samlOpt.GetOptions().(type) {
		case *organizationv1.SAMLSSOOptions_ConnectionDetails:
			connDetails := typedOpt.ConnectionDetails

			signatureAlgorithm := auth0.SAMLSignatureAlgorithmUnspecified
			switch connDetails.GetSignatureAlgorithm() {
			case organizationv1.SAMLSignatureAlgorithm_SAML_SIGNATURE_ALGORITHM_RSA_SHA256:
				signatureAlgorithm = auth0.SAMLSignatureAlgorithmSHA256
			case organizationv1.SAMLSignatureAlgorithm_SAML_SIGNATURE_ALGORITHM_RSA_SHA1:
				signatureAlgorithm = auth0.SAMLSignatureAlgorithmSHA1
			}

			digestAlgorithm := auth0.SAMLDigestAlgorithmUnspecified
			switch connDetails.GetDigestAlgorithm() {
			case organizationv1.SAMLDigestAlgorithm_SAML_DIGEST_ALGORITHM_SHA256:
				digestAlgorithm = auth0.SAMLDigestAlgorithmSHA256
			case organizationv1.SAMLDigestAlgorithm_SAML_DIGEST_ALGORITHM_SHA1:
				digestAlgorithm = auth0.SAMLDigestAlgorithmSHA1
			}

			protocolBinding := auth0.SAMLProtocolBindingUnspecified
			switch connDetails.GetProtocolBinding() {
			case organizationv1.SAMLProtocolBinding_SAML_PROTOCOL_BINDING_HTTP_REDIRECT:
				protocolBinding = auth0.SAMLProtocolBindingHTTPRedirect
			case organizationv1.SAMLProtocolBinding_SAML_PROTOCOL_BINDING_HTTP_POST:
				protocolBinding = auth0.SAMLProtocolBindingHTTPPOST
			}

			connOpt = &auth0.SAMLConnectionDetails{
				SignInEndpoint:           connDetails.GetSignInEndpoint(),
				DisableSignOut:           connDetails.GetDisableSignOut(),
				SignOutEndpoint:          connDetails.GetSignOutEndpoint(),
				Base64EncodedSigningCert: connDetails.GetBase64EncodedSigningCert(),
				SignRequest:              connDetails.GetSignRequest(),
				SignatureAlgorithm:       signatureAlgorithm,
				DigestAlgorithm:          digestAlgorithm,
				ProtocolBinding:          protocolBinding,
			}
		case *organizationv1.SAMLSSOOptions_MetadataXml:
			connOpt = &auth0.SAMLConnectionXMLMetadata{
				Metadata: samlOpt.GetMetadataXml(),
			}
		}

		opts = &auth0.SAMLConnectionOptions{
			Domain:        tenantDomain,
			DomainAliases: domainAliases,
			Option:        connOpt,
		}
	default:
		return nil, status.Error(codes.InvalidArgument, "option must be provided")
	}

	if err := s.Validator.StructCtx(ctx, opts); err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid options: %s", err.Error())
	}

	if !config.IsSelfHosted {
		org, err := os.Get(ctx, req.GetId())
		if err != nil {
			return nil, err
		}
		vd, err := org.GetVerifiedDomains()
		if err != nil {
			return nil, err
		}
		requiresVerification := []string{}
		for _, d := range domainAliases {
			if !vd.IsDomainVerified(d) {
				requiresVerification = append(requiresVerification, d)
			}
		}

		if len(tenantDomain) > 0 && !vd.IsDomainVerified(tenantDomain) {
			requiresVerification = append(requiresVerification, tenantDomain)
		}
		if len(requiresVerification) > 0 {
			return nil, status.Errorf(codes.InvalidArgument, "domain needs to be verified, please submit a verification request in organisation settings for the domains: %s", strings.Join(requiresVerification, ", "))
		}
	}

	conn, err := auth0.NewConnection(req.GetId(), tenantDomain, domainAliases, opts.ToAuth0Options())
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}
	res, err := os.EnsureSSOConfiguration(ctx, req.GetId(), conn, req.GetAutoAddMember())
	if err != nil {
		if organizations.IsConflictErr(err) {
			return nil, status.Error(codes.AlreadyExists, "domain_alias already exists")
		}
		return nil, err
	}

	resp := &organizationv1.EnsureSSOConfigurationResponse{
		AutoAddMember: req.GetAutoAddMember(),
	}
	switch typedOpts := res.GetOptions().(type) {
	case *management.ConnectionOptionsAzureAD:
		resp.Options = &organizationv1.EnsureSSOConfigurationResponse_AzureAd{
			AzureAd: mapAzureADConnectionOptionsToRPCEntity(typedOpts),
		}
	case *management.ConnectionOptionsGoogleApps:
		resp.Options = &organizationv1.EnsureSSOConfigurationResponse_GoogleWorkspace{
			GoogleWorkspace: mapGoogleWorkspaceConnectionToRPCEntity(typedOpts),
		}
	case *management.ConnectionOptionsOIDC:
		resp.Options = &organizationv1.EnsureSSOConfigurationResponse_Oidc{
			Oidc: mapOIDCConnectionToRPCEntity(typedOpts),
		}
	case *management.ConnectionOptionsOkta:
		resp.Options = &organizationv1.EnsureSSOConfigurationResponse_Okta{
			Okta: mapOktaConnectionOptionsToRPCEntity(res.TenantDomain, typedOpts),
		}
	case *management.ConnectionOptionsSAML:
		resp.Options = &organizationv1.EnsureSSOConfigurationResponse_Saml{
			Saml: mapSAMLConnectionToRPCEntity(typedOpts),
		}
	default:
		return nil, status.Errorf(codes.Internal, "unknown connection type: %T", typedOpts)
	}
	return resp, nil
}
