package organization

import (
	"context"
	"encoding/json"

	jsonpatch "github.com/evanphx/json-patch"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) UpdateBillingDetails(
	ctx context.Context,
	req *organizationv1.UpdateBillingDetailsRequest,
) (*organizationv1.UpdateBillingDetailsResponse, error) {
	if config.IsSelfHosted {
		return nil, shared.ErrNotAvailableInSelfHostedVersion
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetId(),
		accesscontrol.NewActionUpdateOrganizationBilling(req.GetId())); err != nil {
		return nil, err
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	existingBillingDataModel, err := os.GetBilling(ctx, req.GetId())
	if err != nil {
		return nil, err
	}

	metadata, err := existingBillingDataModel.GetMetadata()
	if err != nil {
		return nil, err
	}

	existingBillingData := &organizationv1.BillingDetails{
		Email:            existingBillingDataModel.BillingEmail.String,
		CustomerId:       existingBillingDataModel.CustomerID,
		BillingAuthority: existingBillingDataModel.BillingAuthority,
		Metadata: &organizationv1.BillingMetadata{
			Name: metadata.Name,
		},
		Manual: existingBillingDataModel.Manual.Bool,
	}
	existingBillingDataJSON, err := json.Marshal(existingBillingData)
	if err != nil {
		return nil, err
	}

	updatedBillingDataJSON, err := json.Marshal(req.GetBillingDetails())
	if err != nil {
		return nil, err
	}

	patchedBillingDataJSON, err := jsonpatch.MergePatch(existingBillingDataJSON, updatedBillingDataJSON)
	if err != nil {
		return nil, err
	}

	patchedBillingData := &organizationv1.BillingDetails{}
	if err := json.Unmarshal(patchedBillingDataJSON, patchedBillingData); err != nil {
		return nil, err
	}
	if err := os.UpdateBilling(
		ctx, req.GetId(), patchedBillingData.CustomerId, patchedBillingData.Metadata.Name, patchedBillingData.Email); err != nil {
		return nil, err
	}
	return &organizationv1.UpdateBillingDetailsResponse{}, nil
}
