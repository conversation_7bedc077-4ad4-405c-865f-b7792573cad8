package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListOrganizationMembers(
	ctx context.Context,
	req *organizationv1.ListOrganizationMembersRequest,
) (*organizationv1.ListOrganizationMembersResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetId(),
		accesscontrol.NewActionGetOrganizationMembers(accesscontrol.ResourceAny)); err != nil {
		return nil, err
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	members, err := os.ListMembers(ctx, req.GetId())
	if err != nil {
		return nil, err
	}
	return &organizationv1.ListOrganizationMembersResponse{
		Members: newOrganizationMembersV1(members),
	}, nil
}

func newOrganizationMembersV1(members []organizations.OrganizationMember) []*organizationv1.OrganizationMember {
	v1Members := make([]*organizationv1.OrganizationMember, 0)

	for _, member := range members {
		v1Members = append(v1Members, &organizationv1.OrganizationMember{
			Id:    member.ID,
			Email: member.Email,
			Role:  member.Role,
		})
	}

	return v1Members
}
