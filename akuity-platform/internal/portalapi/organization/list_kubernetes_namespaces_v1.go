package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/client"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListKubernetesNamespaces(
	ctx context.Context,
	req *organizationv1.ListKubernetesNamespacesRequest,
) (*organizationv1.ListKubernetesNamespacesResponse, error) {
	enforcer, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	txDB, _ := database.WithTxBeginner(s.Db)
	rs := client.NewRepoSet(txDB)
	resSvc := k8sresource.NewServiceWithOptions(rs, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)), k8sresource.WithEnforcer(enforcer))

	clusterInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), false, req.GetClusterIds()...)
	if err != nil {
		return nil, err
	}
	nss, err := resSvc.ListUniqueNamespaces(ctx, req.GetInstanceId(), clusterInfo.GetClusterIDs(), req.GetNodeName())
	if err != nil {
		return nil, err
	}

	return &organizationv1.ListKubernetesNamespacesResponse{
		Namespaces: nss,
	}, nil
}
