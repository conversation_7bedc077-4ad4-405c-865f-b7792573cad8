package organization

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"google.golang.org/genproto/googleapis/api/httpbody"
	"google.golang.org/genproto/googleapis/rpc/http"
	"google.golang.org/protobuf/types/known/anypb"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/client"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	k8sv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/k8s/v1"
)

func (s *OrganizationV1Server) ListKubernetesDeprecatedAPIs(
	ctx context.Context,
	req *organizationv1.ListKubernetesDeprecatedAPIsRequest,
) (*organizationv1.ListKubernetesDeprecatedAPIsResponse, error) {
	_, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	txDB, _ := database.WithTxBeginner(s.Db)
	rs := client.NewRepoSet(txDB)
	resSvc := k8sresource.NewServiceWithOptions(rs, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)))

	clusterInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), false, req.GetClusterIds()...)
	if err != nil {
		return nil, err
	}

	results, total, err := resSvc.ListKubernetesDeprecatedAPIs(ctx, clusterInfo.GetClusters(), req.GetGroup(), req.GetVersion(), req.GetKind(), req.GetApiVersionContains(), req.GetSeverity(), req.GetOrderBy(), int(req.GetLimit()), int(req.GetOffset()))
	if err != nil {
		return nil, err
	}

	return &organizationv1.ListKubernetesDeprecatedAPIsResponse{
		Apis:       results,
		TotalCount: uint32(total),
	}, nil
}

func (s *OrganizationV1Server) ListKubernetesDeprecatedAPIsToCSV(
	req *organizationv1.ListKubernetesDeprecatedAPIsRequest,
	srv organizationv1.OrganizationService_ListKubernetesDeprecatedAPIsToCSVServer,
) error {
	ctx := srv.Context()

	_, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return err
	}

	txDB, _ := database.WithTxBeginner(s.Db)
	rs := client.NewRepoSet(txDB)
	resSvc := k8sresource.NewServiceWithOptions(rs, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)))

	clusterInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), false, req.GetClusterIds()...)
	if err != nil {
		return err
	}

	httpHeaders, err := anypb.New(&http.HttpHeader{
		Key:   "Content-Disposition",
		Value: fmt.Sprintf("attachment; filename=deprecated_apis_%d.csv", time.Now().UTC().Unix()),
	})
	if err != nil {
		return err
	}

	headerBody := &httpbody.HttpBody{
		ContentType: "text/csv",
		Data: []byte(strings.Join([]string{
			"Instance",
			"Cluster",
			"API Version",
			"Kind",
			"Severity",
			"Kubernetes Version",
			"Deprecated In",
			"Unavailable In",
			"Migrate To",
			"Resource Count",
		}, ",")),
		Extensions: []*anypb.Any{httpHeaders},
	}
	if err = srv.Send(headerBody); err != nil {
		return err
	}

	clusters := clusterInfo.GetClusters()
	limit := 100
	offset := 0
	for {
		results, total, err := resSvc.ListKubernetesDeprecatedAPIs(ctx, clusters, req.GetGroup(), req.GetVersion(), req.GetKind(), req.GetApiVersionContains(), req.GetSeverity(), req.GetOrderBy(), limit, offset)
		if err != nil {
			return err
		}

		rows := lo.Map(results, func(res *k8sv1.DeprecatedInfo, idx int) string {
			cluster := clusterInfo.GetCluster(res.GetClusterId())
			if cluster == nil {
				return ""
			}
			return strings.Join([]string{
				cluster.InstanceName,
				cluster.Name,
				fmt.Sprintf("%s/%s", res.GetGroupVersionKind().GetGroup(), res.GetGroupVersionKind().GetVersion()),
				res.GetGroupVersionKind().GetKind(),
				res.GetSeverity().String(),
				res.GetKubernetesVersion(),
				res.GetDeprecatedIn(),
				res.GetUnavailableIn(),
				res.GetMigrateTo().String(),
				fmt.Sprintf("%d", res.GetResourceCount()),
			}, ",")
		})

		body := &httpbody.HttpBody{
			ContentType: "text/csv",
			Data:        []byte(strings.Join(rows, "\n")),
		}
		if err = srv.Send(body); err != nil {
			return err
		}

		offset += limit
		if offset >= total {
			break
		}
	}
	return nil
}
