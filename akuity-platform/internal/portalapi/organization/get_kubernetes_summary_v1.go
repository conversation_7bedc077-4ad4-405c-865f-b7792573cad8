package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	k8sv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/k8s/v1"
)

func (s *OrganizationV1Server) GetKubernetesSummary(ctx context.Context, req *organizationv1.GetKubernetesSummaryRequest) (*organizationv1.GetKubernetesSummaryResponse, error) {
	enforcer, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	instanceCount := uint32(1)
	if req.GetInstanceId() == "" {
		count, err := s.RepoSet.ArgoCDInstances(
			models.ArgoCDInstanceWhere.OrganizationOwner.EQ(req.GetOrganizationId()),
		).Count(ctx)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to get instance info: %v", err)
		}
		instanceCount = uint32(count)
	}

	resSvc := k8sresource.NewServiceWithOptions(s.RepoSet, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)), k8sresource.WithEnforcer(enforcer))

	// get clusters count
	clustersInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), true)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get clusters info: %v", err)
	}

	enabledClusters := clustersInfo.GetEnabledClusters()

	// get API resources count
	resourceTypes, err := resSvc.GetResourceTypes(
		ctx, req.GetInstanceId(), enabledClusters)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get k8s info: %v", err)
	}
	apiResourcesCount := len(resourceTypes)

	// get object count
	objectCount := uint32(0)
	for _, cluster := range enabledClusters {
		info, err := cluster.GetK8sInfo()
		if err != nil {
			return nil, status.Errorf(codes.Internal, "failed to get k8s info: %v", err)
		}
		objectCount += info.GetObjectCount()
	}

	// get node, pod, container, image, cve, total object, and stuck in deletion counts in a single query
	counts, err := resSvc.GetSummaryCounts(ctx, req.GetInstanceId(), clustersInfo.GetEnabledClusterIDs())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get resource counts: %v", err)
	}

	// get deprecated APIs count
	_, deprecatedApisCount, err := resSvc.ListKubernetesDeprecatedAPIs(ctx, enabledClusters, "", "", "", "", k8sv1.DeprecatedInfoSeverity_DEPRECATED_INFO_SEVERITY_UNSPECIFIED, "", 0, 0)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get deprecated APIs count: %v", err)
	}

	return &organizationv1.GetKubernetesSummaryResponse{
		InstanceCount:        instanceCount,
		ClusterCount:         uint32(len(clustersInfo.GetClusters())),
		NodeCount:            counts.NodeCount,
		PodCount:             counts.PodCount,
		ImageCount:           counts.ImageCount,
		ContainerCount:       counts.ContainerCount,
		ApiResourceCount:     uint32(apiResourcesCount),
		ObjectCount:          objectCount,
		StuckInDeletionCount: uint32(counts.StuckDeletionCount),
		DeprecatedApiCount:   uint32(deprecatedApisCount),
		CveCount:             counts.CVECount,
		IncidentCount:        counts.IncidentCount,
	}, nil
}
