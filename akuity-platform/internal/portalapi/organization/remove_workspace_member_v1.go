package organization

import (
	"context"

	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) RemoveWorkspaceMember(
	ctx context.Context,
	req *organizationv1.RemoveWorkspaceMemberRequest,
) (*organizationv1.RemoveWorkspaceMemberResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.OrganizationId,
		accesscontrol.NewActionDeleteWorkspaceMembers(req.GetWorkspaceId(), req.GetId()))
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetWorkspaces().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "workspaces feature is not enabled")
	}

	teamSvc := teams.NewService(s.Db)
	workspaceSvc := workspaces.NewService(s.Db, teamSvc, s.Cfg.FeatureGatesSource)
	if err := workspaceSvc.RemoveWorkspaceMember(ctx, req.GetId(), req.GetWorkspaceId()); err != nil {
		return nil, errors.Wrap(err, "remove workspace member")
	}
	return &organizationv1.RemoveWorkspaceMemberResponse{
		/* explicitly empty */
	}, nil
}
