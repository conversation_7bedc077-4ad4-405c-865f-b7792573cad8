package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) UpdateOrganization(
	ctx context.Context,
	req *organizationv1.UpdateOrganizationRequest,
) (*organizationv1.UpdateOrganizationResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetId(),
		accesscontrol.NewActionUpdateOrganization(req.GetId())); err != nil {
		return nil, err
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)

	org, err := os.Get(ctx, req.GetId())
	if err != nil {
		return nil, err
	}
	mfa, err := org.GetMFASettings()
	if err != nil {
		return nil, err
	}

	// Check if MFA value is being changed
	if mfa.Enabled != req.GetMfa().GetEnabled() {
		if config.IsSelfHosted {
			return nil, shared.ErrNotAvailableInSelfHostedVersion
		}
		orgID := req.GetId()
		if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetMultiFactorAuth().Enabled() {
			return nil, status.Error(codes.PermissionDenied, "multi-factor auth feature is not enabled")
		}
	}

	if err := os.Update(ctx, req.GetId(), req.GetName(), req.GetMfa(), req.GetAi()); err != nil {
		return nil, err
	}

	return &organizationv1.UpdateOrganizationResponse{}, nil
}
