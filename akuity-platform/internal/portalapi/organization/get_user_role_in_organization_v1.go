package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetUserRoleInOrganization(
	ctx context.Context,
	req *organizationv1.GetUserRoleInOrganizationRequest,
) (*organizationv1.GetUserRoleInOrganizationResponse, error) {
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}

	for _, binding := range actor.OrganizationBindings {
		if binding.OrganizationID == req.GetId() {
			if len(binding.Permissions.Roles) == 0 {
				return &organizationv1.GetUserRoleInOrganizationResponse{
					Role: "",
				}, nil
			}

			var role organizations.Role
			var err error
			highestRole := organizations.RoleMember
			for _, r := range binding.Permissions.Roles {
				if organizations.IsOrganizationRole(r) {
					role, err = organizations.NewRole(r)
					if err != nil {
						return nil, err
					}
					highestRole = chooseGreaterRole(role, highestRole)
				}
			}
			return &organizationv1.GetUserRoleInOrganizationResponse{
				Role: string(highestRole),
			}, nil
		}
	}
	return nil, shared.ErrNotFound
}

func chooseGreaterRole(a, b organizations.Role) organizations.Role {
	if a == organizations.RoleOwner || b == organizations.RoleOwner {
		return organizations.RoleOwner
	}

	return organizations.RoleMember
}
