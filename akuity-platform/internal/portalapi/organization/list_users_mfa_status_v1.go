package organization

import (
	"context"
	"strings"

	"github.com/auth0/go-auth0/management"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListUsersMFAStatus(ctx context.Context, req *organizationv1.ListUsersMFAStatusRequest) (*organizationv1.ListUsersMFAStatusResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateOrganization(req.GetOrganizationId())); err != nil {
		return nil, err
	}

	if config.IsSelfHosted {
		return nil, shared.ErrNotAvailableInSelfHostedVersion
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetMultiFactorAuth().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "multi-factor auth feature is not enabled")
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)

	org, err := os.Get(ctx, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	if mfaSettings, err := org.GetMFASettings(); err != nil || !mfaSettings.Enabled {
		return nil, shared.ErrUnavailable
	}

	members, err := os.ListMembers(ctx, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	var mfaStatuses []*organizationv1.UserMFAStatus
	var emailQueries []string

	for _, member := range members {
		mfaStatuses = append(mfaStatuses, &organizationv1.UserMFAStatus{
			Email: member.Email,
			Role:  member.Role,
		})
		emailQueries = append(emailQueries, "email:"+member.Email)
	}

	joinedEmails := strings.Join(emailQueries, " OR ")

	mfaMap := make(map[string]bool)
	start := 0
	limit := 100

	for {
		// API to get MFA status of users: https://auth0.com/docs/api/management/v2/users/get-users
		users, err := s.auth0Management.User.List(ctx,
			management.Query(joinedEmails),
			management.IncludeFields("multifactor", "email"),
			management.Page(start),
			management.PerPage(limit),
			management.IncludeTotals(true),
		)
		if err != nil {
			return nil, err
		}

		for _, r := range users.Users {
			if r.Email == nil {
				continue
			}
			email := *r.Email
			multifactor := []string{}
			if r.Multifactor != nil {
				multifactor = *r.Multifactor
			}
			mfaMap[email] = mfaMap[email] || (len(multifactor) > 0)
		}

		if users.Start+users.Length >= users.Total {
			break
		}
		start += users.Length
	}

	for _, status := range mfaStatuses {
		if enabled, ok := mfaMap[status.Email]; ok {
			status.Enabled = enabled
		}
	}

	return &organizationv1.ListUsersMFAStatusResponse{
		UserMfaStatus: mfaStatuses,
	}, nil
}
