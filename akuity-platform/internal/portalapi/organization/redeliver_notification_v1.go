package organization

import (
	"context"
	"database/sql"

	"github.com/pkg/errors"
	"github.com/volatiletech/null/v8"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

const (
	maxRedeliveryCount = 3
)

var redeliverSupportedDeliveryMethods = map[string]bool{
	models.DeliveryMethodWebhook: true,
}

func (s *OrganizationV1Server) RedeliverNotification(
	ctx context.Context,
	req *organizationv1.RedeliverNotificationRequest,
) (*organizationv1.RedeliverNotificationResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(
		ctx,
		s.Db,
		s.acs,
		req.OrganizationId,
		// Since Delivery is a subresource of NotificationConfig,
		// use UpdateOrgNotificationConfig action to check permission.
		accesscontrol.NewActionUpdateOrgNotificationConfig(req.GetId()),
	)
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetNotification().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "notification feature is not enabled")
	}

	// Check notification config existence
	cfg, err := s.RepoSet.OrganizationNotificationConfigs(
		models.OrganizationNotificationConfigWhere.OrganizationID.EQ(req.GetOrganizationId()),
	).GetByID(ctx, req.GetConfigId())
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, status.Error(codes.NotFound, "config not found")
		}
		return nil, errors.Wrap(err, "get notification config")
	}

	if _, ok := redeliverSupportedDeliveryMethods[cfg.DeliveryMethod]; !ok {
		return nil, status.Errorf(codes.FailedPrecondition, "unsupported delivery method: %q", cfg.DeliveryMethod)
	}

	notification, err := s.RepoSet.Notifications(
		models.NotificationWhere.TargetID.EQ(null.StringFrom(cfg.GetID())),
	).GetByID(ctx, req.GetId())
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, status.Error(codes.NotFound, "notification not found")
		}
		return nil, errors.Wrap(err, "get notification")
	}

	// Limit redelivery requests
	if notification.Generation >= maxRedeliveryCount {
		return nil, status.Errorf(
			codes.FailedPrecondition, "cannot request redelivery more than %d times", maxRedeliveryCount)
	}
	// Check if notification delivery is in progress
	if !notification.StatusIsDelivered && !notification.StatusFailedDelivery {
		return nil, status.Error(codes.FailedPrecondition, "notification delivery is in progress")
	}

	// Initialize status to trigger redelivery
	notification.Generation++
	notification.StatusIsDelivered = false
	notification.StatusFailedDelivery = false

	metadata, err := notification.GetMetadata()
	if err != nil {
		return nil, errors.Wrap(err, "get notification metadata")
	}
	switch cfg.DeliveryMethod {
	case models.DeliveryMethodWebhook:
		if metadata.WebhookMetadata == nil {
			metadata.WebhookMetadata = &models.WebhookMetadata{}
		}
		metadata.WebhookMetadata.Retries = 0
	}
	if err := notification.SetMetadata(metadata); err != nil {
		return nil, errors.Wrap(err, "set notification metadata")
	}
	if err := s.RepoSet.Notifications().Update(ctx, notification); err != nil {
		return nil, errors.Wrap(err, "")
	}

	return &organizationv1.RedeliverNotificationResponse{}, nil
}
