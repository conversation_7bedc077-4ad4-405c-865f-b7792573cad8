package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetKubernetesContainer(
	ctx context.Context,
	req *organizationv1.GetKubernetesContainerRequest,
) (*organizationv1.GetKubernetesContainerResponse, error) {
	enforcer, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	resSvc := k8sresource.NewServiceWithOptions(
		s.RepoSet,
		s.Db,
		req.GetOrganizationId(),
		k8sresource.WithLogger(logging.Extract(ctx)),
		k8sresource.WithEnforcer(enforcer),
	)

	cluster, err := resSvc.ValidateGetRequest(ctx, req.GetInstanceId(), req.GetClusterId())
	if err != nil {
		return nil, err
	}

	obj, err := resSvc.GetContainer(ctx, req.GetInstanceId(), cluster.ID, req.GetContainerId())
	if err != nil {
		return nil, err
	}

	pod, err := resSvc.GetResource(ctx, cluster.ID, req.GetInstanceId(), obj.OwnerID.String)
	if err != nil {
		return nil, err
	}

	container, err := mapContainerToRPCEntity(obj, pod)
	if err != nil {
		return nil, err
	}

	return &organizationv1.GetKubernetesContainerResponse{
		Container: container,
	}, nil
}
