package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) RemoveTeamMember(
	ctx context.Context,
	req *organizationv1.RemoveTeamMemberRequest,
) (*organizationv1.RemoveTeamMemberResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionDeleteTeamMembers(req.GetTeamName()))
	if err != nil {
		return nil, err
	}

	if err := s.isTeamEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	teamSvc := teams.NewService(s.Db)
	team, err := teamSvc.GetTeam(ctx, req.GetOrganizationId(), req.GetTeamName())
	if err != nil {
		return nil, err
	}

	if err := teamSvc.RemoveTeamMember(ctx, team.ID, req.GetId()); err != nil {
		return nil, err
	}
	return &organizationv1.RemoveTeamMemberResponse{}, nil
}
