package organization

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/samber/lo"
	"github.com/volatiletech/null/v8"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/agent/pkg/common"
	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/ai/clients"
	"github.com/akuityio/akuity-platform/internal/services/ai/reposet"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/metadata"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	metadatautil "github.com/akuityio/akuity-platform/pkg/utils/grpc/metadata"
)

func (s *OrganizationV1Server) SpotlightSearchKubernetesResources(
	ctx context.Context,
	req *organizationv1.SpotlightSearchKubernetesResourcesRequest,
) (*organizationv1.SpotlightSearchKubernetesResourcesResponse, error) {
	enforcer, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	resSvc := k8sresource.NewServiceWithOptions(s.RepoSet, s.Db, req.GetOrganizationId(),
		k8sresource.WithLogger(logging.Extract(ctx)),
		k8sresource.WithEnforcer(enforcer))

	platform := metadata.ExtractPlatform(ctx)
	clusterInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), true)
	if err != nil {
		return nil, err
	}
	clusters := clusterInfo.GetClusters()
	clusterIDs := clusterInfo.GetClusterIDs()
	clusterK8sVersions := clusterInfo.GetKubernetesVersions()

	offset := int(req.GetOffset())
	limit := int(req.GetLimit())
	if limit == 0 {
		limit = 100
	}

	argocdClientSet, err := clients.NewArgoCDClientSet(ctx, reposet.NewResourceRepoSet(s.RepoSet, req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}
	resources, err := resSvc.SpotlightSearchResources(ctx, req.GetInstanceId(), clusterIDs, req.GetQuery(), offset, limit, req.GetAiSearch())
	if err != nil {
		return nil, err
	}

	count, err := resSvc.CountSpotlightSearchResources(ctx, req.GetInstanceId(), clusterIDs, req.GetQuery(), req.GetAiSearch())
	if err != nil {
		return nil, fmt.Errorf("failed to count resources: %w", err)
	}

	deprecatedGVKMap, err := resSvc.GetDeprecatedGVKs(clusters)
	if err != nil {
		return nil, err
	}

	res := make([]*organizationv1.ClusterResource, 0)
	appResources := []*organizationv1.ClusterResource{}

	var wg sync.WaitGroup
	var mu sync.Mutex

	switch platform {
	case metadatautil.PlatformArgoCD:
		if req.GetInstanceId() != "" {
			instanceApps, err := s.listAndMapPermittedApps(ctx, argocdClientSet, clusterInfo, req.GetOrganizationId(), req.GetInstanceId(), req.GetQuery(), 5*time.Second)
			if err != nil {
				return nil, err
			}
			appResources = append(appResources, instanceApps...)
		} else {
			for _, id := range clusterInfo.GetEnabledInstanceIDs() {
				wg.Add(1)
				go func(id string) {
					defer wg.Done()
					instanceApps, err := s.listAndMapPermittedApps(ctx, argocdClientSet, clusterInfo, req.GetOrganizationId(), id, req.GetQuery(), 3*time.Second)
					if err != nil {
						return
					}
					mu.Lock()
					appResources = append(appResources, instanceApps...)
					mu.Unlock()
				}(id)
			}
			wg.Wait()
		}
	case metadatautil.PlatformKargo:
		allowedApps, err := enforcer.GetKargoAllowedApps(ctx)
		if err != nil {
			return nil, err
		}
		apps := lo.Map(allowedApps, func(app k8sresource.KargoAllowedApp, _ int) *organizationv1.ClusterResource {
			return mapAppToClusterResource(*app.ArgocdApp, clusterInfo.GetClusterByName(app.ArgocdInstanceID, app.ArgocdClusterName))
		})
		appResources = append(appResources, apps...)
	}
	res = append(res, appResources...)
	count += int64(len(appResources))

	orgID := req.GetOrganizationId()
	if s.featSvc.GetFeatureStatuses(ctx, &orgID).GetKargoEnterprise().Enabled() {
		var kargoInstances []*models.KargoInstance
		if platform == metadatautil.PlatformKargo {
			kargoID := req.GetKargoInstanceId()
			if kargoID == "" {
				kargoID = enforcer.GetActor().Extras["instanceID"].(string)
			}
			kargoInstance, err := s.RepoSet.KargoInstances(
				models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(req.GetOrganizationId())),
				models.KargoInstanceWhere.ID.EQ(kargoID),
			).One(ctx)
			if err != nil {
				return nil, err
			}
			kargoInstances = append(kargoInstances, kargoInstance)
		} else {
			kargoInstances, err = s.RepoSet.KargoInstances(
				models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(req.GetOrganizationId())),
			).ListAll(ctx)
			if err != nil {
				return nil, err
			}
		}
		kargoClientSet, err := clients.NewKargoClientSet(ctx, reposet.NewResourceRepoSet(s.RepoSet, req.GetOrganizationId()), nil)
		if err != nil {
			return nil, err
		}
		kargoProjects := make([]*organizationv1.ClusterResource, 0)

		for _, kargoInstance := range kargoInstances {
			wg.Add(1)
			go func(instance *models.KargoInstance) {
				defer wg.Done()
				kargoClient := kargoClientSet.GetKargoClient(instance.ID)
				if kargoClient == nil {
					return
				}

				timeoutCtx, cancel := context.WithTimeout(ctx, 3*time.Second)
				projects, err := kargoClient.ListProjects(timeoutCtx)
				cancel()
				if err != nil {
					if errors.IsInstanceReadinessError(err) {
						return
					}
					return
				}

				instanceProjects := make([]*organizationv1.ClusterResource, 0, len(projects))
				for _, project := range projects {
					link := fmt.Sprintf("https://%s/project/%s", instance.StatusHostname.String, project.Name)
					instanceProjects = append(instanceProjects, &organizationv1.ClusterResource{
						InstanceId: instance.ID,
						ClusterId:  instance.ID,
						Name:       project.Name,
						Namespace:  project.Namespace,
						Group:      project.GroupVersionKind().Group,
						Kind:       project.GroupVersionKind().Kind,
						Version:    project.GroupVersionKind().Version,
						CreateTime: project.CreationTimestamp.Format(time.RFC3339),
						Uid:        project.Name,
						// This follows the displaying rule in UI like other resources
						Columns: map[string]string{
							"Instance":  instance.Name,
							"Namespace": common.K3sKargoNamespace,
							"Link":      link,
						},
					})
				}
				mu.Lock()
				kargoProjects = append(kargoProjects, instanceProjects...)
				mu.Unlock()
			}(kargoInstance)
		}

		wg.Wait()
		res = append(res, kargoProjects...)
		count += int64(len(kargoProjects))
	}

	res = append(res, mapResourcesToRPCEntity(resources, deprecatedGVKMap, clusterK8sVersions)...)

	return &organizationv1.SpotlightSearchKubernetesResourcesResponse{
		Resources: res,
		Count:     uint32(count),
	}, nil
}

// mapAppToClusterResource maps an application to ClusterResource using pre-resolved cluster info
func mapAppToClusterResource(app argocd.Application, cluster *k8sresource.EnabledCluster) *organizationv1.ClusterResource {
	syncStatus, healthStatus := getApplicationStatus(app)
	return &organizationv1.ClusterResource{
		InstanceId: cluster.InstanceID,
		ClusterId:  cluster.ID,
		Name:       app.Name,
		Namespace:  app.Namespace,
		Group:      "dashboard.akuity.io",
		Kind:       "Application",
		CreateTime: app.CreationTimestamp.Format(time.RFC3339),
		Columns: map[string]string{
			"Age":       app.CreationTimestamp.Format(time.RFC3339),
			"Cluster":   cluster.Name,
			"Instance":  cluster.InstanceName,
			"Namespace": app.Namespace,
		},
		ArgocdApplicationInfo: &organizationv1.ArgoCDApplicationInfo{
			Name:         app.Name,
			Link:         fmt.Sprintf("https://%s/applications/argocd/%s", cluster.InstanceHostname, app.Name),
			SyncStatus:   syncStatus,
			HealthStatus: healthStatus,
		},
	}
}

func getApplicationStatus(app argocd.Application) (organizationv1.SyncStatus, organizationv1.HealthStatus) {
	syncStatus := organizationv1.SyncStatus_SYNC_STATUS_UNSPECIFIED
	healthStatus := organizationv1.HealthStatus_HEALTH_STATUS_UNSPECIFIED
	switch app.Status.Sync.Status {
	case argocd.SyncStatusCodeUnknown:
		syncStatus = organizationv1.SyncStatus_SYNC_STATUS_UNKNOWN
	case argocd.SyncStatusCodeSynced:
		syncStatus = organizationv1.SyncStatus_SYNC_STATUS_SYNCED
	case argocd.SyncStatusCodeOutOfSync:
		syncStatus = organizationv1.SyncStatus_SYNC_STATUS_OUT_OF_SYNC
	}
	switch app.Status.Health.Status {
	case argocd.HealthStatusUnknown:
		healthStatus = organizationv1.HealthStatus_HEALTH_STATUS_UNKNOWN
	case argocd.HealthStatusHealthy:
		healthStatus = organizationv1.HealthStatus_HEALTH_STATUS_HEALTHY
	case argocd.HealthStatusDegraded:
		healthStatus = organizationv1.HealthStatus_HEALTH_STATUS_DEGRADED
	case argocd.HealthStatusMissing:
		healthStatus = organizationv1.HealthStatus_HEALTH_STATUS_MISSING
	case argocd.HealthStatusProgressing:
		healthStatus = organizationv1.HealthStatus_HEALTH_STATUS_PROGRESSING
	case argocd.HealthStatusSuspended:
		healthStatus = organizationv1.HealthStatus_HEALTH_STATUS_SUSPENDED
	}
	return syncStatus, healthStatus
}

// getClusterNameFromApp extracts cluster name from application destination or labels
func getClusterNameFromApp(app argocd.Application) string {
	clusterName := argocd.GetNameFromDestination(app.Spec.Destination)
	if clusterName == "" {
		// fallback to label when destination has no name/server
		if lbl, ok := app.Labels["cluster"]; ok {
			clusterName = lbl
		}
	}
	return clusterName
}

// listAndMapPermittedApps lists Argo CD applications for an instance, filters by permissions,
// and maps them to ClusterResource entities.
func (s *OrganizationV1Server) listAndMapPermittedApps(
	ctx context.Context,
	argocdClientSet *clients.ArgoCDClientSet,
	clusterInfo *k8sresource.EnabledClustersInfo,
	orgID string,
	instanceID string,
	search string,
	timeout time.Duration,
) ([]*organizationv1.ClusterResource, error) {
	client := argocdClientSet.GetArgoCDClient(instanceID)
	if client == nil {
		return nil, status.Errorf(codes.NotFound, "ArgoCD client not found for instance ID: %s", instanceID)
	}
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	// Request the full spec to get destination info, along with other needed fields
	apps, err := client.ListApplications(timeoutCtx, search, []string{
		"items.metadata.name",
		"items.metadata.namespace",
		"items.metadata.labels",
		"items.metadata.creationTimestamp",
		"items.status.sync.status",
		"items.status.health",
	})
	cancel()
	if err != nil {
		if errors.IsInstanceReadinessError(err) {
			return nil, nil
		}
		return nil, err
	}

	out := lo.FilterMap(apps, func(app argocd.Application, _ int) (*organizationv1.ClusterResource, bool) {
		clusterName := getClusterNameFromApp(app)
		cluster := clusterInfo.GetClusterByName(instanceID, clusterName)
		if clusterName == "in-cluster" {
			instance, err := s.RepoSet.ArgoCDInstances(
				models.ArgoCDInstanceWhere.OrganizationOwner.EQ(orgID),
				models.ArgoCDInstanceWhere.ID.EQ(instanceID),
			).One(ctx)
			if err != nil {
				return nil, false
			}
			cluster = &k8sresource.EnabledCluster{
				ID:               "in-cluster",
				Name:             "in-cluster",
				InstanceID:       instanceID,
				InstanceName:     instance.Name,
				InstanceHostname: instance.StatusHostname.String,
			}
		}
		if cluster == nil {
			return nil, false
		}

		return mapAppToClusterResource(app, cluster), true
	})
	return out, nil
}
