package organization

import (
	"context"
	"fmt"
	"time"

	"github.com/go-logr/logr"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
	"k8s.io/utils/ptr"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/permissions"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	"github.com/akuityio/akuity-platform/internal/utils/ai"
	"github.com/akuityio/akuity-platform/internal/utils/aws"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	featuresv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/features/v1"
)

const (
	// presignedS3UrlExpirationPeriodInSeconds - duration in seconds for a presigned S3 URL to stay valid
	presignedS3UrlExpirationPeriodInSeconds = 3600
)

func mapOrganizationToRPCEntity(
	org models.Organization,
	quota *featuresv1.OrganizationQuota,
	perm permissions.Permissions,
) (*organizationv1.Organization, error) {
	orgStatus, err := org.GetOrgStatus()
	if err != nil {
		return nil, errors.Wrap(err, "get org status")
	}

	var status *organizationv1.OrganizationStatus
	if orgStatus != nil {
		status = &organizationv1.OrganizationStatus{
			Trial:           orgStatus.Trial,
			Expiry:          uint64(orgStatus.ExpiryTime),
			State:           string(orgStatus.State),
			BillingUpdating: orgStatus.BillingUpdating,
		}
	}

	mfaSettings, err := org.GetMFASettings()
	if err != nil {
		return nil, errors.Wrap(err, "get mfa settings")
	}
	var mfa *organizationv1.MFASettings
	if mfaSettings != nil {
		mfa = &organizationv1.MFASettings{
			Enabled: mfaSettings.Enabled,
		}
	}

	spec, err := org.GetSpec()
	if err != nil {
		return nil, err
	}

	provider := lo.Ternary(spec.AI.Provider == "", ai.ProviderOpenAI, spec.AI.Provider)
	return &organizationv1.Organization{
		Id:              org.ID,
		Name:            org.Name,
		MaxInstances:    quota.MaxInstances,
		MaxClusters:     quota.MaxClusters,
		MaxApplications: quota.MaxApplications,
		CreateTime:      timestamppb.New(org.CreationTimestamp),
		Permissions:     shared.NewPermissionsV1(perm),
		Status:          status,
		Quota:           quota,
		Plan:            org.Plan.String,
		MfaSettings:     mfa,
		AiSettings: &organizationv1.AISettings{
			Provider:     provider,
			ModelVersion: ai.GetModelVersion(provider),
			ToolPolicies: lo.Map(spec.AI.ToolPolicies, func(item models.AIToolPolicy, _ int) *organizationv1.AIToolPolicy {
				action := organizationv1.AIToolPolicyAction_AI_TOOL_POLICY_ACTION_APPROVE
				switch item.Action {
				case models.AIToolPolicyActionApprove:
					action = organizationv1.AIToolPolicyAction_AI_TOOL_POLICY_ACTION_APPROVE
				case models.AIToolPolicyActionRequireApproval:
					action = organizationv1.AIToolPolicyAction_AI_TOOL_POLICY_ACTION_REQUIRE_APPROVAL
				}
				actionType := organizationv1.AIToolPolicyActionType_AI_TOOL_POLICY_ACTION_TYPE_UNSPECIFIED
				if item.Target.ActionMutable != nil {
					if *item.Target.ActionMutable {
						actionType = organizationv1.AIToolPolicyActionType_AI_TOOL_POLICY_ACTION_TYPE_MUTABLE
					} else {
						actionType = organizationv1.AIToolPolicyActionType_AI_TOOL_POLICY_ACTION_TYPE_READONLY
					}
				}
				return &organizationv1.AIToolPolicy{
					Action: action,
					Target: &organizationv1.AIToolPolicyTarget{
						ActionName:         item.Target.ActionName,
						ActionArgs:         item.Target.ActionArgs,
						ActionType:         actionType,
						Runbooks:           item.Target.Runbooks,
						Clusters:           item.Target.Clusters,
						K8SNamespaces:      item.Target.K8SNamespaces,
						ArgocdApplications: item.Target.ArgoCDApplications,
						KargoProjects:      item.Target.KargoProjects,
					},
				}
			}),
		},
	}, nil
}

func MapAuditLogToRPCEntity(auditLog *models.AuditLog) (*organizationv1.AuditLog, error) {
	if auditLog == nil {
		return nil, nil
	}
	actor, err := auditLog.GetActor()
	if err != nil {
		return nil, err
	}
	object, err := auditLog.GetObject()
	if err != nil {
		return nil, err
	}
	details, err := auditLog.GetDetails()
	if err != nil {
		return nil, err
	}

	actionType := ""

	if details.ActionType != nil {
		actionType = *details.ActionType
	}

	objectId := &organizationv1.AuditLog_AuditObject_AuditObjId{}

	if object.ID != nil {
		objectId.Name = object.ID.Name
		objectId.Kind = object.ID.Kind
		objectId.Group = object.ID.Group
	}

	parentId := &organizationv1.AuditLog_AuditObject_AuditParentId{}
	if object.ParentID != nil {
		parentId.Name = object.ParentID.Name
		parentId.ParentName = object.ParentID.ParentName
		parentId.ApplicationName = object.ParentID.ApplicationName
	}

	detailsV1 := &organizationv1.AuditLog_AuditDetails{}
	if details != nil {
		detailsV1.Message = details.Message
		detailsV1.Patch = details.Patch
		detailsV1.ActionType = actionType
	}
	lastOccurredTimestamp := ""
	if !auditLog.LastOccurredTimestamp.Time.IsZero() {
		lastOccurredTimestamp = auditLog.LastOccurredTimestamp.Time.String()
	}

	res := &organizationv1.AuditLog{
		Timestamp:             auditLog.Timestamp.String(),
		LastOccurredTimestamp: lastOccurredTimestamp,
		Count:                 uint32(auditLog.Count),
		Action:                auditLog.Action,
		Actor:                 &organizationv1.AuditLog_AuditActor{Id: actor.ID, Type: string(actor.Type), Ip: ptr.To(actor.IP)},
		Object: &organizationv1.AuditLog_AuditObject{
			Type:     string(object.Type),
			Id:       objectId,
			ParentId: parentId,
		},
		Details: detailsV1,
	}
	return res, nil
}

func mapAuditLogArchiveToRPCEntity(ctx context.Context, log *logr.Logger, auditLogArchive *models.AuditLogArchive) (*organizationv1.AuditLogArchive, error) {
	if auditLogArchive == nil {
		return nil, nil
	}

	bucketName, objectKey, err := aws.SplitS3Path(auditLogArchive.Path, log)
	if err != nil {
		return nil, err
	}

	presignedUrlExpiration := time.Now().UTC().Add(presignedS3UrlExpirationPeriodInSeconds * time.Second).Format(time.RFC1123Z)
	presignedUrl, err := aws.PresignedObjectURL(ctx, bucketName, objectKey, presignedS3UrlExpirationPeriodInSeconds)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve a presigned URL for %q: %w", auditLogArchive.Path, err)
	}

	return &organizationv1.AuditLogArchive{
		StartDate: &date.Date{
			Year:  int32(auditLogArchive.StartDate.Year()),
			Month: int32(auditLogArchive.StartDate.Month()),
			Day:   int32(auditLogArchive.StartDate.Day()),
		},
		EndDate: &date.Date{
			Year:  int32(auditLogArchive.EndDate.Year()),
			Month: int32(auditLogArchive.EndDate.Month()),
			Day:   int32(auditLogArchive.EndDate.Day()),
		},
		PresignedUrlExpiration: presignedUrlExpiration,
		PresignedUrl:           presignedUrl,
		Records:                uint32(auditLogArchive.Records),
	}, nil
}

func mapWorkspaceToRPCEntity(ctx context.Context, ws workspaces.Workspace, enforcer accesscontrol.Enforcer) (*organizationv1.Workspace, error) {
	argoInstances := make([]*organizationv1.WorkspaceArgoCDInstance, 0, len(ws.R.ArgoCDInstances))
	for _, argoInstance := range ws.R.ArgoCDInstances {
		action := accesscontrol.NewActionGetWorkspaceInstances(ws.ID, argoInstance.ID)
		ok, err := enforcer.EnforceAction(ctx, action)
		if err != nil {
			return nil, err
		}
		if !ok {
			continue
		}
		argoInstances = append(argoInstances, &organizationv1.WorkspaceArgoCDInstance{
			Id:   argoInstance.ID,
			Name: argoInstance.Name,
		})
	}
	kargoInstances := make([]*organizationv1.WorkspaceKargoInstance, 0, len(ws.R.KargoInstances))
	for _, kargoInstance := range ws.R.KargoInstances {
		action := accesscontrol.NewActionGetWorkspaceKargoInstances(ws.ID, kargoInstance.ID)
		ok, err := enforcer.EnforceAction(ctx, action)
		if err != nil {
			return nil, err
		}
		if !ok {
			continue
		}
		kargoInstances = append(kargoInstances, &organizationv1.WorkspaceKargoInstance{
			Id:   kargoInstance.ID,
			Name: kargoInstance.Name,
		})
	}
	var teamMemberCount, userMemberCount uint32
	for _, member := range ws.Members {
		switch member.Ref.(type) {
		case *teams.Team:
			teamMemberCount++
		case *models.AkuityUser:
			userMemberCount++
		}
	}
	return &organizationv1.Workspace{
		Id:              ws.ID,
		Name:            ws.Name,
		Description:     ws.Description.String,
		CreateTime:      timestamppb.New(ws.CreationTimestamp),
		ArgocdInstances: argoInstances,
		KargoInstances:  kargoInstances,
		TeamMemberCount: teamMemberCount,
		UserMemberCount: userMemberCount,
		IsDefault:       ws.IsDefault,
	}, nil
}

func mapWorkspaceMemberToRPCEntity(m workspaces.WorkspaceMember) *organizationv1.WorkspaceMember {
	member := &organizationv1.WorkspaceMember{
		Id:   m.ID,
		Role: mapWorkspaceMemberRole(m.Role),
	}
	switch ref := m.Ref.(type) {
	case *models.AkuityUser:
		member.Member = &organizationv1.WorkspaceMember_User{
			User: &organizationv1.WorkspaceUserMember{
				Id:    ref.ID,
				Email: ref.Email,
			},
		}
	case *teams.Team:
		member.Member = &organizationv1.WorkspaceMember_Team{
			Team: &organizationv1.WorkspaceTeamMember{
				Id:          ref.ID,
				Name:        ref.Name,
				Description: ref.Description,
				CreateTime:  timestamppb.New(ref.CreationTimestamp),
				MemberCount: ref.MemberCount,
			},
		}
	}
	return member
}

func mapWorkspaceMemberRole(role permissions.Role) organizationv1.WorkspaceMemberRole {
	switch role {
	case permissions.RoleWorkspaceAdmin:
		return organizationv1.WorkspaceMemberRole_WORKSPACE_MEMBER_ROLE_ADMIN
	case permissions.RoleWorkspaceMember:
		return organizationv1.WorkspaceMemberRole_WORKSPACE_MEMBER_ROLE_MEMBER
	default:
		return organizationv1.WorkspaceMemberRole_WORKSPACE_MEMBER_ROLE_UNSPECIFIED
	}
}

func mapNotificationConfigToRPCEntity(
	cfg models.OrganizationNotificationConfig,
	lastNotification *models.Notification,
) (*organizationv1.NotificationConfig, error) {
	var lastDelivery *organizationv1.NotificationDeliverySummary
	if lastNotification != nil {
		res, err := mapNotificationDeliverySummaryToRPCEntity(lastNotification)
		if err != nil {
			return nil, errors.Wrap(err, "map notification delivery history")
		}
		lastDelivery = res
	}

	switch cfg.DeliveryMethod {
	case models.DeliveryMethodWebhook:
		webhookCfg, err := mapOrganizationWebhookConfigToRPCEntity(cfg)
		if err != nil {
			return nil, errors.Wrap(err, "map webhook config to rpc entity")
		}
		return &organizationv1.NotificationConfig{
			Id:             cfg.GetID(),
			Name:           cfg.Name,
			DeliveryMethod: organizationv1.DeliveryMethod_DELIVERY_METHOD_WEBHOOK,
			Config: &organizationv1.NotificationConfig_Webhook{
				Webhook: webhookCfg,
			},
			LastDelivery: lastDelivery,
		}, nil
	default:
		return nil, fmt.Errorf("unsupported delivery method: %q", cfg.DeliveryMethod)
	}
}

func mapNotificationDeliveryDetailToRPCEntity(
	notification *models.Notification,
) (*organizationv1.NotificationDeliveryDetail, error) {
	var deliveryStatus organizationv1.NotificationDeliveryStatus
	if notification.StatusIsDelivered {
		deliveryStatus = organizationv1.NotificationDeliveryStatus_NOTIFICATION_DELIVERY_STATUS_SUCCESS
	} else if notification.StatusFailedDelivery {
		deliveryStatus = organizationv1.NotificationDeliveryStatus_NOTIFICATION_DELIVERY_STATUS_FAILURE
	} else {
		deliveryStatus = organizationv1.NotificationDeliveryStatus_NOTIFICATION_DELIVERY_STATUS_UNSPECIFIED
	}

	res := &organizationv1.NotificationDeliveryDetail{
		Id:                  notification.GetID(),
		EventId:             notification.EventID,
		EventType:           notification.R.Event.EventType.String,
		DeliveryStatus:      deliveryStatus,
		InitialDeliveryTime: timestamppb.New(notification.CreationTimestamp),
		Redelivered:         notification.Generation > 0,
	}

	metadata, err := notification.GetMetadata()
	if err != nil {
		return nil, errors.Wrap(err, "get notification metadata")
	}

	switch notification.DeliveryMethod {
	case models.DeliveryMethodWebhook:
		if metadata.WebhookMetadata == nil {
			return res, nil
		}

		history := make([]*organizationv1.WebhookNotificationDeliveryMetadata, len(metadata.WebhookMetadata.History))
		for idx, md := range metadata.WebhookMetadata.History {
			history[idx] = &organizationv1.WebhookNotificationDeliveryMetadata{
				StatusCode:      proto.Int64(int64(md.StatusCode)),
				RequestHeaders:  md.RequestHeaders,
				Request:         proto.String(md.Request),
				ResponseHeaders: md.ResponseHeaders,
				Response:        proto.String(md.Response),
				Error:           proto.String(md.Error),
				StartTime:       timestamppb.New(time.UnixMilli(md.StartTimestampMs)),
				EndTime:         timestamppb.New(time.UnixMilli(md.EndTimestampMs)),
			}
		}
		res.Detail = &organizationv1.NotificationDeliveryDetail_Webhook{
			Webhook: &organizationv1.WebhookNotificationDeliveryDetail{
				Webhook: history,
			},
		}
		res.RetryCount = uint64(metadata.WebhookMetadata.Retries)
		return res, nil
	default:
		return nil, fmt.Errorf("unsupported delivery method: %q", notification.DeliveryMethod)
	}
}

func mapOrganizationWebhookConfigToRPCEntity(
	cfg models.OrganizationNotificationConfig,
) (*organizationv1.WebhookConfig, error) {
	webhookCfg, err := cfg.GetOrganizationWebhookConfig()
	if err != nil {
		return nil, errors.Wrap(err, "get webhook config")
	}
	events, err := cfg.GetEvents()
	if err != nil {
		return nil, errors.Wrap(err, "get events")
	}

	res := &organizationv1.WebhookConfig{
		Events: events,
		Url:    webhookCfg.URL,
		Active: webhookCfg.Active,
		Filter: &organizationv1.WebhookNotificationFilter{
			Events:                    events,
			FilterArgocdInstanceNames: webhookCfg.Filter.FilterArgoCDInstanceNames,
			ArgocdInstanceNames:       webhookCfg.Filter.ArgoCDInstanceNames,
			FilterKargoInstanceNames:  webhookCfg.Filter.FilterKargoInstanceNames,
			KargoInstanceNames:        webhookCfg.Filter.KargoInstanceNames,
		},
	}
	if webhookCfg.Secret != "" {
		res.Secret = "********"
	}
	return res, nil
}

func mapNotificationDeliveryStatusToRPCEntity(
	notification *models.Notification,
) organizationv1.NotificationDeliveryStatus {
	var deliveryStatus organizationv1.NotificationDeliveryStatus
	if notification.StatusIsDelivered {
		deliveryStatus = organizationv1.NotificationDeliveryStatus_NOTIFICATION_DELIVERY_STATUS_SUCCESS
	} else if notification.StatusFailedDelivery {
		deliveryStatus = organizationv1.NotificationDeliveryStatus_NOTIFICATION_DELIVERY_STATUS_FAILURE
	} else {
		deliveryStatus = organizationv1.NotificationDeliveryStatus_NOTIFICATION_DELIVERY_STATUS_UNSPECIFIED
	}
	return deliveryStatus
}

func mapNotificationDeliveryToRPCEntity(
	notification *models.Notification,
) (*organizationv1.NotificationDelivery, error) {
	res := &organizationv1.NotificationDelivery{
		Id:                  notification.ID,
		EventId:             notification.R.Event.ID,
		EventType:           notification.R.Event.EventType.String,
		DeliveryStatus:      mapNotificationDeliveryStatusToRPCEntity(notification),
		InitialDeliveryTime: timestamppb.New(notification.CreationTimestamp),
		Redelivered:         notification.Generation > 0,
	}
	notificationMetadata, err := notification.GetMetadata()
	if err != nil {
		return nil, errors.Wrap(err, "get metadata")
	}

	switch notification.DeliveryMethod {
	case models.DeliveryMethodWebhook:
		if notificationMetadata.WebhookMetadata == nil {
			return res, nil
		}

		res.RetryCount = uint64(notificationMetadata.WebhookMetadata.Retries)
		if len(notificationMetadata.WebhookMetadata.History) == 0 {
			return res, nil
		}

		// Since we append last delivery to the history, the last element is the last delivery
		lastDelivery := notificationMetadata.WebhookMetadata.History[len(notificationMetadata.WebhookMetadata.History)-1]
		res.Metadata = &organizationv1.NotificationDelivery_Webhook{
			Webhook: &organizationv1.WebhookNotificationDeliveryMetadata{
				StatusCode:      proto.Int64(int64(lastDelivery.StatusCode)),
				RequestHeaders:  lastDelivery.RequestHeaders,
				Request:         proto.String(lastDelivery.Request),
				ResponseHeaders: lastDelivery.ResponseHeaders,
				Response:        proto.String(lastDelivery.Response),
				Error:           proto.String(lastDelivery.Error),
				StartTime:       timestamppb.New(time.UnixMilli(lastDelivery.StartTimestampMs)),
				EndTime:         timestamppb.New(time.UnixMilli(lastDelivery.EndTimestampMs)),
			},
		}
		return res, nil
	default:
		return nil, fmt.Errorf("unsupported delivery method: %q", notification.DeliveryMethod)
	}
}

func mapNotificationDeliverySummaryToRPCEntity(
	notification *models.Notification,
) (*organizationv1.NotificationDeliverySummary, error) {
	res := &organizationv1.NotificationDeliverySummary{
		Id:                  notification.ID,
		EventId:             notification.R.Event.ID,
		EventType:           notification.R.Event.EventType.String,
		DeliveryStatus:      mapNotificationDeliveryStatusToRPCEntity(notification),
		InitialDeliveryTime: timestamppb.New(notification.CreationTimestamp),
		Redelivered:         notification.Generation > 0,
	}
	notificationMetadata, err := notification.GetMetadata()
	if err != nil {
		return nil, errors.Wrap(err, "get metadata")
	}

	switch notification.DeliveryMethod {
	case models.DeliveryMethodWebhook:
		if notificationMetadata.WebhookMetadata == nil {
			return res, nil
		}

		res.RetryCount = uint64(notificationMetadata.WebhookMetadata.Retries)
		if len(notificationMetadata.WebhookMetadata.History) == 0 {
			return res, nil
		}

		// Since we append last delivery to the history, the last element is the last delivery
		lastDelivery := notificationMetadata.WebhookMetadata.History[len(notificationMetadata.WebhookMetadata.History)-1]
		res.Metadata = &organizationv1.NotificationDeliverySummary_Webhook{
			Webhook: &organizationv1.WebhookNotificationDeliveryMetadataSummary{
				StatusCode: proto.Int64(int64(lastDelivery.StatusCode)),
				Error:      proto.String(lastDelivery.Error),
				StartTime:  timestamppb.New(time.UnixMilli(lastDelivery.StartTimestampMs)),
				EndTime:    timestamppb.New(time.UnixMilli(lastDelivery.EndTimestampMs)),
			},
		}
		return res, nil
	default:
		return nil, fmt.Errorf("unsupported delivery method: %q", notification.DeliveryMethod)
	}
}
