package organization

import (
	"context"
	"errors"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) UpdateTeamOIDCMap(ctx context.Context, req *organizationv1.UpdateTeamOIDCMapRequest) (*organizationv1.UpdateTeamOIDCMapResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateOIDCMap(req.GetOrganizationId())); err != nil {
		return nil, err
	}
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetSso().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "sso feature is not enabled")
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	oidcTeamMap := make(map[string]string)
	teams := make(map[string]*models.Team)
	for group, teamName := range req.GetEntries() {
		var teamObj *models.Team
		var err error
		_, ok := teams[teamName]
		if !ok {
			teamObj, err = s.RepoSet.Teams().Filter(models.TeamWhere.Name.EQ(teamName), models.TeamWhere.OrganizationID.EQ(req.GetOrganizationId())).One(ctx)
			if err != nil {
				return nil, errors.New("team not found")
			}
			teams[teamName] = teamObj
		}
		oidcTeamMap[group] = teamName
	}

	if err := os.UpdateTeamOIDCMap(ctx, req.GetOrganizationId(), oidcTeamMap); err != nil {
		return nil, err
	}

	return &organizationv1.UpdateTeamOIDCMapResponse{}, nil
}
