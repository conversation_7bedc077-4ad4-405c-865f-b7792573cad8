package organization

import (
	"context"

	"github.com/volatiletech/sqlboiler/v4/boil"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/kargo"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListKargoInstancesQuota(
	ctx context.Context,
	req *organizationv1.ListKargoInstancesQuotaRequest,
) (*organizationv1.ListKargoInstancesQuotaResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateOrganization(req.GetOrganizationId())); err != nil {
		return nil, err
	}

	org, err := s.RepoSet.Organizations().GetByID(ctx, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}
	spec, err := org.GetSpec()
	if err != nil {
		return nil, err
	}

	existingSpec := spec.KargoInstanceQuota
	instances := make(map[*models.KargoInstance]int)

	for instanceID, maxStages := range existingSpec {
		instance, err := s.RepoSet.KargoInstances().GetByID(ctx, instanceID, models.KargoInstanceColumns.ID, models.KargoInstanceColumns.Name)
		if err != nil {
			return nil, err
		}
		instances[instance] = int(maxStages)
	}

	instancesQuota, err := newKargoInstancesQuotaV1(instances, s.Db, req.GetOrganizationId(), ctx)
	if err != nil {
		return nil, err
	}

	return &organizationv1.ListKargoInstancesQuotaResponse{
		Instances: instancesQuota,
	}, nil
}

func newKargoInstancesQuotaV1(instances map[*models.KargoInstance]int, db boil.ContextExecutor, orgID string, ctx context.Context) ([]*organizationv1.KargoInstanceQuota, error) {
	var kargoInstances []*organizationv1.KargoInstanceQuota
	for instance, quota := range instances {
		instanceStageCount, err := kargo.NewKargoStatSource(db, orgID).GetInstanceStageCount(ctx, instance.ID)
		if err != nil {
			return nil, err
		}

		kargoInstances = append(kargoInstances, &organizationv1.KargoInstanceQuota{
			Instance: &organizationv1.InstanceQuotaSummary{
				Id:   instance.ID,
				Name: instance.Name,
			},
			MaxStageCount:     int32(quota),
			CurrentStageCount: int32(instanceStageCount),
		})
	}
	return kargoInstances, nil
}
