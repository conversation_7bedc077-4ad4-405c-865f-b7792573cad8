package organization

import (
	"context"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

const auditArchivesMaxLimit = uint32(1000)

func (s *OrganizationV1Server) ListAuditLogsArchives(
	ctx context.Context,
	req *organizationv1.ListAuditLogsArchivesRequest,
) (*organizationv1.ListAuditLogsArchivesResponse, error) {
	id := req.GetId()
	if !s.featSvc.GetFeatureStatuses(ctx, &id).GetAuditArchive().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "audit achieve feature is not enabled")
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetId(),
		accesscontrol.NewActionGetOrganizationAuditLog()); err != nil {
		return nil, err
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)

	filters := req.GetFilters()
	if filters == nil {
		filters = &organizationv1.AuditLogArchiveFilters{}
	}

	f := organizations.AuditLogArchiveFilter{}

	var err error
	if filters.StartDate != nil {
		if f.StartDate, err = time.Parse(time.DateOnly, *filters.StartDate); err != nil {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
	}

	if filters.EndDate != nil {
		if f.EndDate, err = time.Parse(time.DateOnly, *filters.EndDate); err != nil {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
	}

	limit := filters.GetLimit()
	offset := filters.GetOffset()

	if limit == 0 {
		limit = 10
	} else if limit > auditArchivesMaxLimit {
		limit = auditArchivesMaxLimit
	}

	auditLogsArchives, err := os.ListAuditLogsArchives(ctx, req.GetId(), f, int(limit), int(offset))
	if err != nil {
		return nil, err
	}

	totalArchivesCount, err := os.CountAuditLogsArchives(ctx, req.GetId(), f)
	if err != nil {
		return nil, err
	}

	archives := make([]*organizationv1.AuditLogArchive, 0, len(auditLogsArchives))
	for _, auditLogArchive := range auditLogsArchives {
		auditLogArchiveV1, err := mapAuditLogArchiveToRPCEntity(ctx, &s.Log, auditLogArchive)
		if err != nil {
			return nil, err
		}
		archives = append(archives, auditLogArchiveV1)
	}

	return &organizationv1.ListAuditLogsArchivesResponse{
		Archives:           archives,
		TotalArchivesCount: uint32(totalArchivesCount),
	}, nil
}
