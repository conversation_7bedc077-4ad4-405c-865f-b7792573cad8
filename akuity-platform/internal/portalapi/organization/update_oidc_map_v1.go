package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) UpdateOIDCMap(ctx context.Context, req *organizationv1.UpdateOIDCMapRequest) (*organizationv1.UpdateOIDCMapResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetId(),
		accesscontrol.NewActionUpdateOIDCMap(req.GetId())); err != nil {
		return nil, err
	}

	id := req.GetId()
	if !s.featSvc.GetFeatureStatuses(ctx, &id).GetSso().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "sso feature is not enabled")
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	oidcMap := make(map[string]string)
	for group, role := range req.GetEntries() {
		oidcMap[group] = role
	}

	if err := os.UpdateOIDCMap(ctx, req.GetId(), oidcMap); err != nil {
		return nil, err
	}

	return &organizationv1.UpdateOIDCMapResponse{}, nil
}
