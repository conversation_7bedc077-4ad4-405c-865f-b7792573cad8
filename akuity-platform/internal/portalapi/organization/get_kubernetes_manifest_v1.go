package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/structpb"
	"k8s.io/apimachinery/pkg/api/errors"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/discovery"
	"k8s.io/client-go/dynamic"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/internal/utils/secret"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetKubernetesManifest(
	ctx context.Context,
	req *organizationv1.GetKubernetesManifestRequest,
) (*organizationv1.GetKubernetesManifestResponse, error) {
	enforcer, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	resSvc := k8sresource.NewServiceWithOptions(
		s.RepoSet,
		s.Db,
		req.GetOrganizationId(),
		k8sresource.WithLogger(logging.Extract(ctx)),
		k8sresource.WithEnforcer(enforcer),
	)

	cluster, err := resSvc.ValidateGetRequest(ctx, req.GetInstanceId(), req.GetClusterId())
	if err != nil {
		return nil, err
	}

	res, err := resSvc.GetResource(ctx, cluster.ID, req.GetInstanceId(), req.GetResourceId())
	if err != nil {
		return nil, err
	}

	deprecatedGVKMap, err := resSvc.GetDeprecatedGVKs([]*k8sresource.EnabledCluster{cluster})
	if err != nil {
		return nil, err
	}

	k8sResource, err := mapResourceToRPCEntity(res, deprecatedGVKMap, cluster.ID)
	if err != nil {
		return nil, err
	}

	restConfig := instances.ClusterRestConfig(cluster.Name, req.GetInstanceId())
	dynamicClient, err := dynamic.NewForConfig(restConfig)
	if err != nil {
		return nil, err
	}
	discoClient, err := discovery.NewDiscoveryClientForConfig(restConfig)
	if err != nil {
		return nil, err
	}
	gvk := schema.GroupVersionKind{Group: k8sResource.Group, Version: k8sResource.Version, Kind: k8sResource.Kind}
	resources, err := discoClient.ServerResourcesForGroupVersion(gvk.GroupVersion().String())
	if err != nil {
		return nil, err
	}
	var resource *v1.APIResource
	for i := range resources.APIResources {
		r := resources.APIResources[i]
		if r.Kind == gvk.Kind {
			resource = &r
			break
		}
	}
	if resource == nil {
		return nil, status.Errorf(codes.NotFound, "resource %s/%s not found", gvk.GroupVersion(), gvk.Kind)
	}
	resourceClient := dynamicClient.Resource(gvk.GroupVersion().WithResource(resource.Name))
	var obj *unstructured.Unstructured
	if resource.Namespaced {
		obj, err = resourceClient.Namespace(k8sResource.Namespace).Get(ctx, k8sResource.Name, v1.GetOptions{})
	} else {
		obj, err = resourceClient.Get(ctx, k8sResource.Name, v1.GetOptions{})
	}

	if err != nil {
		if errors.IsNotFound(err) {
			return nil, status.Errorf(codes.NotFound, "resource %s/%s/%s/%s not found", k8sResource.Group, k8sResource.Version, k8sResource.Kind, k8sResource.Name)
		}
		return nil, err
	}

	if obj.GetAPIVersion() == "v1" && obj.GetKind() == "Secret" {
		secret.ReductSecretData(obj.Object)
	}

	objStruct, err := structpb.NewStruct(obj.Object)
	if err != nil {
		return nil, err
	}

	return &organizationv1.GetKubernetesManifestResponse{
		Object:   objStruct,
		Resource: k8sResource,
	}, nil
}

func mapResourceToRPCEntity(resource *models.ArgoCDClusterK8SObject, deprecatedGVKs *k8sresource.DeprecatedGVKs, clusterK8sVersion string) (*organizationv1.ClusterResource, error) {
	return k8sresource.ClusterK8SObjectToAPI(resource, deprecatedGVKs, clusterK8sVersion)
}
