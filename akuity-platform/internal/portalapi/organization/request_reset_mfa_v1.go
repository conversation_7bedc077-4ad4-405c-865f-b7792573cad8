package organization

import (
	"context"
	"fmt"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) RequestMFAReset(ctx context.Context, req *organizationv1.RequestMFAResetRequest) (*organizationv1.RequestMFAResetResponse, error) {
	_, actor, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateOrganization(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	if config.IsSelfHosted {
		return nil, shared.ErrNotAvailableInSelfHostedVersion
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetMultiFactorAuth().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "multi-factor auth feature is not enabled")
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)
	org, err := os.Get(ctx, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}

	if mfaSettings, err := org.GetMFASettings(); err != nil || !mfaSettings.Enabled {
		return nil, shared.ErrUnavailable
	}

	user, err := s.RepoSet.Users().Filter(qm.Where("email = ?", req.GetEmail())).One(ctx)
	if err != nil {
		return nil, err
	}

	if _, err = os.GetOrganizationUser(ctx, req.GetOrganizationId(), user.ID); err != nil {
		return nil, err
	}

	if s.Cfg.SlackSupportIncomingWebhook != "" {
		initiator := "akp_key: " + actor.ID
		if actor.Type == accesscontrol.ActorTypeUser {
			initiator = actor.Extras["email"].(string)
		}
		msg := s.resetMFAMessage(org, s.Cfg.AimsURL, initiator, user.Email)
		go shared.PostSlackMessageAsSections(&s.Log, s.Cfg.SlackSupportIncomingWebhook, msg)
	}

	return &organizationv1.RequestMFAResetResponse{}, nil
}

func (s *OrganizationV1Server) resetMFAMessage(org *models.Organization, aimsURL, initiator, user string) [][]string {
	organizationIdLink := shared.Code(org.ID)
	if aimsURL != "" {
		organizationIdLink = fmt.Sprintf("<%s|%s>", aimsURL+"/organizations/"+org.ID, org.ID)
	}
	resetLink := fmt.Sprintf("<%s|Reset MFA>", aimsURL+"/organizations/"+org.ID+"?tab=mfa-reset")

	return [][]string{
		{shared.HeaderSectionKey, "New MFA Reset Request 🔒"},
		{"Initiator", initiator},
		{"Organization ID", organizationIdLink},
		{"Organization Name", shared.Code(org.Name)},
		{"Locked User", user},
		{"*Reset Link*", resetLink},
	}
}
