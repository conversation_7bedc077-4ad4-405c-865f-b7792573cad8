package organization

import (
	"context"
	"fmt"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/utils/ai/deprecated"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ResolveKubernetesAssistantConversation(ctx context.Context, req *organizationv1.ResolveKubernetesAssistantConversationRequest) (*organizationv1.ResolveKubernetesAssistantConversationResponse, error) {
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}

	// if actor is AKP user, we need to check the permission with enforcer
	// if actor is ArgoCD, we skip the permission check since ArgoCD user permissions are incompatible with the akp permission model.
	// ArgoCD users can only access resource from their own organization/instance
	if actor.Type != accesscontrol.ActorTypeArgoCD {
		if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetOrganizationId(),
			accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId())); err != nil {
			return nil, err
		}
	} else {
		if actor.Extras["organizationID"] != req.GetOrganizationId() || actor.Extras["instanceID"] != req.GetInstanceId() {
			return nil, status.Errorf(codes.PermissionDenied, "organization or instance mismatch")
		}
	}

	if actor.Type != accesscontrol.ActorTypeArgoCD {
		// if actor is AKP user, we need to check if KubeVision is enabled (AI Assistant is under KubeVision)
		orgID := req.GetOrganizationId()
		if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetMultiClusterK8SDashboard().Enabled() {
			return nil, status.Error(codes.PermissionDenied, "multi-cluster k8s dashboard feature is not enabled")
		}
	} else {
		// if actor is ArgoCD user, we need to check if AI Assistant extension is enabled
		instanceSvc := instances.NewServiceWithOptions(s.Db,
			s.Cfg.DomainSuffix,
			s.featSvc,
			instances.WithOrganizationScope(req.GetOrganizationId()),
			instances.WithLogger(logging.Extract(ctx)),
		)
		instanceConfig, err := instanceSvc.ArgoCDInstanceConfigs().GetByID(ctx, req.GetInstanceId())
		if err != nil {
			return nil, fmt.Errorf("get instance config: %w", err)
		}
		instanceSpec, err := instanceConfig.GetSpec()
		if err != nil {
			return nil, fmt.Errorf("get instance spec: %w", err)
		}
		if !instanceSpec.AkuityIntelligenceExtension.Enabled && !instanceSpec.AssistantExtensionEnabled {
			return nil, status.Error(codes.PermissionDenied, "kubevision or ai assistant extension is not enabled for this organization")
		}
	}

	state, err := deprecated.ResolveAssistantConversation(ctx, s.Db, req.GetInstanceId(), req.GetState(), req.GetResolved())
	return &organizationv1.ResolveKubernetesAssistantConversationResponse{State: state}, err
}
