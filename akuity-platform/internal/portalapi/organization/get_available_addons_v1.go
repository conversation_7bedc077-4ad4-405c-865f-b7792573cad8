package organization

import (
	"context"
	"database/sql"
	"errors"

	"github.com/volatiletech/null/v8"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	"github.com/akuityio/akuity-platform/pkg/billing"
)

func (s *OrganizationV1Server) GetAvailableAddons(
	ctx context.Context,
	req *organizationv1.GetAvailableAddonsRequest,
) (*organizationv1.GetAvailableAddonsResponse, error) {
	if config.IsSelfHosted {
		return nil, shared.ErrNotAvailableInSelfHostedVersion
	}

	plan := req.GetPlan()
	if plan == "" {
		return nil, status.Error(codes.InvalidArgument, "plan is required")
	}

	planEntry, err := s.RepoSet.OrganizationPlans(models.OrganizationPlanWhere.Name.EQ(plan)).One(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return &organizationv1.GetAvailableAddonsResponse{}, nil
		}
		return nil, err
	}

	// This endpoint is Stripe-only for now.
	addons, err := s.RepoSet.AddonPlans(models.AddonPlanWhere.IncludedWithPlan.EQ(null.StringFrom(planEntry.Name))).ListAll(ctx)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return &organizationv1.GetAvailableAddonsResponse{}, nil
		}
		return nil, err
	}

	var resp organizationv1.GetAvailableAddonsResponse
	for _, addon := range addons {
		price, err := s.BillingProviders[billing.StripeBillingProvider].GetPrice(addon.ProductID, &addon.IncludedQuantity.Int)
		if err != nil {
			return nil, err
		}
		priceRes := uint32(price)
		min := uint32(addon.MinQuantity)
		max := uint32(addon.MaxQuantity)
		resp.Addons = append(resp.Addons, &organizationv1.SubscriptionAddon{
			Name:             addon.Name,
			IncludedQuantity: uint32(addon.IncludedQuantity.Int),
			DisplayName:      addon.DisplayName,
			UnitPrice:        &priceRes,
			MinimumQuantity:  &min,
			MaximumQuantity:  &max,
			Description:      &addon.Description.String,
		})
	}

	return &resp, nil
}
