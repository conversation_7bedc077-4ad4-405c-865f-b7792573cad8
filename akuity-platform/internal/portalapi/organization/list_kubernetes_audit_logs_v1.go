package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListKubernetesAuditLogs(
	ctx context.Context,
	req *organizationv1.ListKubernetesAuditLogsRequest,
) (*organizationv1.ListKubernetesAuditLogsResponse, error) {
	enforcer, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionDeleteKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}
	resSvc := k8sresource.NewServiceWithOptions(
		s.RepoSet,
		s.Db,
		req.GetOrganizationId(),
		k8sresource.WithLogger(logging.Extract(ctx)),
		k8sresource.WithEnforcer(enforcer),
	)

	var clusterInfo *k8sresource.EnabledCluster
	if enforcer.GetActor().Type == accesscontrol.ActorTypeArgoCD {
		var cluster *models.ArgoCDCluster
		var err error
		instanceSvc := instances.NewServiceWithOptions(s.Db,
			s.Cfg.DomainSuffix,
			s.featSvc,
			instances.WithOrganizationScope(req.GetOrganizationId()),
			instances.WithLogger(logging.Extract(ctx)),
		)

		cluster, err = instanceSvc.GetClusterByID(ctx, req.GetClusterId())
		if err != nil {
			return nil, err
		}
		if cluster != nil {
			clusterInfo = &k8sresource.EnabledCluster{
				ID:   cluster.ID,
				Name: cluster.Name,
			}
		}
	} else {
		cluster, err := resSvc.ValidateGetRequest(ctx, req.GetInstanceId(), req.GetClusterId())
		if err != nil {
			return nil, err
		}
		clusterInfo = &k8sresource.EnabledCluster{
			ID:   cluster.ID,
			Name: cluster.Name,
		}
	}
	if clusterInfo == nil {
		return nil, status.Error(codes.InvalidArgument, "cluster not found")
	}
	instance, err := models.ArgoCDInstances(models.ArgoCDInstanceWhere.ID.EQ(req.GetInstanceId())).One(ctx, s.Db)
	if err != nil {
		return nil, err
	}

	res, err := resSvc.GetResource(ctx, clusterInfo.ID, req.GetInstanceId(), req.GetResourceId())
	if err != nil {
		return nil, err
	}
	appInfo, err := res.GetArgoCDApplicationInfo()
	if err != nil {
		return nil, err
	}

	offset := int(req.GetOffset())
	limit := int(req.GetLimit())
	if limit == 0 {
		limit = 100
	}

	objects := []organizations.AuditLogObject{{
		ObjectType:                  string(models.K8sResourceAuditObject),
		ObjectName:                  []string{res.Name},
		ObjectGroup:                 []string{res.Group.String},
		ObjectKind:                  []string{res.Kind.String},
		ObjectParentName:            []string{clusterInfo.Name},
		ObjectParentParentName:      []string{instance.Name},
		ObjectParentApplicationName: []string{appInfo.Name},
	}}

	filters := organizations.AuditLogFilter{
		ActorID: req.ActorId,
		Action:  req.Action,
		Objects: objects,
	}

	if req.GetStartTime() != nil {
		filters.StartTime = req.GetStartTime().AsTime()
	}

	if req.GetEndTime() != nil {
		filters.EndTime = req.GetEndTime().AsTime()
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)

	auditLogs, err := os.ListAuditLogs(ctx, req.GetOrganizationId(), filters, limit, offset)
	if err != nil {
		return nil, err
	}
	count, err := os.CountAuditLogs(ctx, req.GetOrganizationId(), filters)
	if err != nil {
		return nil, err
	}

	items := make([]*organizationv1.AuditLog, 0, len(auditLogs))
	for _, auditLog := range auditLogs {
		auditLogV1, err := MapAuditLogToRPCEntity(auditLog)
		if err != nil {
			return nil, err
		}
		items = append(items, auditLogV1)
	}

	return &organizationv1.ListKubernetesAuditLogsResponse{
		Items: items,
		Count: uint32(count),
	}, nil
}
