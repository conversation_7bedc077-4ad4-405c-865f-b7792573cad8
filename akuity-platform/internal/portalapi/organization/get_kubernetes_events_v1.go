package organization

import (
	"context"

	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/timestamppb"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/client-go/kubernetes"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetKubernetesEvents(
	ctx context.Context,
	req *organizationv1.GetKubernetesEventsRequest,
) (*organizationv1.GetKubernetesEventsResponse, error) {
	enforcer, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	resSvc := k8sresource.NewServiceWithOptions(
		s.RepoSet,
		s.Db,
		req.GetOrganizationId(),
		k8sresource.WithLogger(logging.Extract(ctx)),
		k8sresource.WithEnforcer(enforcer),
	)

	cluster, err := resSvc.ValidateGetRequest(ctx, req.GetInstanceId(), req.GetClusterId())
	if err != nil {
		return nil, err
	}

	res, err := resSvc.GetResource(ctx, cluster.ID, req.GetInstanceId(), req.GetResourceId())
	if err != nil {
		return nil, err
	}

	fieldSelector := fields.SelectorFromSet(map[string]string{
		"involvedObject.name":      res.Name,
		"involvedObject.uid":       res.ID,
		"involvedObject.namespace": res.Namespace.String,
	}).String()
	clusterConfig := instances.ClusterRestConfig(cluster.Name, req.GetInstanceId())
	k8sClientset, err := kubernetes.NewForConfig(clusterConfig)
	if err != nil {
		return nil, err
	}
	opts := metav1.ListOptions{FieldSelector: fieldSelector}
	list, err := k8sClientset.CoreV1().Events(res.Namespace.String).List(ctx, opts)
	if err != nil {
		return nil, err
	}

	events := lo.Map(list.Items, func(item v1.Event, _ int) *organizationv1.KubernetesEventsData {
		var firstTimestamp *timestamppb.Timestamp
		if !item.FirstTimestamp.UTC().IsZero() {
			firstTimestamp = timestamppb.New(item.FirstTimestamp.UTC())
		}
		var lastTimestamp *timestamppb.Timestamp
		if !item.LastTimestamp.UTC().IsZero() {
			lastTimestamp = timestamppb.New(item.LastTimestamp.UTC())
		}
		var eventTimestamp *timestamppb.Timestamp
		if !item.EventTime.UTC().IsZero() {
			eventTimestamp = timestamppb.New(item.EventTime.UTC())
		}
		return &organizationv1.KubernetesEventsData{
			Uid:            string(item.UID),
			Type:           item.Type,
			Reason:         item.Reason,
			Message:        item.Message,
			Count:          item.Count,
			FirstTimestamp: firstTimestamp,
			LastTimestamp:  lastTimestamp,
			EventTimestamp: eventTimestamp,
		}
	})
	return &organizationv1.GetKubernetesEventsResponse{Events: events}, nil
}
