package organization

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/csv"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/volatiletech/null/v8"
	"google.golang.org/genproto/googleapis/api/httpbody"
	"google.golang.org/genproto/googleapis/rpc/http"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/anypb"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/services/notifications"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/client"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	"github.com/akuityio/akuity-platform/portal/server"
)

func (s *OrganizationV1Server) GetAuditLogsInCSV(
	req *organizationv1.GetAuditLogsInCSVRequest,
	srv organizationv1.OrganizationService_GetAuditLogsInCSVServer,
) error {
	ctx := srv.Context()
	id := req.GetId()
	if !s.featSvc.GetFeatureStatuses(ctx, &id).GetAuditRecordExport().Enabled() {
		return status.Error(codes.PermissionDenied, "audit record export feature is not enabled")
	}

	enforcer, err := shared.NewOrganizationEnforcer(ctx, s.Db, s.acs, req.GetId())
	if err != nil {
		return err
	}

	filters := req.GetFilters()
	if filters == nil {
		filters = &organizationv1.AuditFilters{}
	}

	action := accesscontrol.NewActionGetOrganizationAuditLog()
	// first check if user has full access to audit logs
	ok, err := enforcer.EnforceAction(ctx, action)
	if err != nil {
		return err
	}

	if !ok {
		if err := s.checkAuditFilterInstancePermissions(ctx, enforcer, filters, req.GetId()); err != nil {
			return err
		}
	}

	writeAuditLog := func(data []string) (string, error) {
		var b []byte
		buf := bytes.NewBuffer(b)
		cw := csv.NewWriter(buf)
		if err := cw.Write(data); err != nil {
			return "", fmt.Errorf("write csv data: %w", err)
		}
		cw.Flush()
		if err := cw.Error(); err != nil {
			return "", err
		}
		s := buf.String()
		if s == "" {
			return "", nil
		}
		return s[:len(s)-1], nil
	}

	txDB, txBeginner := database.WithTxBeginner(s.Db)
	os := s.newOrganizationServiceFunc()(txDB, txBeginner)

	objects := CreateAuditLogObjectsFromFilter(filters)
	f := organizations.AuditLogFilter{
		ActorID:   filters.ActorId,
		Action:    filters.Action,
		ActorType: filters.ActorType,
		Objects:   objects,
	}

	if filters.StartTime != nil {
		if f.StartTime, err = time.Parse(time.RFC3339, *filters.StartTime); err != nil {
			return status.Error(codes.InvalidArgument, err.Error())
		}
	}

	if filters.EndTime != nil {
		if f.EndTime, err = time.Parse(time.RFC3339, *filters.EndTime); err != nil {
			return status.Error(codes.InvalidArgument, err.Error())
		}
	}

	// set bigger limit for csv export
	limit := 100

	// exporting all data ignoring offset
	offset := 0

	var logItems []*models.AuditLog

	header := []string{"Date", "Action", "ActorID", "ActorType", "ObjectType", "ObjectIDKind", "ObjectIDGroup", "ObjectIDName", "ObjectParentIDKind", "ObjectParentIDGroup", "ObjectParentIDName", "DetailsActionType", "DetailsMessage", "DetailsPatch", "LastOccurred", "Count", "ActorIP"}
	res, err := writeAuditLog(header)
	if err != nil {
		return err
	}

	httpHeaders, err := anypb.New(&http.HttpHeader{
		Key:   "Content-Disposition",
		Value: fmt.Sprintf("attachment; filename=audit_logs_%d.csv", time.Now().UTC().Unix()),
	})
	if err != nil {
		return err
	}

	if err := srv.Send(&httpbody.HttpBody{
		ContentType: "text/csv",
		Data:        []byte(res),
		Extensions:  []*anypb.Any{httpHeaders},
	}); err != nil {
		return err
	}

	for ; ; offset += limit {
		logItems, err = os.ListAuditLogs(ctx, req.GetId(), f, limit, offset)
		if err != nil {
			return err
		}
		for _, item := range logItems {
			row := make([]string, len(header))

			actor, err := item.GetActor()
			if err != nil {
				return err
			}

			object, err := item.GetObject()
			if err != nil {
				return err
			}

			details, err := item.GetDetails()
			if err != nil {
				return err
			}

			if item.Timestamp.String() != "" {
				row[0] = item.Timestamp.Format("2006-01-02 15:04:05 -0700 MST")
			} else {
				row[0] = ""
			}
			row[1] = item.Action
			row[2] = actor.ID
			row[3] = string(actor.Type)
			row[4] = string(object.Type)

			if object.ID != nil {
				row[5] = object.ID.Kind
				row[6] = object.ID.Group
				row[7] = object.ID.Name
			}

			if object.ParentID != nil {
				row[10] = object.ParentID.Name
			}

			if details.ActionType != nil {
				row[11] = *details.ActionType
			}

			row[12] = details.Message
			row[13] = details.Patch
			lastOccurredTimestamp := ""
			if !item.LastOccurredTimestamp.Time.IsZero() {
				lastOccurredTimestamp = item.LastOccurredTimestamp.Time.Format("2006-01-02 15:04:05 -0700 MST")
			}
			row[14] = lastOccurredTimestamp
			row[15] = strconv.FormatInt(int64(item.Count), 10)
			row[16] = actor.IP

			res, err := writeAuditLog(row)
			if err != nil {
				return err
			}

			if err := srv.Send(&httpbody.HttpBody{
				ContentType: "text/csv",
				Data:        []byte(res),
				Extensions:  []*anypb.Any{httpHeaders},
			}); err != nil {
				return err
			}
		}

		if len(logItems) != limit {
			break
		}
	}
	if err := addAuditLog(ctx, s.RepoSet, req.GetId()); err != nil {
		return status.Error(codes.Internal, fmt.Errorf("add audit log: %w", err).Error())
	}
	return nil
}

func addAuditLog(ctx context.Context, repoSet client.RepoSet, id string) (err error) {
	logItem := &models.AuditLog{OrganizationID: null.StringFrom(id), Action: models.ExportedAsCSV, Timestamp: time.Now()}
	actorProvider := server.ContextAuditActorProvider{}
	actor := actorProvider.GetActor(ctx)
	if err = logItem.SetActor(actor); err != nil {
		return err
	}
	// Exporting audit log has 'no' object name to refer, so we set it to 'audit log' for now
	if err = logItem.SetObject(&models.AuditObject{Type: models.AuditLogObject, ID: &models.AuditObjID{Name: "audit log"}}); err != nil {
		return err
	}
	if err := notifications.CreateAuditLogWithEvent(ctx, id, repoSet, logItem, nil); err != nil {
		return err
	}
	return nil
}

func (s *OrganizationV1Server) checkAuditFilterInstancePermissions(ctx context.Context, enforcer accesscontrol.Enforcer, filters *organizationv1.AuditFilters, orgID string) error {
	// audit logs needs to be limited to the specific instance.
	if filters.ArgocdInstance != nil && filters.ArgocdInstance.Enabled != nil && *filters.ArgocdInstance.Enabled && len(filters.ArgocdInstance.ObjectName) > 0 {
		instanceSvc := instances.NewServiceWithOptions(s.Db,
			s.Cfg.DomainSuffix,
			s.featSvc,
			instances.WithOrganizationScope(orgID),
			instances.WithLogger(logging.Extract(ctx)),
		)
		for _, name := range filters.ArgocdInstance.ObjectName {
			inst, err := instanceSvc.GetInstanceByName(ctx, name)
			if err != nil {
				if instances.IsNotFoundErr(err) {
					return status.Errorf(codes.NotFound, "instance %q not found", name)
				}
				return err
			}
			// we allow to get audit logs if use has access to the instance.
			action := accesscontrol.NewActionGetWorkspaceInstances(inst.WorkspaceID.String, inst.GetID())
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return err
			}
			if !ok {
				return status.Errorf(codes.PermissionDenied, "no permission to %s on %q %s",
					action.Verb, action.Object, name)
			}
		}
		return nil
	}
	if filters.KargoInstance != nil && filters.KargoInstance.Enabled != nil && *filters.KargoInstance.Enabled && len(filters.KargoInstance.ObjectName) > 0 {
		for _, name := range filters.KargoInstance.ObjectName {
			kargoModel, err := s.RepoSet.KargoInstances().Filter(
				models.KargoInstanceWhere.OrganizationOwner.EQ(null.StringFrom(orgID)),
				models.KargoInstanceWhere.Name.EQ(name),
			).One(ctx)
			if err != nil {
				if errors.Is(err, sql.ErrNoRows) {
					return status.Errorf(codes.NotFound, "instance %q not found", name)
				}
				return err
			}
			// we allow to get audit logs if use has access to the instance.
			action := accesscontrol.NewActionGetWorkspaceKargoInstances(kargoModel.WorkspaceID.String, kargoModel.ID)
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return err
			}
			if !ok {
				return status.Errorf(codes.PermissionDenied, "no permission to %s on %q %s",
					action.Verb, action.Object, name)
			}
		}
		return nil
	}
	if filters.Workspace != nil && filters.Workspace.Enabled != nil && *filters.Workspace.Enabled && len(filters.Workspace.ObjectName) > 0 {
		for _, name := range filters.Workspace.ObjectName {
			ws, err := s.RepoSet.Workspaces().Filter(
				models.WorkspaceWhere.OrganizationID.EQ(orgID),
				models.WorkspaceWhere.Name.EQ(name),
			).One(ctx, models.WorkspaceColumns.ID)
			if err != nil {
				if errors.Is(err, sql.ErrNoRows) {
					return status.Errorf(codes.NotFound, "workspace %q not found", name)
				}
				return err
			}
			action := accesscontrol.NewActionGetOrganizationWorkspaces(ws.ID)
			ok, err := enforcer.EnforceAction(ctx, action)
			if err != nil {
				return err
			}
			if !ok {
				return status.Errorf(codes.PermissionDenied, "no permission to %s on %q %s",
					action.Verb, action.Object, name)
			}
		}
		return nil
	}

	return shared.NewPermissionDeniedErr(accesscontrol.NewActionGetOrganizationAuditLog())
}
