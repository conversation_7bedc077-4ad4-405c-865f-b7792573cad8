package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/ai"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetAIConversation(
	ctx context.Context,
	req *organizationv1.GetAIConversationRequest,
) (*organizationv1.GetAIConversationResponse, error) {
	actor, err := s.checkAISupportEngineerPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(), req.GetKargoInstanceId(),
		accesscontrol.NewActionGetAISupportEngineer(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	featureStatuses := s.featSvc.GetFeatureStatuses(ctx, &orgID)
	aiSvc, err := ai.NewService(s.Db, s.RepoSet, featureStatuses, orgID, s.Cfg.AI, s.hostRestConfig, logging.Extract(ctx))
	if err != nil {
		return nil, err
	}
	conversation, err := aiSvc.GetConversation(ctx, req.GetId(), actor)
	if err != nil {
		return nil, err
	}
	return &organizationv1.GetAIConversationResponse{
		Conversation: conversation,
	}, nil
}

func (s *OrganizationV1Server) GetAIConversationStream(
	req *organizationv1.GetAIConversationStreamRequest,
	server organizationv1.OrganizationService_GetAIConversationStreamServer,
) error {
	ctx := server.Context()
	actor, err := s.checkAISupportEngineerPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(), req.GetKargoInstanceId(),
		accesscontrol.NewActionGetAISupportEngineer(req.GetOrganizationId()))
	if err != nil {
		return err
	}

	orgID := req.GetOrganizationId()
	featureStatuses := s.featSvc.GetFeatureStatuses(ctx, &orgID)
	aiSvc, err := ai.NewService(s.Db, s.RepoSet, featureStatuses, orgID, s.Cfg.AI, s.hostRestConfig, logging.Extract(ctx))
	if err != nil {
		return err
	}

	conversations := aiSvc.WatchConversation(ctx, req.GetId(), actor, s.aiConversationWatcher)
	for conversation := range conversations {
		err := server.Send(&organizationv1.GetAIConversationStreamResponse{
			Conversation: conversation,
		})
		if err != nil {
			return err
		}
	}
	return nil
}
