package organization

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"google.golang.org/genproto/googleapis/api/httpbody"
	"google.golang.org/genproto/googleapis/rpc/http"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/anypb"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetKubeVisionUsage(ctx context.Context, req *organizationv1.GetKubeVisionUsageRequest) (*organizationv1.GetKubeVisionUsageResponse, error) {
	if config.IsSelfHosted {
		return nil, shared.ErrNotAvailableInSelfHostedVersion
	}

	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId())); err != nil {
		return nil, err
	}
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetMultiClusterK8SDashboard().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "multi-cluster k8s dashboard feature is not enabled")
	}

	resSvc := k8sresource.NewServiceWithOptions(s.RepoSet, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)))

	startTime := time.Time{}
	if req.GetStartTime() != nil {
		startTime = req.GetStartTime().AsTime()
	}
	endTime := time.Now()
	if req.GetEndTime() != nil {
		endTime = req.GetEndTime().AsTime()
	}

	usage, err := resSvc.GetKubeVisionUsage(ctx, startTime, endTime)
	if err != nil {
		return nil, err
	}
	return &organizationv1.GetKubeVisionUsageResponse{Usage: usage}, nil
}

func (s *OrganizationV1Server) GetKubeVisionUsageToCSV(
	req *organizationv1.GetKubeVisionUsageRequest,
	srv organizationv1.OrganizationService_GetKubeVisionUsageToCSVServer,
) error {
	if config.IsSelfHosted {
		return shared.ErrNotAvailableInSelfHostedVersion
	}

	ctx := srv.Context()
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId())); err != nil {
		return err
	}
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return shared.ErrUnauthenticated
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetMultiClusterK8SDashboard().Enabled() {
		return status.Error(codes.PermissionDenied, "multi-cluster k8s dashboard feature is not enabled")
	}

	resSvc := k8sresource.NewServiceWithOptions(s.RepoSet, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)))

	startTime := time.Time{}
	if req.GetStartTime() != nil {
		startTime = req.GetStartTime().AsTime()
	}
	endTime := time.Now()
	if req.GetEndTime() != nil {
		endTime = req.GetEndTime().AsTime()
	}

	httpHeaders, err := anypb.New(&http.HttpHeader{
		Key:   "Content-Disposition",
		Value: fmt.Sprintf("attachment; filename=kubevision_usage_%s_%s.csv", startTime.Format(time.DateOnly), endTime.Format(time.DateOnly)),
	})
	if err != nil {
		return err
	}

	body := &httpbody.HttpBody{
		ContentType: "text/csv",
		Data: []byte(strings.Join([]string{
			"Timestamp",
			"Instance Count",
			"Cluster Count",
			"API Resource Count",
			"Object Count",
			"Node Count",
			"Pod Count",
			"Container Count",
		}, ",")),
		Extensions: []*anypb.Any{httpHeaders},
	}
	err = srv.Send(body)
	if err != nil {
		return err
	}
	usage, err := resSvc.GetKubeVisionUsage(ctx, startTime, endTime)
	if err != nil {
		return err
	}
	rows := lo.Map(usage, func(u *organizationv1.KubeVisionUsage, idx int) string {
		return fmt.Sprintf("%s,%d,%d,%d,%d,%d,%d,%d", u.GetTimestamp().AsTime().Format(time.DateOnly), u.GetInstanceCount(), u.GetClusterCount(), u.GetApiResourceCount(), u.GetObjectCount(), u.GetNodeCount(), u.GetPodCount(), u.GetContainerCount())
	})

	body = &httpbody.HttpBody{
		ContentType: "text/csv",
		Data:        []byte(strings.Join(rows, "\n")),
		Extensions:  []*anypb.Any{httpHeaders},
	}
	return srv.Send(body)
}
