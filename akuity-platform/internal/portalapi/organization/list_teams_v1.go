package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

const (
	teamsMaxLimit     = int64(1000)
	teamsDefaultLimit = int64(10)
)

func (s *OrganizationV1Server) ListTeams(
	ctx context.Context,
	req *organizationv1.ListTeamsRequest,
) (*organizationv1.ListTeamsResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.OrganizationId,
		accesscontrol.NewActionGetTeams(accesscontrol.ResourceAny))
	if err != nil {
		return nil, err
	}

	if err := s.isTeamEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	actor := ctxutil.GetActor(ctx)
	if actor == nil || actor.Type != accesscontrol.ActorTypeUser {
		return nil, shared.ErrUnauthenticated
	}

	var groups []string
	g, ok := actor.Extras["groups"].([]string)
	if ok {
		groups = g
	}

	limit := req.GetLimit()
	offset := req.GetOffset()
	if limit == 0 {
		limit = teamsDefaultLimit
	} else if limit > teamsMaxLimit {
		limit = teamsMaxLimit
	}

	if offset < 0 {
		offset = 0
	}

	teamSvc := teams.NewService(s.Db)
	userTeams, err := teamSvc.ListTeams(ctx, req.GetOrganizationId(), actor.ID, int(limit), int(offset), groups)
	if err != nil {
		return nil, err
	}
	teamCount, err := teamSvc.CountTeams(ctx, req.GetOrganizationId())
	if err != nil {
		return nil, err
	}
	return &organizationv1.ListTeamsResponse{
		UserTeams: newUserTeams(userTeams),
		Count:     teamCount,
	}, nil
}

func newUserTeams(userTeams []*teams.UserTeam) []*organizationv1.UserTeam {
	res := make([]*organizationv1.UserTeam, 0, len(userTeams))
	for _, team := range userTeams {
		res = append(res, newUserTeam(team))
	}
	return res
}
