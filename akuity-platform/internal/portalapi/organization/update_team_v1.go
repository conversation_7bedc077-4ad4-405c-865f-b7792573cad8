package organization

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/customroles"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	"github.com/akuityio/akuity-platform/internal/utils/validator"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) UpdateTeam(
	ctx context.Context,
	req *organizationv1.UpdateTeamRequest,
) (*organizationv1.UpdateTeamResponse, error) {
	_, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetOrganizationId(),
		accesscontrol.NewActionUpdateTeams(req.GetName()))
	if err != nil {
		return nil, err
	}

	if err := s.isTeamEnabled(ctx, req.GetOrganizationId()); err != nil {
		return nil, err
	}

	actor := ctxutil.GetActor(ctx)
	if actor == nil || actor.Type != accesscontrol.ActorTypeUser {
		return nil, shared.ErrUnauthenticated
	}

	if err := validator.ValidateResourceDescription(req.GetDescription(), 255); err != nil {
		return nil, err
	}
	if len(req.CustomRoles) > 0 {
		orgID := req.GetOrganizationId()
		if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetCustomRoles().Enabled() {
			return nil, status.Error(codes.PermissionDenied, "custom roles feature is not enabled")
		}
	}
	for _, cr := range req.CustomRoles {
		customRole, err := s.crs.GetCustomRole(ctx, req.GetOrganizationId(), cr)
		if err != nil {
			if errors.Is(err, customroles.ErrNotFound) {
				return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("custom role with id %s not found", cr))
			}
			return nil, err
		}
		if err := accesscontrol.ValidateOrganizationPolicy(customRole.Policy.String); err != nil {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
		if customRole.WorkspaceID.String != "" {
			return nil, status.Error(codes.InvalidArgument, fmt.Sprintf("workspace level custom role %s not allowed for the teams", cr))
		}
	}

	teamSvc := teams.NewService(s.Db)
	userTeam, err := teamSvc.UpdateUserTeam(ctx, req.GetOrganizationId(), actor.ID, req.GetName(), req.GetDescription(), req.GetCustomRoles())
	if err != nil {
		return nil, err
	}

	return &organizationv1.UpdateTeamResponse{
		UserTeam: newUserTeam(userTeam),
	}, nil
}
