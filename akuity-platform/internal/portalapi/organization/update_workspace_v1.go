package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/teams"
	"github.com/akuityio/akuity-platform/internal/services/workspaces"
	"github.com/akuityio/akuity-platform/internal/utils/validator"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) UpdateWorkspace(
	ctx context.Context,
	req *organizationv1.UpdateWorkspaceRequest,
) (*organizationv1.UpdateWorkspaceResponse, error) {
	enforcer, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.OrganizationId,
		accesscontrol.NewActionUpdateOrganizationWorkspaces(req.GetId()))
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetWorkspaces().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "workspaces feature is not enabled")
	}

	// Validate request payload
	if err := validator.ValidateResourceName(req.GetName(), minWorkspaceNameLength, maxWorkspaceNameLength); err != nil {
		return nil, err
	}
	if err := validator.ValidateResourceDescription(req.GetDescription(), 255); err != nil {
		return nil, err
	}

	teamSvc := teams.NewService(s.Db)
	workspaceSvc := workspaces.NewService(s.Db, teamSvc, s.Cfg.FeatureGatesSource)
	ws, err := workspaceSvc.UpdateWorkspace(ctx, req.GetId(), req.GetName(), req.GetDescription())
	if err != nil {
		return nil, err
	}
	v1ws, err := mapWorkspaceToRPCEntity(ctx, *ws, enforcer)
	if err != nil {
		return nil, err
	}
	return &organizationv1.UpdateWorkspaceResponse{
		Workspace: v1ws,
	}, nil
}
