package organization

import (
	"context"

	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/customroles"
	"github.com/akuityio/akuity-platform/internal/utils/validator"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) UpdateWorkspaceCustomRole(
	ctx context.Context,
	req *organizationv1.UpdateWorkspaceCustomRoleRequest,
) (*organizationv1.UpdateWorkspaceCustomRoleResponse, error) {
	orgID := req.GetOrganizationId()
	if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetCustomRoles().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "custom roles feature is not enabled")
	}

	_, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.OrganizationId,
		accesscontrol.NewActionUpdateWorkspaceCustomRole(req.GetWorkspaceId(), req.GetId()))
	if err != nil {
		return nil, err
	}

	if err := accesscontrol.ValidateWorkspacePolicy(req.Policy); err != nil {
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}

	if err := validator.ValidateResourceName(req.Name, 2, 50); err != nil {
		return nil, err
	}

	if err := validator.ValidateResourceDescription(req.Description, 255); err != nil {
		return nil, err
	}

	cr, err := s.crs.UpdateCustomRole(ctx, req.OrganizationId, req.Id, req.Name, req.Description, req.Policy)
	if err != nil {
		if errors.Is(err, customroles.ErrNameConflict) {
			return nil, status.Error(codes.AlreadyExists, err.Error())
		}
		return nil, err
	}

	return &organizationv1.UpdateWorkspaceCustomRoleResponse{
		CustomRole:  newCustomRole(cr),
		WorkspaceId: req.WorkspaceId,
	}, nil
}
