package organization

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"google.golang.org/genproto/googleapis/api/httpbody"
	"google.golang.org/genproto/googleapis/rpc/http"
	"google.golang.org/protobuf/types/known/anypb"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	k8sv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/k8s/v1"
)

func (s *OrganizationV1Server) ListKubernetesResources(ctx context.Context, req *organizationv1.ListKubernetesResourcesRequest) (*organizationv1.ListKubernetesResourcesResponse, error) {
	enforcer, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	resSvc := k8sresource.NewServiceWithOptions(s.RepoSet, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)), k8sresource.WithEnforcer(enforcer))

	offset := int(req.GetOffset())
	limit := int(req.GetLimit())
	if limit == 0 {
		limit = 100
	}
	clusterInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), false, req.GetClusterIds()...)
	if err != nil {
		return nil, err
	}

	existingTypes, err := resSvc.GetResourceTypes(ctx, req.GetInstanceId(), clusterInfo.GetClusters())
	if err != nil {
		return nil, err
	}
	var find bool
	for _, t := range existingTypes {
		if t.GetGroupVersionKind().GetGroup() == req.GetGroup() && t.GetGroupVersionKind().GetKind() == req.GetKind() {
			find = true
			break
		}
	}

	if !find && req.HasDeletionTimestamp == nil {
		return nil, nil
	}

	clusterIDs := clusterInfo.GetClusterIDs()
	clusterK8sVersions := clusterInfo.GetKubernetesVersions()

	if req.GetTreeView() {
		resources, err := resSvc.ListResourcesTreeview(ctx, req.GetInstanceId(), req.GetGroup(), req.GetKind(), req.GetVersion(), clusterIDs, req.GetNamespaces(),
			req.GetTreeViewNameContains(), req.GetTreeViewResourceKinds(), req.GetTreeViewHealthStatuses(), req.GetName())
		if err != nil {
			return nil, err
		}
		return &organizationv1.ListKubernetesResourcesResponse{
			Resources: resources,
		}, nil
	}

	resources, err := resSvc.ListResources(
		ctx, req.GetInstanceId(), req.GetOwnerId(), req.GetGroup(), req.GetKind(), req.GetVersion(), clusterIDs, req.GetNamespaces(), offset, limit,
		req.GetNameContains(), req.GetName(), req.GetOrderBy(), req.GetWhere(), req.HasDeletionTimestamp)
	if err != nil {
		return nil, err
	}
	count, err := resSvc.CountResources(
		ctx, req.GetInstanceId(), req.GetOwnerId(), req.GetGroup(), req.GetKind(), req.GetVersion(), clusterIDs, req.GetNamespaces(), req.GetNameContains(),
		req.GetName(), req.GetWhere(), req.HasDeletionTimestamp)
	if err != nil {
		return nil, fmt.Errorf("failed to count resources: %w", err)
	}

	deprecatedGVKMap, err := resSvc.GetDeprecatedGVKs(clusterInfo.GetClusters())
	if err != nil {
		return nil, err
	}

	return &organizationv1.ListKubernetesResourcesResponse{
		Resources: mapResourcesToRPCEntity(resources, deprecatedGVKMap, clusterK8sVersions),
		Count:     uint32(count),
	}, nil
}

func (s *OrganizationV1Server) ListKubernetesResourcesToCSV(
	req *organizationv1.ListKubernetesResourcesRequest,
	srv organizationv1.OrganizationService_ListKubernetesResourcesToCSVServer,
) error {
	ctx := srv.Context()

	_, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return err
	}

	resSvc := k8sresource.NewServiceWithOptions(s.RepoSet, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)))
	clusterInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), false, req.GetClusterIds()...)
	if err != nil {
		return err
	}

	existingTypes, err := resSvc.GetResourceTypes(ctx, req.GetInstanceId(), clusterInfo.GetClusters())
	if err != nil {
		return err
	}
	var matchedType *k8sv1.ResourceType
	for _, t := range existingTypes {
		if t.GetGroupVersionKind().GetGroup() == req.GetGroup() && t.GetGroupVersionKind().GetKind() == req.GetKind() {
			matchedType = t
			break
		}
	}
	if matchedType == nil && req.HasDeletionTimestamp == nil {
		return nil
	}

	clusterIDs := clusterInfo.GetClusterIDs()
	clusterK8sVersions := clusterInfo.GetKubernetesVersions()

	deprecatedGVKMap, err := resSvc.GetDeprecatedGVKs(clusterInfo.GetClusters())
	if err != nil {
		return err
	}

	offset := 0
	limit := 100
	httpHeaders, err := anypb.New(&http.HttpHeader{
		Key:   "Content-Disposition",
		Value: fmt.Sprintf("attachment; filename=resources_%d.csv", time.Now().UTC().Unix()),
	})
	if err != nil {
		return err
	}

	var headers []string
	if req.HasDeletionTimestamp == nil {
		headers = []string{"Instance", "Cluster", "Namespace", "Name"}
		for _, col := range matchedType.Columns {
			if col.Title != "Instance" && col.Title != "Cluster" && col.Title != "Namespace" && col.Title != "Name" {
				headers = append(headers, col.Title)
			}
		}
		headers = append(headers, "ArgoCD Name", "ArgoCD Link", "ArgoCD Health Status", "ArgoCD Sync Status")
	} else {
		// This is for the stuck in deletion dashboard
		headers = []string{"Instance", "Cluster", "Namespace", "Kind", "Name", "Deletion Timestamp"}
	}

	body := &httpbody.HttpBody{
		ContentType: "text/csv",
		Data:        []byte(strings.Join(headers, ",")),
		Extensions:  []*anypb.Any{httpHeaders},
	}
	err = srv.Send(body)
	if err != nil {
		return err
	}

	count, err := resSvc.CountResources(
		ctx, req.GetInstanceId(), req.GetOwnerId(), req.GetGroup(), req.GetKind(), req.GetVersion(), clusterIDs, req.GetNamespaces(), req.GetNameContains(), req.GetName(), req.GetWhere(), req.HasDeletionTimestamp)
	if err != nil {
		return fmt.Errorf("failed to count resources: %w", err)
	}
	for {
		resources, err := resSvc.ListResources(
			ctx, req.GetInstanceId(), req.GetOwnerId(), req.GetGroup(), req.GetKind(), req.GetVersion(), clusterIDs,
			req.GetNamespaces(), offset, limit, req.GetNameContains(), req.GetName(), req.GetOrderBy(), req.GetWhere(), req.HasDeletionTimestamp)
		if err != nil {
			return err
		}
		rpcEntities := mapResourcesToRPCEntity(resources, deprecatedGVKMap, clusterK8sVersions)
		rows := lo.Map(rpcEntities, func(entity *organizationv1.ClusterResource, idx int) string {
			var row []string
			cluster := clusterInfo.GetCluster(entity.ClusterId)
			if cluster == nil {
				return ""
			}
			if req.HasDeletionTimestamp == nil {
				row = []string{cluster.InstanceName, cluster.Name, entity.Namespace, entity.Name}
				for _, col := range matchedType.Columns {
					if col.Title != "Instance" && col.Title != "Cluster" && col.Title != "Namespace" && col.Title != "Name" {
						row = append(row, entity.Columns[col.Name])
					}
				}
				if entity.ArgocdApplicationInfo != nil {
					row = append(row, entity.ArgocdApplicationInfo.Name, entity.ArgocdApplicationInfo.Link,
						entity.ArgocdApplicationInfo.HealthStatus.String(), entity.ArgocdApplicationInfo.SyncStatus.String())
				} else {
					row = append(row, "", "", "", "")
				}
			} else {
				// This is for the stuck in deletion dashboard
				deleteTime := ""
				if entity.DeleteTime != nil {
					deleteTime = *entity.DeleteTime
				}
				row = []string{cluster.InstanceName, cluster.Name, entity.Kind, entity.Namespace, entity.Kind, entity.Name, deleteTime}
			}
			return strings.Join(row, ",")
		})

		body := &httpbody.HttpBody{
			ContentType: "text/csv",
			Data:        []byte(strings.Join(rows, "\n")),
			Extensions:  []*anypb.Any{httpHeaders},
		}
		err = srv.Send(body)
		if err != nil {
			return err
		}

		offset += limit
		if int64(offset) >= count {
			break
		}
	}
	return nil
}

func mapResourcesToRPCEntity(resources []*models.ArgoCDClusterK8SObject, deprecatedGVKs *k8sresource.DeprecatedGVKs, clusterK8sVersions map[string]string) []*organizationv1.ClusterResource {
	res := make([]*organizationv1.ClusterResource, 0, len(resources))
	for _, r := range resources {
		resource, err := mapResourceToRPCEntity(r, deprecatedGVKs, clusterK8sVersions[r.ClusterID])
		if err != nil {
			continue
		}
		res = append(res, resource)
	}
	return res
}
