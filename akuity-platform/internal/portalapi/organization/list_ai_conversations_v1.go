package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/ai"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListAIConversations(ctx context.Context, req *organizationv1.ListAIConversationsRequest) (*organizationv1.ListAIConversationsResponse, error) {
	actor, err := s.checkAISupportEngineerPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(), req.GetKargoInstanceId(),
		accesscontrol.NewActionGetAISupportEngineer(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	featureStatuses := s.featSvc.GetFeatureStatuses(ctx, &orgID)
	aiSvc, err := ai.NewService(s.Db, s.RepoSet, featureStatuses, orgID, s.Cfg.AI, s.hostRestConfig, logging.Extract(ctx))
	if err != nil {
		return nil, err
	}

	offset := req.GetOffset()
	limit := req.GetLimit()
	if limit == 0 {
		limit = 100
	}
	conversations, count, err := aiSvc.ListConversations(ctx, actor,
		req.GetInstanceId(), req.GetKargoInstanceId(), req.GetIncidentOnly(),
		req.GetIncidentStatus(), req.GetApplication(), req.GetNamespace(), req.GetClusterId(),
		req.GetTitleContains(), req.GetKargoProject(), offset, limit)
	if err != nil {
		return nil, err
	}
	return &organizationv1.ListAIConversationsResponse{Conversations: conversations, Count: uint32(count)}, nil
}
