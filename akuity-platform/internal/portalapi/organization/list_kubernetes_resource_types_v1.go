package organization

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/client"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListKubernetesResourceTypes(
	ctx context.Context,
	req *organizationv1.ListKubernetesResourceTypesRequest,
) (*organizationv1.ListKubernetesResourceTypesResponse, error) {
	_, err := s.checkKubeVisionPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(),
		accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	txDB, _ := database.WithTxBeginner(s.Db)
	rs := client.NewRepoSet(txDB)
	resSvc := k8sresource.NewServiceWithOptions(rs, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)))
	clusterInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), false)
	if err != nil {
		return nil, err
	}

	types, err := resSvc.GetResourceTypes(ctx, req.GetInstanceId(), clusterInfo.GetClusters())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}

	return &organizationv1.ListKubernetesResourceTypesResponse{
		InstanceId:    req.GetInstanceId(),
		ResourceTypes: types,
	}, nil
}
