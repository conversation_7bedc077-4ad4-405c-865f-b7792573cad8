package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) GetFeatureStatuses(
	ctx context.Context,
	req *organizationv1.GetFeatureStatusesRequest,
) (*organizationv1.GetFeatureStatusesResponse, error) {
	if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetId(),
		accesscontrol.NewActionGetOrganization(req.GetId())); err != nil {
		return nil, err
	}

	orgID := req.GetId()
	featureStatuses := s.featSvc.GetFeatureStatuses(ctx, &orgID)

	return &organizationv1.GetFeatureStatusesResponse{
		FeatureStatuses: featureStatuses,
	}, nil
}
