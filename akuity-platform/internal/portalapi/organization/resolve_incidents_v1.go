package organization

import (
	"context"

	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/ai"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ResolveIncidents(ctx context.Context, req *organizationv1.ResolveIncidentsRequest) (*organizationv1.ResolveIncidentsResponse, error) {
	actor, err := s.checkAISupportEngineerPermission(ctx, req.GetOrganizationId(), req.GetInstanceId(), req.GetKargoInstanceId(),
		accesscontrol.NewActionDeleteAISupportEngineer(req.GetOrganizationId()))
	if err != nil {
		return nil, err
	}

	orgID := req.GetOrganizationId()
	featureStatuses := s.featSvc.GetFeatureStatuses(ctx, &orgID)
	aiSvc, err := ai.NewService(s.Db, s.RepoSet, featureStatuses, orgID, s.Cfg.AI, s.hostRestConfig, logging.Extract(ctx))
	if err != nil {
		return nil, err
	}

	err = aiSvc.ResolveIncidents(ctx, req.GetIncidentIds(), req.GetApplyToAll(), req.GetResolved(),
		actor, req.GetInstanceId(), req.GetKargoInstanceId(), req.GetIncidentStatus(), req.GetApplication(), req.GetNamespace(), req.GetClusterId(), req.GetTitleContains(), req.GetKargoProject())
	if err != nil {
		return nil, err
	}

	return &organizationv1.ResolveIncidentsResponse{}, nil
}
