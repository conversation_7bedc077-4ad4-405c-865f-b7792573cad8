package healthcheck

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/status"
)

const (
	MethodNamePrefix = "/grpc.health.v1.Health"
	CheckMethodName  = MethodNamePrefix + "/Check"
	WatchMethodName  = MethodNamePrefix + "/Watch"
	ListMethodName   = MethodNamePrefix + "/List"
)

var _ grpc_health_v1.HealthServer = &server{}

type server struct {
	/* explicitly empty */
}

func New() grpc_health_v1.HealthServer {
	return &server{}
}

func (s *server) Check(_ context.Context, _ *grpc_health_v1.HealthCheckRequest) (*grpc_health_v1.HealthCheckResponse, error) {
	return &grpc_health_v1.HealthCheckResponse{
		Status: grpc_health_v1.HealthCheckResponse_SERVING,
	}, nil
}

//nolint:gosimple
func (s *server) Watch(_ *grpc_health_v1.HealthCheckRequest, stream grpc_health_v1.Health_WatchServer) error {
	res := &grpc_health_v1.HealthCheckResponse{
		Status: grpc_health_v1.HealthCheckResponse_SERVING,
	}
	if err := stream.Send(res); err != nil {
		return err
	}

	<-stream.Context().Done()
	return status.Error(codes.Canceled, "Stream has ended.")
}

func (s *server) List(_ context.Context, _ *grpc_health_v1.HealthListRequest) (*grpc_health_v1.HealthListResponse, error) {
	return &grpc_health_v1.HealthListResponse{
		Statuses: map[string]*grpc_health_v1.HealthCheckResponse{
			"server": {
				Status: grpc_health_v1.HealthCheckResponse_SERVING,
			},
		},
	}, nil
}
