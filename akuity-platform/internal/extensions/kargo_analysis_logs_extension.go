package extensions

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/genproto/googleapis/api/httpbody"
	"google.golang.org/grpc/metadata"

	agentv1 "github.com/akuityio/akuity-platform/agent/pkg/api/gen/agent/v1"
	"github.com/akuityio/akuity-platform/internal/kargo"
	utilerrors "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/models/models"
	extv1 "github.com/akuityio/akuity-platform/pkg/api/gen/extension/v1"
)

const (
	kargoAPITimeout = 5 * time.Second
)

func (srv *ExtensionsV1Server) GetKargoAnalysisLogs(req *extv1.GetKargoAnalysisLogsRequest, stream extv1.ExtensionService_GetKargoAnalysisLogsServer) error {
	ctx := stream.Context()

	// Get instance from request context
	instance, token, err := srv.getKargoInfoFromRequest(ctx)
	if err != nil {
		return err
	}

	if !srv.featSvc.GetFeatureStatuses(ctx, &instance.OrganizationOwner.String).GetKargoAnalysisLogs().Enabled() {
		return utilerrors.NewAPIStatus(http.StatusForbidden, "kargo analysis logs feature is not enabled")
	}

	if err := srv.validateKargoLogToken(instance.ID, token, req.ProjectName, req.AnalysisRun); err != nil {
		return err
	}

	// TODO: check back later if large chunk size has any downsides here
	const chunkSize = 4 * 1000 * 1000
	cursor := 1
	for {
		// stream logs in chunks from db
		resp, totalSize, err := srv.getKargoAnalysisLogs(ctx, cursor, chunkSize, models.LogWhere.InstanceID.EQ(instance.ID),
			models.LogWhere.Type.EQ(agentv1.LogType_LOG_TYPE_KARGO_ANALYSIS_JOB.String()),
			qm.Where("metadata->'kargoAnalysisLog'->>'project' = ?", req.ProjectName),
			qm.Where("metadata->'kargoAnalysisLog'->>'analysisRunID' = ?", req.AnalysisRun),
			qm.Where("metadata->'kargoAnalysisLog'->>'containerName' = ?", req.ContainerName),
			qm.OrderBy("creation_timestamp desc"), qm.Limit(1))
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return utilerrors.NewAPIStatus(http.StatusNotFound, "logs not found")
			}
			return err
		}
		cursor += chunkSize
		// if we read everything and still data is empty skip sending empty response
		if cursor >= totalSize && resp == "" {
			break
		}
		if err := stream.Send(&httpbody.HttpBody{
			ContentType: "text/plain",
			Data:        []byte(resp),
		}); err != nil {
			return err
		}
		// if we read everything break
		if cursor >= totalSize {
			break
		}
	}

	return nil
}

func (srv *ExtensionsV1Server) getKargoInfoFromRequest(ctx context.Context) (*models.KargoInstance, string, error) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return nil, "", fmt.Errorf("no metadata in context")
	}

	var (
		instance *models.KargoInstance
		err      error
	)

	// if instance is in metadata use it (kargo logs use)
	instanceIdList := md.Get("instance-id")
	if len(instanceIdList) > 0 {
		instance, err = models.KargoInstances(models.KargoInstanceWhere.ID.EQ(instanceIdList[0])).One(ctx, srv.Db)
	} else {
		// if request comes directly from UI use x-forwarded-host header to infer instance
		hostMd := md.Get("x-forwarded-host")
		if len(hostMd) != 1 {
			return nil, "", fmt.Errorf("invalid host data, no instance info provided")
		}
		instance, err = models.KargoInstances(models.KargoInstanceWhere.StatusHostname.EQ(null.StringFrom(hostMd[0]))).One(ctx, srv.Db)
	}
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, "", utilerrors.NewAPIStatus(http.StatusUnauthorized, "invalid token")
		}
		return nil, "", fmt.Errorf("failed to get instance: %s", err.Error())
	}

	tokenHeader := md.Get("authorization")
	if len(tokenHeader) == 0 {
		return nil, "", utilerrors.NewAPIStatus(http.StatusUnauthorized, "no authorization token provided")
	}
	token := strings.Replace(tokenHeader[0], "Bearer ", "", 1)

	return instance, token, nil
}

func (srv *ExtensionsV1Server) validateKargoLogToken(instanceID, token, project, analysis string) error {
	url := fmt.Sprintf("http://kargo-api.kargo-%v.svc.cluster.local/akuity.io.kargo.service.v1alpha1.KargoService/GetAnalysisRun", instanceID)

	payload := strings.NewReader(fmt.Sprintf(`{"namespace":"%v", "name":"%v"}`, project, analysis))
	req, err := http.NewRequest(http.MethodPost, url, payload)
	if err != nil {
		return err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+token)

	client := &http.Client{
		Timeout: kargoAPITimeout,
	}
	res, err := client.Do(req)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		return utilerrors.NewAPIStatus(http.StatusUnauthorized, "invalid token")
	}
	return nil
}

func (srv *ExtensionsV1Server) getKargoAnalysisLogs(ctx context.Context, start, chunkSize int, mods ...qm.QueryMod) (string, int, error) {
	queryMods := []qm.QueryMod{
		qm.Select(fmt.Sprintf("SUBSTRING(COALESCE(%s, '') FROM %d FOR %d), metadata->'size'", models.LogColumns.LogDST, start, chunkSize)),
		qm.From(models.TableNames.Logs),
	}
	queryMods = append(queryMods, mods...)
	var (
		size  int
		chunk string
	)
	if err := models.NewQuery(queryMods...).QueryRowContext(ctx, srv.Db).Scan(&chunk, &size); err != nil {
		return "", 0, err
	}

	return chunk, size, nil
}

func (srv *ExtensionsV1Server) validateKargoProjectToken(ctx context.Context, instanceID, token, project string) error {
	kargoClient := kargo.NewClient(token, fmt.Sprintf("kargo-api.kargo-%v.svc.cluster.local:80", instanceID), instanceID, nil)
	_, err := kargoClient.GetProject(ctx, project)
	if err != nil {
		return utilerrors.NewAPIStatus(http.StatusUnauthorized, "invalid token")
	}
	return nil
}

func (srv *ExtensionsV1Server) validateKargoToken(ctx context.Context, instanceID, token string) error {
	kargoClient := kargo.NewClient(token, fmt.Sprintf("kargo-api.kargo-%v.svc.cluster.local:80", instanceID), instanceID, nil)
	_, err := kargoClient.ListProjects(ctx)
	if err != nil {
		return utilerrors.NewAPIStatus(http.StatusUnauthorized, "invalid token")
	}
	return nil
}
