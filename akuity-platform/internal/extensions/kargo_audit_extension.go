package extensions

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/internal/portalapi/organization"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	utilerrors "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/models/models"
	extv1 "github.com/akuityio/akuity-platform/pkg/api/gen/extension/v1"
	orgv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (srv *ExtensionsV1Server) ListAuditRecordForKargoProjects(ctx context.Context, req *extv1.ListAuditRecordForKargoProjectsRequest) (*extv1.ListAuditRecordForKargoProjectsResponse, error) {
	// Get instance from request context
	instance, token, err := srv.getKargoInfoFromRequest(ctx)
	if err != nil {
		return nil, err
	}

	if !srv.featSvc.GetFeatureStatuses(ctx, &instance.OrganizationOwner.String).GetKargoEnterprise().Enabled() {
		return nil, utilerrors.NewAPIStatus(http.StatusForbidden, "kargo enterprise feature is not enabled")
	}

	filters := req.GetFilters()
	if filters == nil {
		filters = &orgv1.AuditFilters{}
	}

	projectName := req.GetProjectName()
	if projectName == "" {
		return nil, utilerrors.NewAPIStatus(http.StatusBadRequest, "project name is required")
	}
	if err := srv.validateKargoProjectToken(ctx, instance.ID, token, projectName); err != nil {
		return nil, err
	}

	var (
		kargoPromoNames    []string
		kargoPromoStages   []string
		kargoFreightNames  []string
		kargoFreightStages []string
	)

	objects := []organizations.AuditLogObject{}
	if filters.GetKargoPromotion() != nil && filters.GetKargoPromotion().GetEnabled() {
		if len(filters.GetKargoPromotion().GetObjectName()) > 0 {
			kargoPromoNames = filters.GetKargoPromotion().GetObjectName()
		}
		if len(filters.GetKargoPromotion().GetObjectParentName()) > 0 {
			kargoPromoStages = filters.GetKargoPromotion().GetObjectParentName()
		}
		objects = append(objects, organizations.AuditLogObject{
			ObjectType:                   string(models.KargoPromotionAuditObject),
			ObjectName:                   kargoPromoNames,
			ObjectParentName:             kargoPromoStages,
			ObjectParentKargoProjectName: []string{projectName},
			ObjectParentParentName:       []string{instance.Name},
		})
	}

	if filters.GetKargoFreight() != nil && filters.GetKargoFreight().GetEnabled() {
		if len(filters.GetKargoFreight().GetObjectName()) > 0 {
			kargoFreightNames = filters.GetKargoFreight().GetObjectName()
		}
		if len(filters.GetKargoFreight().GetObjectParentName()) > 0 {
			kargoFreightStages = filters.GetKargoFreight().GetObjectParentName()
		}
		objects = append(objects, organizations.AuditLogObject{
			ObjectType:                   string(models.KargoFreightAuditObject),
			ObjectName:                   kargoFreightNames,
			ObjectParentName:             kargoFreightStages,
			ObjectParentKargoProjectName: []string{projectName},
			ObjectParentParentName:       []string{instance.Name},
		})
	}

	if len(objects) == 0 {
		return nil, utilerrors.NewAPIStatus(http.StatusBadRequest, "at least one object type is required")
	}

	f := organizations.AuditLogFilter{
		Objects:   objects,
		Action:    filters.Action,
		ActorID:   filters.ActorId,
		ActorType: []string{string(models.KargoActor), string(models.UnknownActor)},
	}

	if filters.StartTime != nil {
		if f.StartTime, err = time.Parse(time.RFC3339, *filters.StartTime); err != nil {
			return nil, utilerrors.NewAPIStatus(http.StatusBadRequest, err.Error())
		}
	}

	if filters.EndTime != nil {
		if f.EndTime, err = time.Parse(time.RFC3339, *filters.EndTime); err != nil {
			return nil, utilerrors.NewAPIStatus(http.StatusBadRequest, err.Error())
		}
	}

	limit := filters.Limit
	offset := filters.Offset

	if limit == nil {
		defaultLimit := uint32(10)
		limit = &defaultLimit
	}

	if offset == nil {
		defaultOffset := uint32(0)
		offset = &defaultOffset
	}

	mods := []qm.QueryMod{
		models.AuditLogWhere.OrganizationID.EQ(instance.OrganizationOwner),
		models.AuditLogWhere.IsDeleted.EQ(false),
	}

	mods = append(f.GetMods(), mods...)

	logItems, err := models.AuditLogs(append(mods,
		qm.OrderBy(fmt.Sprintf("%s desc", models.AuditLogColumns.Timestamp)),
		qm.Limit(int(*limit)), qm.Offset(int(*offset)))...,
	).All(ctx, srv.Db)
	if err != nil {
		return nil, err
	}

	count, err := models.AuditLogs(mods...).Count(ctx, srv.Db)
	if err != nil {
		return nil, err
	}

	var records []*orgv1.AuditLog
	for _, item := range logItems {
		log, err := organization.MapAuditLogToRPCEntity(item)
		if err != nil {
			return nil, err
		}
		records = append(records, log)
	}

	return &extv1.ListAuditRecordForKargoProjectsResponse{
		Items:      records,
		TotalCount: uint32(count),
	}, nil
}
