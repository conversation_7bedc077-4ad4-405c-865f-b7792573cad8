package extensions

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/volatiletech/null/v8"
	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	"github.com/akuityio/akuity-platform/internal/portalapi/organization"
	"github.com/akuityio/akuity-platform/internal/services/organizations"
	utilerrors "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/models/models"
	extv1 "github.com/akuityio/akuity-platform/pkg/api/gen/extension/v1"
	orgv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (srv *ExtensionsV1Server) ListAuditRecordForApplication(ctx context.Context, req *extv1.ListAuditRecordForApplicationRequest) (*extv1.ListAuditRecordForApplicationResponse, error) {
	argocdToken, err := srv.argocdTokenFromRequest(ctx)
	if err != nil {
		return nil, utilerrors.NewAPIStatus(http.StatusUnauthorized, err.Error())
	}

	instance, err := srv.getInstanceFromRequest(ctx)
	if err != nil {
		return nil, err
	}

	instanceConfig, err := models.ArgoCDInstanceConfigs(models.ArgoCDInstanceConfigWhere.InstanceID.EQ(instance.ID)).One(ctx, srv.Db)
	if err != nil {
		return nil, err
	}

	spec, err := instanceConfig.GetSpec()
	if err != nil {
		return nil, err
	}
	if !spec.AuditExtensionEnabled {
		return nil, utilerrors.NewAPIStatus(http.StatusForbidden, "audit extension is not enabled")
	}

	filters := req.GetFilters()
	if filters == nil {
		filters = &orgv1.AuditFilters{}
	}

	if filters.GetArgocdApplication() == nil || len(filters.GetArgocdApplication().GetObjectName()) < 1 {
		return nil, utilerrors.NewAPIStatus(http.StatusBadRequest, "audit records filter must contain Argo CD application name")
	}

	appName := filters.GetArgocdApplication().GetObjectName()[0]

	app, err := srv.getArgoCDApp(argocdToken, instance.ID, appName)
	if err != nil {
		return nil, err
	}

	objects := []organizations.AuditLogObject{
		{
			ObjectType:       string(models.ArgoCDAppAuditObject),
			ObjectName:       filters.ArgocdApplication.ObjectName,
			ObjectParentName: []string{instance.Name},
		},
		{
			ObjectType:                  string(models.K8sResourceAuditObject),
			ObjectParentName:            []string{argocd.GetNameFromDestination(app.Spec.Destination)},
			ObjectParentApplicationName: []string{app.Name},
			ObjectParentParentName:      []string{instance.Name},
		},
	}

	f := organizations.AuditLogFilter{
		Objects:   objects,
		Action:    filters.Action,
		ActorID:   filters.ActorId,
		ActorType: []string{string(models.ArgoCDUserActor), string(models.ArgoCDAutoSyncActor)},
	}

	if filters.StartTime != nil {
		if f.StartTime, err = time.Parse(time.RFC3339, *filters.StartTime); err != nil {
			return nil, utilerrors.NewAPIStatus(http.StatusBadRequest, err.Error())
		}
	}

	if filters.EndTime != nil {
		if f.EndTime, err = time.Parse(time.RFC3339, *filters.EndTime); err != nil {
			return nil, utilerrors.NewAPIStatus(http.StatusBadRequest, err.Error())
		}
	}

	limit := filters.Limit
	offset := filters.Offset

	if limit == nil {
		defaultLimit := uint32(10)
		limit = &defaultLimit
	}

	if offset == nil {
		defaultOffset := uint32(0)
		offset = &defaultOffset
	}

	mods := []qm.QueryMod{
		models.AuditLogWhere.OrganizationID.EQ(null.StringFrom(instance.OrganizationOwner)),
		models.AuditLogWhere.IsDeleted.EQ(false),
	}

	mods = append(f.GetMods(), mods...)

	logItems, err := models.AuditLogs(append(mods,
		qm.OrderBy(fmt.Sprintf("%s desc", models.AuditLogColumns.Timestamp)),
		qm.Limit(int(*limit)), qm.Offset(int(*offset)))...,
	).All(ctx, srv.Db)
	if err != nil {
		return nil, err
	}

	count, err := models.AuditLogs(mods...).Count(ctx, srv.Db)
	if err != nil {
		return nil, err
	}

	var records []*orgv1.AuditLog
	for _, item := range logItems {
		log, err := organization.MapAuditLogToRPCEntity(item)
		if err != nil {
			return nil, err
		}
		records = append(records, log)
	}

	return &extv1.ListAuditRecordForApplicationResponse{
		Items:      records,
		TotalCount: uint32(count),
	}, nil
}
