package extensions

import (
	"context"
	"fmt"
	"net/http"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"

	"github.com/akuityio/akuity-platform/internal/portalapi/argocd"
	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	utilerrors "github.com/akuityio/akuity-platform/internal/utils/errors"
	argocdutils "github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/models"
	argocdv1 "github.com/akuityio/akuity-platform/pkg/api/gen/argocd/v1"
	extv1 "github.com/akuityio/akuity-platform/pkg/api/gen/extension/v1"
)

func (srv *ExtensionsV1Server) GetSyncOperationsEventsForApplication(ctx context.Context, req *extv1.GetSyncOperationsEventsForApplicationRequest) (*extv1.GetSyncOperationsEventsForApplicationResponse, error) {
	filters := req.GetFilter()
	if filters == nil {
		filters = &argocdv1.SyncOperationFilter{}
	}

	if len(filters.GetAppName()) < 1 {
		return nil, utilerrors.NewAPIStatus(http.StatusBadRequest, "sync history filter must contain one application name")
	}

	instanceID, err := srv.getInstanceID(ctx, filters.GetAppName()[0])
	if err != nil {
		return nil, err
	}

	filters.InstanceId = []string{instanceID}

	mods, err := argocd.GetSyncFilters(filters, "")
	if err != nil {
		return nil, err
	}

	instanceSvc := instances.NewInstancesSource(srv.Db, "")
	// if field is specified skip real query and just return all fields
	if req.GetField() != argocdv1.SyncOperationField_SYNC_OPERATION_FIELD_APPS && argocd.MapField(req.GetField(), len(filters.GetInstanceNames()) > 0) != "" {
		field := argocd.MapField(req.GetField(), len(filters.GetInstanceNames()) > 0)
		// limit to 10
		mods = append(mods, qm.Limit(10))
		if req.FieldLike != "" {
			mods = append(mods, qm.Where(fmt.Sprintf("%v like ?", field), req.FieldLike))
		}
		res, err := instanceSvc.GetSyncOperationEventsField(ctx, mods, field)
		if err != nil {
			return nil, err
		}
		return &extv1.GetSyncOperationsEventsForApplicationResponse{
			FieldResult: res,
		}, nil
	}

	count, err := instanceSvc.GetSyncOperationEventsCount(ctx, mods)
	if err != nil {
		return nil, err
	}

	// limit max result count
	maxLimit := argocdutils.MaxPaginationLimit
	if req.Limit != nil {
		if req.GetLimit() < maxLimit {
			maxLimit = req.GetLimit()
		}
	}
	mods = append(mods, qm.Limit(int(maxLimit)))

	if req.Offset != nil {
		mods = append(mods, qm.Offset(int(req.GetOffset())))
	}

	events, err := instanceSvc.GetSyncOperationEvents(ctx, mods)
	if err != nil {
		return nil, err
	}

	eventResp := []*argocdv1.SyncOperationEvent{}

	for _, event := range events {
		e, err := argocd.MapEvent(event)
		if err != nil {
			return nil, err
		}
		eventResp = append(eventResp, e)
	}

	return &extv1.GetSyncOperationsEventsForApplicationResponse{
		SyncOperationEvents: eventResp,
		Count:               count,
	}, nil
}

func (srv *ExtensionsV1Server) GetSyncOperationsStatsForApplication(ctx context.Context, req *extv1.GetSyncOperationsStatsForApplicationRequest) (*extv1.GetSyncOperationsStatsForApplicationResponse, error) {
	filters := req.GetFilter()
	if filters == nil {
		filters = &argocdv1.SyncOperationFilter{}
	}

	if len(filters.GetAppName()) < 1 {
		return nil, utilerrors.NewAPIStatus(http.StatusBadRequest, "sync history filter must contain one application name")
	}

	instanceID, err := srv.getInstanceID(ctx, filters.GetAppName()[0])
	if err != nil {
		return nil, err
	}

	// we have instance details
	// appname
	groupByInterval := shared.MapGroupByInterval(req.GetInterval())

	filters.InstanceId = []string{instanceID}

	if err := shared.ValidateInterval(filters.GetStartTime(), filters.GetEndTime(), groupByInterval); err != nil {
		return nil, err
	}

	mods, err := argocd.GetSyncFilters(filters, "")
	if err != nil {
		return nil, err
	}

	groupByField := argocd.GetGroupByField(req.GetGroupByField(), "")
	instanceSvc := instances.NewInstancesSource(srv.Db, "")

	stats, err := instanceSvc.GetSyncOperationStats(ctx, mods, groupByInterval, groupByField)
	if err != nil {
		return nil, err
	}

	return &extv1.GetSyncOperationsStatsForApplicationResponse{
		SyncOperationStats: argocd.MapStatsToResponse(stats),
	}, nil
}

func (srv *ExtensionsV1Server) getInstanceID(ctx context.Context, appName string) (string, error) {
	argocdToken, err := srv.argocdTokenFromRequest(ctx)
	if err != nil {
		return "", err
	}

	instance, err := srv.getInstanceFromRequest(ctx)
	if err != nil {
		return "", err
	}

	instanceConfig, err := models.ArgoCDInstanceConfigs(models.ArgoCDInstanceConfigWhere.InstanceID.EQ(instance.ID)).One(ctx, srv.Db)
	if err != nil {
		return "", err
	}

	spec, err := instanceConfig.GetSpec()
	if err != nil {
		return "", err
	}

	if !spec.SyncHistoryExtensionEnabled {
		return "", utilerrors.NewAPIStatus(http.StatusForbidden, "sync history extension is not enabled")
	}

	if _, err := srv.getArgoCDApp(argocdToken, instance.ID, appName); err != nil {
		return "", err
	}

	return instance.ID, err
}
