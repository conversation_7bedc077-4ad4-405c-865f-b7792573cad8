package extensions

import (
	"context"
	"database/sql"
	"fmt"
	"net"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-logr/logr"
	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
	"k8s.io/client-go/rest"

	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/ai/deprecated"
	"github.com/akuityio/akuity-platform/internal/utils/errors"
	akuitygrpc "github.com/akuityio/akuity-platform/internal/utils/grpc"
	grpclogging "github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	httputil "github.com/akuityio/akuity-platform/internal/utils/http"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	modelClient "github.com/akuityio/akuity-platform/models/client"
	extv1 "github.com/akuityio/akuity-platform/pkg/api/gen/extension/v1"
)

type ExtensionsV1Server struct {
	extv1.ExtensionServiceServer

	BindAddress string
	BindNetwork string
	Db          *sql.DB

	assistant      deprecated.Assistant
	log            *logr.Logger
	hostRestConfig *rest.Config

	featSvc features.Service
}

func NewExtensionsV1Server(log *logr.Logger, hostRestConfig *rest.Config, bindAddress, bindNetwork string, db *sql.DB, assistant deprecated.Assistant, cfg config.PortalServerConfig) *ExtensionsV1Server {
	return &ExtensionsV1Server{
		BindAddress:    bindAddress,
		BindNetwork:    bindNetwork,
		Db:             db,
		assistant:      assistant,
		log:            log,
		hostRestConfig: hostRestConfig,
		featSvc: features.NewService(
			modelClient.NewRepoSet(db), db, config.IsSelfHosted, config.IsSelfHostedReleaseBuild, cfg.FeatureGatesSource, config.GetLicense(), features.WithLogger(log),
		),
	}
}

func (srv *ExtensionsV1Server) runExtensionsServer(log logr.Logger, errCh chan<- error) (*grpc.Server, error) {
	l, err := net.Listen(srv.BindNetwork, srv.BindAddress)
	if err != nil {
		return nil, fmt.Errorf("new listener: %w", err)
	}

	unaryErrorInterceptor := func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		res, err := handler(ctx, req)
		return res, errors.ConvertError(err)
	}

	streamErrorInterceptor := func(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		err := handler(srv, ss)
		return errors.ConvertError(err)
	}

	grpcSrv := grpc.NewServer(
		akuitygrpc.NewUnaryServerInterceptor(
			&log,
			akuitygrpc.WithLoggingOptions(grpclogging.WithCodeFunc(func(err error) codes.Code {
				return errors.GetAPIStatus(err).GRPCStatus().Code()
			})),
			akuitygrpc.WithUnaryServerInterceptors(
				unaryErrorInterceptor,
			),
		),
		akuitygrpc.NewStreamServerInterceptor(
			&log,
			akuitygrpc.WithLoggingOptions(grpclogging.WithCodeFunc(func(err error) codes.Code {
				return errors.GetAPIStatus(err).GRPCStatus().Code()
			})),
			akuitygrpc.WithStreamServerInterceptors(
				streamErrorInterceptor,
			),
		),
	)

	extv1.RegisterExtensionServiceServer(grpcSrv, srv)

	go func() {
		log.Info(fmt.Sprintf("extensions server is listening on %s://%s", srv.BindNetwork, srv.BindAddress))
		if err := grpcSrv.Serve(l); err != nil {
			errCh <- fmt.Errorf("serve api: %w", err)
		}
	}()

	return grpcSrv, nil
}

func (srv *ExtensionsV1Server) NewExtensionsRequestHandler() (http.HandlerFunc, error) {
	dialOpts := []grpc.DialOption{
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	}
	cc, err := grpc.NewClient(srv.BindAddress, dialOpts...)
	if err != nil {
		return nil, fmt.Errorf("failed to dial to %s: %w", srv.BindAddress, err)
	}

	mux := runtime.NewServeMux(InjectAgentMetadata())
	registerCtx := context.Background()

	if err := extv1.RegisterExtensionServiceHandler(registerCtx, mux, cc); err != nil {
		return nil, fmt.Errorf("register extensionv1: %w", err)
	}

	r := gin.New()
	r.Use(gin.RecoveryWithWriter(logging.SentryWriter{Writer: gin.DefaultErrorWriter}))
	r.Use(httputil.InjectVersionHeader())
	r.Any("/*any", gin.WrapH(mux))

	return r.ServeHTTP, nil
}

func (srv *ExtensionsV1Server) Start(ctx context.Context, log logr.Logger) error {
	errCh := make(chan error, 1)
	api, err := srv.runExtensionsServer(log, errCh)
	if err != nil {
		return fmt.Errorf("run extensions server: %w", err)
	}
	select {
	case <-ctx.Done():
		api.GracefulStop()
		return nil
	case err := <-errCh:
		return err
	}
}

func InjectAgentMetadata() runtime.ServeMuxOption {
	return runtime.WithMetadata(func(ctx context.Context, req *http.Request) metadata.MD {
		md := metadata.New(make(map[string]string))

		instanceIDHeaders := req.Header.Get("instance-id")
		if len(instanceIDHeaders) > 0 {
			md.Set("instance-id", instanceIDHeaders)
		}

		return md
	})
}
