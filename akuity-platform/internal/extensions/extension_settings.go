package extensions

import (
	"context"
	"database/sql"

	"github.com/volatiletech/sqlboiler/v4/queries/qm"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/akuityio/akuity-platform/models/client"
	extv1 "github.com/akuityio/akuity-platform/pkg/api/gen/extension/v1"
	kargov1 "github.com/akuityio/akuity-platform/pkg/api/gen/kargo/v1"
)

// GetExtensionSettings getting extension settings from kargo instance config
func (srv *ExtensionsV1Server) GetExtensionSettings(ctx context.Context, req *extv1.GetExtensionSettingsRequest) (*extv1.GetExtensionSettingsResponse, error) {
	instance, token, err := srv.getKargoInfoFromRequest(ctx)
	if err != nil {
		return nil, err
	}

	featureStatuses := srv.featSvc.GetFeatureStatuses(ctx, &instance.OrganizationOwner.String)
	if !featureStatuses.GetKargoEnterprise().Enabled() {
		return nil, status.Error(codes.PermissionDenied, "kargo enterprise feature is not enabled")
	}

	if err := srv.validateKargoToken(ctx, instance.ID, token); err != nil {
		return nil, err
	}

	repoSet := client.NewRepoSet(srv.Db)
	instanceConfig, err := repoSet.KargoInstanceConfigs(
		qm.Where("instance_id = ?", instance.ID),
	).One(ctx)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, status.Error(codes.NotFound, "kargo instance config not found")
		}
		return nil, status.Errorf(codes.Internal, "failed to get kargo instance config: %v", err)
	}

	spec, err := instanceConfig.GetSpec()
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get kargo instance config spec: %v", err)
	}

	response := &extv1.GetExtensionSettingsResponse{
		InstanceId:         instance.ID,
		OrganizationId:     instance.OrganizationOwner.String,
		AkuityIntelligence: &kargov1.AkuityIntelligence{},
	}

	if spec.AkuityIntelligence != nil {
		response.AkuityIntelligence = spec.AkuityIntelligence
		response.AkuityIntelligence.Enabled = spec.AkuityIntelligence.Enabled && featureStatuses.GetMultiClusterK8SDashboard().Enabled()
		response.AkuityIntelligence.AiSupportEngineerEnabled = response.AkuityIntelligence.Enabled && spec.AkuityIntelligence.AiSupportEngineerEnabled && featureStatuses.GetAiSupportEngineer().Enabled()
	}

	return response, nil
}
