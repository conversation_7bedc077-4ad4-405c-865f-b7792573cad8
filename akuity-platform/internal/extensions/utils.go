package extensions

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"github.com/go-resty/resty/v2"
	"github.com/volatiletech/null/v8"
	"google.golang.org/grpc/metadata"

	"github.com/akuityio/akuity-platform/internal/argoproj/argocd"
	utilerrors "github.com/akuityio/akuity-platform/internal/utils/errors"
	"github.com/akuityio/akuity-platform/models/models"
	httputil "github.com/akuityio/akuity-platform/pkg/utils/http"
)

func (srv *ExtensionsV1Server) getInstanceFromRequest(ctx context.Context) (*models.ArgoCDInstance, error) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return nil, fmt.Errorf("no metadata in context")
	}

	hostMd := md.Get("x-forwarded-host")
	if len(hostMd) != 1 {
		return nil, fmt.Errorf("invalid host data")
	}
	host := hostMd[0]

	return models.ArgoCDInstances(models.ArgoCDInstanceWhere.StatusHostname.EQ(null.StringFrom(host))).One(ctx, srv.Db)
}

func (srv *ExtensionsV1Server) getArgoCDApp(argocdToken, instanceId, appName string) (*argocd.Application, error) {
	app := argocd.Application{}
	client := resty.New()
	cookies, err := httputil.SetArgoCDRequestTokenCookie(argocdToken, true, true, "")
	if err != nil {
		return nil, fmt.Errorf("failed to set ArgoCD token cookie: %w", err)
	}
	resp, err := client.R().SetCookies(cookies).SetResult(&app).Get(fmt.Sprintf("http://argocd-server.argocd-%s.svc.cluster.local/api/v1/applications/%s", instanceId, appName))
	if err != nil {
		return nil, err
	}

	if resp.IsError() {
		return nil, utilerrors.NewAPIStatus(http.StatusUnauthorized, fmt.Sprintf("failed to get application: %s", resp.Status()))
	}
	return &app, nil
}

func (srv *ExtensionsV1Server) argocdTokenFromRequest(ctx context.Context) (string, error) {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return "", fmt.Errorf("no metadata in context")
	}
	cookies := md.Get("grpcgateway-cookie")
	if len(cookies) == 0 {
		return "", nil
	}
	request := http.Request{Header: http.Header{}}
	request.Header.Add("Cookie", cookies[0])
	token, err := httputil.GetArgoCDTokenCookie(&request)
	if err != nil {
		if errors.Is(err, http.ErrNoCookie) {
			return "", nil
		}
		return "", fmt.Errorf("failed to get argocd.token cookie: %w", err)
	}
	return token, nil
}
