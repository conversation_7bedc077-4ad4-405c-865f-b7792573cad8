package notificationcontroller

import (
	"context"
	"fmt"

	"github.com/spf13/cobra"

	"github.com/akuityio/akuity-platform/controllers/notifications/integration"
	"github.com/akuityio/akuity-platform/internal/cli"
	"github.com/akuityio/akuity-platform/internal/config"
	"github.com/akuityio/akuity-platform/internal/services/features"
	"github.com/akuityio/akuity-platform/internal/utils/database"
	"github.com/akuityio/akuity-platform/internal/utils/logging"
	"github.com/akuityio/akuity-platform/internal/utils/metrics"
	"github.com/akuityio/akuity-platform/internal/utils/misc"
	"github.com/akuityio/akuity-platform/models/client"
)

func NewNotificationControllerCommand() *cobra.Command {
	var (
		numWorkers  int
		insecure    bool
		metricsPort int
		debug       bool
	)
	cmd := &cobra.Command{
		Use:   "notification-controller",
		Short: "Akuity Notification Controller",
		Run: func(cmd *cobra.Command, args []string) {
			cfg, err := config.NewNotificationControllerConfig()
			cli.CheckErr(err)

			var logOpts []logging.Option
			if debug {
				logOpts = append(logOpts, logging.WithDebug())
			}
			log, err := logging.NewLogger(logOpts...)
			cli.CheckErr(err)

			// dev: license init needs to happen before feature flag init
			cli.CheckErr(config.InitializeLicense(log))

			err = database.InitializeDataKey(cfg.DBDataKey)
			cli.CheckErr(err)

			ctx, cancel := context.WithCancel(context.Background())
			defer cancel()

			portalDBPool, err := database.GetDBPool(cfg.PortalDBConnection, &cfg.DBConnection)
			if err != nil {
				cli.CheckErr(fmt.Errorf("failed to connect to portal db: %w", err))
			}
			repoSet := client.NewRepoSet(portalDBPool.DB)

			featureSvc := features.NewService(repoSet, portalDBPool.DB, config.IsSelfHosted, config.IsSelfHostedReleaseBuild, cfg.FeatureGatesSource, config.GetLicense(), features.WithLogger(&log))

			settings := integration.ControllerSettings{
				PortalDBRawClient:  portalDBPool.DB,
				PortalDBConnection: cfg.PortalDBConnection,
				RepoSet:            repoSet,
				Log:                &log,
			}

			argocdCDVersion, err := misc.GetArgoCDVersions(log)
			cli.CheckErr(err)
			kargoVersion, _, err := misc.GetKargoVersions(log)
			cli.CheckErr(err)
			notificationController, err := integration.NewNotificationController(&log, settings, cfg, argocdCDVersion, kargoVersion, featureSvc)
			cli.CheckErr(err)

			go func() { cli.CheckErr(metrics.NewMetricsServer(&log, "notification-controller", metricsPort)()) }()

			cli.CheckErr(notificationController.Init(context.Background()))
			cli.CheckErr(notificationController.Run(ctx, numWorkers))
		},
	}
	cmd.Flags().IntVar(&numWorkers, "num-workers", 1, "The number of workers")
	cmd.Flags().BoolVar(&insecure, "insecure", false, "Whether to enable insecure")
	cmd.Flags().IntVar(&metricsPort, "metrics-port", config.DefaultNotificationControllerMetricsPort, "The metrics server port")
	cmd.Flags().BoolVar(&debug, "debug", false, "Enable debug logging")
	return cmd
}
