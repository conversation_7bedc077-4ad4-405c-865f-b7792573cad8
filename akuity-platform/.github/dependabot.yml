# https://docs.github.com/en/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file
# https://docs.github.com/en/code-security/dependabot/dependabot-version-updates/configuring-dependabot-version-updates
# https://docs.github.com/en/code-security/dependabot/working-with-dependabot/keeping-your-actions-up-to-date-with-dependabot
version: 2
updates:
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "monthly"
    commit-message:
      prefix: "chore(deps):"

  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "monthly"
    commit-message:
      prefix: "chore(deps):"
    ignore:
      - dependency-name: "node:*"

  - package-ecosystem: "npm"
    directory: "/aims/ui"
    versioning-strategy: increase
    schedule:
      interval: "monthly"
    commit-message:
      prefix: "chore(deps):"
    groups:
      aims-js:
        update-types:
          - "major"
          - "minor"
          - "patch"

  - package-ecosystem: "npm"
    directory: "/portal/ui"
    versioning-strategy: increase
    schedule:
      interval: "monthly"
    commit-message:
      prefix: "chore(deps):"
    groups:
      akp-js:
        update-types:
          - "minor"
          - "patch"
      akp-js-major:
        update-types:
          - "major"

  - package-ecosystem: "gomod"
    directory: "/"
    schedule:
      interval: "monthly"
    commit-message:
      prefix: "chore(deps):"
    groups:
      go-playwright:
        patterns:
          - "github.com/playwright-community/playwright-go"
      go-k8s:
        patterns:
          - "k8s.io/*"
      go-patch:
        update-types:
          - "patch"
        exclude-patterns:
          - "github.com/playwright-community/playwright-go"
          - "k8s.io/*"
      go-minor:
        update-types:
          - "minor"
        exclude-patterns:
          - "github.com/playwright-community/playwright-go"
          - "k8s.io/*"
      go-major:
        update-types:
          - "major"
        exclude-patterns:
          - "github.com/playwright-community/playwright-go"
          - "k8s.io/*"

    ignore:
      - dependency-name: "github.com/dexidp/dex"
        # For Dex, ignore all updates, it always pulls the wrong version.
