name: Release

on:
  release:
    types: [published]

env:
  GOPRIVATE: "github.com/akuityio"
  GOWORK: "off"

jobs:
  push-images:
    name: Build and Push Docker images
    if: startsWith(github.ref, 'refs/tags/v')
    runs-on: ubuntu-latest
    permissions:
      contents: write # https://github.com/anchore/sbom-action#permissions
      id-token: write # needed for signing the images with GitHub OIDC Token
      actions: read # to find workflow artifacts when attaching release assets
    services:
      registry:
        image: registry:2
        ports:
          - 5000:5000
    env:
      AKUITY_PLATFORM_SH_REPO: us-docker.pkg.dev/akuity/akp-sh/akuity-platform
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        name: Checkout
        with:
          # fetch-depth: 0 needed for `git rev-list --count` to work properly
          fetch-depth: 0
      - name: Install Cosign
        uses: sigstore/cosign-installer@d58896d6a1865668819e1d91763c7751a165e159 # v3.9.2

        # Crane is a tool for interacting with remote images and registries.
        # We use is to retrieve the image digest of container manifest for signing.
      - name: Install Crane
        uses: imjasonh/setup-crane@31b88efe9de28ae0ffa220711af4b60be9435f6e # v0.4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 # v3.11.1
        with:
          driver-opts: network=host

      - name: Cache Docker layers
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Login to Google Artifact Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          registry: us-docker.pkg.dev
          username: ${{ secrets.ARTIFACT_REGISTRY_USERNAME }}
          password: ${{ secrets.ARTIFACT_REGISTRY_PASSWORD }}

      - name: Set Tag
        run: |
          TAG="${GITHUB_REF_NAME:1}"
          echo "RELEASE_TAG=${TAG}" >> $GITHUB_ENV

      - name: Build distroless base image
        run: ./hack/distroless/build-base.sh

      # Build self-hosted images with `SELF_HOSTED` environment variable,
      # which will be used as a `selfhosted` build tag when building binaries to include or exclude some features.
      - name: Build and Push Self-Hosted Images
        if: github.event_name != 'pull_request'
        run: make image
        env:
          PUSH_IMAGE: "true"
          GH_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
          SELF_HOSTED: "true"
          IMAGE_REPO: ${{ env.AKUITY_PLATFORM_SH_REPO }}
          TAG: ${{ env.RELEASE_TAG }}
          LICENSE_PUB_KEY: ${{ secrets.AKUITY_LICENSE_PUBLIC_KEY }}
          BASE_IMAGE: localhost:5000/akp-distroless-base:latest

      - name: Git digest of container images
        if: github.event_name != 'pull_request'
        run: |
          set -xo pipefail
          echo "AKUITY_PLATFORM_SH_DIGEST=$(crane digest ${{ env.AKUITY_PLATFORM_SH_REPO }}:${{ env.RELEASE_TAG }})" >> $GITHUB_ENV

      - name: Sign akuity-platform self-hosted images
        run: |
          cosign sign \
          -a "repo=${{ github.repository }}" \
          -a "workflow=${{ github.workflow }}" \
          -a "sha=${{ github.sha }}" \
          --yes \
          ${AKUITY_PLATFORM_SH_REPO}@${{ env.AKUITY_PLATFORM_SH_DIGEST}}

      # Publish SBOMS
      - name: Publish SBOM
        uses: anchore/sbom-action@7b36ad622f042cab6f59a75c2ac24ccb256e9b45 # v0.20.4
        with:
          image: ${{ env.AKUITY_PLATFORM_SH_REPO }}:${{ env.RELEASE_TAG }}
          artifact-name: akuity-${{ env.RELEASE_TAG }}-sbom.spdx

  trigger-publish-docs:
    name: Trigger Publish Docs Workflow
    needs: [push-images]
    runs-on: ubuntu-latest
    steps:
      - name: Trigger publish docs workflow
        uses: peter-evans/repository-dispatch@ff45666b9427631e3450c54a1bcbee4d9ff4d7c0 # v3.0.0
        with:
          token: ${{ secrets.AKUITYBOT_PAT }}
          event-type: trigger-publish-docs

  publish-chart:
    needs: push-images
    if: startsWith(github.ref, 'refs/tags/v')
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          # fetch-depth: 0 needed for `git rev-list --count` to work properly
          fetch-depth: 0

      - name: Set up Helm
        uses: azure/setup-helm@b9e51907a09c216f16ebe8536097933489208112 # v4.3.0
        with:
          version: '3.10.0'

      - name: Set up Kustomize
        uses: imranismail/setup-kustomize@2ba527d4d055ab63514ba50a99456fc35684947f # v2.1.0
        with:
          github-token: ${{ secrets.AKUITYBOT_PAT }}

      - name: Login to Google Artifact Registry
        if: github.event_name != 'pull_request'
        uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
        with:
          registry: us-docker.pkg.dev
          username: ${{ secrets.ARTIFACT_REGISTRY_USERNAME }}
          password: ${{ secrets.ARTIFACT_REGISTRY_PASSWORD }}

      - name: Set Tag
        run: |
          TAG="${GITHUB_REF_NAME:1}"
          echo "RELEASE_TAG=${TAG}" >> $GITHUB_ENV

      - name: Publish Chart
        run: make chart
        env:
          PUSH_CHART: "true"
          HELM_EXPERIMENTAL_OCI: '1'
          CHART_REPO: us-docker.pkg.dev/akuity/akp-sh/charts
          TAG: ${{ env.RELEASE_TAG }}

  release-cli:
    if: startsWith(github.ref, 'refs/tags/v')
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          # fetch-depth: 0 needed for `git rev-list --count` to work properly
          fetch-depth: 0
      - name: Setup Private Git Repo Access
        run: /usr/bin/git config --global url."https://${{ secrets.AKUITYBOT_PAT }}:<EMAIL>/".insteadOf "https://github.com/"
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.24.5
          check-latest: true
          cache: false
      - name: Install tools
        run: |
          ./hack/download-quill.sh
      - name: Cache Go Modules
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-
      - name: Set Tag
        run: |
          TAG="${GITHUB_REF_NAME:1}"
          echo "RELEASE_TAG=${TAG}" >> $GITHUB_ENV
      - name: Build CLI Binaries
        if: github.event_name != 'pull_request'
        run: make release-cli
        env:
          TAG: ${{ env.RELEASE_TAG }}
          QUILL_LOG_LEVEL: debug
          QUILL_NOTARY_KEY_ID: ${{ secrets.QUILL_NOTARY_KEY_ID }}
          QUILL_NOTARY_ISSUER: ${{ secrets.QUILL_NOTARY_ISSUER }}
          QUILL_NOTARY_KEY: ${{ secrets.QUILL_NOTARY_KEY }}
          QUILL_SIGN_P12: ${{ secrets.QUILL_SIGN_P12 }}
          QUILL_SIGN_PASSWORD: ${{ secrets.QUILL_SIGN_PASSWORD }}
      - name: Configure AWS credentials
        if: github.event_name != 'pull_request'
        uses: aws-actions/configure-aws-credentials@b47578312673ae6fa5b5096b330d9fbac3d116df # v4.2.1
        with:
          role-to-assume: arn:aws:iam::505500221896:role/github-actions
          aws-region: us-west-2
      - name: Push binaries
        if: github.event_name != 'pull_request'
        env:
          CF_DISTRIBUTION_ID: "E2XN0K922XU5UC"
          VERSION: v${{ env.RELEASE_TAG }}
        run: |
          aws s3 sync "./dist/akuity-cli/${VERSION}" "s3://akuity-release/akuity-cli/${VERSION}" --acl public-read

  release-notify:
    if: startsWith(github.ref, 'refs/tags/v')
    name: Release Notification
    needs: publish-chart
    runs-on: ubuntu-latest
    steps:
      - name: Set Tag
        run: |
          TAG="${GITHUB_REF_NAME:1}"
          echo "RELEASE_TAG=${TAG}" >> $GITHUB_ENV
      - name: Notify Slack
        shell: bash
        env:
          SLACK_TOKEN: ${{ secrets.ALERT_SLACK_BOT_TOKEN }}
          VERSION: ${{ env.RELEASE_TAG }}
          CHANNEL: akuity-platform
        run: |
            curl -H "Content-type: application/json" \
            --data "{\"channel\":\"${CHANNEL}\",\"blocks\":[{\"type\":\"section\",\"text\":{\"type\":\"mrkdwn\",\"text\":\":akuity: v${VERSION} of the Akuity Platform has been released. Check https://github.com/akuityio/akuity-platform/releases/tag/v${VERSION}\"}}]}" \
            -H "Authorization: Bearer ${SLACK_TOKEN}" \
            -X POST https://slack.com/api/chat.postMessage
