name: Terraform Provider Sync

on:
  workflow_dispatch:
    inputs:
      commit:
        description: Commit SHA of the source repository to sync
        default: "main"
        type: string
  workflow_call:
    inputs:
      commit:
        description: Commit SHA of the source repository to sync
        default: "main"
        type: string

env:
  GOPRIVATE: "github.com/akuityio"
  GOWORK: "off"

jobs:
  copy-terraform:
    name: Copy Terraform to Repo
    needs: [build-outputs]
    env:
      FOLDERS: akp docs examples templates
      PATHS: Makefile main.go
      SRC_PATH: akuity-platform/terraform
      DEST_PATH: terraform-provider-akp
      DEST_REF: main
      COMMIT: ${{ inputs.commit }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{ env.COMMIT }}
          path: ${{ env.SRC_PATH }}
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          repository: akuity/terraform-provider-akp
          ref: ${{ env.DEST_REF }}
          token: ${{ secrets.AKUITYBOT_PAT }}
          path: ${{ env.DEST_PATH }}
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.24.5
          check-latest: true
          cache: false
      - name: Sync directories
        run: |
          #!/usr/bin/env bash
          set -e

          for folder in ${FOLDERS[@]}; do
            mkdir -p "${DEST_PATH}/${folder}"
            rsync -a --delete "${SRC_PATH}/${folder}/" "${DEST_PATH}/${folder}"
          done
          for path in ${PATHS[@]}; do
            mkdir -p "${DEST_PATH}"
            rsync -a --delete "${SRC_PATH}/${path}" "${DEST_PATH}/${path}"
          done
      - name: Rewrite import path
        working-directory: ${{ env.DEST_PATH }}
        run: |
          sed -i'' 's|github.com/akuityio/akuity-platform/terraform|github.com/akuity/terraform-provider-akp|' ./**/*.go
      - name: Update and tidy go module
        working-directory: ${{ env.DEST_PATH }}
        run: go get -u && go mod tidy
      - name: Commit changes
        uses: stefanzweifel/git-auto-commit-action@778341af668090896ca464160c2def5d1d1a3eb0 # v6.0.1
        with:
          commit_message: Automatic provider sync from ${{ env.COMMIT }}
          repository: ${{ env.DEST_PATH }}
          commit_user_name: Akuity Bot
          commit_user_email: <EMAIL>
          commit_author: Akuity Bot <<EMAIL>>
          # Reenable when we're comfortable auto releasing a version. For now, tag in the repo
          # tagging_message: v${{ env.VERSION }}