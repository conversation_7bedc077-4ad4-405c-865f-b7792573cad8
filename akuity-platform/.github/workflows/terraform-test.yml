# Terraform Provider testing workflow.
name: Tests

on:
  pull_request:
    paths:
      - 'terraform/**'
      - '!terraform/README.md'
      - '.github/workflows/terraform-test.yml'
  push:
    branches:
      - "main"
    paths:
      - 'terraform/**'
      - '!terraform/README.md'
      - '.github/workflows/terraform-test.yml'

# Testing only needs permissions to read the repository contents.
permissions:
  contents: read

env:
  GOPRIVATE: "github.com/akuityio"
  GOWORK: "off"

jobs:
  # Ensure project builds before running testing matrix
  build:
    name: Build
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: 'go.mod'
          cache: true
      - name: Setup Private Git Repo Access
        run: /usr/bin/git config --global url."https://${{ secrets.AKUITYBOT_PAT }}:<EMAIL>/".insteadOf "https://github.com/"
      - run: go mod download
      - run: go build -v .
        working-directory: terraform

  unit-test:
    name: Unit Tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: 'go.mod'
          cache: true
      - name: Setup Private Git Repo Access
        run: /usr/bin/git config --global url."https://${{ secrets.AKUITYBOT_PAT }}:<EMAIL>/".insteadOf "https://github.com/"
      - run: make unit-test
        working-directory: terraform

  # Run acceptance tests in a matrix with Terraform CLI versions
  acceptance-test:
    name: Terraform Provider Acceptance Tests
    needs: unit-test
    runs-on: ubuntu-latest
    timeout-minutes: 45
    strategy:
      fail-fast: true
      matrix:
        # list whatever Terraform versions here you would like to support
        terraform:
          - '1.9.*'
          - '1.10.*'
          - '1.11.*'
          - '1.12.*'
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version-file: 'go.mod'
          cache: true
      - uses: hashicorp/setup-terraform@b9cd54a3c349d3f38e8881555d616ced269862dd # v3.1.2
        with:
          terraform_version: ${{ matrix.terraform }}
          terraform_wrapper: false
      - name: Setup Private Git Repo Access
        run: /usr/bin/git config --global url."https://${{ secrets.AKUITYBOT_PAT }}:<EMAIL>/".insteadOf "https://github.com/"
      - run: go mod download
      - env:
          TF_ACC: "1"
          AKUITY_ORG_NAME: "terraform-provider-acceptance-test"
          AKUITY_INSTANCE_ID: "6pzhawvy4echbd8x"
          AKUITY_API_KEY_ID: "${{ secrets.AKUITY_API_KEY_ID }}"
          AKUITY_API_KEY_SECRET: "${{ secrets.AKUITY_API_KEY_SECRET }}"
        run: make acc-test
        working-directory: terraform
        timeout-minutes: 20
