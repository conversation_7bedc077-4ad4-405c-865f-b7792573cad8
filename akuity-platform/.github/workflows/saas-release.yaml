name: SaaS Release

on:
  workflow_dispatch:
  push:
    branches:
      - main
    paths:
      - 'SAAS_VERSION'
env:
  GOPRIVATE: "github.com/akuityio"
  GOWORK: "off"

jobs:
  build-outputs:
    runs-on: ubuntu-latest
    outputs:
      git-commit: ${{ steps.saas-commit.outputs.commit }}
      version: ${{ steps.saas-version.outputs.version }}
      pseudo-version: ${{ steps.pseudo-version.outputs.pseudo-version }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Extract git-commit
        id: saas-commit
        run: |
          # Dirty little hack to get the a Full_SHA since actions/checkout does not support a short SHA
          SHORT_SHA=$(cat SAAS_VERSION | awk -F'-' '{print $NF}')
          FULL_SHA=$(git rev-parse "$SHORT_SHA")
          echo "Resolved full SHA: $FULL_SHA"
          echo "commit=$FULL_SHA" >> $GITHUB_OUTPUT
      - name: Extract version
        id: saas-version
        run: |
          echo "version=$(cat SAAS_VERSION | awk -F'-' '{print $1}')" >> $GITHUB_OUTPUT
      - name: Extract pseudo-version
        id: pseudo-version
        run: |
          echo "pseudo-version=$(cat SAAS_VERSION)" >> $GITHUB_OUTPUT

  publish-api-cli-docs:
    needs: [build-outputs]
    runs-on: ubuntu-latest
    env: 
      COMMIT: ${{ needs.build-outputs.outputs.git-commit }}
      VERSION: ${{ needs.build-outputs.outputs.version }}
      PSEUDO_VERSION: ${{ needs.build-outputs.outputs.pseudo-version }}
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{env.COMMIT}}
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.24.3
          check-latest: true
          cache: false
      - name: Cache Go Modules
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Setup private git repo access
        env:
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
        run: |
          /usr/bin/git config --global url."https://${GITHUB_TOKEN}:<EMAIL>/".insteadOf "https://github.com/"
      - name: Clone Docs Repo
        env:
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
        run: |
          git clone "https://bot:$<EMAIL>/akuityio/docs.akuity.io.git" ../docs.akuity.io
      - name: Generate Docs
        run: |
          find ../docs.akuity.io/docs/50-reference/cli -type f -name "*.md" -delete || echo "No files to delete"
          mkdir -p ../docs.akuity.io/docs/50-reference/cli
          go run ./hack/cli-docs ../docs.akuity.io/docs/50-reference/cli
          cp docs/generated/swagger/apidocs.swagger.yaml ../docs.akuity.io/static/apidocs.swagger.yaml
          cp docs/generated/notifications.md ../docs.akuity.io/docs/50-reference/notifications.md
      - name: Create PR
        env:
          GITHUB_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
        run: |
          cd ../docs.akuity.io
          git config --global user.name "Akuity Bot"
          git config --global user.email "<EMAIL>"
          git checkout -b akuity-bot/docs-update
          git add .
          git commit -m "Update CLI/API docs"
          git push origin akuity-bot/docs-update --force
          gh pr create --title "Update CLI/API docs to $PSEUDO_VERSION " --body "Update CLI/API docs"
  # NOTE: This is commented out until we are confident we've done all the automation we need to do
  # here. We don't want to copy over everything until we're ready. This also should check that the
  # terraform has actually changed before copying.
  # copy-terraform:
  #   name: Copy Terraform to Repo
  #   needs: [build-outputs]
  #   uses: ./.github/workflows/terraform.yml
  #   with:
  #     commit: ${{ needs.build-outputs.outputs.git-commit }}

  generate-crossplane-types:
    name: Generate Crossplane Types
    needs: [build-outputs]
    env:
      SRC_PATH: akuity-platform
      DEST_PATH: provider-crossplane-akuity
      DEST_REF: main
      COMMIT: ${{ needs.build-outputs.outputs.git-commit }}
      VERSION: ${{ needs.build-outputs.outputs.version }}
      PSEUDO_VERSION: ${{ needs.build-outputs.outputs.pseudo-version }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{env.COMMIT}}
          path: ${{ env.SRC_PATH }}
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          repository: akuity/provider-crossplane-akuity
          ref: ${{ env.DEST_REF }}
          token: ${{ secrets.AKUITYBOT_PAT }}
          path: ${{ env.DEST_PATH }}
          submodules: 'true'
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.24.3
          check-latest: true
          cache: false
      - name: Generate Files
        run: make generate-crossplanetypes
        working-directory: ${{ env.SRC_PATH }}
      - name: Copy Files
        run: |
          #!/usr/bin/env bash
          set -e
          cp -r "${SRC_PATH}/crossplane-gen/." "${DEST_PATH}/internal/types/generated"
      - name: Run Make Generate in Crossplane Provider Repository
        run: |
          #!/usr/bin/env bash
          set -e
          make generate
        working-directory: ${{ env.DEST_PATH }}
      - name: Create Pull Request on Crossplane Provider Repository
        uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
        with:
          path: ${{ env.DEST_PATH }}
          title: Automatic Update Crossplane Types to ${{ env.PSEUDO_VERSION }}
          body: |
            Automatic Update Crossplane Types to ${{ env.PSEUDO_VERSION }}
          branch: update-crossplane-types
          token: ${{ secrets.AKUITYBOT_PAT }}
          delete-branch: true
          commit-message: Automatic Update Crossplane Types to ${{ env.PSEUDO_VERSION }}
          committer: Akuity Bot <<EMAIL>>
          author: Akuity Bot <<EMAIL>>

  release-cli:
    needs: [build-outputs]
    runs-on: ubuntu-latest
    env:
      COMMIT: ${{ needs.build-outputs.outputs.git-commit }}
      VERSION: ${{ needs.build-outputs.outputs.version }}
      PSEUDO_VERSION: ${{ needs.build-outputs.outputs.pseudo-version }}
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{env.COMMIT}}
          fetch-depth: 0
      - name: Setup Private Git Repo Access
        run: /usr/bin/git config --global url."https://${{ secrets.AKUITYBOT_PAT }}:<EMAIL>/".insteadOf "https://github.com/"
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.24.3
          check-latest: true
          cache: false
      - name: Install tools
        run: |
          ./hack/download-quill.sh
      - name: Cache Go Modules
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-
      - name: Build CLI Binaries
        run: make release-cli
        env:
          TAG: ${{ env.VERSION }}
          VERSION: ${{ env.PSEUDO_VERSION }}
          QUILL_LOG_LEVEL: debug
          QUILL_NOTARY_KEY_ID: ${{ secrets.QUILL_NOTARY_KEY_ID }}
          QUILL_NOTARY_ISSUER: ${{ secrets.QUILL_NOTARY_ISSUER }}
          QUILL_NOTARY_KEY: ${{ secrets.QUILL_NOTARY_KEY }}
          QUILL_SIGN_P12: ${{ secrets.QUILL_SIGN_P12 }}
          QUILL_SIGN_PASSWORD: ${{ secrets.QUILL_SIGN_PASSWORD }}
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@b47578312673ae6fa5b5096b330d9fbac3d116df # v4.2.1
        with:
          role-to-assume: arn:aws:iam::505500221896:role/github-actions
          aws-region: us-west-2
      - name: Push binaries
        env:
          CF_DISTRIBUTION_ID: "E2XN0K922XU5UC"
          VERSION: ${{ env.PSEUDO_VERSION }}
        run: |
          aws s3 sync "./dist/akuity-cli/${VERSION}" "s3://akuity-release/akuity-cli/v${VERSION}" --acl public-read

          printf "v${VERSION}" > ./dist/akuity-cli/stable.txt
          aws s3 cp ./dist/akuity-cli/stable.txt s3://akuity-release/akuity-cli/stable.txt --acl public-read
          aws cloudfront create-invalidation \
            --distribution-id="${CF_DISTRIBUTION_ID}" \
            --paths "/akuity-cli/stable.txt"

  release-api-client-go:
    name: Release Go API Client
    needs:
      - build-outputs
      - release-cli
    runs-on: ubuntu-latest
    env:
      FOLDERS: pkg/api pkg/utils
      SRC_PATH: akuity-platform
      DEST_PATH: api-client-go
      DEST_REF: main
      COMMIT: ${{ needs.build-outputs.outputs.git-commit }}
      VERSION: ${{ needs.build-outputs.outputs.version }}
      PSEUDO_VERSION: ${{ needs.build-outputs.outputs.pseudo-version }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          ref: ${{env.COMMIT}}
          path: ${{ env.SRC_PATH }}
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          repository: akuity/api-client-go
          ref: ${{ env.DEST_REF }}
          token: ${{ secrets.AKUITYBOT_PAT }}
          path: ${{ env.DEST_PATH }}
      - name: Setup Go
        uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
        with:
          go-version: 1.24.3
          check-latest: true
          cache: false
      - name: Sync directories
        run: |
          #!/usr/bin/env bash
          set -e

          for folder in ${FOLDERS[@]}; do
            mkdir -p "${DEST_PATH}/${folder}"
            rsync -a --delete "${SRC_PATH}/${folder}/" "${DEST_PATH}/${folder}"
          done
      - name: Rewrite import path
        working-directory: ${{ env.DEST_PATH }}
        run: |
          #!/usr/bin/env bash

          set -exo pipefail

          for pkg in $(go list ./...)
          do
            old_pkg="${pkg/akuity/akuityio}"
            old_pkg="${old_pkg/api-client-go/akuity-platform}"
            gofmt -w -r "\"${old_pkg}\" -> \"${pkg}\"" .
          done
      - name: Tidy go module
        working-directory: ${{ env.DEST_PATH }}
        run: go mod tidy
      - name: Test rewritten package
        working-directory: ${{ env.DEST_PATH }}
        run: go test ./...
      - name: Commit changes
        uses: stefanzweifel/git-auto-commit-action@778341af668090896ca464160c2def5d1d1a3eb0 # v6.0.1
        with:
          commit_message: Automatic Update to ${{ env.PSEUDO_VERSION }}
          repository: ${{ env.DEST_PATH }}
          commit_user_name: Akuity Bot
          commit_user_email: <EMAIL>
          commit_author: Akuity Bot <<EMAIL>>
          tagging_message: v${{ env.VERSION }}

# Disabling bump-cask, they should now automatically get bumped
#  bump-cask:
#    name: Update Homebrew Cask
#    needs:
#      - build-outputs
#      - release-cli
#    runs-on: macos-14
#    env:
#      COMMIT: ${{ needs.build-outputs.outputs.git-commit }}
#      VERSION: ${{ needs.build-outputs.outputs.version }}
#      PSEUDO_VERSION: ${{ needs.build-outputs.outputs.pseudo-version }}
#    steps:
#      - name: Sync Homebrew-Cask Fork
#        shell: bash
#        env:
#            GH_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
#        run: gh repo sync akuity/homebrew-cask -b master
#      - name: Set up Homebrew
#        id: set-up-homebrew
#        uses: Homebrew/actions/setup-homebrew@master
#        with:
#          test-bot: false
#      - name: Bump Akuity Cask
#        shell: bash
#        env:
#          HOMEBREW_NO_INSTALL_FROM_API: "1"
#          HOMEBREW_DEVELOPER: "1"
#          HOMEBREW_GITHUB_API_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
#          GH_TOKEN: ${{ secrets.AKUITYBOT_PAT }}
#          VERSION: ${{ env.PSEUDO_VERSION }}
#        run: |
#            set -xe
#
#            rm -rf /opt/homebrew/Library/Taps/homebrew/homebrew-cask
#            gh repo clone akuity/homebrew-cask /opt/homebrew/Library/Taps/homebrew/homebrew-cask -- --branch master
#
#            git config --global url."https://${GH_TOKEN}:<EMAIL>/".insteadOf "https://github.com/"
#            git config --global user.name "Akuity Bot"
#            git config --global user.email "<EMAIL>"
#
#            brew bump-cask-pr akuity --version ${VERSION} --write-only
#
#            cd /opt/homebrew/Library/Taps/homebrew/homebrew-cask
#            if [[ -n $(git diff --numstat) ]]; then
#                git checkout -b akuity-v${VERSION}
#                git add .
#                git commit -m "akuity v${VERSION}"
#                git push origin akuity-v${VERSION}
#                gh pr create -R Homebrew/homebrew-cask --title "akuity v${VERSION}" --body 'Created with `brew bump-cask-pr`.' -H akuity:akuity-v${VERSION} -B master
#            else
#                echo "No changes needed..."
#            fi
