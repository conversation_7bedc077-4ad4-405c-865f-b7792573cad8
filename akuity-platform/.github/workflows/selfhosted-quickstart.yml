name: Upload SH Quickstart Zip

on:
  push:
    branches:
      - main
    paths:
      - "files/quickstart/**"

jobs:
  upload-sh-support-bundle:
    name: Upload SH Quickstart Zip
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Create SH Quickstart Zip
        run: |
          (cd files/self-hosted/ && zip -r ../../quickstart.zip ./quickstart)

      - name: Upload Support Bundle
        uses: ./.github/actions/upload-s3
        with:
          filePath: quickstart.zip
          bucketPath: self-hosted
