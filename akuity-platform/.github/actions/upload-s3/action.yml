name: "Upload File to Akuity Release S3 Bucket"
description: "Uploads a file to the Akuity release S3 bucket for public access."
inputs:
  filePath:
    description: "Path to the file to upload, relative to the support directory"
    required: true
  bucketPath:
    description: "Path in the S3 bucket where the file will be uploaded. This will be prefixed with 's3://akuity-release/'"
    required: true
runs:
  using: "composite"
  steps:
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@b47578312673ae6fa5b5096b330d9fbac3d116df # v4.2.1
      with:
        role-to-assume: arn:aws:iam::505500221896:role/github-actions
        aws-region: us-west-2

    - name: Validate file exists
      shell: bash
      run: |
        if [ ! -f "${{ inputs.filePath }}" ]; then
          echo "Error: File ${{ inputs.filePath }} does not exist"
          exit 1
        fi
        echo "File validated: ${{ inputs.filePath }}"

    - name: Upload to S3
      shell: bash
      run: |
        aws s3 cp "${{ inputs.filePath }}" "s3://akuity-release/${{ inputs.bucketPath }}/${{ inputs.filePath }}" --acl public-read
        echo "Successfully uploaded ${{ inputs.filename }} to S3"
